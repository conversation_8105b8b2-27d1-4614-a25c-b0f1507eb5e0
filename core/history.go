package core

import (
	"context"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

const (
	InitialVersion = "initial"
)

type File struct {
	Id        string `json:"id,omitempty"`
	SessionId string `json:"session_id,omitempty"`
	Path      string `json:"path,omitempty"`
	Content   string `json:"content,omitempty"`
	Version   string `json:"version,omitempty"`
	CreatedAt int64  `json:"created_at,omitempty"`
	UpdatedAt int64  `json:"updated_at,omitempty"`
}

type HistoryService interface {
	pubsub.Subscriber[File]
	Create(ctx context.Context, sessionId, path, content string) (File, error)
	CreateVersion(ctx context.Context, sessionId, path, content string) (File, error)
	GetByPathAndSession(ctx context.Context, path, sessionId string) (File, error)
	ListBySession(ctx context.Context, sessionId string) ([]File, error)
	ListLatestSessionFiles(ctx context.Context, sessionId string) ([]File, error)
}

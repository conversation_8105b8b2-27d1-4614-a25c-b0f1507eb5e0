package config

import "reflect"

func appendServers(serverMap map[string]McpServer, servers []McpServer, scope ConfigScope) []McpServer {
	if serverMap != nil {
		for name, server := range serverMap {
			server.Name = name
			server.Scope = scope
			servers = append(servers, server)
		}
	}
	return servers
}

func deepCopy(src interface{}) interface{} {
	if src == nil {
		return nil
	}

	original := reflect.ValueOf(src)
	cpy := reflect.New(original.Type()).Elem()
	copyRecursive(original, cpy)

	return cpy.Interface()
}

func copyRecursive(src, dst reflect.Value) {
	switch src.Kind() {
	case reflect.Ptr:
		originalValue := src.Elem()
		if !originalValue.IsValid() {
			return
		}
		dst.Set(reflect.New(originalValue.Type()))
		copyRecursive(originalValue, dst.Elem())
	case reflect.Interface:
		if src.IsNil() {
			return
		}
		originalValue := src.Elem()
		copyValue := reflect.New(originalValue.Type()).Elem()
		copyRecursive(originalValue, copyValue)
		dst.Set(copyValue)
	case reflect.Struct:
		for i := 0; i < src.NumField(); i++ {
			if dst.Field(i).CanSet() {
				copyRecursive(src.Field(i), dst.Field(i))
			}
		}
	case reflect.Slice:
		if src.IsNil() {
			return
		}
		dst.Set(reflect.MakeSlice(src.Type(), src.Len(), src.Cap()))
		for i := 0; i < src.Len(); i++ {
			copyRecursive(src.Index(i), dst.Index(i))
		}
	case reflect.Map:
		if src.IsNil() {
			return
		}
		dst.Set(reflect.MakeMap(src.Type()))
		for _, key := range src.MapKeys() {
			originalValue := src.MapIndex(key)
			copyValue := reflect.New(originalValue.Type()).Elem()
			copyRecursive(originalValue, copyValue)
			copyKey := reflect.New(key.Type()).Elem()
			copyRecursive(key, copyKey)
			dst.SetMapIndex(copyKey, copyValue)
		}
	default:
		dst.Set(src)
	}
}

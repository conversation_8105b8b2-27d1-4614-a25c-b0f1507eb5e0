package config

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/client/transport"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/utils"
	"github.com/qoder-ai/qodercli/core/version"
	"github.com/qoder-ai/qodercli/qoder"
	"github.com/shirou/gopsutil/process"
)

type ConfigScope string

const (
	UserScope    ConfigScope = "user"
	LocalScope   ConfigScope = "local"
	ProjectScope ConfigScope = "project"
)

type McpType string

const (
	McpStdio McpType = "stdio"
	McpSse   McpType = "sse"
	McpHttp  McpType = "http"
)

type AgentName string

const (
	AgentCoder      AgentName = "coder"
	AgentSummarizer AgentName = "summarizer"
	AgentTask       AgentName = "task"
	AgentTitle      AgentName = "title"

	MaxTokensFallbackDefault = 4096
)

type AgentConfig struct {
	Model           models.ModelId `json:"model"`
	MaxTokens       int64          `json:"maxTokens"`
	ReasoningEffort string         `json:"reasoningEffort"`
}

type ProjectConfig struct {
	McpServers map[string]McpServer `json:"mcpServers,omitempty"`
	History    []HistoryEntry       `json:"history"`

	// 以下字段尚未使用，只是从claude配置中复制过来，使用了就往上面挪一下吧
	AllowedTools                            []interface{} `json:"allowedTools"`
	McpContextUris                          []interface{} `json:"mcpContextUris"`
	EnabledMcpjsonServers                   []interface{} `json:"enabledMcpjsonServers"`
	DisabledMcpjsonServers                  []interface{} `json:"disabledMcpjsonServers"`
	HasTrustDialogAccepted                  bool          `json:"hasTrustDialogAccepted"`
	HasTrustDialogHooksAccepted             bool          `json:"hasTrustDialogHooksAccepted"`
	IgnorePatterns                          []interface{} `json:"ignorePatterns"`
	ProjectOnboardingSeenCount              int           `json:"projectOnboardingSeenCount"`
	HasClaudeMdExternalIncludesApproved     bool          `json:"hasClaudeMdExternalIncludesApproved"`
	HasClaudeMdExternalIncludesWarningShown bool          `json:"hasClaudeMdExternalIncludesWarningShown"`
	HasCompletedProjectOnboarding           bool          `json:"hasCompletedProjectOnboarding"`
	ExampleFiles                            []string      `json:"exampleFiles"`
	ExampleFilesGeneratedAt                 int64         `json:"exampleFilesGeneratedAt"`
	LastCost                                float64       `json:"lastCost"`
	LastAPIDuration                         int           `json:"lastAPIDuration"`
	LastDuration                            int           `json:"lastDuration"`
	LastLinesAdded                          int           `json:"lastLinesAdded"`
	LastLinesRemoved                        int           `json:"lastLinesRemoved"`
	LastTotalInputTokens                    int           `json:"lastTotalInputTokens"`
	LastTotalOutputTokens                   int           `json:"lastTotalOutputTokens"`
	LastTotalCacheCreationInputTokens       int           `json:"lastTotalCacheCreationInputTokens"`
	LastTotalCacheReadInputTokens           int           `json:"lastTotalCacheReadInputTokens"`
	LastTotalWebSearchRequests              int           `json:"lastTotalWebSearchRequests"`
	LastSessionId                           string        `json:"lastSessionId"`
}

// HistoryEntry 历史命令存根
type HistoryEntry struct {
	Display        string           `json:"display"`
	PastedContents []PastedFileInfo `json:"pastedContents"`
}

// PastedFileInfo 可恢复的附件信息
type PastedFileInfo struct {
	Path     string `json:"path"`
	MimeType string `json:"mimeType"`
}

type McpServer struct {
	Name      string            `json:"-"`
	Scope     ConfigScope       `json:"-"`
	Command   string            `json:"command,omitempty"`
	Env       map[string]string `json:"env,omitempty"`
	Args      []string          `json:"args"`
	Type      McpType           `json:"type"`
	URL       string            `json:"url,omitempty"`
	Headers   map[string]string `json:"headers,omitempty"`
	serverCmd *exec.Cmd         // MCP服务进程，仅stdio类型的字段有意义
}

func (s *McpServer) commandFunc(ctx context.Context, command string, env []string, args []string) (*exec.Cmd, error) {
	cmd := exec.CommandContext(ctx, command, args...)
	cmd.Env = append(os.Environ(), env...)
	s.serverCmd = cmd
	return cmd, nil
}

func (s *McpServer) AppendEnv(key, value string) {
	if s.Env == nil {
		s.Env = make(map[string]string)
	}
	s.Env[key] = value
}

func (s *McpServer) InitClient(ctx context.Context) (cli *client.Client, err error) {
	// 构建MCP客户端
	switch s.Type {
	case McpStdio:
		var envs []string
		for k, v := range s.Env {
			envs = append(envs, fmt.Sprintf("%s=%s", k, v))
		}
		cli, err = client.NewStdioMCPClientWithOptions(s.Command, envs, s.Args, transport.WithCommandFunc(s.commandFunc))
	case McpSse:
		headers := client.WithHeaders(s.Headers)
		cli, err = client.NewSSEMCPClient(s.URL, headers)
	case McpHttp:
		headers := transport.WithHTTPHeaders(s.Headers)
		cli, err = client.NewStreamableHttpClient(s.URL, headers)
	default:
		return nil, fmt.Errorf("invalid mcp server type")
	}
	if err != nil {
		return nil, err
	}

	if err := cli.Start(ctx); err != nil {
		return nil, fmt.Errorf("failed to start %s transport: %w", s.Type, err)
	}

	// 发送初始化请求
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    core.AppName,
		Version: version.Version,
	}

	// 创建一个可取消的上下文，用于控制初始化过程
	initCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	// 创建一个错误通道，用于接收 verifyMcpServerProcess 的错误
	errChan := make(chan error, 1)

	// 对stdio类型的mcp server, 启动 verifyMcpServerProcess 在后台运行

	if s.Type == McpStdio {
		go s.verifyMcpServerProcess(initCtx, errChan)
	}
	// 在另一个 goroutine 中运行 cli.Initialize，这样我们可以同时监听错误
	initDone := make(chan error, 1)
	go func() {
		_, err := cli.Initialize(initCtx, initRequest)
		initDone <- err
	}()

	// 等待初始化完成或出现错误
	select {
	case err := <-initDone:
		if err != nil {
			logging.Error("mcp server initialization failed", "name", s.Name, "error", err)
			return nil, err
		}
		logging.Info("mcp server initialized", "name", s.Name)
	case err := <-errChan:
		// 如果 verifyMcpServerProcess 检测到错误，取消初始化并返回错误
		cancel()
		logging.Error("mcp server stdio verification failed", "name", s.Name, "error", err)
		return nil, fmt.Errorf("mcp server stdio verification failed: %w", err)
	case <-ctx.Done():
		// 如果原始上下文被取消，取消初始化并返回错误
		cancel()

		return nil, ctx.Err()
	}

	return cli, nil
}

func (s *McpServer) verifyMcpServerProcess(ctx context.Context, errChan chan error) {
	pid := s.serverCmd.Process.Pid
	ticker := time.NewTicker(100 * time.Millisecond) // 每100ms检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			// 检查进程是否存活
			if !isProcessAlive(pid) {
				errChan <- fmt.Errorf("mcp server process is not alive")
				return
			}
		}
	}
}

func isProcessAlive(pid int) bool {
	p, err := process.NewProcess(int32(pid))
	if err != nil {
		return false
	}

	status, err := p.Status()
	if err != nil {
		return false
	}

	return len(status) > 0 && status[0] != 'Z' // Z 表示僵尸进程
}

type ProviderConfig struct {
	ApiKey   string `json:"apiKey"`
	Disabled bool   `json:"disabled"`
}

type LspConfig struct {
	Enabled bool     `json:"enabled"`
	Command string   `json:"command"`
	Args    []string `json:"args"`
	Options any      `json:"options"`
}

// MonitoringConfig 监控/遥测配置（用户级）
type MonitoringConfig struct {
	// 启用将日志通过自定义导出器上报到 Qoder 后端
	EnableQoderExporter bool `json:"enableQoderExporter,omitempty"`

	// 统一使用 OTLP（大写缩写），以及 Traces/Logs/Metrics 复数形式
	EnableOTLPTraces  bool   `json:"enableOtlpTraces,omitempty"`
	OTLPEndpoint      string `json:"otlpEndpoint,omitempty"`
	EnableOTLPLogs    bool   `json:"enableOtlpLogs,omitempty"`
	EnableOTLPMetrics bool   `json:"enableOtlpMetrics,omitempty"`

	// 各信号独立端点，优先于通用 OTLPEndpoint
	OTLPTracesEndpoint  string `json:"otlpTracesEndpoint,omitempty"`
	OTLPLogsEndpoint    string `json:"otlpLogsEndpoint,omitempty"`
	OTLPMetricsEndpoint string `json:"otlpMetricsEndpoint,omitempty"`
}

// Preferences TUI中/config配置项
type Preferences struct {
	AutoCompactEnabled    *bool  `json:"autoCompactEnabled,omitempty"`    // 默认为true，/config > Auto-compact 配置
	TodoFeatureEnabled    *bool  `json:"todoFeatureEnabled,omitempty"`    // 默认为true，/config > Use todo list 配置
	CheckpointingEnabled  *bool  `json:"checkpointingEnabled,omitempty"`  // 默认为true，/config > Checkpointing 配置
	Verbose               *bool  `json:"verbose,omitempty"`               // 默认为false，/config > Verbose output 配置
	AutoUpdates           *bool  `json:"autoUpdates,omitempty"`           // 默认为true，/config > Auto-updates 配置
	Theme                 string `json:"theme,omitempty"`                 // 默认为true，/config > Theme 配置
	PreferredNotifChannel string `json:"preferredNotifChannel,omitempty"` // 默认为空表示auto，disabled=notifications_disabled，/config > Notifications 配置
	EditorMode            string `json:"editorMode,omitempty"`            // 默认为空表示normal，否则为vim，/config > Editor Mode 配置
	AutoConnectIde        *bool  `json:"autoConnectIde,omitempty"`        // 默认为false，/config > Editor Mode 配置
}

func (p *Preferences) setDefaults() {
	if p.AutoCompactEnabled == nil {
		autoCompact := true
		p.AutoCompactEnabled = &autoCompact
	}
	if p.TodoFeatureEnabled == nil {
		todoFeature := true
		p.TodoFeatureEnabled = &todoFeature
	}
	if p.CheckpointingEnabled == nil {
		checkpointing := true
		p.CheckpointingEnabled = &checkpointing
	}
	if p.Verbose == nil {
		verbose := false
		p.Verbose = &verbose
	}
	if p.AutoUpdates == nil {
		autoUpdates := true
		p.AutoUpdates = &autoUpdates
	}
	if len(p.Theme) == 0 {
		p.Theme = "tron"
	}
	if p.AutoConnectIde == nil {
		autoConnectIde := false
		p.AutoConnectIde = &autoConnectIde
	}
}

// userConfig 配置文件路径~/.qoder.json，类比~/.claude.json
// 不提供对外修改接口，所有更新操作通过config.Service类进行
type userConfig struct {
	// 与Claude Code同步的配置
	Preferences
	Projects        map[string]ProjectConfig `json:"projects,omitempty"`   // ~/.qoder.json配置文件，后续添加三级settings.json配置
	McpServers      map[string]McpServer     `json:"mcpServers,omitempty"` // ~/.qoder.json配置文件
	CachedChangelog string                   `json:"cachedChangelog"`

	// 未与Claude Code同步的配置
	Providers  map[models.ModelProvider]ProviderConfig `json:"providers,omitempty"`
	LspConfigs map[string]LspConfig                    `json:"lspConfigs,omitempty"`
	Agents     map[AgentName]AgentConfig               `json:"agents,omitempty"` // 考虑去掉，在Agent.md中配置

	// 监控配置（用户级持久化）
	Monitoring MonitoringConfig `json:"monitoring,omitempty"`

	// 以下为使用字段，从ClaudeCode拷贝过来的
	NumStartups   int    `json:"numStartups"` // 同时启动的CC数量
	InstallMethod string `json:"installMethod"`
	TipsHistory   struct {
		NewUserWarmup int `json:"new-user-warmup"`
	} `json:"tipsHistory"`
	FirstStartTime               time.Time `json:"firstStartTime"`
	UserID                       string    `json:"userID"`
	AppleTerminalSetupInProgress bool      `json:"appleTerminalSetupInProgress"`
	AppleTerminalBackupPath      string    `json:"appleTerminalBackupPath"`
	OptionAsMetaKeyInstalled     bool      `json:"optionAsMetaKeyInstalled"`
	HasCompletedOnboarding       bool      `json:"hasCompletedOnboarding"`
	LastOnboardingVersion        string    `json:"lastOnboardingVersion"`
	SubscriptionNoticeCount      int       `json:"subscriptionNoticeCount"`
	HasAvailableSubscription     bool      `json:"hasAvailableSubscription"`
	ChangelogLastFetched         int64     `json:"changelogLastFetched"`
	IsQualifiedForDataSharing    bool      `json:"isQualifiedForDataSharing"`
}

func readUserConfig() (*userConfig, error) {
	cfg := userConfig{}
	path := utils.GetUserConfigPath()
	if _, err := os.Stat(path); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			return nil, err
		}
	} else if bytes, err := os.ReadFile(path); err != nil {
		return nil, err
	} else if err := json.Unmarshal(bytes, &cfg); err != nil {
		return nil, err
	}

	fetchChangelog(&cfg)
	setDefaults(&cfg)
	return &cfg, cfg.validate()
}

// Fetch changelog and set to cfg.CachedChangelog (best-effort, ignore errors)
func fetchChangelog(cfg *userConfig) {
	c := &http.Client{Timeout: 5 * time.Second}
	resp, err := c.Get("https://lingma-agents-public.oss-cn-hangzhou.aliyuncs.com/qoder-cli/changelog")
	if err != nil {
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return
	}
	if data, err := io.ReadAll(resp.Body); err == nil {
		cfg.CachedChangelog = string(data)
	}
}

func setDefaults(cfg *userConfig) {
	cfg.Preferences.setDefaults()

	if cfg.McpServers == nil {
		cfg.McpServers = make(map[string]McpServer)
	}

	if cfg.LspConfigs == nil {
		cfg.LspConfigs = make(map[string]LspConfig)
	}

	if cfg.Agents == nil {
		cfg.Agents = map[AgentName]AgentConfig{}
	}

	if cfg.Providers == nil {
		cfg.Providers = map[models.ModelProvider]ProviderConfig{}
	}

	agentNames := []AgentName{AgentCoder, AgentSummarizer, AgentTask, AgentTitle}
	for _, name := range agentNames {
		if _, exists := cfg.Agents[name]; !exists {
			cfg.Agents[name] = AgentConfig{
				MaxTokens:       MaxTokensFallbackDefault,
				Model:           models.QoderAuto,
				ReasoningEffort: "normal",
			}
		}
	}

	// OpenAI
	if apiKey := os.Getenv("QODER_OPENAI_API_KEY"); apiKey != "" {
		for _, name := range agentNames {
			agent := cfg.Agents[name]
			agent.Model = models.GPT5
			if name == AgentTitle {
				agent.MaxTokens = 1000
			} else {
				agent.MaxTokens = models.OpenAIModels[models.GPT5].DefaultMaxTokens
			}
			cfg.Agents[name] = agent
		}
		cfg.Providers[models.ProviderOpenAI] = ProviderConfig{
			ApiKey: os.Getenv("QODER_OPENAI_API_KEY"),
		}
		return
	}

	// Idealab
	if apiKey := os.Getenv("QODER_IDEALAB_API_KEY"); apiKey != "" {
		for _, name := range agentNames {
			agent := cfg.Agents[name]
			agent.Model = models.IdeaLabSonnet4Thinking
			if name == AgentTitle {
				agent.MaxTokens = 80
			} else {
				agent.MaxTokens = models.IdeaLabModels[models.IdeaLabSonnet4Thinking].DefaultMaxTokens
			}
			cfg.Agents[name] = agent
		}
		cfg.Providers[models.ProviderOpenAI] = ProviderConfig{
			ApiKey: os.Getenv("QODER_IDEALAB_API_KEY"),
		}
		return
	}

	// 如果配了百炼，优先千问
	if apiKey := os.Getenv("QODER_DASHSCOPE_API_KEY"); apiKey != "" {
		for _, name := range agentNames {
			agent := cfg.Agents[name]
			agent.Model = models.Qwen3CoderPlus
			if name == AgentTitle {
				agent.MaxTokens = 80
			} else {
				agent.MaxTokens = models.DashScopeModels[models.Qwen3CoderPlus].DefaultMaxTokens
			}
			cfg.Agents[name] = agent
		}
		cfg.Providers[models.ProviderOpenAI] = ProviderConfig{
			ApiKey: os.Getenv("QODER_DASHSCOPE_API_KEY"),
		}
		return
	}

	// 否则用Qoder
	for _, name := range agentNames {
		agent := cfg.Agents[name]
		agent.Model = models.QoderAuto
		if name == AgentTitle {
			agent.MaxTokens = 80
		} else {
			agent.MaxTokens = models.QoderModels[models.QoderAuto].DefaultMaxTokens
		}
		cfg.Agents[name] = agent
	}

	for k, v := range cfg.McpServers {
		if v.Type == "" {
			v.Type = McpStdio
			cfg.McpServers[k] = v
		}
	}
}

func (c *userConfig) validateAgent(name AgentName, agent AgentConfig) error {
	model, modelExists := models.SupportedModels[agent.Model]
	if !modelExists {
		logging.Warn("unsupported model configured, reverting to default", "agent", name, "configured_model", agent.Model)

		if c.setDefaultModelForAgent(name) {
			logging.Info("set default model for agent", "agent", name, "model", c.Agents[name].Model)
		} else {
			return fmt.Errorf("no valid provider available for agent %s", name)
		}
		return nil
	}

	provider := model.Provider
	providerCfg, providerExists := c.Providers[provider]

	if !providerExists {
		apiKey := getProviderAPIKey(provider)
		if apiKey == "" {
			logging.Warn("provider not configured for model, reverting to default", "agent", name, "model", agent.Model, "provider", provider)

			if c.setDefaultModelForAgent(name) {
				logging.Info("set default model for agent", "agent", name, "model", c.Agents[name].Model)
			} else {
				return fmt.Errorf("no valid provider available for agent %s", name)
			}
		} else {
			c.Providers[provider] = ProviderConfig{
				ApiKey: apiKey,
			}
			logging.Info("added provider from environment", "provider", provider)
		}
	} else if providerCfg.Disabled || providerCfg.ApiKey == "" {
		logging.Warn("provider is disabled or has no API key, reverting to default",
			"agent", name,
			"model", agent.Model,
			"provider", provider)

		if c.setDefaultModelForAgent(name) {
			logging.Info("set default model for agent", "agent", name, "model", c.Agents[name].Model)
		} else {
			return fmt.Errorf("no valid provider available for agent %s", name)
		}
	}

	// Validate max tokens
	if agent.MaxTokens <= 0 {
		logging.Warn("invalid max tokens, setting to default",
			"agent", name,
			"model", agent.Model,
			"max_tokens", agent.MaxTokens)

		// Update the agent with default max tokens
		updatedAgent := c.Agents[name]

		if name == AgentTitle {
			updatedAgent.MaxTokens = 80
		} else if model.DefaultMaxTokens > 0 {
			updatedAgent.MaxTokens = model.DefaultMaxTokens
		} else {
			updatedAgent.MaxTokens = MaxTokensFallbackDefault
		}
		c.Agents[name] = updatedAgent
	} else if model.ContextWindow > 0 && agent.MaxTokens > model.ContextWindow/2 {
		// Ensure max tokens doesn't exceed half the context window (reasonable limit)
		logging.Warn("max tokens exceeds half the context window, adjusting",
			"agent", name,
			"model", agent.Model,
			"max_tokens", agent.MaxTokens,
			"context_window", model.ContextWindow)

		// Update the agent with adjusted max tokens
		updatedAgent := c.Agents[name]
		updatedAgent.MaxTokens = model.ContextWindow / 2
		c.Agents[name] = updatedAgent
	}

	if model.CanReason && provider == models.ProviderOpenAI {
		if agent.ReasoningEffort == "" {
			// Set default reasoning effort for models that support it
			logging.Info("setting default reasoning effort for model that supports reasoning",
				"agent", name,
				"model", agent.Model)

			// Update the agent with default reasoning effort
			updatedAgent := c.Agents[name]
			updatedAgent.ReasoningEffort = "medium"
			c.Agents[name] = updatedAgent
		} else {
			effort := strings.ToLower(agent.ReasoningEffort)
			if effort != "low" && effort != "medium" && effort != "high" {
				logging.Warn("invalid reasoning effort, setting to medium",
					"agent", name,
					"model", agent.Model,
					"reasoning_effort", agent.ReasoningEffort)

				updatedAgent := c.Agents[name]
				updatedAgent.ReasoningEffort = "medium"
				c.Agents[name] = updatedAgent
			}
		}
	} else if !model.CanReason && agent.ReasoningEffort != "" {
		// Model doesn't support reasoning but reasoning effort is set
		logging.Warn("model doesn't support reasoning but reasoning effort is set, ignoring",
			"agent", name,
			"model", agent.Model,
			"reasoning_effort", agent.ReasoningEffort)

		// Update the agent to remove reasoning effort
		updatedAgent := c.Agents[name]
		updatedAgent.ReasoningEffort = ""
		c.Agents[name] = updatedAgent
	}

	return nil
}

func getProviderAPIKey(provider models.ModelProvider) string {
	switch provider {
	case models.ProviderQoder:
		if qoder.GetCachedUserInfo() == nil {
			return ""
		}
		return qoder.GetCachedUserInfo().Name
	case models.ProviderIdeaLab:
		return os.Getenv("QODER_IDEALAB_API_KEY")
	case models.ProviderDashScope:
		return os.Getenv("QODER_DASHSCOPE_API_KEY")
	case models.ProviderOpenAI:
		return os.Getenv("QODER_OPENAI_API_KEY")
	}
	return ""
}

func (c *userConfig) setDefaultModelForAgent(agent AgentName) bool {
	if os.Getenv("QODER_OPENAI_API_KEY") != "" {
		maxTokens := int64(50000)
		if agent == AgentTitle {
			maxTokens = 80
		}

		c.Agents[agent] = AgentConfig{
			Model:     models.GPT5,
			MaxTokens: maxTokens,
		}

		return true
	}

	if os.Getenv("QODER_IDEALAB_API_KEY") != "" {
		maxTokens := int64(50000)
		if agent == AgentTitle {
			maxTokens = 80
		}

		c.Agents[agent] = AgentConfig{
			Model:     models.IdeaLabSonnet4Thinking,
			MaxTokens: maxTokens,
		}

		return true
	}

	if os.Getenv("QODER_DASHSCOPE_API_KEY") != "" {
		maxTokens := int64(50000)
		if agent == AgentTitle {
			maxTokens = 80
		}

		c.Agents[agent] = AgentConfig{
			Model:     models.Qwen3CoderPlus,
			MaxTokens: maxTokens,
		}

		return true
	}

	if qoder.GetCachedUserInfo() == nil || qoder.GetCachedUserInfo().Name == "" {
		return false
	}

	maxTokens := int64(50000)
	if agent == AgentTitle {
		maxTokens = 80
	}

	c.Agents[agent] = AgentConfig{
		Model:     models.QoderAuto,
		MaxTokens: maxTokens,
	}
	return true
}

func (c *userConfig) validate() error {
	for name, agent := range c.Agents {
		if err := c.validateAgent(name, agent); err != nil {
			return err
		}
	}

	for provider, providerCfg := range c.Providers {
		if providerCfg.ApiKey == "" && !providerCfg.Disabled {
			fmt.Printf("provider has no API key, marking as disabled %s", provider)
			logging.Warn("provider has no API key, marking as disabled", "provider", provider)
			providerCfg.Disabled = true
			c.Providers[provider] = providerCfg
		}
	}

	for language, lspConfig := range c.LspConfigs {
		if lspConfig.Command == "" && lspConfig.Enabled {
			logging.Warn("LSP configuration has no command, marking as disabled", "language", language)
			lspConfig.Enabled = false
			c.LspConfigs[language] = lspConfig
		}
	}

	return nil
}

func (c *userConfig) save() error {
	bytes, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return err
	}
	if len(bytes) == 0 {
		return fmt.Errorf("failed to save empty user config")
	}
	return os.WriteFile(utils.GetUserConfigPath(), bytes, 0644)
}

func (c *userConfig) addMcpServer(name string, mcpServer McpServer) error {
	if c.McpServers == nil {
		c.McpServers = make(map[string]McpServer)
	}
	if _, ok := c.McpServers[name]; ok {
		return fmt.Errorf("MCP server %s already exists in user config", name)
	}
	c.McpServers[name] = mcpServer
	return c.save()
}

func (c *userConfig) removeMcpServer(name string) (bool, error) {
	if c.McpServers == nil {
		return false, nil
	}
	if _, ok := c.McpServers[name]; !ok {
		return false, nil
	}
	delete(c.McpServers, name)
	return true, c.save()
}

func (c *userConfig) addProjectMcpServer(projectPath string, name string, mcpServer McpServer) error {
	if c.Projects == nil {
		c.Projects = make(map[string]ProjectConfig)
	}
	if _, ok := c.Projects[projectPath]; !ok {
		c.Projects[projectPath] = ProjectConfig{}
	}
	project := c.Projects[projectPath]
	if project.McpServers == nil {
		project.McpServers = make(map[string]McpServer)
	}
	if _, ok := project.McpServers[name]; ok {
		return fmt.Errorf("MCP server %s already exists in project %s", name, projectPath)
	}
	project.McpServers[name] = mcpServer
	c.Projects[projectPath] = project
	return c.save()
}

func (c *userConfig) removeProjectMcpServer(projectPath string, name string) (bool, error) {
	if c.Projects == nil {
		return false, nil
	}
	if _, ok := c.Projects[projectPath]; !ok {
		return false, nil
	}
	project := c.Projects[projectPath]
	if project.McpServers == nil {
		return false, nil
	}
	if _, ok := project.McpServers[name]; !ok {
		return false, nil
	}
	delete(c.Projects[projectPath].McpServers, name)
	return true, c.save()
}

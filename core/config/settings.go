package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"github.com/qoder-ai/qodercli/core/utils"
)

type Permission struct {
}

type Settings struct {
	ApiKeyHelper               any                        `json:"apiKeyHelper,omitempty"`
	CleanupPeriodDays          any                        `json:"cleanupPeriodDays,omitempty"`
	Env                        map[string]string          `json:"env,omitempty"`
	IncludeCoAuthoredBy        any                        `json:"includeCoAuthoredBy,omitempty"`
	Permissions                []Permission               `json:"permissions,omitempty"`
	Hooks                      map[HookEvent][]HookConfig `json:"hooks,omitempty"`
	Model                      string                     `json:"model,omitempty"`
	StatusLine                 any                        `json:"statusLine,omitempty"`
	ForceLoginMethod           any                        `json:"forceLoginMethod,omitempty"`
	EnableAllProjectMcpServers bool                       `json:"enableAllProjectMcpServers,omitempty"`
	EnabledMcpjsonServers      []string                   `json:"enabledMcpjsonServers,omitempty"`
	DisabledMcpjsonServers     []string                   `json:"disabledMcpjsonServers,omitempty"`
	AwsAuthRefresh             any                        `json:"awsAuthRefresh,omitempty"`
	AwsCredentialExport        any                        `json:"awsCredentialExport,omitempty"`
	OutputStyle                string                     `json:"outputStyle,omitempty"`

	// 监控设置（项目级覆盖）
	Monitoring MonitoringSettings `json:"monitoring,omitempty"`
}

// MonitoringSettings 监控/遥测配置（项目/本地覆盖）
type MonitoringSettings struct {
	EnableQoderExporter *bool `json:"enableQoderExporter,omitempty"`

	// 统一使用 OTLP 缩写（大写），并采用 Traces/Logs/Metrics 复数形式
	EnableOTLPTraces  *bool `json:"enableOtlpTraces,omitempty"`
	EnableOTLPLogs    *bool `json:"enableOtlpLogs,omitempty"`
	EnableOTLPMetrics *bool `json:"enableOtlpMetrics,omitempty"`

	OTLPEndpoint string `json:"otlpEndpoint,omitempty"`

	// 各信号单独端点，优先于通用 OTLPEndpoint
	OTLPTracesEndpoint  string `json:"otlpTracesEndpoint,omitempty"`
	OTLPLogsEndpoint    string `json:"otlpLogsEndpoint,omitempty"`
	OTLPMetricsEndpoint string `json:"otlpMetricsEndpoint,omitempty"`
}

func saveSettings(settings *Settings, path string) error {
	bytes, err := json.MarshalIndent(settings, "", "  ")
	if err != nil {
		return err
	}
	if len(bytes) == 0 {
		return fmt.Errorf("failed to save empty settings to %s", path)
	}
	if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
		return err
	}
	return os.WriteFile(path, bytes, 0644)
}

func loadSettings(path string) (*Settings, error) {
	var settings Settings
	if _, err := os.Stat(path); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			return nil, err
		}
	} else if bytes, err := os.ReadFile(path); err != nil {
		return nil, err
	} else if err := json.Unmarshal(bytes, &settings); err != nil {
		return nil, err
	}
	return &settings, nil
}

func userSettingsPath() string {
	return filepath.Join(utils.GetUserStorageDir(), "settings.json")
}

func projectSettingsPath(workingDir string) string {
	return filepath.Join(utils.GetWorkspaceStorageDir(workingDir), "settings.json")
}

func projectLocalSettingsPath(workingDir string) string {
	return filepath.Join(utils.GetWorkspaceStorageDir(workingDir), "settings.local.json")
}

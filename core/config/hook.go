package config

type HookEvent string

const (
	HookEventPreToolUse       HookEvent = "PreToolUse"
	HookEventPostToolUse      HookEvent = "PostToolUse"
	HookEventNotification     HookEvent = "Notification"
	HookEventUserPromptSubmit HookEvent = "UserPromptSubmit"
	HookEventStop             HookEvent = "Stop"
	HookEventSubagentStop     HookEvent = "SubagentStop"
	HookEventPreCompact       HookEvent = "PreCompact"
	HookEventSessionStart     HookEvent = "SessionStart"
)

type HookType string

const (
	HookTypeCommand HookType = "command"
)

type Hook struct {
	Type    HookType `json:"type"`
	Command string   `json:"command"`
}

type HookConfig struct {
	Event   HookEvent `json:"-"`
	Matcher string    `json:"matcher,omitempty"`
	Hooks   []Hook    `json:"hooks,omitempty"`
}

type ToolInput struct {
	FilePath string `json:"file_path"`
	Content  string `json:"content"`
}

type ToolResponse struct {
	FilePath string `json:"filePath"`
	Success  bool   `json:"success"`
}

type HookPreToolUseInput struct {
	SessionId      string    `json:"session_id"`
	TranscriptPath string    `json:"transcript_path"`
	Cwd            string    `json:"cwd"`
	HookEventName  HookEvent `json:"hook_event_name"`
	ToolName       string    `json:"tool_name"`
	ToolInput      ToolInput `json:"tool_input"`
}

type HookPostToolUseInput struct {
	SessionId      string       `json:"session_id"`
	TranscriptPath string       `json:"transcript_path"`
	Cwd            string       `json:"cwd"`
	HookEventName  HookEvent    `json:"hook_event_name"`
	ToolName       string       `json:"tool_name"`
	ToolInput      ToolInput    `json:"tool_input"`
	ToolResponse   ToolResponse `json:"tool_response"`
}

type HookNotificationInput struct {
	SessionId      string    `json:"session_id"`
	TranscriptPath string    `json:"transcript_path"`
	Cwd            string    `json:"cwd"`
	HookEventName  HookEvent `json:"hook_event_name"`
	Message        string    `json:"message"`
}

type HookUserPromptSubmitInput struct {
	SessionId      string    `json:"session_id"`
	TranscriptPath string    `json:"transcript_path"`
	Cwd            string    `json:"cwd"`
	HookEventName  HookEvent `json:"hook_event_name"`
	Prompt         string    `json:"prompt"`
}

type HookStopInput struct {
	SessionId      string    `json:"session_id"`
	TranscriptPath string    `json:"transcript_path"`
	HookEventName  HookEvent `json:"hook_event_name"`
	StopHookActive bool      `json:"stop_hook_active"`
}

type HookSubagentStopInput HookStopInput

type HookPreCompactInput struct {
	SessionId          string    `json:"session_id"`
	TranscriptPath     string    `json:"transcript_path"`
	HookEventName      HookEvent `json:"hook_event_name"`
	Trigger            string    `json:"trigger"`
	CustomInstructions string    `json:"custom_instructions"`
}

type HookSessionStartInput struct {
	SessionId      string    `json:"session_id"`
	TranscriptPath string    `json:"transcript_path"`
	HookEventName  HookEvent `json:"hook_event_name"`
	Source         string    `json:"source"`
}

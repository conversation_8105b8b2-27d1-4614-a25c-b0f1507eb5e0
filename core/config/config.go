package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"os"
	"strconv"

	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/utils"
	"github.com/qoder-ai/qodercli/qoder"
)

// Service 这个配置作为全局的配置服务，配置来源为多个文件，对外提供固定的接口
// 配置来源：~/.qoder.json、三级settings.json文件
type Service struct {
	serviceOptions

	// 禁止直接访问如下配置，通过接口获取副本、通过接口修改配置
	userConfig           *userConfig // ~/.qoder.json
	userSettings         *Settings   // ~/.qoder/settings.json
	projectSettings      *Settings   // ${project}/.qoder/settings.json
	projectLocalSettings *Settings   // ${project}/.qoder/settings.local.json
}

// Option 定义构造 Service 的可选项
// 使用方式：NewService(WithWorkingDir(wd), WithDebug(debug))
// 若未指定 WorkingDir，将使用当前工作目录
// 若未指定 Debug，默认为 false
type Option func(*serviceOptions)

type serviceOptions struct {
	WorkingDir          string
	Debug               bool
	MaxTurns            int
	ContainerImage      string
	WorktreePath        string
	Branch              string
	Print               string
	Continue            bool
	Resume              string
	SystemPrompt        string
	OutputFormat        string
	InitPromptWithTui   string
	SkipAllPermissions  bool
	KubernetesImage     string
	KubernetesConfig    string
	KubernetesNamespace string
}

// WithWorkingDir 指定工作目录
func WithWorkingDir(wd string) Option {
	return func(o *serviceOptions) { o.WorkingDir = wd }
}

// WithContainerImage 配置容器镜像
func WithContainerImage(image string) Option {
	return func(o *serviceOptions) { o.ContainerImage = image }
}

// WithKubernetesImage 配置Kubernetes镜像
func WithKubernetesImage(image string) Option {
	return func(o *serviceOptions) { o.KubernetesImage = image }
}

// WithKubernetesNamespace 配置Kubernetes命名空间
func WithKubernetesNamespace(namespace string) Option {
	return func(o *serviceOptions) { o.KubernetesNamespace = namespace }
}

// WithKubernetesConfig 配置Kubernetes配置文件
func WithKubernetesConfig(config string) Option {
	return func(o *serviceOptions) { o.KubernetesConfig = config }
}

// WithWorktreePath 设置工作树路径
func WithWorktreePath(path string) Option {
	return func(o *serviceOptions) { o.WorktreePath = path }
}

// WithBranch 配置分支
func WithBranch(branch string) Option {
	return func(o *serviceOptions) { o.Branch = branch }
}

// WithPrint 配置打印
func WithPrint(print string) Option {
	return func(options *serviceOptions) {
		options.Print = print
	}
}

// WithContinue 是否继续运行最后一次会话
func WithContinue(continueRun bool) Option {
	return func(o *serviceOptions) { o.Continue = continueRun }
}

// WithResume 是否恢复指定的会话
func WithResume(sessionId string) Option {
	return func(o *serviceOptions) { o.Resume = sessionId }
}

// WithSystemPrompt 自定义系统提示词
func WithSystemPrompt(prompt string) Option {
	return func(options *serviceOptions) {
		options.SystemPrompt = prompt
	}
}

// WithOutputFormat 配置输出格式
func WithOutputFormat(format string) Option {
	return func(o *serviceOptions) { o.OutputFormat = format }
}

// WithDebug 启用/关闭调试日志
func WithDebug(debug bool) Option {
	return func(o *serviceOptions) { o.Debug = debug }
}

// WithMaxTurns 最大运行轮次
func WithMaxTurns(maxTurns int) Option {
	return func(o *serviceOptions) { o.MaxTurns = maxTurns }
}

func WithSkipAllPermissions(skipAllPermissions bool) Option {
	return func(o *serviceOptions) { o.SkipAllPermissions = skipAllPermissions }
}

// NewService 使用 Options 模式创建 Service
func NewService(options ...Option) (*Service, error) {
	opts := serviceOptions{}
	for _, opt := range options {
		opt(&opts)
	}

	wd := opts.WorkingDir
	if wd == "" {
		c, err := os.Getwd()
		if err != nil {
			return nil, fmt.Errorf("failed to get current working directory: %w", err)
		}
		wd = c
	}

	if err := setLogging(opts.Debug); err != nil {
		return nil, err
	}

	userCfg, err := readUserConfig()
	if err != nil {
		return nil, err
	}

	path := userSettingsPath()
	userSettings, err := loadSettings(path)
	if err != nil {
		return nil, err
	}

	path = projectSettingsPath(wd)
	projectSettings, err := loadSettings(path)
	if err != nil {
		return nil, err
	}

	path = projectLocalSettingsPath(wd)
	projectLocalSettings, err := loadSettings(path)
	if err != nil {
		return nil, err
	}

	return &Service{
		serviceOptions: opts,

		userConfig:           userCfg,
		userSettings:         userSettings,
		projectSettings:      projectSettings,
		projectLocalSettings: projectLocalSettings,
	}, nil
}

func setLogging(debug bool) error {
	defaultLevel := slog.LevelInfo
	if debug {
		defaultLevel = slog.LevelDebug
	}

	if os.Getenv("QODER_CLI_DEV_DEBUG") == "true" {
		loggingFile := fmt.Sprintf("%s/%s", utils.GetUserStorageDir(), "debug.log")
		messagesPath := fmt.Sprintf("%s/%s", utils.GetUserStorageDir(), "messages")

		if _, err := os.Stat(loggingFile); os.IsNotExist(err) {
			if err := os.MkdirAll(utils.GetUserStorageDir(), 0o755); err != nil {
				return fmt.Errorf("failed to create directory: %w", err)
			}
			if _, err := os.Create(loggingFile); err != nil {
				return fmt.Errorf("failed to create log file: %w", err)
			}
		}

		if _, err := os.Stat(messagesPath); os.IsNotExist(err) {
			if err := os.MkdirAll(messagesPath, 0o756); err != nil {
				return fmt.Errorf("failed to create directory: %w", err)
			}
		}
		logging.MessageDir = messagesPath

		sloggingFileWriter, err := os.OpenFile(loggingFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0o666)
		if err != nil {
			return fmt.Errorf("failed to open log file: %w", err)
		}
		logger := slog.New(slog.NewTextHandler(sloggingFileWriter, &slog.HandlerOptions{
			Level: defaultLevel,
		}))
		slog.SetDefault(logger)
	} else {
		logger := slog.New(slog.NewTextHandler(logging.NewWriter(), &slog.HandlerOptions{
			Level: defaultLevel,
		}))
		slog.SetDefault(logger)
	}
	return nil
}

func (s *Service) projectMcpJsonPath() string {
	return fmt.Sprintf("%s/.mcp.json", s.WorkingDir)
}

// loadMcpServersInProjectMcpJson ${project}/.mcp.json
func (s *Service) loadMcpServersInProjectMcpJson() (map[string]McpServer, error) {
	obj := make(map[string]map[string]McpServer)
	configPath := s.projectMcpJsonPath()
	bytes, err := os.ReadFile(configPath)
	if err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			return nil, err
		}
	} else if err := json.Unmarshal(bytes, &obj); err != nil {
		return nil, err
	}
	if servers, ok := obj["mcpServers"]; ok {
		if servers != nil {
			return servers, nil
		}
	}
	return map[string]McpServer{}, nil
}

func (s *Service) GetChangeLog() string {
	return s.userConfig.CachedChangelog
}

func (s *Service) AddMcpServers(scope ConfigScope, name string, mcpServer McpServer) error {
	switch scope {
	case LocalScope:
		return s.userConfig.addProjectMcpServer(s.WorkingDir, name, mcpServer)
	case UserScope:
		return s.userConfig.addMcpServer(name, mcpServer)
	case ProjectScope:
		mcpServers, err := s.loadMcpServersInProjectMcpJson()
		if err != nil {
			return err
		}
		mcpServers[name] = mcpServer
		var obj = map[string]any{"mcpServers": mcpServers}
		configPath := s.projectMcpJsonPath()
		if bytes, err := json.Marshal(obj); err != nil {
			return err
		} else if err := os.WriteFile(configPath, bytes, 0644); err != nil {
			return err
		}
		return nil
	default:
		return fmt.Errorf("invalid scope: %s", scope)
	}
}

func (s *Service) ListMcpServers() ([]McpServer, error) {

	// load mcp servers from project
	var servers []McpServer
	mcpServers, err := s.loadMcpServersInProjectMcpJson()
	if err != nil {
		return nil, err
	}
	servers = appendServers(mcpServers, servers, ProjectScope)

	// load mcp servers from local config
	if s.userConfig.Projects != nil {
		if project, ok := s.userConfig.Projects[s.WorkingDir]; ok {
			servers = appendServers(project.McpServers, servers, LocalScope)
		}
	}

	// load mcp servers from user config
	servers = appendServers(s.userConfig.McpServers, servers, UserScope)
	token := qoder.GetQoderAuthToken()
	for _, server := range servers {
		server.AppendEnv("QODER_CURRENT_WORKDIR", s.WorkingDir)
		server.AppendEnv("QODER_AUTH_TOKEN", token)
	}
	return servers, nil
}

func (s *Service) RemoveMcpServer(name string, scope ConfigScope) (bool, error) {
	switch scope {
	case LocalScope:
		return s.userConfig.removeProjectMcpServer(s.WorkingDir, name)
	case UserScope:
		return s.userConfig.removeMcpServer(name)
	case ProjectScope:
		mcpServers, err := s.loadMcpServersInProjectMcpJson()
		if err != nil {
			return false, err
		}
		if _, ok := mcpServers[name]; !ok {
			return false, nil
		}
		delete(mcpServers, name)

		configPath := s.projectMcpJsonPath()
		obj := make(map[string]map[string]McpServer)
		obj["mcpServers"] = mcpServers
		if bytes, err := json.Marshal(obj); err != nil {
			return true, err
		} else if err := os.WriteFile(configPath, bytes, 0644); err != nil {
			return true, err
		}
		return true, nil
	default:
		return false, fmt.Errorf("invalid scope: %s", scope)
	}
}

func (s *Service) GetLspConfigs() map[string]LspConfig {
	return s.userConfig.LspConfigs
}

func (s *Service) GetProviders() map[models.ModelProvider]ProviderConfig {
	return s.userConfig.Providers
}

func (s *Service) GetAgents() map[AgentName]AgentConfig {
	return s.userConfig.Agents
}

func (s *Service) GetAgent(name AgentName) (AgentConfig, bool) {
	agent, exists := s.userConfig.Agents[name]
	return agent, exists
}

func (s *Service) GetProvider(provider models.ModelProvider) (ProviderConfig, bool) {
	providerConfig, exists := s.userConfig.Providers[provider]
	return providerConfig, exists
}

func (s *Service) UpdateAgentModel(agentName AgentName, modelID models.ModelId) error {
	model, ok := models.SupportedModels[modelID]
	if !ok {
		return fmt.Errorf("model %s not supported", modelID)
	}

	existingAgentCfg := s.userConfig.Agents[agentName]
	maxTokens := existingAgentCfg.MaxTokens
	if model.DefaultMaxTokens > 0 {
		maxTokens = model.DefaultMaxTokens
	}

	newAgentCfg := AgentConfig{
		Model:           modelID,
		MaxTokens:       maxTokens,
		ReasoningEffort: existingAgentCfg.ReasoningEffort,
	}
	s.userConfig.Agents[agentName] = newAgentCfg

	if err := s.userConfig.validateAgent(agentName, newAgentCfg); err != nil {
		s.userConfig.Agents[agentName] = existingAgentCfg
		return fmt.Errorf("failed to update agent model: %w", err)
	}
	return s.userConfig.save()
}

func (s *Service) GetPreferences() Preferences {
	return s.userConfig.Preferences
}

func (s *Service) UpdatePreferences(preferences Preferences) error {
	s.userConfig.Preferences = preferences
	return s.userConfig.save()
}

func (s *Service) GetHooks() map[HookEvent][]HookConfig {
	allHooks := make(map[HookEvent][]HookConfig)
	addHook := func(hooks map[HookEvent][]HookConfig) {
		if hooks == nil {
			return
		}
		for event, items := range hooks {
			for _, hook := range items {
				hook.Event = event
				if configs, ok := allHooks[event]; ok {
					configs = append(configs, hook)
				} else {
					allHooks[event] = []HookConfig{hook}
				}
			}
		}
	}

	addHook(s.projectLocalSettings.Hooks)
	addHook(s.projectSettings.Hooks)
	addHook(s.userSettings.Hooks)
	return allHooks
}

func (s *Service) GetOutputStyle() string {
	if len(s.projectLocalSettings.OutputStyle) > 0 {
		return s.projectLocalSettings.OutputStyle
	} else if len(s.projectSettings.OutputStyle) > 0 {
		return s.projectSettings.OutputStyle
	} else if len(s.userSettings.OutputStyle) > 0 {
		return s.userSettings.OutputStyle
	}
	return ""
}

func (s *Service) SetOutputStyle(style string) error {
	s.projectLocalSettings.OutputStyle = style
	return saveSettings(s.projectLocalSettings, projectLocalSettingsPath(s.WorkingDir))
}

// GetMonitoringConfig 合并用户配置、项目配置与环境变量为最终的监控配置
func (s *Service) GetMonitoringConfig() *MonitoringConfig {
	// 1) 从用户级持久化配置读取基础值
	cfg := s.userConfig.Monitoring

	// 2) 叠加项目与本地 settings（数值/字符串覆盖，布尔用指针区分是否显式设置）
	overlay := func(ms *Settings) {
		if ms.Monitoring.EnableQoderExporter != nil {
			cfg.EnableQoderExporter = *ms.Monitoring.EnableQoderExporter
		}
		if ms.Monitoring.EnableOTLPTraces != nil {
			cfg.EnableOTLPTraces = *ms.Monitoring.EnableOTLPTraces
		}
		if ms.Monitoring.EnableOTLPLogs != nil {
			cfg.EnableOTLPLogs = *ms.Monitoring.EnableOTLPLogs
		}
		if ms.Monitoring.EnableOTLPMetrics != nil {
			cfg.EnableOTLPMetrics = *ms.Monitoring.EnableOTLPMetrics
		}
		if ms.Monitoring.OTLPEndpoint != "" {
			cfg.OTLPEndpoint = ms.Monitoring.OTLPEndpoint
		}
		if ms.Monitoring.OTLPTracesEndpoint != "" {
			cfg.OTLPTracesEndpoint = ms.Monitoring.OTLPTracesEndpoint
		}
		if ms.Monitoring.OTLPLogsEndpoint != "" {
			cfg.OTLPLogsEndpoint = ms.Monitoring.OTLPLogsEndpoint
		}
		if ms.Monitoring.OTLPMetricsEndpoint != "" {
			cfg.OTLPMetricsEndpoint = ms.Monitoring.OTLPMetricsEndpoint
		}
	}

	overlay(s.userSettings)
	overlay(s.projectSettings)
	overlay(s.projectLocalSettings)

	// 3) 环境变量最高优先级
	if v := os.Getenv("QODER_ENABLE_QODER_EXPORTER"); v != "" {
		if b, err := strconv.ParseBool(v); err == nil {
			cfg.EnableQoderExporter = b
		}
	}
	if v := os.Getenv("QODER_ENABLE_OTLP_TRACES"); v != "" {
		if b, err := strconv.ParseBool(v); err == nil {
			cfg.EnableOTLPTraces = b
		}
	}
	if v := os.Getenv("QODER_ENABLE_OTLP_LOGS"); v != "" {
		if b, err := strconv.ParseBool(v); err == nil {
			cfg.EnableOTLPLogs = b
		}
	}
	if v := os.Getenv("QODER_ENABLE_OTLP_METRICS"); v != "" {
		if b, err := strconv.ParseBool(v); err == nil {
			cfg.EnableOTLPMetrics = b
		}
	}
	if v := os.Getenv("QODER_OTLP_ENDPOINT"); v != "" {
		cfg.OTLPEndpoint = v
	}
	if v := os.Getenv("QODER_OTLP_LOGS_ENDPOINT"); v != "" {
		cfg.OTLPLogsEndpoint = v
	}
	if v := os.Getenv("QODER_OTLP_METRICS_ENDPOINT"); v != "" {
		cfg.OTLPMetricsEndpoint = v
	}
	if v := os.Getenv("QODER_OTLP_TRACES_ENDPOINT"); v != "" {
		cfg.OTLPTracesEndpoint = v
	}

	return &cfg
}

// AppendHistory 追加历史记录到当前工作区项目配置
func (s *Service) AppendHistory(entry HistoryEntry) error {
	if s.userConfig.Projects == nil {
		s.userConfig.Projects = make(map[string]ProjectConfig)
	}
	pc := s.userConfig.Projects[s.WorkingDir]

	// 使用强类型列表
	history := append([]HistoryEntry(nil), pc.History...)

	// 相邻去重
	if len(history) > 0 && history[len(history)-1].Display == entry.Display {
		// 不追加重复项
	} else {
		history = append(history, entry)
	}

	// 裁剪上限
	const maxHistory = 200
	if len(history) > maxHistory {
		history = history[len(history)-maxHistory:]
	}

	// 回写
	pc.History = history
	s.userConfig.Projects[s.WorkingDir] = pc
	return s.userConfig.save()
}

// GetHistoryList 获取当前工作区历史记录
func (s *Service) GetHistoryList() []HistoryEntry {
	if s.userConfig.Projects == nil {
		return nil
	}
	pc, ok := s.userConfig.Projects[s.WorkingDir]
	if !ok {
		return nil
	}
	return pc.History
}

package config

import (
	"bufio"
	"context"
	"fmt"
	"os/exec"
	"testing"
)

func Test_runCommand(t *testing.T) {
	type args struct {
		command string
		args    []string
	}
	command := args{
		command: "npx",
		args:    []string{"-y", "xxxx"},
	}
	ctx := context.Background()
	cmd := exec.CommandContext(ctx, command.command, command.args...)
	_, err := cmd.StdinPipe()
	if err != nil {
		t.Fatalf("failed to create stdin pipe")
	}

	stdout, err := cmd.StdoutPipe()
	if err != nil {
		t.Fatalf("failed to create stdout pipe")
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		t.Fatalf("failed to create stderr pipe")
	}
	outReader := bufio.NewReader(stdout)
	errReader := bufio.NewReader(stderr)
	go func() {
		for {
			line, _, err := outReader.ReadLine()
			if err != nil {
				break
			}
			fmt.Println("OUT: " + string(line))
		}
	}()
	go func() {
		for {
			line, _, err := errReader.ReadLine()
			if err != nil {
				break
			}
			fmt.Println("ERR: " + string(line))
		}
	}()
	if err := cmd.Start(); err != nil {
		t.Fatalf("failed to start command: %v", err)
	}

	defer cmd.Wait()
}

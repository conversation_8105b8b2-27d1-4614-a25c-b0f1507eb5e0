package diff

import (
	"github.com/aymanbagabas/go-udiff"
	"strings"
)

func GenerateDiff(beforeContent, afterContent, fileName, workingDir string) (string, int, int) {
	fileName = strings.TrimPrefix(fileName, workingDir)
	fileName = strings.TrimPrefix(fileName, "/")

	var (
		unified   = udiff.Unified("a/"+fileName, "b/"+fileName, beforeContent, afterContent)
		additions = 0
		removals  = 0
	)

	lines := strings.SplitSeq(unified, "\n")
	for line := range lines {
		if strings.HasPrefix(line, "+") && !strings.HasPrefix(line, "+++") {
			additions++
		} else if strings.HasPrefix(line, "-") && !strings.HasPrefix(line, "---") {
			removals++
		}
	}

	return unified, additions, removals
}

package utils

import (
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/logging"
	"os"
	"path/filepath"
)

func GetUserConfigPath() string {
	home, err := os.UserHomeDir()
	if err != nil {
		logging.Error("fail to get home dir", "err", err)
		panic(err)
	}
	return filepath.Join(home, core.ConfigFileName)
}

func GetUserStorageDir() string {
	home, err := os.UserHomeDir()
	if err != nil {
		logging.Error("fail to get home dir", "err", err)
		panic(err)
	}
	return filepath.Join(home, core.DataDirName)
}

func GetWorkspaceStorageDir(workingDir string) string {
	return filepath.Join(workingDir, core.DataDirName)
}

func GetClaudeCodeUserStorageDir() string {
	home, err := os.UserHomeDir()
	if err != nil {
		logging.Error("fail to get home dir", "err", err)
		panic(err)
	}
	return filepath.Join(home, ".claude")
}

func GetClaudeCodeWorkspaceStorageDir(workingDir string) string {
	return filepath.Join(workingDir, ".claude")
}

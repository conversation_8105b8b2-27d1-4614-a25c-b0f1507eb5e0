package internal

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"syscall"
	"time"

	"github.com/google/go-github/v57/github"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/assets"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/qoder"
	"golang.org/x/crypto/nacl/box"
)

// GitHubService GitHub App安装服务实现
type GitHubService struct {
	assets       *assets.Assets
	workingDir   string
	installState *core.GitHubInstallState
	token        string // GitHub token
}

// NewGitHubService 创建新的GitHub服务实例
func NewGitHubService(assetsManager *assets.Assets, workingDir string) *GitHubService {
	return &GitHubService{
		assets:     assetsManager,
		workingDir: workingDir,
	}
}

// getQoderAuthInfo 获取Qoder认证信息，返回机器ID和用户信息
func (s *GitHubService) getQoderAuthInfo() (string, string, error) {
	// 直接读取机器ID文件
	machineIdFile := qoder.GetMachineIdFile()
	machineIdContent, err := os.ReadFile(machineIdFile)
	if err != nil {
		return "", "", fmt.Errorf("unable to read machine ID file %s: %v", machineIdFile, err)
	}
	machineId := strings.TrimSpace(string(machineIdContent))

	// 直接读取用户信息文件
	userFile := qoder.GetUserFile()
	userContent, err := os.ReadFile(userFile)
	if err != nil {
		return "", "", fmt.Errorf("unable to read user file %s: %v", userFile, err)
	}
	userInfo := string(userContent)

	return machineId, userInfo, nil
}

// InitializeInstallation 初始化安装流程
func (s *GitHubService) InitializeInstallation() {
	defaultRepo := s.GetCurrentRepository()

	s.installState = &core.GitHubInstallState{
		CurrentStep: core.GitHubStepRepositoryInput,
		Repository:  defaultRepo,
		WorkflowOptions: core.GitHubWorkflowOptions{
			CodeReview:     true,
			MentionSupport: true,
		},
		TaskProgress: core.GitHubTaskProgress{},
	}
}

// GetInstallState 获取当前安装状态
func (s *GitHubService) GetInstallState() *core.GitHubInstallState {
	if s.installState == nil {
		s.InitializeInstallation()
	}
	return s.installState
}

// SetWorkflowOptions 设置工作流选项
func (s *GitHubService) SetWorkflowOptions(options core.GitHubWorkflowOptions) {
	if s.installState == nil {
		s.InitializeInstallation()
	}
	s.installState.WorkflowOptions = options
}

// setRepository 设置仓库
func (s *GitHubService) setRepository(repository string) {
	if s.installState == nil {
		s.InitializeInstallation()
	}
	s.installState.Repository = repository
}

// updateInstallStep 更新安装步骤
func (s *GitHubService) updateInstallStep(step core.GitHubInstallStep) {
	if s.installState == nil {
		s.InitializeInstallation()
	}
	s.installState.CurrentStep = step
}

// setError 设置错误信息
func (s *GitHubService) setError(errorMsg string) {
	if s.installState == nil {
		s.InitializeInstallation()
	}
	s.installState.Error = errorMsg
}

// clearError 清除错误信息
func (s *GitHubService) clearError() {
	if s.installState == nil {
		s.InitializeInstallation()
	}
	s.installState.Error = ""
}

// StartRepositoryValidation 开始仓库验证并打开安装页面
func (s *GitHubService) StartRepositoryValidation(repository string) (bool, string, error) {
	logging.Debug("GitHubService.StartRepositoryValidation: Starting validation", "repository", repository)

	// 设置仓库
	s.setRepository(repository)

	// 验证仓库
	validationResult := s.validateRepository(repository)
	if !validationResult.IsValid {
		s.setError(validationResult.ErrorMessage)
		return false, validationResult.ErrorMessage, nil
	}

	// 验证成功，直接打开安装页面
	s.updateInstallStep(core.GitHubStepAppInstallInfo)
	s.clearError()

	// 打开安装URL
	installURL := s.getInstallationURL()
	if installURL == "" {
		errorMsg := "Invalid repository format"
		s.setError(errorMsg)
		return false, errorMsg, nil
	}

	// 尝试打开浏览器
	err := s.openBrowser(installURL)
	var message string
	if err == nil {
		message = "Opening browser for qoder-assist installation...\n\nInstallation page: " + installURL + "\n\nPlease complete the installation, then press Enter to continue."
	} else {
		message = "Cannot open browser automatically.\n\nInstallation page: " + installURL + "\n\nPlease open this URL in your browser to install qoder-assist, then press Enter to continue."
	}

	return true, message, nil
}

// StartWorkflowConfiguration 开始工作流配置
func (s *GitHubService) StartWorkflowConfiguration() {
	s.updateInstallStep(core.GitHubStepWorkflowSelection)
	s.clearError()
}

// StartTaskExecution 开始任务执行
func (s *GitHubService) StartTaskExecution() (bool, string, error) {
	// 检查是否至少选择了一个工作流
	if !s.installState.WorkflowOptions.CodeReview && !s.installState.WorkflowOptions.MentionSupport {
		errorMsg := "Please select at least one workflow feature"
		s.setError(errorMsg)
		return false, errorMsg, nil
	}

	// 跳过API key配置，直接进入任务列表
	s.updateInstallStep(core.GitHubStepTaskList)
	s.installState.TaskProgress.CurrentStep = core.GitHubTaskStepRepoInfo
	s.clearError()

	return true, "Starting setup", nil
}

// ExecuteNextTask 执行下一个任务
func (s *GitHubService) ExecuteNextTask() (bool, string, error) {
	logging.Debug("GitHubService.ExecuteNextTask: Starting task execution", "currentStep", s.installState.TaskProgress.CurrentStep, "repository", s.installState.Repository)

	// 获取git凭证
	if s.token == "" {
		logging.Debug("GitHubService.ExecuteNextTask: Getting GitHub token")
		token, err := s.getGitHubToken()
		if err != nil {
			logging.Error("GitHubService.ExecuteNextTask: Failed to get GitHub token", "error", err.Error())
			errorMsg := "Failed to get GitHub token. Please configure your GitHub token.\n\nDetails: " + err.Error()
			s.setError(errorMsg)
			s.updateInstallStep(core.GitHubStepComplete)
			return false, "GitHub token configuration required. Please see the instructions above.", nil
		}
		s.token = token
		logging.Debug("GitHubService.ExecuteNextTask: Successfully got GitHub token", "hasToken", token != "")
	}

	// 根据当前步骤执行任务
	switch s.installState.TaskProgress.CurrentStep {
	case core.GitHubTaskStepRepoInfo:
		return s.executeRepoInfoTask()
	case core.GitHubTaskStepCreateBranch:
		return s.executeCreateBranchTask()
	case core.GitHubTaskStepCreateWorkflow:
		return s.executeCreateWorkflowTask()
	case core.GitHubTaskStepSetupSecrets:
		return s.executeSetupSecretsTask()
	case core.GitHubTaskStepCreatePR:
		return s.executeCreatePRTask()
	default:
		s.updateInstallStep(core.GitHubStepComplete)
		return true, "Installation completed", nil
	}
}

// validateRepository 验证仓库
func (s *GitHubService) validateRepository(repository string) core.GitHubRepoValidationResult {
	// 第一步：验证仓库名称格式
	if !s.isValidRepositoryFormat(repository) {
		return core.GitHubRepoValidationResult{
			IsValid:      false,
			Method:       "Format check",
			ErrorMessage: fmt.Sprintf("Invalid repository name format: %s\nCorrect format should be: username/repository-name", repository),
		}
	}

	logging.Debug("Repository format validation passed", "repository", repository)

	return core.GitHubRepoValidationResult{
		IsValid: true,
		Method:  "Format validation",
	}
}

// getGitHubToken 获取GitHub访问令牌
func (s *GitHubService) getGitHubToken() (string, error) {
	logging.Debug("GitHubService.GetGitHubToken: Starting token acquisition")

	// 策略1: 尝试从环境变量获取
	if token, err := s.getTokenViaEnv(); err == nil {
		logging.Debug("GitHubService.GetGitHubToken: Got token via environment variables")
		// 校验 token
		if s.validateToken(token) {
			logging.Debug("GitHubService.GetGitHubToken: Token validation successful")
			return token, nil
		}
		logging.Debug("GitHubService.GetGitHubToken: Token validation failed for environment variable")
	} else {
		logging.Debug("GitHubService.GetGitHubToken: Environment variables method failed", "error", err.Error())
	}

	// 策略2: 检查 gh CLI 是否安装并获取 token
	if s.isGhCLIInstalled() {
		logging.Debug("GitHubService.GetGitHubToken: gh CLI is installed, trying to get token")
		if token, err := s.getTokenViaGhCLI(); err == nil {
			logging.Debug("GitHubService.GetGitHubToken: Got token via gh CLI")
			// 校验 token
			if s.validateToken(token) {
				logging.Debug("GitHubService.GetGitHubToken: Token validation successful")
				return token, nil
			}
			logging.Debug("GitHubService.GetGitHubToken: Token validation failed for gh CLI")
		} else {
			logging.Debug("GitHubService.GetGitHubToken: gh CLI token acquisition failed", "error", err.Error())
		}
	} else {
		logging.Debug("GitHubService.GetGitHubToken: gh CLI is not installed")
	}

	// 策略3: 尝试使用 git credential fill（非交互式）
	if token, err := s.getTokenViaGitCredentialFillNonInteractive(); err == nil {
		logging.Debug("GitHubService.GetGitHubToken: Got token via git credential fill")
		// 校验 token
		if s.validateToken(token) {
			logging.Debug("GitHubService.GetGitHubToken: Token validation successful")
			return token, nil
		}
		logging.Debug("GitHubService.GetGitHubToken: Token validation failed for git credential fill")
	} else {
		logging.Debug("GitHubService.GetGitHubToken: Git credential fill method failed", "error", err.Error())
	}

	logging.Error("GitHubService.GetGitHubToken: All token acquisition methods failed")
	return "", fmt.Errorf("No valid GitHub token found. Please install and configure GitHub CLI:\n\n" +
		"Install GitHub CLI:\n" +
		"  • macOS: brew install gh\n" +
		"  • Windows: winget install GitHub.cli\n" +
		"  • Linux: See https://github.com/cli/cli#installation\n\n" +
		"Configure GitHub CLI:\n" +
		"  gh auth login\n\n" +
		"Then run this command again.")
}

// setSecret 设置GitHub仓库secret
func (s *GitHubService) setSecret(secretName, secretValue string) error {
	repository := s.installState.Repository
	parts := strings.Split(repository, "/")
	if len(parts) != 2 {
		return fmt.Errorf("invalid repository format, should be owner/repo")
	}
	owner, repo := parts[0], parts[1]

	client := s.getGitHubClient()
	ctx := context.Background()

	// 获取仓库的公钥
	publicKey, _, err := client.Actions.GetRepoPublicKey(ctx, owner, repo)
	if err != nil {
		return fmt.Errorf("failed to get repository public key: %v", err)
	}

	// 使用公钥加密secret值
	encryptedValue, err := s.encryptSecret(publicKey.GetKey(), secretValue)
	if err != nil {
		return fmt.Errorf("failed to encrypt secret: %v", err)
	}

	// 构造加密的secret对象
	encryptedSecret := &github.EncryptedSecret{
		Name:           secretName,
		KeyID:          publicKey.GetKeyID(),
		EncryptedValue: encryptedValue,
	}

	// 调用GitHub API设置secret
	_, err = client.Actions.CreateOrUpdateRepoSecret(ctx, owner, repo, encryptedSecret)
	if err != nil {
		return fmt.Errorf("failed to set secret: %v", err)
	}

	return nil
}

// getMainBranchInfo 获取主分支SHA和默认分支名
func (s *GitHubService) getMainBranchInfo() (string, string, error) {
	return s.getMainBranchSHA(s.installState.Repository)
}

// createBranch 创建新分支
func (s *GitHubService) createBranch() (string, error) {
	repository := s.installState.Repository
	mainSha := s.installState.TaskProgress.MainSHA
	logging.Debug("GitHubService.createBranch: Creating new branch", "repository", repository, "mainSha", mainSha)

	branchName := fmt.Sprintf("qoder-setup-%d", time.Now().Unix())
	logging.Debug("GitHubService.createBranch: Generated branch name", "branchName", branchName)

	// 创建新分支
	err := s.createBranchViaAPI(repository, branchName, mainSha)
	if err != nil {
		logging.Error("GitHubService.createBranch: Failed to create branch", "branchName", branchName, "error", err.Error())
		return "", fmt.Errorf("failed to create branch: %v", err)
	}
	logging.Debug("GitHubService.createBranch: Branch created successfully", "branchName", branchName)
	return branchName, nil
}

// createWorkflowFiles 创建工作流文件
func (s *GitHubService) createWorkflowFiles() error {
	repository := s.installState.Repository
	branchName := s.installState.TaskProgress.BranchName
	options := s.installState.WorkflowOptions
	logging.Debug("GitHubService.createWorkflowFiles: Creating workflow files", "repository", repository, "branchName", branchName, "codeReview", options.CodeReview, "mentionSupport", options.MentionSupport)

	// 如果两个功能都没有选择，不创建任何工作流
	if !options.CodeReview && !options.MentionSupport {
		logging.Error("GitHubService.createWorkflowFiles: No workflow features selected")
		return fmt.Errorf("no workflow features selected")
	}

	// 根据用户选择创建对应的工作流文件
	if options.CodeReview {
		logging.Debug("GitHubService.createWorkflowFiles: Creating code review workflow file")
		// 创建代码审查工作流
		codeReviewContent := s.loadTemplateFile("qoder-auto-review.yaml")
		if codeReviewContent == "" {
			logging.Error("GitHubService.createWorkflowFiles: Failed to load code review template")
			return fmt.Errorf("failed to load code review template")
		}
		err := s.createFileViaAPI(repository, branchName, ".github/workflows/qoder-auto-review.yml", codeReviewContent)
		if err != nil {
			logging.Error("GitHubService.createWorkflowFiles: Failed to create code review workflow file", "error", err.Error())
			return fmt.Errorf("failed to create code review workflow file: %v", err)
		}
		logging.Debug("GitHubService.createWorkflowFiles: Code review workflow file created successfully")
	}

	if options.MentionSupport {
		logging.Debug("GitHubService.createWorkflowFiles: Creating mention support workflow file")
		// 创建提及支持工作流
		mentionContent := s.loadTemplateFile("qoder-mention.yaml")
		if mentionContent == "" {
			logging.Error("GitHubService.createWorkflowFiles: Failed to load mention template")
			return fmt.Errorf("failed to load mention template")
		}
		err := s.createFileViaAPI(repository, branchName, ".github/workflows/qoder-mention.yml", mentionContent)
		if err != nil {
			logging.Error("GitHubService.createWorkflowFiles: Failed to create mention support workflow file", "error", err.Error())
			return fmt.Errorf("failed to create mention support workflow file: %v", err)
		}
		logging.Debug("GitHubService.createWorkflowFiles: Mention support workflow file created successfully")
	}

	logging.Debug("GitHubService.createWorkflowFiles: All workflow files created successfully")
	return nil
}

// createPR 创建Pull Request
func (s *GitHubService) createPR() (string, error) {
	repository := s.installState.Repository
	branchName := s.installState.TaskProgress.BranchName
	defaultBranch := s.installState.TaskProgress.DefaultBranch
	options := s.installState.WorkflowOptions

	parts := strings.Split(repository, "/")
	if len(parts) != 2 {
		return "", fmt.Errorf("invalid repository format")
	}
	owner, repo := parts[0], parts[1]

	client := s.getGitHubClient()
	ctx := context.Background()

	prTitle := "Setup Qoder AI Workflow"
	prBody := s.generatePRDescription(options)

	newPR := &github.NewPullRequest{
		Title: github.String(prTitle),
		Body:  github.String(prBody),
		Head:  github.String(branchName),
		Base:  github.String(defaultBranch),
	}

	// 创建PR
	pr, _, err := client.PullRequests.Create(ctx, owner, repo, newPR)
	if err != nil {
		return "", fmt.Errorf("failed to create PR: %v", err)
	}

	return pr.GetHTMLURL(), nil
}

// getInstallationURL 获取GitHub App安装URL
func (s *GitHubService) getInstallationURL() string {
	repository := s.installState.Repository
	parts := strings.Split(repository, "/")
	if len(parts) != 2 {
		return ""
	}
	owner := parts[0]
	return fmt.Sprintf("https://github.com/apps/qoder-assist/installations/new?suggested_target_id=%s", owner)
}

// GetCurrentRepository 获取当前Git仓库
func (s *GitHubService) GetCurrentRepository() string {
	cmd := exec.Command("git", "remote", "get-url", "origin")
	// 设置工作目录为配置的工作目录
	cmd.Dir = s.workingDir
	output, err := cmd.Output()
	if err != nil {
		return ""
	}

	url := strings.TrimSpace(string(output))
	if strings.Contains(url, "github.com") {
		url = strings.TrimSuffix(url, ".git")
		if strings.HasPrefix(url, "**************:") {
			return strings.TrimPrefix(url, "**************:")
		} else if strings.Contains(url, "github.com/") {
			parts := strings.Split(url, "github.com/")
			if len(parts) > 1 {
				return parts[1]
			}
		}
	}
	return ""
}

// openBrowser 打开浏览器
func (s *GitHubService) openBrowser(url string) error {
	var cmdName string
	var cmdArgs []string

	switch runtime.GOOS {
	case "darwin":
		cmdName = "open"
		cmdArgs = []string{url}
	case "linux":
		// Check if we're in a container environment or if xdg-open is available
		if _, err := exec.LookPath("xdg-open"); err != nil {
			return fmt.Errorf("xdg-open not found, likely running in container environment")
		}
		// Check if DISPLAY is set (X11) or if we're in Wayland
		if os.Getenv("DISPLAY") == "" && os.Getenv("WAYLAND_DISPLAY") == "" {
			return fmt.Errorf("no display server detected, likely running in headless environment")
		}
		cmdName = "xdg-open"
		cmdArgs = []string{url}
	case "windows":
		cmdName = "cmd"
		cmdArgs = []string{"/c", "start", url}
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}

	// 添加调试信息
	logging.Debug("OpenBrowser: starting browser command", "os", runtime.GOOS, "cmdName", cmdName, "cmdArgs", cmdArgs, "url", url)

	// Set a timeout to avoid hanging in container environments
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	cmd := exec.CommandContext(ctx, cmdName, cmdArgs...)

	// 尝试启动命令并捕获详细错误
	err := cmd.Start()
	if err != nil {
		logging.Error("OpenBrowser: failed to start browser command", "cmdName", cmdName, "cmdArgs", cmdArgs, "error", err)
		return fmt.Errorf("failed to start browser command '%s %v': %v", cmdName, cmdArgs, err)
	}

	logging.Debug("OpenBrowser: command started successfully", "cmdName", cmdName)

	// 等待命令完成或超时
	done := make(chan error, 1)
	go func() {
		done <- cmd.Wait()
	}()

	select {
	case err := <-done:
		if err != nil {
			logging.Warn("OpenBrowser: command finished with error", "error", err)
			// 不返回错误，因为浏览器可能已经打开
		} else {
			logging.Debug("OpenBrowser: command completed successfully")
		}
	case <-ctx.Done():
		logging.Warn("OpenBrowser: command timed out, but browser may have opened")
		// 不返回错误，因为超时不代表浏览器没有打开
	}

	return nil
}

// executeRepoInfoTask 执行仓库信息获取任务
func (s *GitHubService) executeRepoInfoTask() (bool, string, error) {
	logging.Debug("GitHubService.executeRepoInfoTask: Starting repository info task", "repository", s.installState.Repository)
	mainSHA, defaultBranch, err := s.getMainBranchInfo()
	if err != nil {
		logging.Error("GitHubService.executeRepoInfoTask: Repository info failed", "repository", s.installState.Repository, "error", err.Error())
		errorMsg := "Failed to get repository info: " + err.Error()
		s.setError(errorMsg)
		s.updateInstallStep(core.GitHubStepComplete)
		return false, "Repository access failed. Please check repository name and permissions.", nil
	}
	logging.Debug("GitHubService.executeRepoInfoTask: Repository info completed successfully", "mainSHA", mainSHA, "defaultBranch", defaultBranch)
	s.installState.TaskProgress.MainSHA = mainSHA
	s.installState.TaskProgress.DefaultBranch = defaultBranch
	s.installState.TaskProgress.CurrentStep = core.GitHubTaskStepCreateBranch
	return true, "Repository info retrieved", nil
}

// executeCreateBranchTask 执行创建分支任务
func (s *GitHubService) executeCreateBranchTask() (bool, string, error) {
	logging.Debug("GitHubService.executeCreateBranchTask: Starting branch creation task", "repository", s.installState.Repository, "mainSHA", s.installState.TaskProgress.MainSHA)
	branchName, err := s.createBranch()
	if err != nil {
		logging.Error("GitHubService.executeCreateBranchTask: Branch creation failed", "repository", s.installState.Repository, "error", err.Error())
		errorMsg := "Failed to create branch: " + err.Error()
		s.setError(errorMsg)
		s.updateInstallStep(core.GitHubStepComplete)
		return false, "Branch creation failed. Please check repository permissions.", nil
	}
	logging.Debug("GitHubService.executeCreateBranchTask: Branch creation completed successfully", "branchName", branchName)
	s.installState.TaskProgress.BranchName = branchName
	s.installState.TaskProgress.CurrentStep = core.GitHubTaskStepCreateWorkflow
	return true, "Branch created: " + branchName, nil
}

// executeCreateWorkflowTask 执行创建工作流任务
func (s *GitHubService) executeCreateWorkflowTask() (bool, string, error) {
	logging.Debug("GitHubService.executeCreateWorkflowTask: Starting workflow files creation task", "repository", s.installState.Repository, "branchName", s.installState.TaskProgress.BranchName, "workflowOptions", fmt.Sprintf("%+v", s.installState.WorkflowOptions))
	err := s.createWorkflowFiles()
	if err != nil {
		logging.Error("GitHubService.executeCreateWorkflowTask: Workflow files creation failed", "repository", s.installState.Repository, "error", err.Error())
		// 检查错误类型并显示相应的错误信息
		var errorMessage string
		if strings.Contains(err.Error(), "Failed to create") && strings.Contains(err.Error(), "workflow file") {
			errorMessage = "Workflow file creation failed. Please check repository permissions."
		} else if strings.Contains(err.Error(), "Failed to load") && strings.Contains(err.Error(), "template") {
			errorMessage = "Workflow template loading failed. Please report this issue."
		} else {
			errorMessage = "Workflow files creation failed. " + err.Error()
		}
		s.setError(errorMessage)
		s.updateInstallStep(core.GitHubStepComplete)
		return false, "Please manually complete the installation steps.", nil
	}
	logging.Debug("GitHubService.executeCreateWorkflowTask: Workflow files creation completed successfully")
	s.installState.TaskProgress.CurrentStep = core.GitHubTaskStepSetupSecrets
	return true, "Workflow files created", nil
}

// executeSetupSecretsTask 执行设置secrets任务
func (s *GitHubService) executeSetupSecretsTask() (bool, string, error) {
	machineId, userInfo, err := s.getQoderAuthInfo()
	if err != nil {
		errorMsg := "Failed to get Qoder auth info: " + err.Error()
		s.setError(errorMsg)
		s.updateInstallStep(core.GitHubStepComplete)
		return false, "Failed to get authentication info. Please ensure you are logged in.", nil
	}

	// Set QODER_MACHINE_ID secret
	err = s.setSecret("QODER_MACHINE_ID", machineId)
	if err != nil {
		// Continue even if secret setting fails, log the error
	}

	// Set QODER_USER_INFO secret
	err = s.setSecret("QODER_USER_INFO", userInfo)
	if err != nil {
		// Continue even if secret setting fails, log the error
	}

	s.installState.TaskProgress.CurrentStep = core.GitHubTaskStepCreatePR
	return true, "Qoder authentication configured", nil
}

// executeCreatePRTask 执行创建PR任务
func (s *GitHubService) executeCreatePRTask() (bool, string, error) {
	prURL, err := s.createPR()
	if err != nil {
		errorMsg := "Failed to create PR: " + err.Error()
		s.setError(errorMsg)
		s.updateInstallStep(core.GitHubStepComplete)
		return false, "PR creation failed. Please create manually.", nil
	}

	// Open PR page automatically
	go func() {
		s.openBrowser(prURL)
	}()

	s.installState.PRURL = prURL
	s.updateInstallStep(core.GitHubStepComplete)
	return true, "All tasks completed successfully!", nil
}

// isValidRepositoryFormat 检查仓库名称格式
func (s *GitHubService) isValidRepositoryFormat(repository string) bool {
	parts := strings.Split(repository, "/")
	return len(parts) == 2 && parts[0] != "" && parts[1] != ""
}

// validateToken 校验 GitHub token 是否有效
func (s *GitHubService) validateToken(token string) bool {
	if token == "" {
		return false
	}

	client := github.NewClient(nil).WithAuthToken(token)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 尝试获取当前用户信息来验证 token
	_, _, err := client.Users.Get(ctx, "")
	return err == nil
}

// getGitHubClient 获取配置好的GitHub客户端
func (s *GitHubService) getGitHubClient() *github.Client {
	return github.NewClient(nil).WithAuthToken(s.token)
}

// isGhCLIInstalled 检查 gh CLI 是否安装
func (s *GitHubService) isGhCLIInstalled() bool {
	_, err := exec.LookPath("gh")
	return err == nil
}

// getTokenViaGhCLI 通过 gh CLI 获取令牌
func (s *GitHubService) getTokenViaGhCLI() (string, error) {
	cmd := exec.Command("gh", "auth", "token")
	cmd.Dir = s.workingDir

	// 设置较短的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	cmd = exec.CommandContext(ctx, "gh", "auth", "token")
	cmd.Dir = s.workingDir

	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("gh auth token command failed: %v", err)
	}

	token := strings.TrimSpace(string(output))
	if token == "" {
		return "", fmt.Errorf("gh CLI returned empty token")
	}

	return token, nil
}

// getTokenViaEnv 从环境变量获取令牌
func (s *GitHubService) getTokenViaEnv() (string, error) {
	token := os.Getenv("GITHUB_TOKEN")
	if token == "" {
		token = os.Getenv("GH_TOKEN")
	}

	if token == "" {
		return "", fmt.Errorf("GITHUB_TOKEN or GH_TOKEN environment variable not found")
	}

	return token, nil
}

// encryptSecret 使用GitHub公钥加密secret值
func (s *GitHubService) encryptSecret(publicKeyStr, secretValue string) (string, error) {
	publicKeyBytes, err := base64.StdEncoding.DecodeString(publicKeyStr)
	if err != nil {
		return "", fmt.Errorf("failed to decode public key: %v", err)
	}

	if len(publicKeyBytes) != 32 {
		return "", fmt.Errorf("invalid public key length, expected 32 bytes, got %d bytes", len(publicKeyBytes))
	}

	var publicKey [32]byte
	copy(publicKey[:], publicKeyBytes)

	secretBytes := []byte(secretValue)
	encryptedBytes, err := box.SealAnonymous(nil, secretBytes, &publicKey, rand.Reader)
	if err != nil {
		return "", fmt.Errorf("encryption failed: %v", err)
	}

	return base64.StdEncoding.EncodeToString(encryptedBytes), nil
}

// getTokenViaGitCredentialFillNonInteractive 通过git credential fill获取令牌
func (s *GitHubService) getTokenViaGitCredentialFillNonInteractive() (string, error) {
	input := "protocol=https\nhost=github.com\n\n"

	// 使用极短的超时时间，立即检测交互等待
	ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
	defer cancel()

	cmd := exec.CommandContext(ctx, "git", "credential", "fill")
	cmd.Dir = s.workingDir

	// 使用管道隔离stdin
	stdinReader, stdinWriter := io.Pipe()
	cmd.Stdin = stdinReader

	// 使用字节缓冲区捕获输出
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// 设置进程组隔离，避免继承父进程终端
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true, // 创建新进程组
		Pgid:    0,    // 使用新的进程组ID
	}

	// 在独立goroutine中提供输入并立即关闭
	go func() {
		defer stdinWriter.Close()
		// 提供预设输入
		stdinWriter.Write([]byte(input))
		// 立即关闭输入流，强制EOF
	}()

	// 在goroutine中运行命令，避免主线程阻塞
	done := make(chan error, 1)
	go func() {
		defer stdinReader.Close()
		done <- cmd.Run()
	}()

	// 超时检测和强制终止
	select {
	case err := <-done:
		// 命令正常完成
		if err != nil {
			// 检查是否是超时
			if errors.Is(ctx.Err(), context.DeadlineExceeded) {
				return "", fmt.Errorf("git credential fill timed out - no stored credentials or credential helper waiting for input")
			}
			// 其他错误，可能是没有配置credential helper
			stderrOutput := stderr.String()
			if strings.Contains(stderrOutput, "credential") {
				return "", fmt.Errorf("git credential helper not configured or no stored credentials")
			}
			return "", fmt.Errorf("git credential fill failed: %v", err)
		}
	case <-ctx.Done():
		// 超时！这说明命令在等待用户输入
		// 强制终止整个进程组
		if cmd.Process != nil {
			// 使用SIGKILL直接终止，不给任何机会显示提示
			syscall.Kill(-cmd.Process.Pid, syscall.SIGKILL)
		}
		return "", fmt.Errorf("git credential fill waiting for interactive input - no stored credentials found")
	}

	// 检查输出
	outputStr := stdout.String()
	if outputStr == "" {
		return "", fmt.Errorf("git credential fill returned empty output - no stored credentials")
	}

	// 解析credential输出，只需要password字段
	lines := strings.Split(strings.TrimSpace(outputStr), "\n")
	var token string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "password=") {
			token = strings.TrimPrefix(line, "password=")
			break // 只需要 password，找到后直接跳出
		}
	}

	// 验证令牌完整性
	if token == "" {
		return "", fmt.Errorf("no password found in git credential helper output")
	}

	logging.Debug("GitHubService.getTokenViaGitCredentialFillNonInteractive: Successfully got token")
	return token, nil
}

// getMainBranchSHA 获取主分支SHA值和默认分支名称
func (s *GitHubService) getMainBranchSHA(repository string) (string, string, error) {
	parts := strings.Split(repository, "/")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid repository format")
	}
	owner, repo := parts[0], parts[1]

	client := s.getGitHubClient()
	ctx := context.Background()

	// 首先获取仓库信息，直接获取默认分支名称
	repoInfo, _, err := client.Repositories.Get(ctx, owner, repo)
	if err != nil {
		return "", "", fmt.Errorf("failed to get repository info: %v", err)
	}

	// 获取默认分支名称
	defaultBranch := repoInfo.GetDefaultBranch()
	if defaultBranch == "" {
		return "", "", fmt.Errorf("repository has no default branch")
	}

	// 使用默认分支名称获取分支引用和SHA
	ref, _, err := client.Git.GetRef(ctx, owner, repo, fmt.Sprintf("refs/heads/%s", defaultBranch))
	if err != nil {
		return "", "", fmt.Errorf("failed to get default branch '%s' ref: %v", defaultBranch, err)
	}

	return ref.Object.GetSHA(), defaultBranch, nil
}

// createBranchViaAPI 通过SDK创建分支
func (s *GitHubService) createBranchViaAPI(repository, branchName, sha string) error {
	parts := strings.Split(repository, "/")
	if len(parts) != 2 {
		return fmt.Errorf("invalid repository format")
	}
	owner, repo := parts[0], parts[1]

	client := s.getGitHubClient()
	ctx := context.Background()

	ref := &github.Reference{
		Ref: github.String(fmt.Sprintf("refs/heads/%s", branchName)),
		Object: &github.GitObject{
			SHA: github.String(sha),
		},
	}

	_, _, err := client.Git.CreateRef(ctx, owner, repo, ref)
	if err != nil {
		return fmt.Errorf("failed to create branch: %v", err)
	}

	return nil
}

// createFileViaAPI 通过SDK创建或更新文件
func (s *GitHubService) createFileViaAPI(repository, branchName, filePath, content string) error {
	parts := strings.Split(repository, "/")
	if len(parts) != 2 {
		return fmt.Errorf("invalid repository format")
	}
	owner, repo := parts[0], parts[1]

	client := s.getGitHubClient()
	ctx := context.Background()

	// 检查文件是否已存在，如果存在需要获取 SHA
	existingFile, _, _, err := client.Repositories.GetContents(ctx, owner, repo, filePath, &github.RepositoryContentGetOptions{
		Ref: branchName,
	})

	fileContent := &github.RepositoryContentFileOptions{
		Message: github.String("Add Qoder AI workflow for code review and mention support"),
		Content: []byte(content),
		Branch:  github.String(branchName),
	}

	// 如果文件已存在，需要提供 SHA
	if err == nil && existingFile != nil {
		fileContent.SHA = github.String(existingFile.GetSHA())
		_, _, err = client.Repositories.UpdateFile(ctx, owner, repo, filePath, fileContent)
		if err != nil {
			return fmt.Errorf("failed to update file: %v", err)
		}
	} else {
		// 文件不存在，创建新文件
		_, _, err = client.Repositories.CreateFile(ctx, owner, repo, filePath, fileContent)
		if err != nil {
			return fmt.Errorf("failed to create file: %v", err)
		}
	}

	return nil
}

// generatePRDescription 生成Pull Request的详细描述
func (s *GitHubService) generatePRDescription(options core.GitHubWorkflowOptions) string {
	description := `# Qoder AI Workflow Setup

This PR adds Qoder AI workflow(s) to the repository with the following features:

`

	// 生成功能列表
	var workflowFiles []string
	if options.CodeReview {
		description += "- **Code Review**: Automatic code review for PRs\n"
		workflowFiles = append(workflowFiles, ".github/workflows/qoder-auto-review.yml")
	}
	if options.MentionSupport {
		description += "- **Mention Support**: AI assistant response when @qoder is mentioned in comments\n"
		workflowFiles = append(workflowFiles, ".github/workflows/qoder-mention.yml")
	}

	// 添加文件列表
	description += "\n## Workflow Files Created\n\n"
	for _, file := range workflowFiles {
		description += fmt.Sprintf("- `%s`\n", file)
	}

	description += `
## Configuration

- QODER_MACHINE_ID and QODER_USER_INFO secrets have been set
- Workflow will be triggered on the following events:`

	if options.CodeReview {
		description += "\n  - Pull Request events (opened, synchronize)"
	}
	if options.MentionSupport {
		description += "\n  - Issue comment events (when @qoder is mentioned)"
		description += "\n  - Pull request review comment events (when @qoder is mentioned)"
		description += "\n  - Issue events (when @qoder is mentioned)"
		description += "\n  - Pull request review events (when @qoder is mentioned)"
	}

	description += `

## Usage

`

	if options.CodeReview {
		description += "1. Qoder AI will automatically perform code review when creating PRs\n"
	}
	if options.MentionSupport {
		if options.CodeReview {
			description += "2. Mention @qoder in comments to get AI assistant help\n"
		} else {
			description += "1. Mention @qoder in comments to get AI assistant help\n"
		}
	}

	description += "\nPlease merge this PR to enable Qoder AI features."

	// 添加Qoder签名
	description += "\n\n---\n\n"
	description += "*This PR was automatically generated by [Qoder CLI](https://qoder.com) - Your AI-powered development assistant* 🤖"

	return description
}

// loadTemplateFile 加载模板文件
func (s *GitHubService) loadTemplateFile(filename string) string {
	if s.assets == nil {
		return ""
	}

	content, err := s.assets.GetWorkflowTemplate(filename)
	if err != nil {
		return ""
	}

	return content
}

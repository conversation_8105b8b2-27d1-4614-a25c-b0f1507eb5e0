package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/qoder-ai/qodercli/core/llm/filter"
	"github.com/qoder-ai/qodercli/core/llm/shell"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/internal/llm/prompt"
	"github.com/qoder-ai/qodercli/core/internal/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/llm/provider"
	coreTools "github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

const TaskPrompt = `Launch a new agent to handle complex, multi-step tasks autonomously.

Available agent types and the tools they have access to:
%s
When using the Task tool, you must specify a subagent_type parameter to select which agent type to use.

When to use the Task tool:
- When you are instructed to execute custom slash commands. Use the Task tool with the slash command invocation as the entire prompt. The slash command can take arguments. For example: Task(description="Check the file", prompt="/check-file path/to/file.py")
- Complex multi-step tasks requiring autonomous execution
- When you need to search for keywords or files and are not confident about finding the right match quickly
- Tasks that benefit from specialized agent capabilities

When NOT to use the Task tool:
- If you want to read a specific file path, use the read or Glob tool instead of the Task tool, to find the match more quickly
- If you are searching for a specific class definition like "class Foo", use the Glob tool instead, to find the match more quickly
- If you are searching for code within a specific file or set of 2-3 files, use the read tool instead of the Task tool, to find the match more quickly
- Other tasks that are not related to the agent descriptions above

Usage notes:
1. Launch multiple agents concurrently whenever possible, to maximize performance; to do that, use a single message with multiple tool uses
2. When the agent is done, it will return a single message back to you. The result returned by the agent is not visible to the user. To show the user the result, you should send a text message back to the user with a concise summary of the result.
3. Each agent invocation is stateless. You will not be able to send additional messages to the agent, nor will the agent be able to communicate with you outside of its final report. Therefore, your prompt should contain a highly detailed task description for the agent to perform autonomously and you should specify exactly what information the agent should return back to you in its final and only message to you.
4. The agent's outputs should generally be trusted
5. Clearly tell the agent whether you expect it to write code or just to do research (search, file reads, web fetches, etc.), since it is not aware of the user's intent`

const SynthesizePrompt = `Original task: %s

I've assigned multiple agents to tackle this task. Each agent has analyzed the problem and provided their findings.

%s

Based on all the information provided by these agents, synthesize a comprehensive and cohesive response that:
1. Combines the key insights from all agents
2. Resolves any contradictions between agent findings
3. Presents a unified solution that addresses the original task
4. Includes all important details and code examples from the individual responses
5. Is well-structured and complete

Your synthesis should be thorough but focused on the original task.
`

type taskTool struct {
	mcp             llm.McpOperator
	sessions        llm.SessionOperator
	messages        llm.MessageOperator
	memories        llm.MemoryOperator
	files           core.HistoryService
	permissions     llm.PermissionTrigger
	providers       provider.Service
	shells          shell.ShellService
	agents          agent.SubAgentService
	config          *config.Service
	messageRefactor filter.MessageRefactor
	workingDir      string
	turnLimiter     TurnLimiter
}

func (t *taskTool) Info() coreTools.ToolInfo {
	return coreTools.ToolInfo{
		Name:        coreTools.TaskToolName,
		Description: t.generateDescription(),
		Parameters: map[string]any{
			"description": map[string]any{
				"type":        "string",
				"description": "A short (3-5 word) description of the task",
			},
			"prompt": map[string]any{
				"type":        "string",
				"description": "The task for the agent to perform",
			},
			"subagent_type": map[string]any{
				"type":        "string",
				"description": "The type of specialized agent to use for this task",
			},
		},
		Required: []string{"description", "prompt", "subagent_type"},
	}
}

// getAvailableAgents 返回可用的代理类型列表
func (t *taskTool) getAvailableAgents() []agent.SubAgent {
	// 这个函数应该要保证内置 Agent 可以读取
	if result, err := t.agents.List(); err != nil {
		logging.ErrorPersist(fmt.Sprintf("failed to load agents: %v", err))
		return result
	} else {
		return result
	}
}

func (t *taskTool) generateDescription() string {
	availableAgents := t.getAvailableAgents()

	agentList := ""
	for _, info := range availableAgents {
		toolsList := ""
		if len(info.Tools) == 1 && info.Tools[0] == "*" {
			toolsList = "All available tools"
		} else {
			toolsList = strings.Join(info.Tools, ", ")
		}
		agentList += fmt.Sprintf("- %s: %s (Tools: %s)\n", info.Name, info.Description, toolsList)
	}

	return fmt.Sprintf(TaskPrompt, agentList)
}

func (t *taskTool) Run(ctx coreTools.ToolExecutionContext, call coreTools.ToolCall) (coreTools.ToolResponse, error) {
	startTime := time.Now()

	params, err := specs.TaskSpec.GetParams(call.Input)
	if err != nil {
		return coreTools.NewTextErrorResponse(fmt.Sprintf("error parsing parameters: %s", err)), nil
	}

	if params.Prompt == "" {
		return coreTools.NewTextErrorResponse("prompt is required"), nil
	}

	if params.SubagentType == "" {
		return coreTools.NewTextErrorResponse("subagent_type is required"), nil
	}

	sessionId := ctx.GetSessionId()
	messageId := ctx.GetMessageId()
	if sessionId == "" || messageId == "" {
		return coreTools.ToolResponse{}, fmt.Errorf("session_id and message_id are required")
	}

	defer func() {
		// TODO hooks SubagentStop
	}()

	// TODO: 获取配置中的parallelTasksCount
	parallelTasksCount := 1 // 默认为1，后续可以从配置读取
	if parallelTasksCount > 1 {
		// fixme 这里统计信息没有更新
		return t.runParallelAgents(ctx, call, params, parallelTasksCount, startTime)
	} else {
		return t.runSingleAgent(ctx, call, params, startTime)
	}
}

func (t *taskTool) runSingleAgent(ctx coreTools.ToolExecutionContext, call coreTools.ToolCall, params *specs.TaskParams, startTime time.Time) (coreTools.ToolResponse, error) {
	sessionId := ctx.GetSessionId()

	// 使用createTaskAgent而不是NewAgent，这样可以使用自定义SystemPrompt
	taskAgent, err := t.createTaskAgent(params.SubagentType)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error creating agent: %s", err)
	}

	session, err := t.sessions.CreateTaskSession(ctx, call.Id, sessionId, fmt.Sprintf("Task: %s", params.Description))
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error creating session: %s", err)
	}

	done, err := taskAgent.Run(ctx, session.Id, params.Prompt)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error running agent: %s", err)
	}

	// 等待agent完成
	var lastEvent agent.Event
	for event := range done {
		if event.Type == agent.AgentEventTypeError {
			return coreTools.ToolResponse{}, fmt.Errorf("agent error: %s", event.Error)
		}
		lastEvent = event
	}

	if lastEvent.Message.Role != message.Assistant {
		return coreTools.NewTextErrorResponse("no response from agent"), nil
	}

	// 更新父会话成本
	if err := t.updateParentSessionCost(ctx, sessionId, session.Id); err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error updating session cost: %s", err)
	}

	session, err = t.sessions.GetSession(ctx, session.Id)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("fail to get task session: %s", err)
	}

	// 计算工具调用量
	messages, err := t.messages.ListMessages(ctx, session.Id)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("fail to list task messages: %s", err)
	}

	tc := 0
	for _, msg := range messages {
		if msg.Role == message.Tool {
			tc += len(msg.ToolResults())
		}
	}

	// 构建结果
	duration := time.Since(startTime).Milliseconds()
	taskResult := specs.TaskResult{
		Content:           []message.ContentPart{lastEvent.Message.Content()},
		TotalDurationMs:   duration,
		TotalTokens:       int(session.PromptTokens + session.CompletionTokens),
		TotalToolUseCount: tc, // 简化统计
	}

	resultJson, _ := json.Marshal(taskResult)
	return coreTools.NewTextResponse(string(resultJson)), nil
}

func (t *taskTool) runParallelAgents(ctx coreTools.ToolExecutionContext, call coreTools.ToolCall, params *specs.TaskParams, parallelCount int, startTime time.Time) (coreTools.ToolResponse, error) {
	sessionID := ctx.GetSessionId()

	// 创建并行任务
	var wg sync.WaitGroup
	results := make([]specs.TaskResult, parallelCount)
	errors := make([]error, parallelCount)

	enhancedPrompt := fmt.Sprintf("%s\n\nProvide a thorough and complete analysis.", params.Prompt)

	for i := 0; i < parallelCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			// 使用createTaskAgent而不是NewAgent，这样可以使用自定义SystemPrompt
			taskAgent, err := t.createTaskAgent(params.SubagentType)
			if err != nil {
				errors[index] = err
				return
			}

			session, err := t.sessions.CreateTaskSession(ctx, fmt.Sprintf("%s_%d", call.Id, index), sessionID, fmt.Sprintf("Task Agent %d: %s", index+1, params.Description))
			if err != nil {
				errors[index] = err
				return
			}

			done, err := taskAgent.Run(ctx, session.Id, enhancedPrompt)
			if err != nil {
				errors[index] = err
				return
			}

			// 等待agent完成
			var lastEvent agent.Event
			for event := range done {
				if event.Type == agent.AgentEventTypeError {
					errors[index] = event.Error
					return
				}
				lastEvent = event
			}

			if lastEvent.Message.Role == message.Assistant {
				results[index] = specs.TaskResult{
					Content:           []message.ContentPart{lastEvent.Message.Content()},
					TotalTokens:       100, // 简化统计
					TotalToolUseCount: 0,   // 简化统计
				}
			}
		}(i)
	}

	wg.Wait()

	// 检查错误
	for i, err := range errors {
		if err != nil {
			return coreTools.ToolResponse{}, fmt.Errorf("agent %d error: %s", i+1, err)
		}
	}

	// 合成结果
	synthesisResult, err := t.synthesizeResults(ctx, call, params, results, startTime)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("synthesis error: %s", err)
	}

	return synthesisResult, nil
}

func (t *taskTool) synthesizeResults(ctx coreTools.ToolExecutionContext, call coreTools.ToolCall, params *specs.TaskParams, results []specs.TaskResult, startTime time.Time) (coreTools.ToolResponse, error) {
	sessionID := ctx.GetSessionId()

	// 生成合成提示
	synthesisPrompt := t.buildSynthesisPrompt(params.Prompt, results)

	// 获取agent的SystemPrompt并使用createTaskAgent
	taskAgent, err := t.createTaskAgent(params.SubagentType)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error creating synthesis agent: %s", err)
	}

	session, err := t.sessions.CreateTaskSession(ctx, fmt.Sprintf("%s_synthesis", call.Id), sessionID, fmt.Sprintf("Synthesis: %s", params.Description))
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error creating synthesis session: %s", err)
	}

	done, err := taskAgent.Run(ctx, session.Id, synthesisPrompt)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error running synthesis agent: %s", err)
	}

	// 等待合成agent完成
	var lastEvent agent.Event
	for event := range done {
		if event.Type == agent.AgentEventTypeError {
			return coreTools.ToolResponse{}, fmt.Errorf("synthesis agent error: %s", event.Error)
		}
		lastEvent = event
	}

	if lastEvent.Message.Role != message.Assistant {
		return coreTools.NewTextErrorResponse("no response from synthesis agent"), nil
	}

	// 计算总计数据
	totalTokens := 100 // 简化统计
	totalToolUseCount := 0

	for _, r := range results {
		totalTokens += r.TotalTokens
		totalToolUseCount += r.TotalToolUseCount
	}

	// 更新父会话成本
	if err := t.updateParentSessionCost(ctx, sessionID, session.Id); err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error updating session cost: %s", err)
	}

	duration := time.Since(startTime).Milliseconds()
	finalResult := specs.TaskResult{
		Content:           []message.ContentPart{lastEvent.Message.Content()},
		TotalDurationMs:   duration,
		TotalTokens:       totalTokens,
		TotalToolUseCount: totalToolUseCount,
	}

	resultJson, _ := json.Marshal(finalResult)
	return coreTools.NewTextResponse(string(resultJson)), nil
}

func (t *taskTool) buildSynthesisPrompt(originalTask string, results []specs.TaskResult) string {
	var subAgentResult strings.Builder
	for i, result := range results {
		content := ""
		for _, part := range result.Content {
			if textPart, ok := part.(message.TextContent); ok {
				content += textPart.String() + "\n\n"
			}
		}
		subAgentResult.WriteString(fmt.Sprintf("== AGENT %d RESPONSE ==\n%s\n\n", i+1, content))
	}
	return fmt.Sprintf(SynthesizePrompt, originalTask, subAgentResult.String())
}

func (t *taskTool) updateParentSessionCost(ctx context.Context, parentSessionID, childSessionID string) error {
	childSession, err := t.sessions.GetSession(ctx, childSessionID)
	if err != nil {
		return err
	}

	parentSession, err := t.sessions.GetSession(ctx, parentSessionID)
	if err != nil {
		return err
	}

	parentSession.Cost += childSession.Cost
	return t.sessions.SaveSession(ctx, parentSession)
}

func NewTaskTool(app llm.AppSupport, tl TurnLimiter) coreTools.BaseTool {
	return &taskTool{
		mcp:             app,
		permissions:     app,
		files:           app.GetHistoryService(),
		sessions:        app,
		messages:        app,
		memories:        app,
		shells:          app.GetShellService(),
		providers:       app.GetProviderService(),
		agents:          app.GetAgentService(),
		config:          app.GetConfig(),
		workingDir:      app.GetWorkingDir(),
		messageRefactor: app.GetMessageRefactor(),
		turnLimiter:     tl,
	}
}

// createTaskAgent 创建一个使用自定义SystemPrompt的agent
func (t *taskTool) createTaskAgent(subagentType string) (agent.Agent, error) {
	// 获取基础的agent配置（使用task agent的配置作为基础）
	agentConfig, ok := t.config.GetAgent(config.AgentTask)
	if !ok {
		return nil, fmt.Errorf("task agent configuration not found")
	}

	agentInfo, agentTools := t.getAgentInfo(subagentType)
	modelName := agentConfig.Model
	if agentInfo.Model != "" {
		// 使用SubAgent自定义的model
		modelName = models.ModelId(agentInfo.Model)
	}
	model, ok := models.SupportedModels[modelName]
	if !ok {
		return nil, fmt.Errorf("model %s not supported", modelName)
	}

	providerCfg, ok := t.config.GetProvider(model.Provider)
	if !ok {
		return nil, fmt.Errorf("client %s not supported", model.Provider)
	}
	if providerCfg.Disabled {
		return nil, fmt.Errorf("client %s is not enabled", model.Provider)
	}

	maxTokens := model.DefaultMaxTokens
	if agentConfig.MaxTokens > 0 {
		maxTokens = agentConfig.MaxTokens
	}

	// 创建provider，使用自定义的SystemPrompt
	systemPrompt := t.getAgentSystemPrompt(subagentType, model.Provider)
	opts := []provider.ClientOption{
		provider.WithAPIKey(providerCfg.ApiKey),
		provider.WithModel(model),
		provider.WithSystemMessage(systemPrompt), // 使用自定义的SystemPrompt
		provider.WithMaxTokens(maxTokens),
		provider.WithDebug(t.config.Debug),
	}

	client, err := t.providers.NewClient(model.Provider, opts...)
	if err != nil {
		return nil, fmt.Errorf("could not create client: %v", err)
	}

	// 创建agent实例

	taskAgent := &AgentRunner{
		Broker:          pubsub.NewBroker[agent.Event](),
		name:            config.AgentTask,
		client:          client,
		messages:        t.messages,
		sessions:        t.sessions,
		tools:           agentTools,
		activeRequests:  sync.Map{},
		config:          t.config,
		messageRefactor: t.messageRefactor,
		turnLimiter:     t.turnLimiter,
		// 注意：Task agent不需要title和summarize client
	}

	return taskAgent, nil
}

// getAgentSystemPrompt 获取指定agent类型的SystemPrompt
func (t *taskTool) getAgentSystemPrompt(name string, provider models.ModelProvider) string {
	var systemPrompt string
	availableAgents := t.getAvailableAgents()
	for _, info := range availableAgents {
		if info.Name == name {
			systemPrompt = info.SystemPrompt
		}
	}
	// 使用默认的SystemPrompt
	return prompt.GetAgentPrompt(config.AgentTask, provider, systemPrompt, t.workingDir)
}

func (t *taskTool) getAgentInfo(name string) (agent.SubAgent, []coreTools.BaseTool) {
	// 获取指定类型agent的工具配置
	availableAgents := t.getAvailableAgents()

	var agentInfo agent.SubAgent
	for _, info := range availableAgents {
		if info.Name == name {
			agentInfo = info
			break
		}
	}

	// 组织工具创建函数
	toolCreators := map[string]func() coreTools.BaseTool{
		coreTools.GlobToolName: tools.NewGlobTool,
		coreTools.GrepToolName: tools.NewGrepTool,
		coreTools.LSToolName:   tools.NewLsTool,
		coreTools.ReadToolName: tools.NewReadTool,
		coreTools.WebFetchToolName: func() coreTools.BaseTool {
			return tools.NewWebFetchTool(t.permissions)
		},
		coreTools.WebSearchToolName: tools.NewSearchWebTool,
		coreTools.BashToolName: func() coreTools.BaseTool {
			return tools.NewBashTool(t.permissions, t.shells)
		},
		coreTools.EditToolName: func() coreTools.BaseTool {
			return tools.NewEditTool(t.permissions, t.files)
		},
		coreTools.MultiEditToolName: func() coreTools.BaseTool {
			return tools.NewMultiEditTool(t.permissions, t.files)
		},
		coreTools.WriteToolName: func() coreTools.BaseTool {
			return tools.NewWriteTool(t.permissions, t.files)
		},
		coreTools.TodoWriteToolName: tools.NewTodoWriteTool,
	}

	mcpTools := t.mcp.ListMcpTools(context.TODO())
	for _, t := range mcpTools {
		resultT := t
		toolCreators[t.Info().Name] = func() coreTools.BaseTool {
			return resultT
		}
	}

	regexNames := agentInfo.Tools
	if len(regexNames) == 0 {
		regexNames = []string{"*"}
	}

	var agentTools []coreTools.BaseTool
	addedTools := map[string]bool{}
	for _, regexName := range regexNames {
		for creatorName, creator := range toolCreators {
			if _, ok := addedTools[creatorName]; !ok && match(regexName, creatorName) {
				agentTools = append(agentTools, creator())
				addedTools[creatorName] = true
			}
		}
	}
	sort.Slice(agentTools, func(i, j int) bool {
		return agentTools[i].Info().Name < agentTools[j].Info().Name
	})
	return agentInfo, agentTools
}

// match matcher：匹配工具名称的模式，区分大小写
// - 简单字符串精确匹配：Write 仅匹配 Write 工具
// - 支持正则表达式：Edit|Write 或 Notebook.*
// - 使用 * 匹配所有工具。您也可以使用空字符串（""）或将 matcher 留空。
func match(matcher string, tool string) bool {
	// 处理空字符串或 "*" 的情况，匹配所有工具
	if matcher == "" || matcher == "*" {
		return true
	}

	// 检查是否包含正则表达式的特殊字符
	regexChars := []string{"*", ".", "|", "(", ")", "[", "]", "?", "+", "^", "$", "\\"}
	isRegex := false
	for _, char := range regexChars {
		if strings.Contains(matcher, char) {
			isRegex = true
			break
		}
	}

	// 如果包含正则表达式特殊字符，则使用正则匹配
	if isRegex {
		reg, err := regexp.Compile(matcher)
		if err == nil {
			return reg.MatchString(tool)
		}
		// 如果正则表达式无效，回退到精确匹配
		return matcher == tool
	}

	// 不包含特殊字符时使用精确匹配
	return matcher == tool
}

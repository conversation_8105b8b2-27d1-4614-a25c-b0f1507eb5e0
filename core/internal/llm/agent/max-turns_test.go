package agent

import (
	"errors"
	"sync"
	"sync/atomic"
	"testing"
)

func TestTurnLimiterUnlimited(t *testing.T) {
	limiter := NewTurnLimiter(0)
	const total = 1000
	var wg sync.WaitGroup
	wg.Add(total)
	for i := 0; i < total; i++ {
		go func() {
			defer wg.Done()
			if err := limiter.Advance(); err != nil {
				t.<PERSON>("unexpected error in unlimited mode: %v", err)
			}
		}()
	}
	wg.Wait()

	if got := limiter.Count(); got != total {
		t.Fatalf("Count() = %d, want %d", got, total)
	}
	if rem := limiter.Remaining(); rem != -1 {
		t.Fatalf("Remaining() = %d, want -1 (unlimited)", rem)
	}
}

func TestTurnLimiterWithLimitSequential(t *testing.T) {
	limiter := NewTurnLimiter(3)
	for i := 0; i < 3; i++ {
		if err := limiter.Advance(); err != nil {
			t.Fatalf("advance #%d failed: %v", i+1, err)
		}
	}
	if err := limiter.Advance(); err == nil || !errors.Is(err, ErrMaxTurnsReached) {
		t.Fatalf("expected ErrMaxTurnsReached on 4th advance, got: %v", err)
	}
	if got := limiter.Count(); got != 3 {
		t.Fatalf("Count() = %d, want 3", got)
	}
	if rem := limiter.Remaining(); rem != 0 {
		t.Fatalf("Remaining() = %d, want 0", rem)
	}
	if lim := limiter.Limit(); lim != 3 {
		t.Fatalf("Limit() = %d, want 3", lim)
	}
}

func TestTurnLimiterWithLimitConcurrent(t *testing.T) {
	const limit = 100
	const goroutines = 1000
	limiter := NewTurnLimiter(limit)

	var wg sync.WaitGroup
	var successes int64
	var failures int64
	wg.Add(goroutines)
	for i := 0; i < goroutines; i++ {
		go func() {
			defer wg.Done()
			if err := limiter.Advance(); err != nil {
				atomic.AddInt64(&failures, 1)
				return
			}
			atomic.AddInt64(&successes, 1)
		}()
	}
	wg.Wait()

	if successes != limit {
		t.Fatalf("successes = %d, want %d", successes, limit)
	}
	if limiter.Count() != limit {
		t.Fatalf("Count() = %d, want %d", limiter.Count(), limit)
	}
}

func TestTurnLimiterSetLimitDecrease(t *testing.T) {
	limiter := NewTurnLimiter(10)
	for i := 0; i < 5; i++ {
		if err := limiter.Advance(); err != nil {
			t.Fatalf("advance #%d failed: %v", i+1, err)
		}
	}
	limiter.SetLimit(3)
	if lim := limiter.Limit(); lim != 3 {
		t.Fatalf("Limit() = %d, want 3", lim)
	}
	if rem := limiter.Remaining(); rem != 0 {
		t.Fatalf("Remaining() = %d, want 0 after decreasing below count", rem)
	}
	if err := limiter.Advance(); err == nil || !errors.Is(err, ErrMaxTurnsReached) {
		t.Fatalf("expected ErrMaxTurnsReached after decreasing limit, got: %v", err)
	}
}

func TestTurnLimiterReset(t *testing.T) {
	limiter := NewTurnLimiter(5)
	for i := 0; i < 3; i++ {
		if err := limiter.Advance(); err != nil {
			t.Fatalf("advance #%d failed: %v", i+1, err)
		}
	}
	limiter.Reset()
	if c := limiter.Count(); c != 0 {
		t.Fatalf("Count() after Reset = %d, want 0", c)
	}
	if rem := limiter.Remaining(); rem != 5 {
		t.Fatalf("Remaining() after Reset = %d, want 5", rem)
	}
	for i := 0; i < 5; i++ {
		if err := limiter.Advance(); err != nil {
			t.Fatalf("advance after reset #%d failed: %v", i+1, err)
		}
	}
	if err := limiter.Advance(); err == nil || !errors.Is(err, ErrMaxTurnsReached) {
		t.Fatalf("expected ErrMaxTurnsReached after hitting limit again, got: %v", err)
	}
}

func TestTurnLimiterIncreaseLimit(t *testing.T) {
	limiter := NewTurnLimiter(3)
	for i := 0; i < 3; i++ {
		if err := limiter.Advance(); err != nil {
			t.Fatalf("advance #%d failed: %v", i+1, err)
		}
	}
	// 提高上限后应允许更多次数
	limiter.SetLimit(5)
	for i := 0; i < 2; i++ {
		if err := limiter.Advance(); err != nil {
			t.Fatalf("advance after increase #%d failed: %v", i+1, err)
		}
	}
	if err := limiter.Advance(); err == nil || !errors.Is(err, ErrMaxTurnsReached) {
		t.Fatalf("expected ErrMaxTurnsReached after reaching new limit, got: %v", err)
	}
}

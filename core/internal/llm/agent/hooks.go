package agent

import (
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/hook"
	"github.com/qoder-ai/qodercli/core/llm/tools"
)

func (a *AgentRunner) executePreToolUseHooks(sessionId string, tool tools.BaseTool) error {
	toolName := tool.Info().Name
	executors := a.hooks.GetPreToolUseHookExecutor(hook.Input{
		Tool: toolName,
		Args: config.HookPreToolUseInput{
			SessionId:      sessionId,
			TranscriptPath: "",
			Cwd:            a.config.WorkingDir,
			HookEventName:  config.HookEventPreToolUse,
			ToolName:       toolName,
			ToolInput:      config.ToolInput{},
		},
	})

	for _, executor := range executors {
		result, err := executor.Execute()
		if err != nil {
			return err
		}

		decision := result.DecisionControl
		if decision != nil {
			if decision.Continue == nil || *decision.Continue {
				if decision.SuppressOutput != nil && *decision.SuppressOutput {
					continue // 忽略结果
				} else {
					// TUI 展示结果
				}
			} else {
				// 直接结束
				return nil
			}
		} else if result.ExitCode == 0 {
			//result.Stdout 向用户展示
		} else {
			//result.Stderr 向用户展示
			if result.ExitCode == 2 {
				return nil
			}
		}
	}
	return nil
}

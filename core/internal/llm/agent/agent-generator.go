package agent

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/internal/llm/prompt"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/provider"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/utils"
)

type AgentGenerator struct {
	messages   llm.MessageOperator
	runner     *AgentRunner
	config     *config.Service
	providers  provider.Service
	agents     agent.SubAgentService
	client     provider.Client
	workingDir string
}

func NewAgentGenerator(runner *AgentRunner) *AgentGenerator {
	return &AgentGenerator{
		runner:     runner,
		messages:   runner.messages,
		config:     runner.config,
		providers:  runner.providers,
		agents:     runner.agents,
		workingDir: runner.workingDir,
	}
}

func (a *AgentGenerator) GenerateAgent(ctx context.Context, sessionId string, requirement string, location agent.SubAgentLocation) error {
	client, err := a.getClient()
	if err != nil {
		return err
	}

	a.client = client
	exists, _ := a.agents.List()

	var names []string
	for _, subAgent := range exists {
		names = append(names, subAgent.Name)
	}

	userMsg, err := a.createUserMessage(ctx, sessionId, requirement, names)
	if err != nil {
		return err
	}

	generatorCtx, cancel := context.WithCancel(ctx)
	a.runner.Activate(sessionId, cancel)
	defer a.runner.Deactivate(sessionId)

	a.runner.PublishStartEvent(sessionId, nil, a.client.GetModel().Id)

	messages := a.runner.messageRefactor.Refactor(generatorCtx, sessionId, []message.Message{userMsg})
	eventChan := client.StreamResponse(generatorCtx, messages, nil)

	assistantMsg, err := a.messages.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.Assistant,
		Parts:    []message.ContentPart{},
		Model:    client.GetModel().Id,
		ParentId: userMsg.Id,
	})

	if err != nil {
		return err
	}

	msgCtx := context.WithValue(generatorCtx, tools.MessageIDContextKey, assistantMsg.Id)

	for event := range eventChan {
		if processErr := a.processEvent(msgCtx, sessionId, &assistantMsg, event, location); processErr != nil {
			a.err(fmt.Errorf("failed to process agent generation events: %w", err))
			a.finishMessage(ctx, &assistantMsg, message.FinishReasonError)
			return nil
		}
		if msgCtx.Err() != nil {
			a.finishMessage(generatorCtx, &assistantMsg, message.FinishReasonCanceled)
			return nil
		}
	}

	return nil
}

func (a *AgentGenerator) createUserMessage(ctx context.Context, sessionId string, requirement string, names []string) (message.Message, error) {
	parts := prompt.GetAgentGeneratorUserPrompt(requirement, names)
	lastMsg, err := a.messages.GetLastMessageOfSession(ctx, sessionId)
	if err != nil {
		logging.Error("fail to get last message of session "+sessionId, "err", err)
		return message.Message{}, err
	}

	parentId := ""
	if lastMsg != nil {
		parentId = lastMsg.Id
	}

	msg, err := a.messages.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.User,
		Parts:    parts,
		ParentId: parentId,
		IsMeta:   true,
	})
	return msg, nil
}

func (a *AgentGenerator) getClient() (provider.Client, error) {
	model, err := a.runner.getModelOfAgent(config.AgentCoder)
	if err != nil {
		return nil, err
	}

	providerCfg, ok := a.config.GetProvider(model.Provider)
	if !ok {
		logging.Error("fail to get provider config")
		return nil, fmt.Errorf("client %s not supported", model.Provider)
	}
	if providerCfg.Disabled {
		logging.Error(fmt.Sprintf("client %s is not enabled", model.Provider))
		return nil, fmt.Errorf("client %s is not enabled", model.Provider)
	}

	agentConfig, ok := a.config.GetAgent(config.AgentCoder)
	maxTokens := model.DefaultMaxTokens
	if agentConfig.MaxTokens > 0 {
		maxTokens = agentConfig.MaxTokens
	}

	opts := []provider.ClientOption{
		provider.WithAPIKey(providerCfg.ApiKey),
		provider.WithModel(*model),
		provider.WithSystemMessage(prompt.GetAgentGeneratorSystemPrompt()),
		provider.WithMaxTokens(maxTokens),
		provider.WithDebug(a.config.Debug),
	}

	return a.providers.NewClient(
		model.Provider,
		opts...,
	)
}

func (a *AgentGenerator) processEvent(
	ctx context.Context,
	sessionID string,
	assistantMsg *message.Message,
	event provider.Event,
	location agent.SubAgentLocation) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	switch event.Type {
	case provider.EventSending:
		assistantMsg.Status = message.StatusSending
		return a.messages.UpdateMessage(ctx, *assistantMsg)
	case provider.EventReceiving:
		assistantMsg.Status = message.StatusReceiving
		return a.messages.UpdateMessage(ctx, *assistantMsg)
	case provider.EventContentDelta:
		assistantMsg.AppendContent(event.Content)
		return a.messages.UpdateMessage(ctx, *assistantMsg)
	case provider.EventError:
		if errors.Is(event.Error, context.Canceled) {
			logging.InfoPersist(fmt.Sprintf("Event processing canceled for session: %s", sessionID))
			return context.Canceled
		}
		logging.ErrorPersist(event.Error.Error())
		return event.Error
	case provider.EventComplete:
		result, err := a.processResult(assistantMsg, location)
		if err != nil {
			assistantMsg.Parts = []message.ContentPart{message.TextContent{
				Type: "text",
				Text: "Fail to generate agent configuration: " + err.Error(),
			}}
			assistantMsg.AddFinish(message.FinishReasonError)
		} else {
			assistantMsg.Parts = []message.ContentPart{message.TextContent{
				Type: "text",
				Text: result,
			}}
			assistantMsg.AddFinish(message.FinishReasonEndTurn)
		}

		assistantMsg.Status = message.StatusFinished
		// 保存token用量信息到消息
		assistantMsg.SetUsage(message.TokenUsage{
			InputTokens:         event.Response.Usage.InputTokens,
			OutputTokens:        event.Response.Usage.OutputTokens,
			CacheCreationTokens: event.Response.Usage.CacheCreationTokens,
			CacheReadTokens:     event.Response.Usage.CacheReadTokens,
		})
		if err := a.messages.UpdateMessage(ctx, *assistantMsg); err != nil {
			return fmt.Errorf("failed to update message: %w", err)
		}

		if err := a.runner.TrackUsage(ctx, sessionID, a.client.GetModel(), event.Response.Usage); err != nil {
			return err
		}
		return nil
	}

	return nil
}

func (a *AgentGenerator) processResult(msg *message.Message, location agent.SubAgentLocation) (string, error) {
	errMsg := "invalid agent configuration generated, please try again later"
	if msg.Content().String() == "" {
		return "", errors.New(errMsg)
	}

	data := make(map[string]string)
	err := json.Unmarshal([]byte(msg.Content().String()), &data)
	if err != nil {
		return "", errors.New(errMsg)
	}

	if data["identifier"] == "" || data["whenToUse"] == "" || data["systemPrompt"] == "" {
		return "", errors.New(errMsg)
	}

	agentConfig := "---\n"
	agentConfig += "name: " + data["identifier"] + "\n"
	agentConfig += "description: |\n  " + strings.ReplaceAll(data["whenToUse"], "\n", "\n  ") + "\n"
	agentConfig += "---\n\n"
	agentConfig += data["systemPrompt"]

	// 保存 Agent 配置文件
	fileName := data["identifier"] + ".md"
	storagePath := ""
	switch location {
	case agent.UserSubAgentLocation:
		storagePath = filepath.Join(utils.GetUserStorageDir(), "agents")
	case agent.ProjectSubAgentLocation:
		storagePath = filepath.Join(utils.GetWorkspaceStorageDir(a.workingDir), "agents")
	default:
		logging.Error("unsupported sub agent location: " + string(location))
		return "", errors.New("unsupported sub agent location")
	}

	if _, err = os.Stat(storagePath); os.IsNotExist(err) {
		err = os.MkdirAll(storagePath, 0755)
		if err != nil {
			return "", err
		}
	} else if err != nil {
		return "", err
	}

	err = os.WriteFile(filepath.Join(storagePath, fileName), []byte(agentConfig), 0644)
	if err != nil {
		return "", err
	}

	result := fmt.Sprintf("I have created a agent configuration named **%s**, stored to `%s`, configuration as below, you can modify it by editing the configuration file:\n\n", data["identifier"], storagePath)
	result += "# Name\n"
	result += data["identifier"] + "\n"
	result += "# Description\n"
	result += data["whenToUse"] + "\n"
	result += "# SystemPrompt\n"
	result += data["systemPrompt"]
	return result, nil
}

func (a *AgentGenerator) finishMessage(ctx context.Context, msg *message.Message, finishReason message.FinishReason) {
	msg.AddFinish(finishReason)
	msg.Status = message.StatusFinished
	_ = a.messages.UpdateMessage(ctx, *msg)
}

func (a *AgentGenerator) err(err error) agent.Event {
	return agent.Event{
		Type:  agent.AgentEventTypeError,
		Error: err,
	}
}

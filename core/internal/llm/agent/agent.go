package agent

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/qoder-ai/qodercli/core/llm/filter"
	"github.com/qoder-ai/qodercli/core/llm/output_style"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/internal/llm/prompt"
	internaltools "github.com/qoder-ai/qodercli/core/internal/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/llm/provider"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/monitoring"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

type AgentRunner struct {
	*pubsub.Broker[agent.Event]
	name            config.AgentName
	messages        llm.MessageOperator
	sessions        llm.SessionOperator
	hooks           llm.HookManager
	providers       provider.Service
	agents          agent.SubAgentService
	tools           []tools.BaseTool
	config          *config.Service
	client          provider.Client
	titleClient     provider.Client
	summarizeClient provider.Client
	messageRefactor filter.MessageRefactor
	activeRequests  sync.Map
	workingDir      string
	outputStyles    output_style.OutputStyleService
	turnLimiter     TurnLimiter
}

// SubscribeCompactCommand subscribes to command events and triggers compact when received.
func (a *AgentRunner) SubscribeCompactCommand(ctx context.Context, cmdCh <-chan pubsub.Event[command.Event]) {
	go func() {
		for ev := range cmdCh {
			if ev.Payload.CommandName == "compact_internal" {
				sessionId := ev.Payload.SessionId
				args := ev.Payload.Args

				// 从 payload 中获取通信通道
				doneChan, _ := ev.Payload.Payload.(map[string]interface{})["done_chan"].(chan bool)
				errorChan, _ := ev.Payload.Payload.(map[string]interface{})["error_chan"].(chan error)

				// 执行压缩任务
				go func() {
					err := a.SummarizeWithInstructions(ctx, sessionId, args)
					if err != nil && errorChan != nil {
						errorChan <- err
					} else if doneChan != nil {
						doneChan <- true
					}
				}()
			}
		}
	}()
}

func NewAgent(
	agentName config.AgentName,
	app llm.AppSupport,
	agentTools []tools.BaseTool,
	turnLimiter TurnLimiter,
) (*AgentRunner, error) {
	cfg := app.GetConfig()

	a := &AgentRunner{
		Broker:          pubsub.NewBroker[agent.Event](),
		name:            agentName,
		messages:        app,
		providers:       app.GetProviderService(),
		sessions:        app,
		hooks:           app.GetHookService(),
		tools:           agentTools,
		outputStyles:    app.GetOutputStyleService(),
		activeRequests:  sync.Map{},
		config:          cfg,
		workingDir:      app.GetWorkingDir(),
		agents:          app.GetAgentService(),
		messageRefactor: app.GetMessageRefactor(),
		turnLimiter:     turnLimiter,
	}

	err := a.initLlmClients(agentName)
	if err != nil {
		return nil, err
	}

	return a, nil
}

func (a *AgentRunner) initLlmClients(agentName config.AgentName) error {
	client, err := a.createLlmClient(agentName)
	if err != nil {
		return err
	}

	a.client = client
	if agentName == config.AgentCoder {
		titleProvider, err := a.createLlmClient(config.AgentTitle)
		if err != nil {
			return err
		}
		summarizeProvider, err := a.createLlmClient(config.AgentSummarizer)
		if err != nil {
			return err
		}
		a.summarizeClient = summarizeProvider
		a.titleClient = titleProvider
	}

	return nil
}

func (a *AgentRunner) Model() models.Model {
	return a.client.GetModel()
}

func (a *AgentRunner) Cancel(sessionId string) {
	// Cancel regular requests
	if cancelFunc, exists := a.activeRequests.LoadAndDelete(sessionId); exists {
		if cancel, ok := cancelFunc.(context.CancelFunc); ok {
			logging.InfoPersist(fmt.Sprintf("Request cancellation initiated for session: %s", sessionId))
			cancel()
		}
	}

	// Also check for summarize requests
	if cancelFunc, exists := a.activeRequests.LoadAndDelete(sessionId + "-summarize"); exists {
		if cancel, ok := cancelFunc.(context.CancelFunc); ok {
			logging.InfoPersist(fmt.Sprintf("Summarize cancellation initiated for session: %s", sessionId))
			cancel()
		}
	}
}

func (a *AgentRunner) IsBusy() bool {
	busy := false
	a.activeRequests.Range(func(key, value interface{}) bool {
		if cancelFunc, ok := value.(context.CancelFunc); ok {
			if cancelFunc != nil {
				busy = true
				return false // Stop iterating
			}
		}
		return true // Continue iterating
	})
	return busy
}

// IsSessionBusy 检查session是否忙碌（包括普通执行和summarize执行）
func (a *AgentRunner) IsSessionBusy(sessionId string) bool {
	// 检查普通Agent执行
	if _, busy := a.activeRequests.Load(sessionId); busy {
		return true
	}
	// 检查summarize执行
	if _, busy := a.activeRequests.Load(sessionId + "-summarize"); busy {
		return true
	}
	return false
}

func (a *AgentRunner) generateTitle(ctx context.Context, sessionId string, content string) error {
	if content == "" {
		return nil
	}
	if a.titleClient == nil {
		return nil
	}

	ctx = context.WithValue(ctx, tools.SessionIDContextKey, sessionId)
	parts := []message.ContentPart{message.TextContent{Text: content, Type: "text"}}
	titleMessages := []message.Message{
		{
			Role:  message.User,
			Parts: parts,
		},
	}

	// 创建标题生成trace
	tm := monitoring.NewTraceManager(sessionId)
	titleCtx, titleSpan := tm.StartTitleGeneration(ctx, string(a.titleClient.GetModel().Id), content[:min(100, len(content))])
	defer titleSpan.End()

	response, err := a.titleClient.SendMessages(
		titleCtx,
		titleMessages,
		make([]tools.BaseTool, 0),
	)

	if err != nil {
		titleSpan.RecordResult(false, err)
		return err
	}

	if response == nil {
		err := errors.New("response is nil")
		titleSpan.RecordResult(false, err)
		return err
	}

	title := strings.TrimSpace(strings.ReplaceAll(response.Content, "\n", " "))
	titleSpan.RecordContent("input_content_preview", content[:min(100, len(content))])
	titleSpan.RecordContent("generated_title", title)
	titleSpan.RecordResult(title != "", nil)

	if title == "" {
		return nil
	}

	return a.sessions.UpdateSessionTitle(titleCtx, sessionId, title)
}

func (a *AgentRunner) err(err error) agent.Event {
	return agent.Event{
		Type:  agent.AgentEventTypeError,
		Error: err,
	}
}

func (a *AgentRunner) Activate(sessionId string, cancel context.CancelFunc) {
	a.activeRequests.Store(sessionId, cancel)
}

func (a *AgentRunner) Deactivate(sessionId string) {
	a.activeRequests.Delete(sessionId)
}

func (a *AgentRunner) PublishStartEvent(sessionId string, toolNames []string, modelId models.ModelId) {
	a.Publish(pubsub.CreatedEvent, agent.Event{
		Type:      agent.AgentEventTypeSystem,
		Subtype:   "init",
		SessionId: sessionId,
		Config: &agent.Config{
			Model:          modelId,
			Tools:          toolNames,
			PermissionMode: "bypassPermissions",
		},
	})
}

func (a *AgentRunner) RunWithContentParts(ctx context.Context, sessionId string, content string, parts ...message.ContentPart) (<-chan agent.Event, error) {
	// 使用带缓冲的通道，避免发送结果时阻塞，导致 defer 不执行，根 span 无法结束/flush
	events := make(chan agent.Event, 1)
	if a.IsSessionBusy(sessionId) {
		return nil, agent.ErrSessionBusy
	}

	// TODO hooks SessionStart

	genCtx, cancel := context.WithCancel(ctx)
	a.Activate(sessionId, cancel)
	go func() {
		logging.Debug("Request started", "sessionId", sessionId)
		defer cancel()
		defer logging.RecoverPanic("agent.Run", func() {
			events <- a.err(fmt.Errorf("panic while running the agent"))
		})

		var toolNames []string
		for _, tool := range a.tools {
			toolNames = append(toolNames, tool.Info().Name)
		}

		a.PublishStartEvent(sessionId, toolNames, a.client.GetModel().Id)

		result := a.processGenerationWithParts(genCtx, sessionId, content, parts)
		if result.Error != nil && !errors.Is(result.Error, agent.ErrRequestCancelled) && !errors.Is(result.Error, context.Canceled) {
			logging.ErrorPersist(result.Error.Error())
		}
		logging.Debug("Request completed", "sessionId", sessionId)
		a.Deactivate(sessionId)
		a.Publish(pubsub.CreatedEvent, result)
		events <- result
		close(events)
	}()
	return events, nil
}

func (a *AgentRunner) Run(ctx context.Context, sessionId string, content string, attachments ...message.Attachment) (<-chan agent.Event, error) {
	// TODO hooks UserPromptSubmit

	if !a.client.GetModel().SupportsAttachments && attachments != nil {
		attachments = nil
	}
	var parts []message.ContentPart
	parts = append(parts, message.TextContent{Text: content, Type: "text"})
	for _, attachment := range attachments {
		parts = append(parts, message.BinaryContent{Path: attachment.FilePath, MIMEType: attachment.MimeType, Data: attachment.Content})
	}
	return a.RunWithContentParts(ctx, sessionId, content, parts...)
}

func (a *AgentRunner) processGenerationWithParts(ctx context.Context, sessionId, content string, parts []message.ContentPart) agent.Event {
	msgs, err := a.messages.ListMessages(ctx, sessionId)
	if err != nil {
		return a.err(fmt.Errorf("failed to list messages: %w", err))
	}

	if a.shouldGenerateTitle(msgs) {
		go func() {
			defer logging.RecoverPanic("agent.Run", func() {
				logging.ErrorPersist("panic while generating title")
			})

			titleErr := a.generateTitle(ctx, sessionId, content)
			if titleErr != nil {
				logging.ErrorPersist(fmt.Sprintf("failed to generate title: %v", titleErr))
			}
		}()
	}

	ses, err := a.sessions.GetSession(ctx, sessionId)
	if err != nil {
		return a.err(fmt.Errorf("failed to get session: %w", err))
	}
	if ses.SummaryMessageId != "" {
		summaryMsgInex := -1
		for i, msg := range msgs {
			if msg.Id == ses.SummaryMessageId {
				summaryMsgInex = i
				break
			}
		}
		if summaryMsgInex != -1 {
			msgs = msgs[summaryMsgInex:]
			msgs[0].Role = message.User
		}
	}

	parentId := ""
	if len(msgs) > 0 {
		parentId = msgs[len(msgs)-1].Id
	}

	userMsg, err := a.messages.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.User,
		Parts:    parts,
		ParentId: parentId,
	})

	if err != nil {
		return a.err(fmt.Errorf("failed to create user message: %w", err))
	}

	// 检查是否需要自动压缩（在用户消息创建后，让用户先看到自己的消息）
	compactPerformed, err := a.checkAndPerformAutoCompactAfterUserMessage(ctx, sessionId)
	if err != nil {
		return a.err(fmt.Errorf("auto compact failed: %w", err))
	}

	// 如果执行了自动压缩，需要重新获取消息历史并重新创建用户消息
	if compactPerformed {
		msgs, err = a.messages.ListMessages(ctx, sessionId)
		if err != nil {
			return a.err(fmt.Errorf("failed to reload messages after auto compact: %w", err))
		}

		// 重新处理压缩消息
		ses, err = a.sessions.GetSession(ctx, sessionId)
		if err != nil {
			return a.err(fmt.Errorf("failed to get session after auto compact: %w", err))
		}
		if ses.SummaryMessageId != "" {
			summaryMsgIndex := -1
			for i, msg := range msgs {
				if msg.Id == ses.SummaryMessageId {
					summaryMsgIndex = i
					break
				}
			}
			if summaryMsgIndex != -1 {
				msgs = msgs[summaryMsgIndex:]
				msgs[0].Role = message.User
			}
		}

		// 关键修复：重新创建用户消息，确保ParentId正确指向压缩后的最后一条消息
		newParentId := ""
		if len(msgs) > 0 {
			newParentId = msgs[len(msgs)-1].Id
		}

		// 更新用户消息的ParentId
		userMsg.ParentId = newParentId
		err = a.messages.UpdateMessage(ctx, userMsg)
		if err != nil {
			return a.err(fmt.Errorf("failed to update user message parent after auto compact: %w", err))
		}
	}

	// 改写消息，添加记忆和任务列表相关提示
	msgHistory := a.messageRefactor.Refactor(ctx, sessionId, append(msgs, userMsg))

	for {
		select {
		case <-ctx.Done():
			return a.err(ctx.Err())
		default:
		}
		if err := a.turnLimiter.Advance(); err != nil {
			msg := fmt.Sprintf("WARN: Max turns %d reached, use `qodercli -c -p \"continue\"` to continue this session", a.turnLimiter.Limit())
			return agent.Event{
				Type: agent.AgentEventTypeSystem,
				Message: &message.Message{
					SessionId: sessionId,
					Role:      message.Assistant,
					Parts:     []message.ContentPart{message.TextContent{Text: msg, Type: "text"}},
				},
				SessionId: sessionId,
				Done:      false,
			}
		}
		agentMessage, toolResults, err := a.streamAndHandleEvents(ctx, sessionId, msgHistory)
		if err != nil {
			if errors.Is(err, context.Canceled) {
				agentMessage.AddFinish(message.FinishReasonCanceled)
				agentMessage.Status = message.StatusCanceled
				a.messages.UpdateMessage(context.Background(), agentMessage)
				return a.err(agent.ErrRequestCancelled)
			}
			return a.err(fmt.Errorf("failed to process events: %w", err))
		}

		if a.config.Debug {
			seqId := (len(msgHistory) + 1) / 2
			toolResultFilepath := logging.WriteToolResultsJson(sessionId, seqId, toolResults)
			logging.Info("Result", "message", agentMessage.FinishReason(), "toolResults", "{}", "filepath", toolResultFilepath)
		} else {
			logging.Info("Result", "message", agentMessage.FinishReason(), "toolResults", toolResults)
		}
		if (agentMessage.FinishReason() == message.FinishReasonToolUse) && toolResults != nil {
			// We are not done, we need to respond with the tool response
			msgHistory = append(msgHistory, agentMessage, *toolResults)
			continue
		}

		// TODO hooks stop

		return agent.Event{
			Type:      agent.AgentEventTypeResult,
			Subtype:   "success",
			Message:   &agentMessage,
			SessionId: sessionId,
			Done:      true,
		}
	}
}

// 判断一个消息列是否应该生成标题，目前简单做，如果没有跟 Agent 产生交互就要生成 title
// 后续可以根据动态需求来生成
func (a *AgentRunner) shouldGenerateTitle(msgs []message.Message) bool {
	for _, msg := range msgs {
		if msg.Role == message.Assistant || msg.Role == message.Tool {
			return false
		}
	}

	return true
}

func (a *AgentRunner) chatRequest(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (string, error) {
	res, err := a.client.SendMessages(ctx, messages, tools)
	if err != nil {
		return "", err
	}
	if res == nil {
		return "", fmt.Errorf("received nil response from client")
	}
	return res.Content, nil
}

func (a *AgentRunner) streamAndHandleEvents(ctx context.Context, sessionId string, msgHistory []message.Message) (message.Message, *message.Message, error) {
	ctx = context.WithValue(ctx, tools.SessionIDContextKey, sessionId)

	// 创建主编码LLM调用trace
	tm := monitoring.NewTraceManager(sessionId)
	streamCtx, llmSpan := tm.StartCoderLLMCall(ctx, string(a.client.GetModel().Id), msgHistory)
	defer func() {
		llmSpan.RecordContent("provider", string(a.client.GetModel().Provider))
		llmSpan.End()
	}()

	eventChan := a.client.StreamResponse(streamCtx, msgHistory, a.tools)

	parentId := ""
	if len(msgHistory) > 0 {
		parentId = msgHistory[len(msgHistory)-1].Id
	}
	assistantMsg, err := a.messages.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.Assistant,
		Parts:    []message.ContentPart{},
		Model:    a.client.GetModel().Id,
		ParentId: parentId,
	})

	if err != nil {
		return assistantMsg, nil, fmt.Errorf("failed to create assistant message: %w", err)
	}

	ctx = context.WithValue(ctx, tools.MessageIDContextKey, assistantMsg.Id)

	for event := range eventChan {
		if processErr := a.processEvent(ctx, sessionId, &assistantMsg, event); processErr != nil {
			a.finishMessage(ctx, &assistantMsg, message.FinishReasonCanceled)
			llmSpan.RecordResult(false, processErr)
			return assistantMsg, nil, processErr
		}

		// 记录LLM调用完成的trace信息
		if event.Type == provider.EventComplete && event.Response != nil {
			llmSpan.RecordContent("final_response", event.Response.Content)
			llmSpan.RecordContent("finish_reason", string(event.Response.FinishReason))
			llmSpan.RecordContent("token_usage", event.Response.Usage)
			llmSpan.RecordResult(true, nil)
		}

		if ctx.Err() != nil {
			a.finishMessage(streamCtx, &assistantMsg, message.FinishReasonCanceled)
			llmSpan.RecordResult(false, ctx.Err())
			return assistantMsg, nil, ctx.Err()
		}
	}

	toolResults := make([]message.ToolResult, len(assistantMsg.ToolCalls()))
	toolCalls := assistantMsg.ToolCalls()

	// 创建工具批量执行trace
	var toolExecutionSpan *monitoring.TraceSpan
	if len(toolCalls) > 0 {
		_, toolExecutionSpan = tm.StartToolExecution(streamCtx, len(toolCalls))
		defer toolExecutionSpan.End()
	}

	successCount := 0
	for i, toolCall := range toolCalls {
		select {
		case <-ctx.Done():
			a.finishMessage(streamCtx, &assistantMsg, message.FinishReasonCanceled)
			for j := i; j < len(toolCalls); j++ {
				toolResults[j] = message.ToolResult{
					ToolCallId: toolCalls[j].Id,
					Content:    "Tool execution canceled by user",
					IsError:    true,
				}
			}
			goto out
		default:
			assistantMsg.Status = message.StatusToolCalling
			_ = a.messages.UpdateMessage(ctx, assistantMsg)
			var tool tools.BaseTool
			for _, availableTool := range a.tools {
				if availableTool.Info().Name == toolCall.Name {
					tool = availableTool
					break
				}
			}

			// Tool not found
			if tool == nil {
				toolResults[i] = message.ToolResult{
					ToolCallId: toolCall.Id,
					Content:    fmt.Sprintf("Tool not found: %s", toolCall.Name),
					IsError:    true,
				}
				continue
			}

			// TODO hooks 执行 PreToolUse Hook 操作
			//a.executePreToolUseHooks(sessionId, tool)

			// Create ToolExecutionContext for the tool
			ses, err := a.sessions.GetSession(ctx, sessionId)
			if err != nil {
				toolResults[i] = message.ToolResult{
					ToolCallId: toolCall.Id,
					Content:    fmt.Sprintf("Failed to get session: %v", err),
					IsError:    true,
				}
				continue
			}

			// 创建单个工具调用trace
			toolCtxWithSpan, toolCallSpan := tm.StartToolCall(streamCtx, toolCall.Name, toolCall.Input)

			toolCtx := tools.NewToolExecutionContextBuilder(toolCtxWithSpan).
				WithSessionID(sessionId).
				WithMessageID(assistantMsg.Id).
				WithWorkingDir(ses.WorkingDir).
				WithContextPaths(ses.ContextPaths).
				Build()

			call := tools.ToolCall{
				ChatRequest: a.chatRequest,
				Id:          toolCall.Id,
				Name:        toolCall.Name,
				Input:       toolCall.Input,
			}

			start := time.Now()
			toolResult, toolErr := tool.Run(toolCtx, call)
			duration := time.Since(start)

			// 记录工具调用结果
			toolCallSpan.RecordContent("output_content", toolResult.Content)
			toolCallSpan.RecordResult(toolErr == nil && !toolResult.IsError, toolErr)
			toolCallSpan.End()

			if toolErr == nil && !toolResult.IsError {
				successCount++
			}

			monitoring.RecordToolCalling(toolCtxWithSpan, sessionId, call, toolResult, duration, toolErr)

			// TODO hooks postToolUse

			if toolErr != nil {
				if errors.Is(toolErr, core.ErrorPermissionDenied) {
					toolResults[i] = message.ToolResult{
						ToolCallId: toolCall.Id,
						Content:    "Permission denied",
						IsError:    true,
					}
					for j := i + 1; j < len(toolCalls); j++ {
						toolResults[j] = message.ToolResult{
							ToolCallId: toolCalls[j].Id,
							Content:    "Tool execution canceled by user",
							IsError:    true,
						}
					}
					a.finishMessage(ctx, &assistantMsg, message.FinishReasonPermissionDenied)
					break
				}
			}
			toolResults[i] = message.ToolResult{
				ToolCallId: toolCall.Id,
				Content:    toolResult.Content,
				Metadata:   toolResult.Metadata,
				IsError:    toolResult.IsError,
			}
		}
	}
out:
	// 记录工具批量执行结果
	if toolExecutionSpan != nil {
		toolExecutionSpan.RecordContent("success_count", successCount)
		toolExecutionSpan.RecordContent("total_count", len(toolCalls))
		toolExecutionSpan.RecordResult(successCount > 0, nil)
	}
	if len(toolResults) == 0 {
		return assistantMsg, nil, nil
	}
	parts := make([]message.ContentPart, 0)
	for _, tr := range toolResults {
		parts = append(parts, tr)
	}
	msg, err := a.messages.CreateMessage(streamCtx, assistantMsg.SessionId, message.CreateMessageParams{
		Role:     message.Tool,
		Parts:    parts,
		ParentId: assistantMsg.Id,
	})
	if err != nil {
		return assistantMsg, nil, fmt.Errorf("failed to create cancelled tool message: %w", err)
	}

	return assistantMsg, &msg, err
}

func (a *AgentRunner) finishMessage(ctx context.Context, msg *message.Message, finishReason message.FinishReason) {
	msg.AddFinish(finishReason)
	msg.Status = message.StatusFinished
	_ = a.messages.UpdateMessage(ctx, *msg)
}

func (a *AgentRunner) processEvent(ctx context.Context, sessionID string, assistantMsg *message.Message, event provider.Event) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	switch event.Type {
	case provider.EventSending:
		assistantMsg.Status = message.StatusSending
		return a.messages.UpdateMessage(ctx, *assistantMsg)
	case provider.EventReceiving:
		assistantMsg.Status = message.StatusReceiving
		return a.messages.UpdateMessage(ctx, *assistantMsg)
	case provider.EventThinkingDelta:
		assistantMsg.AppendReasoningContent(event.Content)
		return a.messages.UpdateMessage(ctx, *assistantMsg)
	case provider.EventContentDelta:
		assistantMsg.AppendContent(event.Content)
		return a.messages.UpdateMessage(ctx, *assistantMsg)
	case provider.EventToolUseStart:
		assistantMsg.AddToolCall(*event.ToolCall)
		return a.messages.UpdateMessage(ctx, *assistantMsg)
	case provider.EventToolUseStop:
		assistantMsg.FinishToolCall(event.ToolCall.Id)
		return a.messages.UpdateMessage(ctx, *assistantMsg)
	case provider.EventError:
		if errors.Is(event.Error, context.Canceled) {
			logging.InfoPersist(fmt.Sprintf("Event processing canceled for session: %s", sessionID))
			return context.Canceled
		}
		logging.ErrorPersist(event.Error.Error())
		return event.Error
	case provider.EventComplete:
		assistantMsg.SetToolCalls(event.Response.ToolCalls)
		assistantMsg.AddFinish(event.Response.FinishReason)
		assistantMsg.Status = message.StatusFinished
		if event.Response.Signature != "" {
			assistantMsg.SetReasoningSignature(event.Response.Signature)
		}
		// 保存token用量信息到消息
		assistantMsg.SetUsage(message.TokenUsage{
			InputTokens:         event.Response.Usage.InputTokens,
			OutputTokens:        event.Response.Usage.OutputTokens,
			CacheCreationTokens: event.Response.Usage.CacheCreationTokens,
			CacheReadTokens:     event.Response.Usage.CacheReadTokens,
		})
		if err := a.messages.UpdateMessage(ctx, *assistantMsg); err != nil {
			return fmt.Errorf("failed to update message: %w", err)
		}
		// 先进行会话级持久化/统计
		if err := a.TrackUsage(ctx, sessionID, a.client.GetModel(), event.Response.Usage); err != nil {
			return err
		}
		return nil
	}

	return nil
}

// checkAndPerformAutoCompactAfterUserMessage 在用户消息创建后检查是否需要自动压缩
func (a *AgentRunner) checkAndPerformAutoCompactAfterUserMessage(ctx context.Context, sessionID string) (bool, error) {
	// 检查是否启用自动压缩功能
	if a.config.GetPreferences().AutoCompactEnabled != nil && !*a.config.GetPreferences().AutoCompactEnabled {
		return false, nil // 自动压缩功能已禁用
	}

	// 获取当前模型信息
	model := a.client.GetModel()
	if model.ContextWindow <= 0 {
		model.ContextWindow = 200000
	}

	// 获取当前会话上下文的token使用量
	session, err := a.sessions.GetSession(ctx, sessionID)
	if err != nil {
		logging.Error("Failed to get session for auto compact check", "session", sessionID, "error", err)
		return false, err
	}
	contextTokens := session.PromptTokens + session.CompletionTokens

	// 计算使用率
	usageRate := float64(contextTokens) / float64(model.ContextWindow)

	logging.Info("Token usage check",
		"session", sessionID,
		"context_tokens", contextTokens,
		"context_window", model.ContextWindow,
		"usage_rate_percent", usageRate*100)

	// 如果使用率超过92%，触发自动压缩
	if usageRate >= 0.92 {
		logging.Info("Triggering auto compact",
			"session", sessionID,
			"usage_rate_percent", usageRate*100)

		// 在trace中创建自动压缩span
		tm := monitoring.NewTraceManager(sessionID)
		compactCtx, compactSpan := tm.StartAutoCompact(ctx, usageRate, contextTokens, model.ContextWindow)
		defer compactSpan.End()

		// 发布自动压缩开始事件（作为请求处理的一个阶段）
		startEvent := agent.Event{
			Type:      "auto_compact_phase",
			SessionId: sessionID,
			Progress:  fmt.Sprintf("Context usage at %.1f%%, compacting context before processing...", usageRate*100),
		}
		a.Publish(pubsub.CreatedEvent, startEvent)

		// 执行自动压缩
		err = a.performAutoCompact(compactCtx, sessionID)
		if err != nil {
			compactSpan.RecordResult(false, err)
			errorEvent := agent.Event{
				Type:      "auto_compact_error",
				SessionId: sessionID,
				Error:     err,
				Progress:  "Auto-compact failed",
			}
			a.Publish(pubsub.CreatedEvent, errorEvent)
			return false, err
		}

		compactSpan.RecordResult(true, nil)

		// 发布自动压缩完成事件
		completeEvent := agent.Event{
			Type:      "auto_compact_phase",
			SessionId: sessionID,
			Progress:  "Context compacted, continuing with request...",
			Done:      true,
		}
		a.Publish(pubsub.CreatedEvent, completeEvent)

		return true, nil
	}

	return false, nil
}

// performAutoCompact 执行自动压缩，专门用于请求前置的自动压缩
func (a *AgentRunner) performAutoCompact(ctx context.Context, sessionID string) error {
	if a.summarizeClient == nil {
		return fmt.Errorf("summarize client not available")
	}

	// 使用内部方法进行自动压缩，跳过busy检查
	return a.SummarizeWithInstructionsInternal(ctx, sessionID, "", true)
}

// SummarizeWithInstructionsInternal 内部压缩方法，支持自动模式跳过busy检查
func (a *AgentRunner) SummarizeWithInstructionsInternal(ctx context.Context, sessionId string, instructions string, isAutoMode bool) error {
	if a.summarizeClient == nil {
		return fmt.Errorf("summarize client not available")
	}

	// 只有手动模式才检查session busy状态
	if !isAutoMode && a.IsSessionBusy(sessionId) {
		return agent.ErrSessionBusy
	}

	// TODO hooks PreCompact
	//a.hooks.GetPreCompactHookExecutor()

	summarizeCtx, cancel := context.WithCancel(ctx)
	a.activeRequests.Store(sessionId+"-summarize", cancel)

	// go func() {
	defer a.activeRequests.Delete(sessionId + "-summarize")
	defer cancel()

	// 根据模式发送不同的事件类型
	var eventType agent.EnvType
	if isAutoMode {
		eventType = "auto_compact_phase"
	} else {
		eventType = agent.AgentEventTypeSummarize
	}

	event := agent.Event{
		Type:     eventType,
		Progress: "Starting compression...",
	}

	a.Publish(pubsub.CreatedEvent, event)
	msgs, err := a.messages.ListMessages(summarizeCtx, sessionId)
	if err != nil {
		event = agent.Event{
			Type:  agent.AgentEventTypeError,
			Error: fmt.Errorf("failed to list messages: %w", err),
			Done:  true,
		}
		a.Publish(pubsub.CreatedEvent, event)
		return fmt.Errorf("failed to list messages: %w", err)
	}

	// 如果已经有压缩消息，只获取压缩消息之后的内容进行压缩
	ses, err := a.sessions.GetSession(summarizeCtx, sessionId)
	if err != nil {
		event = agent.Event{
			Type:  agent.AgentEventTypeError,
			Error: fmt.Errorf("failed to get session for compression: %w", err),
			Done:  true,
		}
		a.Publish(pubsub.CreatedEvent, event)
		return fmt.Errorf("failed to get session for compression: %w", err)
	}

	if ses.SummaryMessageId != "" {
		summaryMsgIndex := -1
		for i, msg := range msgs {
			if msg.Id == ses.SummaryMessageId {
				summaryMsgIndex = i
				break
			}
		}
		if summaryMsgIndex != -1 {
			msgs = msgs[summaryMsgIndex:]
			// 确保压缩消息作为用户消息包含在上下文中
			msgs[0].Role = message.User
		}
	}
	summarizeCtx = context.WithValue(summarizeCtx, tools.SessionIDContextKey, sessionId)

	if len(msgs) == 0 {
		event = agent.Event{
			Type:  agent.AgentEventTypeError,
			Error: fmt.Errorf("no messages to compress"),
			Done:  true,
		}
		a.Publish(pubsub.CreatedEvent, event)
		return fmt.Errorf("no messages to compress")
	}

	event = agent.Event{
		Type:     eventType,
		Progress: "Analyzing conversation...",
	}
	a.Publish(pubsub.CreatedEvent, event)

	// Use user prompt template
	userPromptText := prompt.SummarizerUserPrompt(instructions)

	// Build user message with user prompt
	promptMsg := message.Message{
		Role: message.User,
		Parts: []message.ContentPart{
			message.TextContent{Text: userPromptText, Type: "text"},
		},
	}

	// 避免slice底层数组共享导致的数据污染，使用深拷贝
	msgsWithPrompt := make([]message.Message, len(msgs)+1)
	copy(msgsWithPrompt, msgs)
	msgsWithPrompt[len(msgs)] = promptMsg

	// 改写消息
	msgsWithPrompt = a.messageRefactor.Refactor(summarizeCtx, sessionId, msgsWithPrompt)

	// Add Read tool to the tools list
	availableTools := []tools.BaseTool{
		internaltools.NewReadTool(),
	}

	event = agent.Event{
		Type:     eventType,
		Progress: "Generating summary...",
	}

	a.Publish(pubsub.CreatedEvent, event)

	// 创建assistant消息用于显示流式内容
	parentId := ""
	if len(msgsWithPrompt) > 0 {
		parentId = msgsWithPrompt[len(msgsWithPrompt)-1].Id
	}
	assistantMsg, err := a.messages.CreateMessage(summarizeCtx, sessionId, message.CreateMessageParams{
		Role:     message.Assistant,
		Parts:    []message.ContentPart{},
		Model:    a.summarizeClient.GetModel().Id,
		ParentId: parentId,
	})
	if err != nil {
		event = agent.Event{
			Type:  agent.AgentEventTypeError,
			Error: fmt.Errorf("failed to create assistant message: %w", err),
			Done:  true,
		}
		a.Publish(pubsub.CreatedEvent, event)
		return fmt.Errorf("failed to create assistant message: %w", err)
	}

	// 使用流式调用
	eventChan := a.summarizeClient.StreamResponse(summarizeCtx, msgsWithPrompt, availableTools)

	var rawSummary string
	var finalResponse *provider.Response
	for providerEvent := range eventChan {
		// 处理流式事件，更新assistant消息
		if err := a.processEvent(summarizeCtx, sessionId, &assistantMsg, providerEvent); err != nil {
			if err == context.Canceled {
				event = agent.Event{
					Type:  agent.AgentEventTypeError,
					Error: fmt.Errorf("compression was cancelled"),
					Done:  true,
				}
				a.Publish(pubsub.CreatedEvent, event)
				return fmt.Errorf("compression was cancelled")
			}
			logging.Error("Error processing compress event", "error", err)
		}

		// 在完成时获取内容和响应信息
		if providerEvent.Type == provider.EventComplete {
			if providerEvent.Response != nil {
				rawSummary = strings.TrimSpace(providerEvent.Response.Content)
				finalResponse = providerEvent.Response
			}
			break
		}
	}

	if rawSummary == "" || finalResponse == nil {
		event = agent.Event{
			Type:  agent.AgentEventTypeError,
			Error: fmt.Errorf("empty summary returned or no response received"),
			Done:  true,
		}
		a.Publish(pubsub.CreatedEvent, event)
		return fmt.Errorf("empty summary returned or no response received")
	}

	summary := a.formatSummaryContent(rawSummary, instructions)

	// 更新assistant消息为格式化后的摘要内容
	textUpdated := false
	for i, part := range assistantMsg.Parts {
		if textContent, ok := part.(message.TextContent); ok {
			assistantMsg.Parts[i] = message.TextContent{Text: summary, Type: textContent.Type}
			textUpdated = true
			break
		}
	}
	// 如果没有找到文本内容，添加一个
	if !textUpdated {
		assistantMsg.Parts = append([]message.ContentPart{
			message.TextContent{Text: summary, Type: "text"},
		}, assistantMsg.Parts...)
	}
	err = a.messages.UpdateMessage(summarizeCtx, assistantMsg)
	if err != nil {
		event = agent.Event{
			Type:  agent.AgentEventTypeError,
			Error: fmt.Errorf("failed to update summary message: %w", err),
			Done:  true,
		}
		a.Publish(pubsub.CreatedEvent, event)
		return fmt.Errorf("failed to update summary message: %w", err)
	}

	event = agent.Event{
		Type:     eventType,
		Progress: "Context compressed successfully",
		Done:     true,
	}

	// Update session summary reference
	ses.SummaryMessageId = assistantMsg.Id
	if err := a.sessions.SaveSession(summarizeCtx, ses); err != nil {
		logging.Error("Failed to update session summary reference", "error", err)
	}

	// Track usage for compression
	if err := a.TrackUsage(summarizeCtx, sessionId, a.summarizeClient.GetModel(), finalResponse.Usage); err != nil {
		logging.Error("Failed to track compression usage", "error", err)
	}

	a.Publish(pubsub.CreatedEvent, event)
	// }()

	return nil
}

func (a *AgentRunner) TrackUsage(ctx context.Context, sessionID string, model models.Model, usage provider.TokenUsage) error {
	cost := model.CostPer1MInCached/1e6*float64(usage.CacheCreationTokens) +
		model.CostPer1MOutCached/1e6*float64(usage.CacheReadTokens) +
		model.CostPer1MIn/1e6*float64(usage.InputTokens) +
		model.CostPer1MOut/1e6*float64(usage.OutputTokens)

	promptTokens := usage.InputTokens + usage.CacheCreationTokens
	completionTokens := usage.OutputTokens + usage.CacheReadTokens

	return a.sessions.UpdateSessionTokens(ctx, sessionID, promptTokens, completionTokens, cost)
}

func (a *AgentRunner) UpdateModel(agentName config.AgentName, modelID models.ModelId) error {
	if a.IsBusy() {
		return fmt.Errorf("cannot change model while processing requests")
	}

	if err := a.config.UpdateAgentModel(agentName, modelID); err != nil {
		return fmt.Errorf("failed to update config: %w", err)
	}

	client, err := a.createLlmClient(agentName)
	if err != nil {
		return fmt.Errorf("failed to create client for model %s: %w", modelID, err)
	}

	a.client = client

	return nil
}

func (a *AgentRunner) Summarize(ctx context.Context, sessionId string) error {
	return a.SummarizeWithInstructions(ctx, sessionId, "")
}

func (a *AgentRunner) SummarizeWithInstructions(ctx context.Context, sessionId string, instructions string) error {
	return a.SummarizeWithInstructionsInternal(ctx, sessionId, instructions, false)
}

// formatSummaryContent 格式化压缩内容，将 XML 标签转换为可读格式
func (a *AgentRunner) formatSummaryContent(content string, instructions string) string {
	prefix := "===================== Previous Conversation Compacted =====================\n\n"
	prefix += "This session is being continued from a previous conversation that ran out of context. The conversation is summarized below:"
	if strings.TrimSpace(instructions) != "" {
		prefix += "\n\nFocus/Instructions: " + strings.TrimSpace(instructions)
	}
	prefix += "\n\n"

	// 使用正则表达式匹配并替换 <analysis> 标签
	analysisRegex := regexp.MustCompile(`(?s)<analysis>\s*(.*?)\s*</analysis>`)
	analysisMatches := analysisRegex.FindStringSubmatch(content)

	// 使用正则表达式匹配并替换 <summary> 标签
	summaryRegex := regexp.MustCompile(`(?s)<summary>\s*(.*?)\s*</summary>`)
	summaryMatches := summaryRegex.FindStringSubmatch(content)

	formattedContent := ""

	if len(analysisMatches) > 1 {
		analysisContent := analysisMatches[1]
		if strings.TrimSpace(analysisContent) != "" {
			formattedContent += "Analysis:\n\n" + analysisContent + "\n\n"
		}
	}

	if len(summaryMatches) > 1 {
		summaryContent := summaryMatches[1]
		if strings.TrimSpace(summaryContent) != "" {
			formattedContent += "Summary:\n\n" + summaryContent
		}
	}

	// 如果没有找到标签，使用原始内容
	if formattedContent == "" {
		formattedContent = content
	}

	return prefix + formattedContent
}

func (a *AgentRunner) getModelOfAgent(agentName config.AgentName) (*models.Model, error) {
	agentConfig, ok := a.config.GetAgent(agentName)
	if !ok {
		return nil, fmt.Errorf("agent %s not found", agentName)
	}
	model, ok := models.SupportedModels[agentConfig.Model]
	if !ok {
		return nil, fmt.Errorf("model %s not supported", agentConfig.Model)
	}

	return &model, nil
}

func (a *AgentRunner) getSystemPrompt(agentName config.AgentName, provider models.ModelProvider) string {
	var systemPrompt string
	if len(a.config.SystemPrompt) > 0 {
		// 优先使用命令行传入的自定义系统提示词
		systemPrompt = a.config.SystemPrompt
	} else if a.outputStyles != nil {
		// 主Agent可能会设置OutputStyle
		name := a.config.GetOutputStyle()
		if len(name) > 0 {
			style, err := a.outputStyles.Get(name)
			if err == nil {
				systemPrompt = style.Prompt
			}
		}
	}
	return prompt.GetAgentPrompt(agentName, provider, systemPrompt, a.workingDir)
}

func (a *AgentRunner) createLlmClient(agentName config.AgentName) (provider.Client, error) {
	model, err := a.getModelOfAgent(agentName)
	if err != nil {
		return nil, err
	}

	providerCfg, found := a.config.GetProvider(model.Provider)
	if !found {
		return nil, fmt.Errorf("client %s not supported", model.Provider)
	}
	if providerCfg.Disabled {
		return nil, fmt.Errorf("client %s is not enabled", model.Provider)
	}

	agentConfig, _ := a.config.GetAgent(agentName)
	maxTokens := model.DefaultMaxTokens
	if agentConfig.MaxTokens > 0 {
		maxTokens = agentConfig.MaxTokens
	}

	opts := []provider.ClientOption{
		provider.WithAPIKey(providerCfg.ApiKey),
		provider.WithModel(*model),
		provider.WithSystemMessage(a.getSystemPrompt(agentName, model.Provider)),
		provider.WithMaxTokens(maxTokens),
		provider.WithDebug(a.config.Debug),
	}

	client, err := a.providers.NewClient(
		model.Provider,
		opts...,
	)

	if err != nil {
		return nil, fmt.Errorf("could not create client: %v", err)
	}

	return client, nil
}

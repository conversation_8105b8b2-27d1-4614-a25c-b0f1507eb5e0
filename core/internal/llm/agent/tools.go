package agent

import (
	"context"
	"github.com/qoder-ai/qodercli/core/internal/llm"

	"github.com/qoder-ai/qodercli/core/internal/llm/tools"
	coreTools "github.com/qoder-ai/qodercli/core/llm/tools"
)

func CoderAgentTools(ctx context.Context, app llm.AppSupport, tl TurnLimiter) []coreTools.BaseTool {
	mcpTools := app.ListMcpTools(ctx)
	if tl == nil {
		// No Limit
		tl = NewTurnLimiter(0)
	}
	return append(
		[]coreTools.BaseTool{
			//tools.NewWebSearchTool(), // 依赖ClaudeAPI标准实现，暂时无法使用
			tools.NewBashTool(app, app.GetShellService()),
			tools.NewBashKillTool(app, app.GetShellService()),
			tools.NewBashOutputTool(app, app.GetShellService()),
			tools.NewWebFetchTool(app),
			tools.NewEditTool(app, app.GetHistoryService()),
			tools.NewMultiEditTool(app, app.GetHistoryService()),
			tools.NewGlobTool(),
			tools.NewGrepTool(),
			tools.NewLsTool(),
			tools.NewReadTool(),
			tools.NewWriteTool(app, app.GetHistoryService()),
			tools.NewTodoWriteTool(),
			tools.NewExitPlanModeTool(app),
			tools.NewSearchWebTool(),
			NewTaskTool(app, tl), // TODO 考虑会话隔离问题
		}, mcpTools...,
	)
}

package agent

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/monitoring"
)

type MCPClient interface {
	Initialize(ctx context.Context, request mcp.InitializeRequest) (*mcp.InitializeResult, error)
	ListTools(ctx context.Context, request mcp.ListToolsRequest) (*mcp.ListToolsResult, error)
	CallTool(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)
	Close() error
}

type mcpTool struct {
	mcpName     string
	tool        mcp.Tool
	cli         MCPClient
	permissions llm.PermissionTrigger
}

func NewMcpTool(name string, tool mcp.Tool, cli MCPClient, permissions llm.PermissionTrigger) tools.BaseTool {
	return &mcpTool{
		mcpName:     name,
		tool:        tool,
		cli:         cli,
		permissions: permissions,
	}
}

func (b *mcpTool) Info() tools.ToolInfo {
	required := b.tool.InputSchema.Required
	if required == nil {
		required = make([]string, 0)
	}
	return tools.ToolInfo{
		Name:        fmt.Sprintf("mcp__%s__%s", b.mcpName, b.tool.Name),
		Description: b.tool.Description,
		Parameters:  b.tool.InputSchema.Properties,
		Required:    required,
	}
}

func (b *mcpTool) Run(ctx tools.ToolExecutionContext, params tools.ToolCall) (tools.ToolResponse, error) {
	sessionID := ctx.GetSessionId()
	messageID := ctx.GetMessageId()
	if sessionID == "" || messageID == "" {
		return tools.ToolResponse{}, fmt.Errorf("session Id and message Id are required for creating a new file")
	}
	permissionDescription := fmt.Sprintf("execute %s with the following parameters: %s", b.Info().Name, params.Input)
	p := b.permissions.CreateRequestWithContext(ctx,
		core.CreatePermissionRequest{
			SessionId:   sessionID,
			Path:        ctx.GetWorkingDir(),
			ToolName:    b.Info().Name,
			Action:      "execute",
			Description: permissionDescription,
			Params:      params.Input,
		},
	)
	if !p {
		return tools.NewTextErrorResponse("permission denied"), nil
	}

	return b.runTool(ctx, params.Input)
}

func (b *mcpTool) runTool(ctx context.Context, input string) (tools.ToolResponse, error) {
	// 获取sessionId用于trace
	sessionId := ""
	if sid, ok := ctx.Value(tools.SessionIDContextKey).(string); ok {
		sessionId = sid
	}

	toolRequest := mcp.CallToolRequest{}
	toolRequest.Params.Name = b.tool.Name
	var args map[string]any
	if input == "" {
		args = make(map[string]any)
	} else {
		if err := json.Unmarshal([]byte(input), &args); err != nil {
			return tools.NewTextErrorResponse(fmt.Sprintf("error parsing parameters: %s", err)), nil
		}
	}

	toolRequest.Params.Arguments = args

	// 创建MCP外部调用trace
	var mcpSpan *monitoring.TraceSpan
	if sessionId != "" {
		tm := monitoring.NewTraceManager(sessionId)
		mcpCtx, span := tm.StartExternalNetworkCall(ctx, "mcp_call", fmt.Sprintf("mcp://%s", b.mcpName))
		mcpSpan = span
		mcpSpan.RecordContent("mcp.server_name", b.mcpName)
		mcpSpan.RecordContent("mcp.tool_name", b.tool.Name)
		mcpSpan.RecordContent("mcp.request_params", args)
		ctx = mcpCtx
	}

	result, err := b.cli.CallTool(ctx, toolRequest)

	if mcpSpan != nil {
		if err == nil {
			mcpSpan.RecordContent("mcp.response_content", result.Content)
			mcpSpan.RecordResult(true, nil)
		} else {
			mcpSpan.RecordResult(false, err)
		}
		mcpSpan.End()
	}

	if err != nil {
		return tools.NewTextErrorResponse(err.Error()), nil
	}

	output := ""
	for _, v := range result.Content {
		if v, ok := v.(mcp.TextContent); ok {
			output = v.Text
		} else {
			output = fmt.Sprintf("%v", v)
		}
	}

	return tools.NewTextResponse(output), nil
}

package agent

import (
	"testing"

	"github.com/qoder-ai/qodercli/core/config"
)

// Basic test to ensure the agent package compiles
func TestAgentPackage(t *testing.T) {
	// Test that we can create a config service
	cfg, err := config.NewService()
	if err != nil {
		t.<PERSON><PERSON>rf("Expected no error creating config service, got: %v", err)
	}
	
	if cfg == nil {
		t.Error("Expected config service to be non-nil")
	}
	
	// Test that agent names are defined
	if config.AgentCoder == "" {
		t.<PERSON>rror("Expected AgentCoder to be defined")
	}
}
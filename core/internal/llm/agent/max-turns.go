package agent

import (
	"fmt"
	"sync"
)

// ErrMaxTurnsReached 当尝试推进但已达到最大轮次数时返回
var ErrMaxTurnsReached = fmt.Errorf("max turns reached")

// TurnLimiter 用于在代理对话循环中限制最大可执行轮次
// 典型用法：在每次循环开始处调用 Advance()，当达到上限时返回错误
type TurnLimiter interface {
	// Advance 推进到下一轮；若超过上限返回错误
	Advance() error
	// Reset 重置已用轮次数
	Reset()
	// SetLimit 运行期更新最大轮次数（<=0 表示不限制）
	SetLimit(max int)
	// Limit 返回最大轮次数（<=0 表示不限制）
	Limit() int
	// Count 返回当前已用轮次数
	Count() int
	// Remaining 返回剩余可用轮次数（无限制时返回 -1）
	Remaining() int
}

type defaultTurnLimiter struct {
	mu    sync.Mutex
	max   int
	count int
}

// NewTurnLimiter 创建一个默认的轮次限制器
// max <= 0 表示不限制
func NewTurnLimiter(max int) TurnLimiter {
	return &defaultTurnLimiter{max: max, count: 0}
}

func (d *defaultTurnLimiter) Advance() error {
	d.mu.Lock()
	defer d.mu.Unlock()
	// 不限制
	if d.max <= 0 {
		d.count += 1
		return nil
	}
	// 达到/超过上限则报错（不再增加计数）
	if d.count >= d.max {
		return fmt.Errorf("%w: max turns %d reached", ErrMaxTurnsReached, d.max)
	}
	d.count += 1
	return nil
}

func (d *defaultTurnLimiter) Reset() {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.count = 0
}

func (d *defaultTurnLimiter) SetLimit(max int) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.max = max
}

func (d *defaultTurnLimiter) Limit() int {
	d.mu.Lock()
	defer d.mu.Unlock()
	return d.max
}

func (d *defaultTurnLimiter) Count() int {
	d.mu.Lock()
	defer d.mu.Unlock()
	return d.count
}

func (d *defaultTurnLimiter) Remaining() int {
	d.mu.Lock()
	defer d.mu.Unlock()
	if d.max <= 0 {
		return -1
	}
	remain := d.max - d.count
	if remain < 0 {
		return 0
	}
	return remain
}

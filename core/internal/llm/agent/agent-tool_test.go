package agent

import (
	"context"
	"testing"
	"time"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/message"
)

// Additional mock implementations for comprehensive testing
type MockSessionOperator struct {
	createTaskSessionFunc func(ctx context.Context, taskId, parentSessionId, title string) (core.Session, error)
	getSessionFunc        func(ctx context.Context, sessionId string) (core.Session, error)
	saveSessionFunc       func(ctx context.Context, session core.Session) error
	updateSessionTitleFunc func(ctx context.Context, sessionId string, title string) error
	updateSessionTokensFunc func(ctx context.Context, sessionId string, promptTokens int64, completionTokens int64, costDelta float64) error
}

func (m *MockSessionOperator) CreateTaskSession(ctx context.Context, taskId, parentSessionId, title string) (core.Session, error) {
	if m.createTaskSessionFunc != nil {
		return m.createTaskSessionFunc(ctx, taskId, parentSessionId, title)
	}
	return core.Session{Id: "task-session-id", Title: title}, nil
}

func (m *MockSessionOperator) GetSession(ctx context.Context, sessionId string) (core.Session, error) {
	if m.getSessionFunc != nil {
		return m.getSessionFunc(ctx, sessionId)
	}
	return core.Session{Id: sessionId, Cost: 100}, nil
}

func (m *MockSessionOperator) SaveSession(ctx context.Context, session core.Session) error {
	if m.saveSessionFunc != nil {
		return m.saveSessionFunc(ctx, session)
	}
	return nil
}

func (m *MockSessionOperator) UpdateSessionTitle(ctx context.Context, sessionId string, title string) error {
	if m.updateSessionTitleFunc != nil {
		return m.updateSessionTitleFunc(ctx, sessionId, title)
	}
	return nil
}

func (m *MockSessionOperator) UpdateSessionTokens(ctx context.Context, sessionId string, promptTokens int64, completionTokens int64, costDelta float64) error {
	if m.updateSessionTokensFunc != nil {
		return m.updateSessionTokensFunc(ctx, sessionId, promptTokens, completionTokens, costDelta)
	}
	return nil
}

type MockTaskAgent struct {
	runFunc func(ctx context.Context, sessionId, prompt string) (<-chan agent.Event, error)
}

func (m *MockTaskAgent) Run(ctx context.Context, sessionId, prompt string) (<-chan agent.Event, error) {
	if m.runFunc != nil {
		return m.runFunc(ctx, sessionId, prompt)
	}
	ch := make(chan agent.Event, 1)
	ch <- agent.Event{
		Type: agent.AgentEventTypeAssistant,
		Message: &message.Message{
			Role: message.Assistant,
			Parts: []message.ContentPart{
				message.TextContent{Type: "text", Text: "Test response"},
			},
		},
	}
	close(ch)
	return ch, nil
}

type MockToolExecutionContext struct {
	context.Context
	sessionId    string
	messageId    string
	workingDir   string
	contextPaths []string
	config       map[string]interface{}
}

func (m *MockToolExecutionContext) GetSessionId() string {
	return m.sessionId
}

func (m *MockToolExecutionContext) GetMessageId() string {
	return m.messageId
}

func (m *MockToolExecutionContext) GetWorkingDir() string {
	return m.workingDir
}

func (m *MockToolExecutionContext) GetContextPaths() []string {
	return m.contextPaths
}

func (m *MockToolExecutionContext) GetStringConfig(key, defaultValue string) string {
	if val, ok := m.config[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return defaultValue
}

func (m *MockToolExecutionContext) GetBoolConfig(key string, defaultValue bool) bool {
	if val, ok := m.config[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return defaultValue
}

func (m *MockToolExecutionContext) GetIntConfig(key string, defaultValue int) int {
	if val, ok := m.config[key]; ok {
		if i, ok := val.(int); ok {
			return i
		}
	}
	return defaultValue
}

func (m *MockToolExecutionContext) SetConfig(key string, value interface{}) {
	if m.config == nil {
		m.config = make(map[string]interface{})
	}
	m.config[key] = value
}

func (m *MockToolExecutionContext) Deadline() (deadline time.Time, ok bool) {
	if m.Context != nil {
		return m.Context.Deadline()
	}
	return time.Time{}, false
}

func (m *MockToolExecutionContext) Done() <-chan struct{} {
	if m.Context != nil {
		return m.Context.Done()
	}
	return nil
}

func (m *MockToolExecutionContext) Err() error {
	if m.Context != nil {
		return m.Context.Err()
	}
	return nil
}

func (m *MockToolExecutionContext) Value(key interface{}) interface{} {
	if m.Context != nil {
		return m.Context.Value(key)
	}
	return nil
}

// Basic test to ensure the mock implementations work
func TestMockImplementations(t *testing.T) {
	// Test MockSessionOperator
	mockSessions := &MockSessionOperator{}
	session, err := mockSessions.CreateTaskSession(context.Background(), "task-1", "parent-1", "Test Task")
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if session.Id != "task-session-id" {
		t.Errorf("Expected session ID 'task-session-id', got: %s", session.Id)
	}

	// Test MockTaskAgent
	mockAgent := &MockTaskAgent{}
	eventCh, err := mockAgent.Run(context.Background(), "session-1", "test prompt")
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	
	select {
	case event := <-eventCh:
		if event.Type != agent.AgentEventTypeAssistant {
			t.Errorf("Expected event type %s, got: %s", agent.AgentEventTypeAssistant, event.Type)
		}
	case <-time.After(time.Second):
		t.Error("Expected to receive an event")
	}

	// Test MockToolExecutionContext
	ctx := &MockToolExecutionContext{
		Context:   context.Background(),
		sessionId: "test-session",
		messageId: "test-message",
		workingDir: "/test/dir",
		config:    make(map[string]interface{}),
	}
	
	if ctx.GetSessionId() != "test-session" {
		t.Errorf("Expected session ID 'test-session', got: %s", ctx.GetSessionId())
	}
	
	ctx.SetConfig("test-key", "test-value")
	if ctx.GetStringConfig("test-key", "default") != "test-value" {
		t.Errorf("Expected 'test-value', got: %s", ctx.GetStringConfig("test-key", "default"))
	}
}
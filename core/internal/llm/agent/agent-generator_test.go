package agent

import (
	"context"
	"encoding/json"
	"errors"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/llm/provider"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/message"
)

// Mock implementations
type MockMessageOperator struct {
	createMessageFunc           func(ctx context.Context, sessionId string, params message.CreateMessageParams) (message.Message, error)
	updateMessageFunc           func(ctx context.Context, msg message.Message) error
	listMessagesFunc            func(ctx context.Context, sessionId string) ([]message.Message, error)
	getLastMessageOfSessionFunc func(ctx context.Context, sessionId string) (*message.Message, error)
}

func (m *MockMessageOperator) CreateMessage(ctx context.Context, sessionId string, params message.CreateMessageParams) (message.Message, error) {
	if m.createMessageFunc != nil {
		return m.createMessageFunc(ctx, sessionId, params)
	}
	return message.Message{Id: "test-msg-id"}, nil
}

func (m *MockMessageOperator) UpdateMessage(ctx context.Context, msg message.Message) error {
	if m.updateMessageFunc != nil {
		return m.updateMessageFunc(ctx, msg)
	}
	return nil
}

func (m *MockMessageOperator) ListMessages(ctx context.Context, sessionId string) ([]message.Message, error) {
	if m.listMessagesFunc != nil {
		return m.listMessagesFunc(ctx, sessionId)
	}
	return []message.Message{}, nil
}

func (m *MockMessageOperator) GetLastMessageOfSession(ctx context.Context, sessionId string) (*message.Message, error) {
	if m.getLastMessageOfSessionFunc != nil {
		return m.getLastMessageOfSessionFunc(ctx, sessionId)
	}
	return nil, nil
}

type MockSubAgentService struct {
	getFunc    func(name string) (*agent.SubAgent, error)
	listFunc   func() ([]agent.SubAgent, error)
	saveFunc   func(info agent.SubAgent) error
	deleteFunc func(name string) error
}

func (m *MockSubAgentService) Get(name string) (*agent.SubAgent, error) {
	if m.getFunc != nil {
		return m.getFunc(name)
	}
	return nil, nil
}

func (m *MockSubAgentService) List() ([]agent.SubAgent, error) {
	if m.listFunc != nil {
		return m.listFunc()
	}
	return []agent.SubAgent{}, nil
}

func (m *MockSubAgentService) Save(info agent.SubAgent) error {
	if m.saveFunc != nil {
		return m.saveFunc(info)
	}
	return nil
}

func (m *MockSubAgentService) Delete(name string) error {
	if m.deleteFunc != nil {
		return m.deleteFunc(name)
	}
	return nil
}

type MockProviderService struct {
	newClientFunc func(providerName models.ModelProvider, opts ...provider.ClientOption) (provider.Client, error)
}

func (m *MockProviderService) NewClient(providerName models.ModelProvider, opts ...provider.ClientOption) (provider.Client, error) {
	if m.newClientFunc != nil {
		return m.newClientFunc(providerName, opts...)
	}
	return &MockClient{}, nil
}

type MockClient struct {
	streamResponseFunc func(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan provider.Event
	sendMessagesFunc   func(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (*provider.Response, error)
	getModelFunc       func() models.Model
}

func (m *MockClient) StreamResponse(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan provider.Event {
	if m.streamResponseFunc != nil {
		return m.streamResponseFunc(ctx, messages, tools)
	}
	ch := make(chan provider.Event)
	close(ch)
	return ch
}

func (m *MockClient) SendMessages(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (*provider.Response, error) {
	if m.sendMessagesFunc != nil {
		return m.sendMessagesFunc(ctx, messages, tools)
	}
	return &provider.Response{}, nil
}

func (m *MockClient) GetModel() models.Model {
	if m.getModelFunc != nil {
		return m.getModelFunc()
	}
	return models.Model{Id: "test-model"}
}

type MockAgentRunner struct {
	messages        *MockMessageOperator
	config          *config.Service
	providers       *MockProviderService
	agents          *MockSubAgentService
	messageRefactor *MockMessageRefactor
	workingDir      string
	
	activateFunc           func(sessionId string, cancel context.CancelFunc)
	deactivateFunc         func(sessionId string)
	publishStartEventFunc  func(sessionId string, tools []interface{}, modelId string)
	getModelOfAgentFunc    func(agentType string) (models.Model, error)
	trackUsageFunc         func(ctx context.Context, sessionId string, model models.Model, usage provider.TokenUsage) error
}

func (m *MockAgentRunner) Activate(sessionId string, cancel context.CancelFunc) {
	if m.activateFunc != nil {
		m.activateFunc(sessionId, cancel)
	}
}

func (m *MockAgentRunner) Deactivate(sessionId string) {
	if m.deactivateFunc != nil {
		m.deactivateFunc(sessionId)
	}
}

func (m *MockAgentRunner) PublishStartEvent(sessionId string, tools []interface{}, modelId string) {
	if m.publishStartEventFunc != nil {
		m.publishStartEventFunc(sessionId, tools, modelId)
	}
}

func (m *MockAgentRunner) getModelOfAgent(agentType string) (models.Model, error) {
	if m.getModelOfAgentFunc != nil {
		return m.getModelOfAgentFunc(agentType)
	}
	return models.Model{Id: "test-model", Provider: "test-provider"}, nil
}

func (m *MockAgentRunner) TrackUsage(ctx context.Context, sessionId string, model models.Model, usage provider.TokenUsage) error {
	if m.trackUsageFunc != nil {
		return m.trackUsageFunc(ctx, sessionId, model, usage)
	}
	return nil
}

type MockMessageRefactor struct {
	refactorFunc func(ctx context.Context, sessionId string, messages []message.Message) []message.Message
}

func (m *MockMessageRefactor) Refactor(ctx context.Context, sessionId string, messages []message.Message) []message.Message {
	if m.refactorFunc != nil {
		return m.refactorFunc(ctx, sessionId, messages)
	}
	return messages
}

func TestNewAgentGenerator(t *testing.T) {
	// Since AgentRunner is a complex struct that's hard to mock,
	// we'll just test that NewAgentGenerator doesn't panic with a nil input
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected NewAgentGenerator to panic with nil input")
		}
	}()
	
	NewAgentGenerator(nil)
}

func TestAgentGenerator_getClient(t *testing.T) {
	// Test that getClient method exists and can be called
	// Since this requires complex mocking of AgentRunner, we'll just test basic functionality
	generator := &AgentGenerator{}
	
	// This should panic or return an error since no runner is set
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected panic when calling getClient with no runner")
		}
	}()
	
	generator.getClient()
}

func TestAgentGenerator_createUserMessage(t *testing.T) {
	// Test basic functionality with a mock message operator
	mockMsgOp := &MockMessageOperator{}
	mockMsgOp.getLastMessageOfSessionFunc = func(ctx context.Context, sessionId string) (*message.Message, error) {
		return nil, nil
	}
	mockMsgOp.createMessageFunc = func(ctx context.Context, sessionId string, params message.CreateMessageParams) (message.Message, error) {
		return message.Message{Id: "new-msg-id"}, nil
	}

	generator := &AgentGenerator{
		messages: mockMsgOp,
	}

	msg, err := generator.createUserMessage(context.Background(), "test-session", "Create a test agent", []string{})

	if err != nil {
		t.Errorf("Expected no error but got: %s", err.Error())
	}
	if msg.Id == "" {
		t.Error("Expected message ID to be non-empty")
	}
}

func TestAgentGenerator_processResult(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "agent-test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	tests := []struct {
		name        string
		msgContent  string
		location    agent.SubAgentLocation
		workingDir  string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "empty content",
			msgContent:  "",
			location:    agent.UserSubAgentLocation,
			expectError: true,
			errorMsg:    "invalid agent configuration generated",
		},
		{
			name:        "invalid JSON",
			msgContent:  "not json",
			location:    agent.UserSubAgentLocation,
			expectError: true,
			errorMsg:    "invalid agent configuration generated",
		},
		{
			name:        "missing required fields",
			msgContent:  `{"identifier": "test"}`,
			location:    agent.UserSubAgentLocation,
			expectError: true,
			errorMsg:    "invalid agent configuration generated",
		},
		{
			name: "successful processing - user location",
			msgContent: `{
				"identifier": "test-agent",
				"whenToUse": "For testing purposes",
				"systemPrompt": "You are a test agent"
			}`,
			location:    agent.UserSubAgentLocation,
			expectError: false,
		},
		{
			name: "successful processing - project location",
			msgContent: `{
				"identifier": "project-agent",
				"whenToUse": "For project testing",
				"systemPrompt": "You are a project test agent"
			}`,
			location:    agent.ProjectSubAgentLocation,
			workingDir:  tempDir,
			expectError: false,
		},
		{
			name: "unsupported location",
			msgContent: `{
				"identifier": "test-agent",
				"whenToUse": "For testing purposes",
				"systemPrompt": "You are a test agent"
			}`,
			location:    agent.SubAgentLocation("unsupported"),
			expectError: true,
			errorMsg:    "unsupported sub agent location",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			generator := &AgentGenerator{
				workingDir: tt.workingDir,
			}

			msg := &message.Message{
				Parts: []message.ContentPart{
					message.TextContent{
						Type: "text",
						Text: tt.msgContent,
					},
				},
			}

			result, err := generator.processResult(msg, tt.location)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
				}
				if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("Expected error to contain '%s', got: %s", tt.errorMsg, err.Error())
				}
				if result != "" {
					t.Error("Expected empty result on error")
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %s", err.Error())
				}
				if result == "" {
					t.Error("Expected non-empty result")
				}

				// Verify file was created for project location
				if tt.location == agent.ProjectSubAgentLocation {
					var data map[string]string
					json.Unmarshal([]byte(tt.msgContent), &data)
					fileName := data["identifier"] + ".md"
					expectedPath := filepath.Join(tt.workingDir, ".qoder", "agents", fileName)
					
					if _, err := os.Stat(expectedPath); os.IsNotExist(err) {
						t.Errorf("Expected file to be created at %s", expectedPath)
					} else {
						// Verify file content
						content, err := os.ReadFile(expectedPath)
						if err != nil {
							t.Errorf("Failed to read created file: %s", err.Error())
						} else {
							contentStr := string(content)
							if !strings.Contains(contentStr, data["identifier"]) {
								t.Error("File content should contain identifier")
							}
							if !strings.Contains(contentStr, data["whenToUse"]) {
								t.Error("File content should contain whenToUse")
							}
							if !strings.Contains(contentStr, data["systemPrompt"]) {
								t.Error("File content should contain systemPrompt")
							}
						}
					}
				}
			}
		})
	}
}

func TestAgentGenerator_finishMessage(t *testing.T) {
	mockMsgOp := &MockMessageOperator{}
	mockMsgOp.updateMessageFunc = func(ctx context.Context, msg message.Message) error {
		if msg.Status != message.StatusFinished {
			t.Error("Expected message status to be finished")
		}
		if msg.FinishReason() != message.FinishReasonError {
			t.Error("Expected finish reason to be error")
		}
		return nil
	}

	generator := &AgentGenerator{
		messages: mockMsgOp,
	}

	msg := &message.Message{
		Id: "test-msg",
	}

	generator.finishMessage(context.Background(), msg, message.FinishReasonError)

	if msg.Status != message.StatusFinished {
		t.Error("Expected message status to be finished")
	}
	if msg.FinishReason() != message.FinishReasonError {
		t.Error("Expected finish reason to be error")
	}
}

func TestAgentGenerator_err(t *testing.T) {
	generator := &AgentGenerator{}
	testErr := errors.New("test error")

	event := generator.err(testErr)

	if event.Type != agent.AgentEventTypeError {
		t.Error("Expected event type to be error")
	}
	if event.Error != testErr {
		t.Error("Expected event error to match test error")
	}
}
package converter

import (
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/qoder"
)

func parseContentPart(msg message.Message) []qoder.ChatMessagePart {
	var content []qoder.ChatMessagePart
	for _, part := range msg.Parts {
		if r, ok := part.(message.ReminderContent); ok {
			block := qoder.ChatMessagePart{
				Type: qoder.ChatMessagePartTypeText,
				Text: r.GetReminderContent(),
			}
			content = append(content, block)
		} else if c, ok := part.(message.TextContent); ok {
			block := qoder.ChatMessagePart{
				Type: qoder.ChatMessagePartTypeText,
				Text: c.String(),
			}
			content = append(content, block)
		} else if b, ok := part.(message.BinaryContent); ok {
			url := b.String(models.ProviderOpenAI)
			block := qoder.ChatMessagePart{
				Type: qoder.ChatMessagePartTypeImageURL,
				ImageURL: &qoder.ChatMessageImageURL{
					URL: url,
				},
			}
			content = append(content, block)
		}
	}

	return content
}

func ConvertQoderLlmMessage(msg message.Message) []*qoder.Message {
	switch msg.Role {
	case message.User:
		return []*qoder.Message{{
			Role:         qoder.RoleTypeUser,
			MultiContent: parseContentPart(msg),
		}}
	case message.Assistant:
		m := &qoder.Message{}
		m.Role = qoder.RoleTypeAssistant
		m.MultiContent = parseContentPart(msg)
		m.ReasoningContent = msg.ReasoningContent().String()
		m.ReasoningContentSignature = msg.ReasoningContent().Signature

		if len(msg.ToolCalls()) > 0 {
			for _, call := range msg.ToolCalls() {
				tc := qoder.ToolCall{}
				tc.Id = call.Id
				tc.Type = "function"
				tc.Function = qoder.FunctionCall{
					Name:      call.Name,
					Arguments: call.Input,
				}
				m.ToolCalls = append(m.ToolCalls, tc)
			}
		}

		return []*qoder.Message{m}
	case message.Tool:
		var messages []*qoder.Message
		for _, result := range msg.ToolResults() {
			m := qoder.Message{
				Role:       qoder.RoleTypeTool,
				Content:    result.Content,
				ToolCallId: result.ToolCallId,
			}
			messages = append(messages, &m)
		}
		return messages
	}
	return nil
}

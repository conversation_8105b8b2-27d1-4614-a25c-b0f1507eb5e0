package converter

import (
	"encoding/base64"
	"encoding/json"
	"github.com/anthropics/anthropic-sdk-go"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/version"
	"time"
)

type AnthropicStoredMessage struct {
	ParentUuid  *string                `json:"parentUuid"`
	IsSidechain bool                   `json:"isSidechain"`
	UserType    string                 `json:"userType"`
	Cwd         string                 `json:"cwd"`
	SessionId   string                 `json:"sessionId"`
	Version     string                 `json:"version"`
	GitBranch   string                 `json:"gitBranch"`
	Type        string                 `json:"type"`
	Message     anthropic.MessageParam `json:"message"`
	Uuid        string                 `json:"uuid"`
	Timestamp   string                 `json:"timestamp"`
	IsMeta      bool                   `json:"isMeta"`
}

func ConvertAnthropicStoredMessage(msg message.Message, wd, gitBranch string) *AnthropicStoredMessage {
	am := ConvertAnthropicLlmMessage(msg, false, false)
	if len(am) == 0 {
		return nil
	}

	var typ string
	if msg.Role == message.Assistant {
		typ = "assistant"
	} else {
		typ = "user"
	}

	sm := AnthropicStoredMessage{
		IsSidechain: false,
		UserType:    "external",
		Cwd:         wd,
		SessionId:   msg.SessionId,
		Version:     version.Version,
		GitBranch:   gitBranch,
		Type:        typ,
		Message:     am[0],
		Uuid:        msg.Id,
		ParentUuid:  &msg.ParentId,
		Timestamp:   time.UnixMilli(msg.CreatedAt).UTC().Format("2006-01-02T15:04:05.000Z"),
		IsMeta:      msg.IsMeta,
	}

	return &sm
}

func ConvertAnthropicLlmMessage(msg message.Message, needCache bool, withReminder bool) []anthropic.MessageParam {
	switch msg.Role {
	case message.User:
		var blocks []anthropic.ContentBlockParamUnion
		for _, part := range msg.Parts {
			if r, ok := part.(message.ReminderContent); ok {
				content := anthropic.NewTextBlock(r.GetReminderContent())
				blocks = append(blocks, content)
			} else if c, ok := part.(message.TextContent); ok {
				content := anthropic.NewTextBlock(c.String())
				if needCache {
					content.OfText.CacheControl = anthropic.CacheControlEphemeralParam{
						Type: "ephemeral",
					}
				}
				blocks = append(blocks, content)
			} else if b, ok := part.(message.BinaryContent); ok {
				if b.MIMEType == "application/pdf" {
					pdfParam := anthropic.Base64PDFSourceParam{
						Data:      base64.StdEncoding.EncodeToString(b.Data),
						MediaType: "application/pdf",
						Type:      "base64",
					}
					docBlock := anthropic.NewDocumentBlock(pdfParam)
					blocks = append(blocks, docBlock)
				} else {
					base64Image := b.String(models.ProviderAnthropic)
					imageBlock := anthropic.NewImageBlockBase64(b.MIMEType, base64Image)
					blocks = append(blocks, imageBlock)
				}
			}
		}

		return []anthropic.MessageParam{anthropic.NewUserMessage(blocks...)}
	case message.Assistant:
		var blocks []anthropic.ContentBlockParamUnion
		if msg.ReasoningContent().Thinking != "" {
			content := anthropic.NewThinkingBlock(msg.ReasoningContent().Signature, msg.ReasoningContent().Thinking)
			blocks = append(blocks, content)
		}

		for _, textContent := range msg.TextContents() {
			content := anthropic.NewTextBlock(textContent.String())
			if needCache {
				content.OfText.CacheControl = anthropic.CacheControlEphemeralParam{
					Type: "ephemeral",
				}
			}
			blocks = append(blocks, content)
		}

		for _, binaryContent := range msg.BinaryContent() {
			base64Image := binaryContent.String(models.ProviderAnthropic)
			imageBlock := anthropic.NewImageBlockBase64(binaryContent.MIMEType, base64Image)
			blocks = append(blocks, imageBlock)
		}

		for _, toolCall := range msg.ToolCalls() {
			var inputMap map[string]any

			err := json.Unmarshal([]byte(toolCall.Input), &inputMap)

			if err != nil {
				logging.Warn("Fail to unmarshal toolCall.Input", "input", toolCall.Input, "err", err)
				blocks = append(blocks, anthropic.NewToolUseBlock(toolCall.Id, make(map[string]string), toolCall.Name))
			} else {
				blocks = append(blocks, anthropic.NewToolUseBlock(toolCall.Id, inputMap, toolCall.Name))
			}
		}

		if len(blocks) == 0 {
			logging.Warn("There is a message without content, investigate, this should not happen")
			return nil
		}
		return []anthropic.MessageParam{anthropic.NewAssistantMessage(blocks...)}
	case message.Tool:
		results := make([]anthropic.ContentBlockParamUnion, len(msg.ToolResults()))
		for i, toolResult := range msg.ToolResults() {
			results[i] = anthropic.NewToolResultBlock(toolResult.ToolCallId, toolResult.Content, toolResult.IsError)
		}
		return []anthropic.MessageParam{anthropic.NewUserMessage(results...)}
	}

	return nil
}

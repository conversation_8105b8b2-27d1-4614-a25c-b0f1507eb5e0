package shell

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"github.com/qoder-ai/qodercli/core/utils"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// 常量定义
const (
	timeoutDuration = 10 * time.Second

	zshFunctionSaveCommand = `
echo "# Functions" >> "$SNAPSHOT_FILE"

# Force autoload all functions first
typeset -f > /dev/null 2>&1

# Now get user function names - filter system ones and write directly to file
typeset +f | grep -vE '^(_|__)' | while read func; do
typeset -f "$func" >> "$SNAPSHOT_FILE"
done

echo "# Shell Options" >> "$SNAPSHOT_FILE"
setopt | sed 's/^/setopt /' | head -n 1000 >> "$SNAPSHOT_FILE"
`
	notZshFunctionSaveCommand = `
echo "# Functions" >> "$SNAPSHOT_FILE"

# Force autoload all functions first
declare -f > /dev/null 2>&1

# Now get user function names - filter system ones and give the rest to eval in b64 encoding
declare -F | cut -d' ' -f3 | grep -vE '^(_|__)' | while read func; do
# Encode the function to base64, preserving all special characters
encoded_func=$(declare -f "$func" | base64 )
# Write the function definition to the snapshot
echo "eval \"\$(echo '$encoded_func' | base64 -d)\" > /dev/null 2>&1" >> "$SNAPSHOT_FILE"
done

echo "# Shell Options" >> "$SNAPSHOT_FILE"
shopt -p | head -n 1000 >> "$SNAPSHOT_FILE"
set -o | grep "on" | awk '{print "set -o " $1}' | head -n 1000 >> "$SNAPSHOT_FILE"
echo "shopt -s expand_aliases" >> "$SNAPSHOT_FILE"
`
	snapshotCommand = `SNAPSHOT_FILE="%s"
source "%s" < /dev/null

# First, create/clear the snapshot file
echo "# Snapshot file" >| "$SNAPSHOT_FILE"

# When this file is sourced, we first unalias to avoid conflicts
# This is necessary because aliases get "frozen" inside function definitions at definition time,
# which can cause unexpected behavior when functions use commands that conflict with aliases
echo "# Unset all aliases to avoid conflicts with functions" >> "$SNAPSHOT_FILE"
echo "unalias -a 2>/dev/null || true" >> "$SNAPSHOT_FILE"

%s

echo "# Aliases" >> "$SNAPSHOT_FILE"
# Filter out winpty aliases on Windows to avoid "stdin is not a tty" errors
# Git Bash automatically creates aliases like "alias node='winpty node.exe'" for
# programs that need Win32 Console in mintty, but winpty fails when there's no TTY
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
  alias | grep -v "='winpty " | sed 's/^alias //g' | sed 's/^/alias -- /' | head -n 1000 >> "$SNAPSHOT_FILE"
else
  alias | sed 's/^alias //g' | sed 's/^/alias -- /' | head -n 1000 >> "$SNAPSHOT_FILE"
fi

# Check if rg is available, if not create an alias to bundled ripgrep
echo "# Check for rg availability" >> "$SNAPSHOT_FILE"
echo "if ! command -v rg >/dev/null 2>&1; then" >> "$SNAPSHOT_FILE"
echo "  alias rg='%s'" >> "$SNAPSHOT_FILE"
echo "fi" >> "$SNAPSHOT_FILE"

# Add PATH to the file
echo "export PATH='%s'" >> "$SNAPSHOT_FILE"
`
)

// 获取用户主目录
func getUserHomeDir() string {
	home, err := os.UserHomeDir()
	if err != nil {
		return ""
	}
	return home
}

// 获取shell配置文件路径
func getProfilePath(shellPath string) string {
	home := getUserHomeDir()

	if strings.Contains(shellPath, "zsh") {
		return filepath.Join(home, ".zshrc")
	} else if strings.Contains(shellPath, "bash") {
		return filepath.Join(home, ".bashrc")
	} else {
		return filepath.Join(home, ".profile")
	}
}

// 生成随机字符串
func generateRandomString(length int) string {
	bytes := make([]byte, length)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)[:length]
}

// 构建包装命令
func buildWrappedCommand(shellPath, snapshotFile string) string {
	profilePath := getProfilePath(shellPath)
	var funcSaveCmd = zshFunctionSaveCommand
	if !strings.HasSuffix(profilePath, ".zshrc") {
		funcSaveCmd = notZshFunctionSaveCommand
	}

	rgPath, err := exec.LookPath("rg")
	if err != nil {
		rgPath = ""
	}
	envPath := os.Getenv("PATH")
	return fmt.Sprintf(snapshotCommand, snapshotFile, profilePath, funcSaveCmd, rgPath, envPath)
}

// 创建shell快照
func CreateSnapshot(shellPath string) (string, error) {
	// 确定shell类型
	var shellType string
	if strings.Contains(shellPath, "zsh") {
		shellType = "zsh"
	} else if strings.Contains(shellPath, "bash") {
		shellType = "bash"
	} else {
		shellType = "sh"
	}

	//logMessage(fmt.Sprintf("Creating shell snapshot for %s (%s)", shellType, shellPath))

	// 检查配置文件是否存在
	profilePath := getProfilePath(shellPath)
	if _, err := os.Stat(profilePath); err != nil {
		return "", nil
	}

	// 生成快照文件名
	timestamp := time.Now().UnixMilli()
	randomStr := generateRandomString(6)
	snapshotsDir := filepath.Join(utils.GetUserStorageDir(), "shell-snapshots")
	snapshotFile := filepath.Join(snapshotsDir, fmt.Sprintf("snapshot-%s-%d-%s.sh", shellType, timestamp, randomStr))

	// 创建目录
	if err := os.MkdirAll(snapshotsDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create snapshots directory: %w", err)
	}

	// 构建命令
	cmd := buildWrappedCommand(shellPath, snapshotFile)
	//logMessage(fmt.Sprintf("Creating snapshot at: %s", snapshotFile))

	// 设置环境变量
	env := os.Environ()
	env = append(env, fmt.Sprintf("SHELL=%s", shellPath))
	env = append(env, "GIT_EDITOR=true")

	// 创建上下文（带超时）
	ctx, cancel := context.WithTimeout(context.Background(), timeoutDuration)
	defer cancel()

	// 执行命令
	execCmd := exec.CommandContext(ctx, shellPath, "-c", "-l", cmd)
	execCmd.Env = env
	execCmd.Stdout = nil
	execCmd.Stderr = nil
	err := execCmd.Run()
	if err != nil {
		return "", fmt.Errorf("Failed to create shell snapshot: %s", err)
	}

	// 检查快照文件是否创建成功
	_, err = os.Stat(snapshotFile)
	if err != nil {
		return "", fmt.Errorf("Snapshot file disappeared before locking: %s", snapshotFile)
	}
	//logMessage(fmt.Sprintf("Shell snapshot created successfully (%d bytes)", fileInfo.Size()))

	// 尝试锁定文件
	//logMessage(fmt.Sprintf("Attempting to lock created snapshot file: %s", snapshotFile))
	//fileLock, Error := createFileLock(snapshotFile)
	//if Error != nil {
	//	logMessage(fmt.Sprintf("Failed to lock created snapshot file: %v", Error))
	//} else {
	//	logMessage("Successfully locked snapshot file")
	//
	//	// 在程序退出时释放锁
	//	go func() {
	//		defer func() {
	//			if Error := fileLock.Release(); Error != nil {
	//				logMessage(fmt.Sprintf("Error releasing snapshot lock: %v", Error))
	//			} else {
	//				logMessage("Released snapshot lock on shutdown")
	//			}
	//		}()
	//
	//		// 监听程序退出信号
	//		select {
	//		case <-ctx.Done():
	//			return
	//		}
	//	}()
	//}
	return snapshotFile, nil
}

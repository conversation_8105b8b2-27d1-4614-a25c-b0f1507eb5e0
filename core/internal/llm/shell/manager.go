package shell

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/qoder-ai/qodercli/core/llm/shell"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

const (
	CleanupInterval = 5 * time.Minute
	CleanupTimeout  = 30 * time.Minute
)

type Manager struct {
	pubsub.Broker[shell.Event]
	sync.Mutex
	shellPath     string
	counter       int
	shells        map[string]*PersistentShell
	cleanupTicker *time.Ticker
	done          chan bool
	snapshotPath  string
}

func NewManager() *Manager {
	shellPath := FindShell()

	m := &Manager{
		Broker:        *pubsub.NewBroker[shell.Event](), // 正确初始化嵌入的Broker
		shellPath:     shellPath,
		counter:       0,
		shells:        make(map[string]*PersistentShell),
		cleanupTicker: time.NewTicker(CleanupInterval),
		done:          make(chan bool),
	}

	// 每次启动只有一个Snapshot文件，忽略创建错误，可以考虑重建
	snapshotPath, _ := CreateSnapshot(shellPath)
	m.snapshotPath = snapshotPath

	// 启动自动清理goroutine
	go m.startCleanup()

	return m
}

func (m *Manager) Exec(ctx context.Context, workingDir string, command string, timeout time.Duration) (string, error) {
	var sh *PersistentShell
	m.Lock()
	m.counter += 1
	bashId := fmt.Sprintf("bash-%d", m.counter)
	sh = &PersistentShell{
		bashId:        bashId,
		shellPath:     m.shellPath,
		snapshotPath:  m.snapshotPath,
		cwd:           workingDir,
		eventCallback: m.Publish,
	}
	m.shells[bashId] = sh
	m.Unlock()
	return bashId, sh.Exec(ctx, command, timeout)
}

// startCleanup 启动自动清理goroutine
func (m *Manager) startCleanup() {
	for {
		select {
		case <-m.cleanupTicker.C:
			m.cleanup()
		case <-m.done:
			return
		}
	}
}

// cleanup 清理30分钟未使用的shell
func (m *Manager) cleanup() {
	m.Lock()
	defer m.Unlock()

	now := time.Now()
	for bashId, sh := range m.shells {
		// 如果shell正在执行，跳过
		if sh.IsExecuting() {
			continue
		}

		// 如果超过30分钟未使用，删除
		startTime := sh.GetStartTime()
		if startTime.Unix() != 0 && now.Sub(startTime) > CleanupTimeout {
			delete(m.shells, bashId)
		}
	}
}

func (m *Manager) ListShells() []shell.Shell {
	m.Lock()
	shells := make([]shell.Shell, 0, len(m.shells))
	for _, sh := range m.shells {
		shells = append(shells, sh)
	}
	m.Unlock()
	return shells
}

// GetShell 获取指定工作目录的shell
func (m *Manager) GetShell(bashId string) (shell.Shell, bool) {
	m.Lock()
	defer m.Unlock()

	s, ok := m.shells[bashId]
	return s, ok
}

// Close 停止manager，清理资源
func (m *Manager) Close() {
	close(m.done)
	m.cleanupTicker.Stop()
}

package shell

import (
	"fmt"
	"os"
	"syscall"
)

type FileLock struct {
	file *os.File
	path string
}

// 创建文件锁
func createFileLock(filePath string) (*FileLock, error) {
	// 创建或打开文件
	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_RDWR, 0666)
	if err != nil {
		return nil, fmt.Errorf("failed to open file for locking: %w", err)
	}

	// 尝试获取独占锁
	err = syscall.Flock(int(file.Fd()), syscall.LOCK_EX|syscall.LOCK_NB)
	if err != nil {
		file.Close()
		return nil, fmt.Errorf("failed to acquire file lock: %w", err)
	}

	return &FileLock{
		file: file,
		path: filePath,
	}, nil
}

// 释放文件锁
func (fl *FileLock) Release() error {
	if fl.file != nil {
		// 释放锁
		syscall.Flock(int(fl.file.Fd()), syscall.LOCK_UN)
		return fl.file.Close()
	}
	return nil
}

// 检查锁是否仍然有效
func (fl *FileLock) IsValid() bool {
	return fl.file != nil
}

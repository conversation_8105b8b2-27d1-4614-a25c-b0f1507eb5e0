package shell

import (
	"fmt"
	"testing"
)

func Test_CreateSnapshot(t *testing.T) {
	shellPath := FindShell()
	t.Logf("Found system shell: %s", shellPath)

	snapshotPath, err := CreateSnapshot(shellPath)
	if err != nil {
		fmt.Printf("Error creating snapshot: %v\n", err)
		return
	}

	if snapshotPath != "" {
		fmt.Printf("Snapshot created successfully at: %s\n", snapshotPath)
	} else {
		fmt.Println("No snapshot was created")
	}
}

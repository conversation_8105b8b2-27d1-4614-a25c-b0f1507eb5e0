package shell

import (
	"bufio"
	"context"
	"fmt"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/shell"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

// EventCallback defines the callback function type for shell events
type EventCallback func(eventType pubsub.EventType, event shell.Event)

type PersistentShell struct {
	bashId       string
	cwd          string
	shellPath    string
	snapshotPath string
	mu           sync.Mutex

	// 异步执行相关字段
	execResult     shell.CommandResult
	isExecuting    bool
	completionChan chan struct{} // 用于通知命令执行完成
	currentCmd     *exec.Cmd     // 当前正在执行的命令

	// 事件回调
	eventCallback EventCallback
}

// SetEventCallback sets the event callback function
func (s *PersistentShell) SetEventCallback(callback EventCallback) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.eventCallback = callback
}

// Exec 异步执行命令，立即返回
func (s *PersistentShell) Exec(c context.Context, command string, timeout time.Duration) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 如果正在执行，返回错误
	if s.isExecuting {
		return fmt.Errorf("shell is already executing a command")
	}
	s.isExecuting = true

	// 创建新的completion channel
	s.completionChan = make(chan struct{})

	// 发送命令开始事件
	if s.eventCallback != nil {
		event := shell.Event{
			WorkingDir:    s.cwd,
			CommandResult: s.execResult,
		}
		s.eventCallback(shell.ShellCommandStarted, event)
	}

	// 在goroutine中执行命令
	go s.executeCommand(c, command, timeout)

	return nil
}

// executeCommand 实际执行命令的方法
func (s *PersistentShell) executeCommand(ctx context.Context, command string, timeout time.Duration) {

	s.execResult.BashId = s.bashId
	s.execResult.Command = command
	s.execResult.StartTime = time.Now()
	s.execResult.Status = shell.ShellStatusRunning

	defer func() {
		s.mu.Lock()
		s.isExecuting = false
		s.currentCmd = nil
		s.mu.Unlock()
	}()

	randObj := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomNum := fmt.Sprintf("%04x", randObj.Intn(65536))
	tmpDir := os.TempDir()
	cwdTmpFile := filepath.Join(tmpDir, fmt.Sprintf("%s-%s-cwd", core.ReleaseName, randomNum))

	// 构建最终命令
	finalCmd := quote([]string{command, "<", "/dev/null"})
	if strings.Contains(s.shellPath, "bash") {
		parts := strings.Split(command, "|")
		if len(parts) > 1 {
			finalCmd = quote([]string{parts[0], "<", "/dev/null", "|", strings.Join(parts[1:], "|")})
		}
	}

	var cmdList []string
	if len(s.snapshotPath) > 0 {
		cmdList = append(cmdList, fmt.Sprintf("source %s", quote([]string{s.snapshotPath})))
	}
	cmdList = append(cmdList, fmt.Sprintf("eval %s", finalCmd))
	cmdList = append(cmdList, fmt.Sprintf("pwd -P >| %s", cwdTmpFile))
	finalCmd = strings.Join(cmdList, " && ")

	// 执行命令
	cmdCtx := context.TODO()
	if timeout > 0 {
		var cancel context.CancelFunc
		cmdCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}

	cmd := exec.CommandContext(cmdCtx, s.shellPath, "-l", "-c", finalCmd)
	cmd.Env = append(os.Environ(), "GIT_EDITOR=true")
	cmd.Dir = s.cwd
	cmd.SysProcAttr = &syscall.SysProcAttr{Setpgid: true}

	// 设置当前执行的命令
	s.mu.Lock()
	s.currentCmd = cmd
	s.mu.Unlock()

	// 创建输出管道
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		s.notifyResult(err, shell.ShellStatusFailed)
		return
	}
	stderr, err := cmd.StderrPipe()
	if err != nil {
		s.notifyResult(err, shell.ShellStatusFailed)
		return
	}

	// 准备缓冲区
	var wg sync.WaitGroup
	wg.Add(2)

	// 启动命令
	if err := cmd.Start(); err != nil {
		exitCode := cmd.ProcessState.ExitCode()
		s.execResult.ExitCode = &exitCode
		s.notifyResult(err, shell.ShellStatusFailed)
		return
	}

	// 在goroutine中读取输出
	go func() {
		defer wg.Done()
		scanner := bufio.NewScanner(stdout)
		for scanner.Scan() {
			text := scanner.Text()
			s.execResult.Stdout += text + "\n"
		}
	}()

	go func() {
		defer wg.Done()
		scanner := bufio.NewScanner(stderr)
		for scanner.Scan() {
			text := scanner.Text()
			s.execResult.Stderr += text + "\n"
		}
	}()

	// 创建一个channel来传递命令执行的错误
	done := make(chan error)
	go func() {
		wg.Wait()
		done <- cmd.Wait()
	}()

	// 等待命令完成或超时
	select {
	case <-cmdCtx.Done():
		// 命令执行超时
		_ = syscall.Kill(-cmd.Process.Pid, syscall.SIGKILL)
		s.execResult.Timeout = true
		s.notifyResult(cmdCtx.Err(), shell.ShellStatusFailed)
	case err := <-done:
		// 命令执行完成
		exitCode := cmd.ProcessState.ExitCode()
		s.execResult.ExitCode = &exitCode
		s.execResult.Error = err
		if err == nil {
			s.notifyResult(nil, shell.ShellStatusCompleted)
		} else {
			s.notifyResult(err, shell.ShellStatusFailed)
		}
	}
}

// notifyResult 通知所有监听者执行结果并发送事件
func (s *PersistentShell) notifyResult(err error, status shell.ShellStatus) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.execResult.Error = err
	s.execResult.Status = status
	s.execResult.EndTime = time.Now()

	// 关闭completion channel通知命令执行完成
	if s.completionChan != nil {
		close(s.completionChan)
		s.completionChan = nil
	}

	// 发送事件回调
	if s.eventCallback != nil {
		event := shell.Event{
			WorkingDir:    s.cwd,
			CommandResult: s.execResult,
		}

		// 根据结果确定事件类型
		var eventType pubsub.EventType
		if s.execResult.Timeout {
			eventType = shell.ShellCommandTimeout
		} else if s.execResult.Error != nil {
			eventType = shell.ShellCommandFailed
		} else {
			eventType = shell.ShellCommandCompleted
		}
		s.eventCallback(eventType, event)
	}
}

// GetResult 获取最后一次执行的结果
func (s *PersistentShell) GetResult() shell.CommandResult {
	s.mu.Lock()
	defer s.mu.Unlock()

	return s.execResult
}

// IsExecuting 检查是否正在执行命令
func (s *PersistentShell) IsExecuting() bool {
	s.mu.Lock()
	defer s.mu.Unlock()

	return s.isExecuting
}

// GetStartTime 获取最后使用时间
func (s *PersistentShell) GetStartTime() time.Time {
	s.mu.Lock()
	defer s.mu.Unlock()

	return s.execResult.StartTime
}

// WaitForResult 阻塞式等待命令执行完成并返回结果
func (s *PersistentShell) WaitForResult() shell.CommandResult {
	s.mu.Lock()

	// 如果没有正在执行的命令，直接返回最后的结果
	if !s.isExecuting {
		defer s.mu.Unlock()
		return s.execResult
	}

	// 获取completion channel的引用
	completionChan := s.completionChan
	s.mu.Unlock()

	// 如果有completion channel，等待其关闭
	if completionChan != nil {
		<-completionChan
	}

	// 返回执行结果
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.execResult
}

// KillProcess 杀掉当前执行的进程
func (s *PersistentShell) KillProcess() error {
	// 如果没有正在执行的命令，返回错误
	if !s.isExecuting || s.currentCmd == nil {
		return fmt.Errorf("no command is currently executing")
	}

	// 如果进程已经结束，返回nil
	if s.currentCmd.Process == nil {
		return nil
	}

	// 杀掉进程组（包括子进程）
	err := syscall.Kill(-s.currentCmd.Process.Pid, syscall.SIGKILL)
	if err != nil {
		return fmt.Errorf("failed to kill process: %w", err)
	}

	s.notifyResult(fmt.Errorf("process killed"), shell.ShellStatusKilled)
	return nil
}

func quote(args []string) string {
	// 简单的引号处理
	result := make([]string, len(args))
	for i, arg := range args {
		if strings.Contains(arg, " ") {
			result[i] = fmt.Sprintf(`"%s"`, arg)
		} else {
			result[i] = arg
		}
	}
	return strings.Join(result, " ")
}

package shell

import (
	"github.com/qoder-ai/qodercli/core"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// checkExecutable checks if a file is executable
func checkExecutable(path string) bool {
	// Check file permission
	if _, err := os.Stat(path); err == nil {
		if err := exec.Command(path, "--version").Run(); err == nil {
			return true
		}
	}
	return false
}

// FindShell finds an appropriate shell for execution
func FindShell() string {
	// Helper function to find command path
	findCommandPath := func(cmd string) string {
		path, err := exec.LookPath(cmd)
		if err != nil {
			return ""
		}
		return path
	}

	// Get shell from environment
	envShell := os.Getenv("SHELL")
	isPreferredShell := envShell != "" && (strings.Contains(envShell, "bash") || strings.Contains(envShell, "zsh"))
	isBash := strings.Contains(envShell, "bash")

	// Find zsh and bash paths
	zshPath := findCommandPath("zsh")
	bashPath := findCommandPath("bash")

	// Define common binary paths
	binPaths := []string{
		"/bin",
		"/usr/bin",
		"/usr/local/bin",
		"/opt/homebrew/bin",
	}

	// Prepare shell names based on preference
	var shellNames []string
	if isBash {
		shellNames = []string{"bash", "zsh"}
	} else {
		shellNames = []string{"zsh", "bash"}
	}

	// Generate all possible shell paths
	var possibleShellPaths []string

	// Add environment shell if it's preferred
	if isPreferredShell && checkExecutable(envShell) {
		possibleShellPaths = append(possibleShellPaths, envShell)
	}

	// Add found paths based on preference
	if isBash {
		if bashPath != "" {
			possibleShellPaths = append(possibleShellPaths, bashPath)
		}
		if zshPath != "" {
			possibleShellPaths = append(possibleShellPaths, zshPath)
		}
	} else {
		if zshPath != "" {
			possibleShellPaths = append(possibleShellPaths, zshPath)
		}
		if bashPath != "" {
			possibleShellPaths = append(possibleShellPaths, bashPath)
		}
	}

	// Add all possible combinations of bin paths and shell names
	for _, binPath := range binPaths {
		for _, shellName := range shellNames {
			possibleShellPaths = append(possibleShellPaths, filepath.Join(binPath, shellName))
		}
	}

	// Find first executable shell
	for _, shellPath := range possibleShellPaths {
		if shellPath != "" && checkExecutable(shellPath) {
			return shellPath
		}
	}

	panic("No suitable shell found. " + core.AppName + " requires a Posix shell environment. Please ensure you have a valid shell installed and the SHELL environment variable set.")
}

package llm

import (
	"context"

	"github.com/qoder-ai/qodercli/core/llm/filter"

	"github.com/qoder-ai/qodercli/core/llm/hook"
	coreOutputStyle "github.com/qoder-ai/qodercli/core/llm/output_style"
	"github.com/qoder-ai/qodercli/core/llm/shell"
	"github.com/qoder-ai/qodercli/core/pubsub"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/memory"
	"github.com/qoder-ai/qodercli/core/llm/provider"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/lsp"
)

type AppSupport interface {
	PermissionTrigger
	SessionOperator
	MessageOperator
	MemoryOperator
	McpOperator
	EventSubscriber
	GetShellService() shell.ShellService
	GetHookService() hook.HookService
	GetAgentService() agent.SubAgentService
	GetHistoryService() core.HistoryService
	GetProviderService() provider.Service
	GetProjectService() core.ProjectService
	GetOutputStyleService() coreOutputStyle.OutputStyleService
	GetConfig() *config.Service
	GetWorkingDir() string
	GetLspManager() LspManager
	GetMessageRefactor() filter.MessageRefactor
}

type LspManager interface {
	GetSolidLspClient() *lsp.SolidLspClient
}

type McpOperator interface {
	ListMcpTools(ctx context.Context) []tools.BaseTool
}

type SessionOperator interface {
	CreateTaskSession(ctx context.Context, toolCallId, parentSessionId, title string) (core.Session, error)
	GetSession(ctx context.Context, id string) (core.Session, error)
	SaveSession(ctx context.Context, session core.Session) error
	UpdateSessionTitle(ctx context.Context, sessionId string, title string) error
	UpdateSessionTokens(ctx context.Context, sessionId string, promptTokens int64, completionTokens int64, costDelta float64) error
}

type MessageOperator interface {
	CreateMessage(ctx context.Context, sessionId string, params message.CreateMessageParams) (message.Message, error)
	UpdateMessage(ctx context.Context, message message.Message) error
	ListMessages(ctx context.Context, sessionId string) ([]message.Message, error)
	GetLastMessageOfSession(ctx context.Context, sessionId string) (*message.Message, error)
}

type MemoryOperator interface {
	GetMemories() []memory.Memory
	GetMemoriesContent() []string
	GetUserMemoryFilePath() string
	GetProjectMemoryFilePath() string
	GetProjectLocalMemoryFilePath() string
}

type HookManager interface {
	GetHookExecutors(event config.HookEvent, input hook.Input) []hook.Executor
	GetPreToolUseHookExecutor(input hook.Input) []hook.Executor
	GetPostToolUseHookExecutor(input hook.Input) []hook.Executor
	GetNotificationHookExecutor(input hook.Input) []hook.Executor
	GetUserPromptSubmitHookExecutor(input hook.Input) []hook.Executor
	GetStopHookExecutor(input hook.Input) []hook.Executor
	GetSubagentStopHookExecutor(input hook.Input) []hook.Executor
	GetPreCompactHookExecutor(input hook.Input) []hook.Executor
	GetSessionStartHookExecutor(input hook.Input) []hook.Executor
}

type PermissionTrigger interface {
	CreateRequestWithContext(ctx core.SessionContext, opts core.CreatePermissionRequest) bool
}

type EventSubscriber interface {
	SubscribeSession(ctx context.Context) <-chan pubsub.Event[core.Session]
	SubscribeHistory(ctx context.Context) <-chan pubsub.Event[core.File]
	SubscribeMessage(ctx context.Context) <-chan pubsub.Event[message.Message]
	SubscribePermission(ctx context.Context) <-chan pubsub.Event[core.PermissionRequest]
	SubscribeCoderAgent(ctx context.Context) <-chan pubsub.Event[agent.Event]
	SubscribeShell(ctx context.Context) <-chan pubsub.Event[shell.Event]
}

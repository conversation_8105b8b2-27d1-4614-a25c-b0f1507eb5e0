package prompt

import (
	"bytes"
	_ "embed"
	"fmt"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/message"
	"strings"
	"text/template"
)

//go:embed agent_generator.tpl
var agentGeneratorSystemPromptTpl string

var agentGeneratorUserPromptTpl = `Create an agent configuration based on this request: "%s".

IMPORTANT: The following identifiers already exist and must NOT be used: %s

Return ONLY the valid JSON object, no other text and no Markdown surround.`

func GetAgentGeneratorSystemPrompt() string {
	t := template.Must(template.New("agent generator").Parse(agentGeneratorSystemPromptTpl))
	var buf bytes.Buffer
	d := map[string]string{
		"TaskToolName": tools.TaskToolName,
	}

	if err := t.Execute(&buf, d); err != nil {
		panic(err)
	}

	return buf.String()
}

func GetAgentGeneratorUserPrompt(requirement string, exists []string) []message.ContentPart {
	return []message.ContentPart{message.TextContent{
		Type: "text",
		Text: fmt.Sprintf(agentGeneratorUserPromptTpl, requirement, strings.Join(exists, ", ")),
	}}
}

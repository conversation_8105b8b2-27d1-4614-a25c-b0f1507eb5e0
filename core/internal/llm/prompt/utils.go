package prompt

import (
	"context"
	"fmt"
	"github.com/qoder-ai/qodercli/core/config"
	tools2 "github.com/qoder-ai/qodercli/core/internal/llm/tools"
	coreTools "github.com/qoder-ai/qodercli/core/llm/tools"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

const envPromptTemplate = `Here is useful information about the environment you are running in:
<env>
Working directory: %s
Is directory a git repo: %s
Platform: %s
OS Version: %s
Today's date: %s
</env>`

const gitPromptTemplate = `This is the git status at the start of the conversation. Note that this status is a snapshot in time, and will not update during the conversation.
Current branch: %s

Main branch (you will usually use this for PRs): %s

Status:
%s

Recent commits:
%s`

func getEnvInfo(workingDir string) string {
	isGit := isGitRepo(workingDir)
	platform := runtime.GOOS
	version := osVersion()
	date := time.Now().Format("1/2/2006")
	return fmt.Sprintf(envPromptTemplate, workingDir, boolToYesNo(isGit), platform, version, date)
}

func getProjectInfo(workingDir string) string {
	ls := tools2.NewLsTool()
	toolCtx := coreTools.NewToolExecutionContextBuilder(context.Background()).WithWorkingDir(workingDir).Build()
	r, _ := ls.Run(toolCtx, coreTools.ToolCall{
		Input: `{"path":"."}`,
	})
	// TODO r.Content 超长需要截断
	return fmt.Sprintf("<project>\n%s\n</project>", r.Content)
}

func getGitInfo(workingDir string) string {
	if !isGitRepo(workingDir) {
		return ""
	}
	curBranch := gitCurrentBranch(workingDir)
	mainBranch := gitMainBranch(workingDir)
	status := gitStatus(workingDir)
	commits := gitLatestCommits(workingDir)
	return fmt.Sprintf(gitPromptTemplate, curBranch, mainBranch, status, commits)
}

func gitLatestCommits(workingDir string) interface{} {
	cmd := exec.Command("git", "log", "--oneline", "-n", "5")
	cmd.Dir = workingDir
	output, err := cmd.Output()
	if err != nil {
		return "unknown"
	}
	return string(output)
}

func isGitRepo(dir string) bool {
	_, err := os.Stat(filepath.Join(dir, ".git"))
	return err == nil
}

func gitMainBranch(workingDir string) string {
	cmd := exec.Command("git", "rev-parse", "--abbrev-ref", "origin/HEAD")
	cmd.Dir = workingDir
	output, err := cmd.Output()
	if err != nil {
		return "unknown"
	}
	branch := string(output)
	return strings.Replace(branch, "origin/", "", 1)
}

func gitCurrentBranch(workingDir string) string {
	cmd := exec.Command("git", "branch", "--show-current")
	cmd.Dir = workingDir
	output, err := cmd.Output()
	if err != nil {
		return "unknown"
	}
	return string(output)
}

func gitStatus(workingDir string) string {
	cmd := exec.Command("git", "status", "--short")
	cmd.Dir = workingDir
	output, err := cmd.Output()
	if err != nil {
		return "unknown"
	}

	content := string(output)
	if len(content) > 40000 {
		content = content[:40000]
		content += "\n... (truncated because it exceeds 40k characters. If you need more information, run \"git status\" using BashTool)"
	} else if len(content) == 0 {
		content = "(clean)"
	}
	return content
}

func osVersion() string {
	cmd := exec.Command("uname", "-sr")
	output, err := cmd.Output()
	if err != nil {
		return "unknown"
	}
	return string(output)
}

func lspInformationWithConfig(lspConfigs map[string]config.LspConfig) string {
	hasLSP := false

	if lspConfigs != nil {
		// Use provided configs
		for _, v := range lspConfigs {
			if v.Enabled {
				hasLSP = true
				break
			}
		}
	}

	if !hasLSP {
		return ""
	}
	return ""
	//	return `# LSP Information
	//Tools that support it will also include useful diagnostics such as linting and typechecking.
	//- These diagnostics will be automatically enabled when you run the tool, and will be displayed in the output at the bottom within the <file_diagnostics></file_diagnostics> and <project_diagnostics></project_diagnostics> tags.
	//- Take necessary actions to fix the issues.
	//- You should ignore diagnostics of files that you did not change or are not related or caused by your changes unless the user explicitly asks you to fix them.
	//`
}

func boolToYesNo(b bool) string {
	if b {
		return "Yes"
	}
	return "No"
}

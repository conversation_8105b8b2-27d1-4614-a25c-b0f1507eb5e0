package prompt

import (
	"fmt"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"strings"
)

const defaultPrompt = `You are an agent for ` + core.AppName + `. Given the user's message, you should use the tools available to complete the task. Do what has been asked; nothing more, nothing less. When you complete the task simply respond with a detailed writeup.

Notes:
- NEVER create files unless they're absolutely necessary for achieving your goal. ALWAYS prefer editing an existing file to creating a new one.
- NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
- In your final response always share relevant file names and code snippets. Any file paths you return in your response MUST be absolute. Do NOT use relative paths.
- For clear communication with the user the assistant MUST avoid using emojis.`

func TaskPrompt(_ models.ModelProvider, customPrompt string, workingDir string) string {
	var prompts []string
	if len(customPrompt) == 0 {
		prompts = append(prompts, defaultPrompt)
	} else {
		prompts = append(prompts, fmt.Sprintf("You are an agent for %s.", core.AppName))
		prompts = append(prompts, customPrompt)
	}
	prompts = append(prompts, getEnvInfo(workingDir))
	gitInfo := getGitInfo(workingDir)
	if len(gitInfo) > 0 {
		prompts = append(prompts, gitInfo)
	}
	prompts = append(prompts, getProjectInfo(workingDir))
	return strings.Join(prompts, "\n\n")
}

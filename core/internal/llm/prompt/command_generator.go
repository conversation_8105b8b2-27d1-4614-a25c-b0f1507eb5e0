package prompt

import (
	"bytes"
	_ "embed"
	"fmt"
	"strings"
	"text/template"

	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/message"
)

//go:embed command_generator.tpl
var commandGeneratorSystemPromptTpl string

var commandGeneratorUserPromptTpl = `Create a command configuration based on this request: "%s".

IMPORTANT: The following identifiers already exist and must NOT be used: %s

Return ONLY the valid JSON object, no other text and no Markdown surround.`

func GetCommandGeneratorSystemPrompt() string {
	t := template.Must(template.New("command generator").Parse(commandGeneratorSystemPromptTpl))
	var buf bytes.Buffer
	d := map[string]string{
		"TaskToolName": tools.TaskToolName,
	}

	if err := t.Execute(&buf, d); err != nil {
		panic(err)
	}

	return buf.String()
}

func GetCommandGeneratorUserPrompt(requirement string, exists []string) []message.ContentPart {
	return []message.ContentPart{message.TextContent{
		Type: "text",
		Text: fmt.Sprintf(commandGeneratorUserPromptTpl, requirement, strings.Join(exists, ", ")),
	}}
}

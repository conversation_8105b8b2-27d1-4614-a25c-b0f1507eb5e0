package prompt

import (
	"bytes"
	_ "embed"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/models"
	coreTools "github.com/qoder-ai/qodercli/core/llm/tools"
	"strings"
	"text/template"
)

var data = map[string]string{
	"BrandName":         core.BrandName,
	"AppName":           core.AppName,
	"EmailAddress":      core.EmailAddress,
	"DocDomainURL":      core.DocDomainURL,
	"TodoWriteToolName": coreTools.TodoWriteToolName,
	"WebFetchToolName":  coreTools.WebFetchToolName,
	"BashToolName":      coreTools.BashToolName,
	"MemoryFileName":    core.MemoryFileName,
}

//const productPrompt = `IMPORTANT: Assist with defensive security tasks only. Refuse to create, modify, or improve code that may be used maliciously. Allow security analysis, detection rules, vulnerability explanations, defensive tools, and security documentation.`
//const benchmarkPrompt = `IMPORTANT: After finishing the task, ALWAYS try to check whether the generated code and programs work correctly—by compiling, running, or other appropriate methods—if conditions allow.`

//go:embed coder.tpl
var baseCoderPromptTpl string

func CoderPrompt(_ models.ModelProvider, customPrompt string, workingDir string) string {
	basePrompt := customPrompt
	if len(customPrompt) == 0 {
		t := template.Must(template.New("test").Parse(baseCoderPromptTpl))
		var buf bytes.Buffer
		if err := t.Execute(&buf, data); err != nil {
			panic(err)
		}
		basePrompt = buf.String()
	}

	prompts := []string{basePrompt}
	prompts = append(prompts, getEnvInfo(workingDir))
	gitInfo := getGitInfo(workingDir)
	if len(gitInfo) > 0 {
		prompts = append(prompts, gitInfo)
	}
	prompts = append(prompts, getProjectInfo(workingDir))
	// TODO 添加模型信息
	// TODO 添加LSP信息
	return strings.Join(prompts, "\n\n")
}

package prompt

import (
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/models"
)

func GetAgentPrompt(agentName config.AgentName, provider models.ModelProvider, customPrompt string, workingDir string) string {
	switch agentName {
	case config.AgentCoder:
		return CoderPrompt(provider, customPrompt, workingDir)
	case config.AgentTitle:
		return TitlePrompt(provider)
	case config.AgentTask:
		return TaskPrompt(provider, customPrompt, workingDir)
	case config.AgentSummarizer:
		return SummarizerPrompt(provider)
	default:
		return "You are a helpful assistant"
	}
}

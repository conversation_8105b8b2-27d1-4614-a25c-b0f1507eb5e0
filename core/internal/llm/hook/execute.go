package hook

import (
	"bytes"
	"encoding/json"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/hook"
	"os"
	"os/exec"
	"strings"
)

type defaultExecutor struct {
	input any
	hook  config.Hook
}

func (e *defaultExecutor) Execute() (*hook.Result, error) {
	bytes, err := json.Marshal(e.input)
	if err != nil {
		return nil, err
	}

	stdout, stderr, exitCode, _ := executeCommand(e.hook.Command, string(bytes))
	result := hook.Result{ExitCode: exitCode, Stdout: stdout, Stderr: stderr}
	if len(stdout) != 0 {
		var output hook.DecisionControl
		if err := json.Unmarshal([]byte(stdout), &output); err == nil {
			result.DecisionControl = &output
		}
	}
	return &result, nil
}

func executeCommand(command string, input string) (string, string, int, error) {
	if strings.HasPrefix(command, "~") {
		homedir := os.Getenv("HOME")
		command = strings.Replace(command, "~", homedir, 1)
	}
	cmd := exec.Command(command)

	// 写入标准输入
	cmd.Stdin = strings.NewReader(input)

	// 读取标准输出
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// 运行命令
	err := cmd.Run()
	exitCode := cmd.ProcessState.ExitCode()
	return stdout.String(), stderr.String(), exitCode, err
}

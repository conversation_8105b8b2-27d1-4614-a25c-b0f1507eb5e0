package hook

import (
	"context"
	"fmt"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/hook"
	"path/filepath"
	"regexp"
	"strings"
)

type Service struct {
	app               llm.AppSupport
	notificationHooks []config.HookConfig
}

func NewService(app llm.AppSupport) *Service {
	service := &Service{
		app: app,
	}

	hooks := app.GetConfig().GetHooks()
	if h, ok := hooks[config.HookEventNotification]; ok && len(h) > 0 {
		service.notificationHooks = h
		go service.handlePermissionNotification()
	}
	return service

}

func (s Service) transcriptPath(sessionId string) string {
	projectPath := s.app.GetProjectService().GetStorageDir()
	return filepath.Join(projectPath, sessionId+".jsonl")
}

func (s Service) handlePermissionNotification() {
	ctx := context.Background()
	permChain := s.app.SubscribePermission(ctx)
	agentChain := s.app.SubscribeCoderAgent(ctx)
	for {
		var sessionId string
		var message string
		select {
		case event := <-permChain:
			sessionId = event.Payload.SessionId
			message = fmt.Sprintf("Permission event: %s", event.Payload.Description)
		case event := <-agentChain:
			if event.Payload.Type != agent.AgentEventTypeResult {
				continue
			}
			sessionId = event.Payload.SessionId
			message = fmt.Sprintf("Agent stopped: %s", event.Payload.Message.FinishReason())
		default:
			continue
		}

		input := hook.Input{
			Args: config.HookNotificationInput{
				SessionId:      sessionId,
				TranscriptPath: s.transcriptPath(sessionId),
				Cwd:            s.app.GetConfig().WorkingDir,
				HookEventName:  config.HookEventNotification,
				Message:        message,
			},
		}
		executors := getExecutors(s.notificationHooks, false, input)
		for _, executor := range executors {
			_, _ = executor.Execute()
		}
	}
}

func (s Service) GetPreToolUseHookExecutor(input hook.Input) []hook.Executor {
	return s.GetHookExecutors(config.HookEventPreToolUse, input)
}

func (s Service) GetPostToolUseHookExecutor(input hook.Input) []hook.Executor {
	return s.GetHookExecutors(config.HookEventPostToolUse, input)
}

func (s Service) GetNotificationHookExecutor(input hook.Input) []hook.Executor {
	return s.GetHookExecutors(config.HookEventNotification, input)
}

func (s Service) GetUserPromptSubmitHookExecutor(input hook.Input) []hook.Executor {
	return s.GetHookExecutors(config.HookEventUserPromptSubmit, input)
}

func (s Service) GetStopHookExecutor(input hook.Input) []hook.Executor {
	return s.GetHookExecutors(config.HookEventStop, input)
}

func (s Service) GetSubagentStopHookExecutor(input hook.Input) []hook.Executor {
	return s.GetHookExecutors(config.HookEventSubagentStop, input)
}

func (s Service) GetPreCompactHookExecutor(input hook.Input) []hook.Executor {
	return s.GetHookExecutors(config.HookEventPreCompact, input)
}

func (s Service) GetSessionStartHookExecutor(input hook.Input) []hook.Executor {
	return s.GetHookExecutors(config.HookEventSessionStart, input)
}

func (s Service) GetHookExecutors(event config.HookEvent, input hook.Input) []hook.Executor {
	hooks := s.app.GetConfig().GetHooks()
	if hooks == nil {
		return nil
	}

	isToolUse := event == config.HookEventPreToolUse || event == config.HookEventPostToolUse
	if items, ok := hooks[event]; ok {
		return getExecutors(items, isToolUse, input)
	}
	return nil
}

func getExecutors(items []config.HookConfig, isToolUse bool, input hook.Input) []hook.Executor {
	var executors []hook.Executor
	for _, item := range items {
		if isToolUse && !match(item.Matcher, input.Tool) {
			continue
		}

		for _, h := range item.Hooks {
			if h.Type == config.HookTypeCommand {
				executors = append(executors, &defaultExecutor{input: input.Args, hook: h})
			}
		}
	}
	return executors
}

// match matcher：匹配工具名称的模式，区分大小写
// - 简单字符串精确匹配：Write 仅匹配 Write 工具
// - 支持正则表达式：Edit|Write 或 Notebook.*
// - 使用 * 匹配所有工具。您也可以使用空字符串（""）或将 matcher 留空。
func match(matcher string, tool string) bool {
	// 处理空字符串或 "*" 的情况，匹配所有工具
	if matcher == "" || matcher == "*" {
		return true
	}

	// 检查是否包含正则表达式的特殊字符
	regexChars := []string{"*", ".", "|", "(", ")", "[", "]", "?", "+", "^", "$", "\\"}
	isRegex := false
	for _, char := range regexChars {
		if strings.Contains(matcher, char) {
			isRegex = true
			break
		}
	}

	// 如果包含正则表达式特殊字符，则使用正则匹配
	if isRegex {
		reg, err := regexp.Compile(matcher)
		if err == nil {
			return reg.MatchString(tool)
		}
		// 如果正则表达式无效，回退到精确匹配
		return matcher == tool
	}

	// 不包含特殊字符时使用精确匹配
	return matcher == tool
}

package hook

import "testing"

func TestMatch(t *testing.T) {
	// 定义测试用例结构
	tests := []struct {
		name    string // 测试用例名称
		matcher string // 输入的matcher
		tool    string // 输入的tool
		want    bool   // 期望的结果
	}{
		{
			name:    "Exact match - should match",
			matcher: "Write",
			tool:    "Write",
			want:    true,
		},
		{
			name:    "Exact match - should not match",
			matcher: "Write",
			tool:    "WriteNote",
			want:    false,
		},
		{
			name:    "Regex OR operator - match first option",
			matcher: "Edit|Write",
			tool:    "Edit",
			want:    true,
		},
		{
			name:    "Regex OR operator - match second option",
			matcher: "Edit|Write",
			tool:    "Write",
			want:    true,
		},
		{
			name:    "Regex wildcard - exact match",
			matcher: "Notebook.*",
			tool:    "Notebook",
			want:    true,
		},
		{
			name:    "Regex wildcard - with suffix",
			matcher: "Notebook.*",
			tool:    "NotebookPro",
			want:    true,
		},
		{
			name:    "Wildcard matcher",
			matcher: "*",
			tool:    "AnyTool",
			want:    true,
		},
		{
			name:    "Empty matcher",
			matcher: "",
			tool:    "AnyTool",
			want:    true,
		},
		{
			name:    "Invalid regex falls back to exact match - should not match",
			matcher: "[invalid",
			tool:    "invalid",
			want:    false,
		},
	}

	// 执行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := match(tt.matcher, tt.tool)
			if got != tt.want {
				t.Errorf("match(%q, %q) = %v, want %v",
					tt.matcher, tt.tool, got, tt.want)
			}
		})
	}
}

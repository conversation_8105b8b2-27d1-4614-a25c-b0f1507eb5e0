package output_style

import (
	"bufio"
	"fmt"
	"github.com/qoder-ai/qodercli/core/llm/output_style"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/utils"
	"os"
	"path/filepath"
	"strings"
)

type defaultOutputStyleService struct {
}

func NewOutputStyleService() output_style.OutputStyleService {
	return &defaultOutputStyleService{}
}

func (d defaultOutputStyleService) Get(name string) (*output_style.OutputStyle, error) {
	styles, err := d.List()
	if err != nil {
		return nil, err
	}
	for _, style := range styles {
		if style.Name == name {
			return &style, nil
		}
	}
	return nil, fmt.Errorf("output style [%s] not found", name)
}

func (d defaultOutputStyleService) List() ([]output_style.OutputStyle, error) {
	styles := getBuiltInOutputStyles()

	outputStyleDir := filepath.Join(utils.GetUserStorageDir(), "output-styles")
	projectStyles, err := d.loadOutputStyles(outputStyleDir, output_style.OutputStyleSourceUser)
	if err != nil {
		return nil, err
	}

	claudeCodeOutputStyleDir := filepath.Join(utils.GetClaudeCodeUserStorageDir(), "output-styles")
	claudeStyles, err := d.loadOutputStyles(claudeCodeOutputStyleDir, output_style.OutputStyleSourceClaudeCodeUser)
	if err != nil {
		return nil, err
	}

	styles = append(styles, projectStyles...)
	styles = append(styles, claudeStyles...)
	return styles, err
}

func (d defaultOutputStyleService) loadOutputStyles(dir string, source output_style.OutputStyleSource) ([]output_style.OutputStyle, error) {
	var styles []output_style.OutputStyle
	if _, err := os.Stat(dir); err != nil {
		return styles, nil
	}
	return styles, filepath.WalkDir(dir, func(path string, e os.DirEntry, err error) error {
		if err == nil && !e.IsDir() && filepath.Ext(path) == ".md" {
			if style, err := d.parseOutputStyle(path); err != nil {
				logging.ErrorPersist(fmt.Sprintf("failed to parse output style [%s]: %v", path, err))
				return nil
			} else {
				style.Source = source
				styles = append(styles, *style)
			}
		}
		return nil
	})
}

func (d defaultOutputStyleService) parseOutputStyle(path string) (*output_style.OutputStyle, error) {
	style := &output_style.OutputStyle{StoragePath: path}
	bytes, err := os.ReadFile(path)
	if err != nil {
		return style, err
	}

	var part = 0
	scanner := bufio.NewScanner(strings.NewReader(string(bytes)))
	for scanner.Scan() {
		line := scanner.Text()
		content := strings.TrimSpace(line)
		if content == "---" {
			part++
			continue
		}

		if part == 1 {
			if content == "" {
				continue
			}
			kv := strings.SplitN(content, ":", 2)
			if len(kv) != 2 {
				continue
			}

			key := strings.TrimSpace(kv[0])
			value := strings.TrimSpace(kv[1])
			switch key {
			case "name":
				style.Name = value
			case "description":
				style.Description = value
			}
		} else if part == 2 {
			style.Prompt += line + "\n"
		}
	}
	style.Prompt = strings.TrimSpace(style.Prompt)
	return style, d.validOutputStyle(*style)
}

func (d defaultOutputStyleService) validOutputStyle(style output_style.OutputStyle) error {
	if style.Name == "" {
		return fmt.Errorf("output style [%s] is invalid, name is required", style.StoragePath)
	} else if style.Prompt == "" {
		return fmt.Errorf("output style [%s] is invalid, prompt is required", style.StoragePath)
	} else if style.Description == "" {
		return fmt.Errorf("output style [%s] is invalid, description is required", style.StoragePath)
	}
	return nil
}

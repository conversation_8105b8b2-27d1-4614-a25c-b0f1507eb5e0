package output_style

import (
	_ "embed"
	"fmt"
	"github.com/qoder-ai/qodercli/core"
)
import "github.com/qoder-ai/qodercli/core/llm/output_style"

//go:embed explanatory.md
var explanatoryPrompt string

//go:embed learning.md
var learningPrompt string

func getBuiltInOutputStyles() []output_style.OutputStyle {
	return []output_style.OutputStyle{
		{
			Name:            "Explanatory",
			Description:     fmt.Sprintf("%s explains its implementation choices and codebase patterns", core.AppName),
			IsCodingRelated: true,
			Prompt:          explanatoryPrompt,
			Source:          output_style.OutputStyleSourceBuiltIn,
		},
		{
			Name:            "Learning",
			Description:     fmt.Sprintf("%s pauses and asks you to write small pieces of code for hands-on practice", core.AppName),
			IsCodingRelated: true,
			Prompt:          learningPrompt,
			Source:          output_style.OutputStyleSourceBuiltIn,
		},
	}
}

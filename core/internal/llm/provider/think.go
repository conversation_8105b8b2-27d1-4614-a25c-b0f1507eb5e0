package provider

import (
	"github.com/qoder-ai/qodercli/core/message"
	"regexp"
)

type ThinkLevel struct {
	TokenCount int
	Level      string
	RegExps    []*regexp.Regexp
}

var (
	ThinkLevelNone  = ThinkLevel{Level: "none", TokenCount: 0, RegExps: nil}
	ThinkLevelBasic = ThinkLevel{Level: "basic", TokenCount: 4000, RegExps: []*regexp.Regexp{
		regexp.MustCompile("\\bthink\\b"),
		regexp.MustCompile("考えて"),
		regexp.MustCompile("想"),
		regexp.MustCompile("思考"),
		regexp.MustCompile("\\bpienso\\b"),
		regexp.MustCompile("\\bpensando\\b"),
		regexp.MustCompile("\\bpense\\b"),
		regexp.MustCompile("\\bréfléchir\\b"),
		regexp.MustCompile("\\bdenke\\b"),
		regexp.MustCompile("\\bnachdenken\\b"),
		regexp.MustCompile("\\b생각\\b"),
		regexp.MustCompile("\\bpenso\\b"),
		regexp.MustCompile("\\bpensare\\b"),
		regexp.MustCompile("\\bpensando\\b"),
		regexp.MustCompile("\\briflettere\\b"),
	}}

	ThinkLevelMiddle = ThinkLevel{Level: "middle", TokenCount: 1e4, RegExps: []*regexp.Regexp{
		regexp.MustCompile("\\bthink about it\\b"),
		regexp.MustCompile("\\bthink a lot\\b"),
		regexp.MustCompile("\\bthink deeply\\b"),
		regexp.MustCompile("\\bthink hard\\b"),
		regexp.MustCompile("\\bthink more\\b"),
		regexp.MustCompile("\\bmegathink\\b"),
		regexp.MustCompile("もっと考えて"),
		regexp.MustCompile("たくさん考えて"),
		regexp.MustCompile("長考"),
		regexp.MustCompile("多想想"),
		regexp.MustCompile("好好想"),
		regexp.MustCompile("\\bpiensa\\b"),
		regexp.MustCompile("\\bréfléchis\\b"),
		regexp.MustCompile("\\bdenk nach\\b"),
		regexp.MustCompile("\\bdenk\\b"),
		regexp.MustCompile("많이 생각"),
		regexp.MustCompile("더 생각"),
		regexp.MustCompile("잘 생각"),
		regexp.MustCompile("\\bpensa\\b"),
		regexp.MustCompile("\\bpensa molto\\b"),
		regexp.MustCompile("\\brifletti\\b"),
	}}

	ThinkLevelHighest = ThinkLevel{Level: "highest", TokenCount: 31999, RegExps: []*regexp.Regexp{
		regexp.MustCompile("\\bthink harder\\b"),
		regexp.MustCompile("\\bthink intensely\\b"),
		regexp.MustCompile("\\bthink longer\\b"),
		regexp.MustCompile("\\bthink really hard\\b"),
		regexp.MustCompile("\\bthink super hard\\b"),
		regexp.MustCompile("\\bthink very hard\\b"),
		regexp.MustCompile("\\bultrathink\\b"),
		regexp.MustCompile("熟考"),
		regexp.MustCompile("深く考えて"),
		regexp.MustCompile("しっかり考えて"),
		regexp.MustCompile("多想一会"),
		regexp.MustCompile("深思"),
		regexp.MustCompile("仔细思考"),
		regexp.MustCompile("\\bpiensa más\\b"),
		regexp.MustCompile("\\bpiensa mucho\\b"),
		regexp.MustCompile("\\bpiensa profundamente\\b"),
		regexp.MustCompile("\\bréfléchis plus\\b"),
		regexp.MustCompile("\\bréfléchis beaucoup\\b"),
		regexp.MustCompile("\\bréfléchis profondément\\b"),
		regexp.MustCompile("\\bdenk mehr\\b"),
		regexp.MustCompile("\\bdenk gründlich\\b"),
		regexp.MustCompile("\\bdenk tief\\b"),
		regexp.MustCompile("더 오래 생각"),
		regexp.MustCompile("깊이 생각"),
		regexp.MustCompile("심사숙고"),
		regexp.MustCompile("곰곰이 생각"),
		regexp.MustCompile("\\bpensa di più\\b"),
		regexp.MustCompile("\\bpensa a lungo\\b"),
		regexp.MustCompile("\\bpensa profondamente\\b"),
		regexp.MustCompile("\\brifletti a fondo\\b"),
	}}
)

func GetThinkLevel(msg message.Message) ThinkLevel {
	textContent := ""
	for _, c := range msg.TextContents() {
		textContent += "|" + c.Text
	}

	if textContent == "" {
		return ThinkLevelNone
	}

	for _, exp := range ThinkLevelHighest.RegExps {
		if exp.MatchString(textContent) {
			return ThinkLevelHighest
		}
	}

	for _, exp := range ThinkLevelMiddle.RegExps {
		if exp.MatchString(textContent) {
			return ThinkLevelMiddle
		}
	}

	for _, exp := range ThinkLevelBasic.RegExps {
		if exp.MatchString(textContent) {
			return ThinkLevelBasic
		}
	}

	return ThinkLevelNone
}

package provider

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"time"

	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
	"github.com/openai/openai-go/shared"
	"github.com/qoder-ai/qodercli/core/internal/llm/converter"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/llm/provider"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
)

type ideaLabClient struct {
	options *provider.ClientBuilder
	client  openai.Client
}

type IdeaLabClient client

func newIdeaLabClient(opts *provider.ClientBuilder) IdeaLabClient {
	// OpenAI 客户端
	var openaiClientOptions []option.RequestOption
	// NOTE: 如果修改 IdeaLab Base URL，这里与 /status 的展示需要同步更新：
	// core/internal/llm/command/status.go -> buildStatusText()
	openaiClientOptions = append(openaiClientOptions, option.WithBaseURL("https://idealab-sg.alibaba-inc.com/api/openai/v1"))

	if opts.ApiKey != "" {
		openaiClientOptions = append(openaiClientOptions, option.WithAPIKey(opts.ApiKey))
	}

	client := openai.NewClient(openaiClientOptions...)
	return &ideaLabClient{
		options: opts,
		client:  client,
	}
}

func (o *ideaLabClient) convertTools(tools []tools.BaseTool) []openai.ChatCompletionToolParam {
	openaiTools := make([]openai.ChatCompletionToolParam, len(tools))

	for i, tool := range tools {
		info := tool.Info()
		openaiTools[i] = openai.ChatCompletionToolParam{
			Function: openai.FunctionDefinitionParam{
				Name:        info.Name,
				Description: openai.String(info.Description),
				Parameters: openai.FunctionParameters{
					"type":       "object",
					"properties": info.Parameters,
					"required":   info.Required,
				},
			},
		}
	}

	return openaiTools
}

func (o *ideaLabClient) finishReason(reason string) message.FinishReason {
	switch reason {
	case "stop":
		return message.FinishReasonEndTurn
	case "length":
		return message.FinishReasonMaxTokens
	case "tool_calls":
		return message.FinishReasonToolUse
	default:
		return message.FinishReasonUnknown
	}
}

func (o *ideaLabClient) preparedParams(tools []openai.ChatCompletionToolParam) openai.ChatCompletionNewParams {
	params := openai.ChatCompletionNewParams{
		Model: o.options.Model.APIModel,
		Tools: tools,
	}

	if o.options.Model.CanReason == true {
		params.MaxCompletionTokens = openai.Int(o.options.MaxTokens)
		params.MaxTokens = openai.Int(o.options.MaxTokens)
		switch o.options.ReasoningEffort {
		case provider.ReasoningEffortLow:
			params.ReasoningEffort = shared.ReasoningEffortLow
		case provider.ReasoningEffortMedium:
			params.ReasoningEffort = shared.ReasoningEffortMedium
		case provider.ReasoningEffortHigh:
			params.ReasoningEffort = shared.ReasoningEffortHigh
		default:
			params.ReasoningEffort = shared.ReasoningEffortMedium
		}
	} else {
		params.MaxCompletionTokens = openai.Int(o.options.MaxTokens)
		params.MaxTokens = openai.Int(o.options.MaxTokens)
	}

	return params
}

func (o *ideaLabClient) send(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (response *provider.Response, err error) {
	params := o.preparedParams(o.convertTools(tools))
	if o.options.Debug {
		jsonData, _ := json.Marshal(params)
		logging.Debug("Prepared messages", "messages", string(jsonData))
	}

	msgs := append([]interface{}{openai.SystemMessage(o.options.SystemMessage)}, o.convertAdaptedMessages(messages)...)
	attempts := 0
	for {
		attempts++
		openaiResponse, err := o.client.Chat.Completions.New(
			ctx,
			params,
			option.WithJSONSet("messages", msgs),
		)
		// If there is an error we are going to see if we can retry the call
		if err != nil {
			retry, after, retryErr := o.shouldRetry(attempts, err)
			if retryErr != nil {
				return nil, retryErr
			}
			if retry {
				logging.WarnPersist(fmt.Sprintf("Retrying due to rate limit... attempt %d of %d", attempts, maxRetries), logging.PersistTimeArg, time.Millisecond*time.Duration(after+100))
				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				case <-time.After(time.Duration(after) * time.Millisecond):
					continue
				}
			}
			return nil, retryErr
		}

		content := ""
		if openaiResponse.Choices[0].Message.Content != "" {
			content = openaiResponse.Choices[0].Message.Content
		}

		toolCalls := o.toolCalls(*openaiResponse)
		finishReason := o.finishReason(openaiResponse.Choices[0].FinishReason)

		if finishReason == message.FinishReasonMaxTokens {
			err = errors.New("reached max tokens limit")
		}

		return &provider.Response{
			Content:      content,
			ToolCalls:    toolCalls,
			Usage:        o.usage(*openaiResponse),
			FinishReason: finishReason,
		}, err
	}
}

func (o *ideaLabClient) convertUserMessage(msg message.Message) interface{} {
	var content []openai.ChatCompletionContentPartUnionParam
	for _, part := range msg.Parts {
		if r, ok := part.(message.ReminderContent); ok {
			textBlock := openai.ChatCompletionContentPartTextParam{Text: r.GetReminderContent()}
			content = append(content, openai.ChatCompletionContentPartUnionParam{OfText: &textBlock})
		} else if c, ok := part.(message.TextContent); ok {
			textBlock := openai.ChatCompletionContentPartTextParam{Text: c.String()}
			content = append(content, openai.ChatCompletionContentPartUnionParam{OfText: &textBlock})
		}
	}

	for _, binaryContent := range msg.BinaryContent() {
		imageURL := openai.ChatCompletionContentPartImageImageURLParam{URL: binaryContent.String(models.ProviderOpenAI)}
		imageBlock := openai.ChatCompletionContentPartImageParam{ImageURL: imageURL}

		content = append(content, openai.ChatCompletionContentPartUnionParam{OfImageURL: &imageBlock})
	}

	return openai.UserMessage(content)
}

func (o *ideaLabClient) convertToolMessages(msg message.Message) []interface{} {
	var converted []interface{}
	for _, result := range msg.ToolResults() {
		converted = append(converted, openai.ToolMessage(result.Content, result.ToolCallId))
	}
	return converted
}

// idealab的处理比较复杂，部分消息是anthropic格式，部分是openai格式，要做个规整
func (o *ideaLabClient) convertAdaptedMessages(messages []message.Message) []interface{} {
	var converted []interface{}
	for _, msg := range messages {
		switch msg.Role {
		case message.User:
			converted = append(converted, o.convertUserMessage(msg))
		case message.Assistant:
			m := converter.ConvertAnthropicLlmMessage(msg, false, true)
			for i := range m {
				converted = append(converted, m[i])
			}
		case message.Tool:
			converted = append(converted, o.convertToolMessages(msg)...)
		}
	}
	return converted
}

func (o *ideaLabClient) stream(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan provider.Event {
	params := o.preparedParams(o.convertTools(tools))
	params.StreamOptions = openai.ChatCompletionStreamOptionsParam{
		IncludeUsage: openai.Bool(true),
	}

	if o.options.Debug {
		jsonData, _ := json.Marshal(params)
		logging.Debug("Prepared messages", "messages", string(jsonData))
	}

	attempts := 0
	eventChan := make(chan provider.Event)
	// 根据最后一条消息的内容，确定思考等级
	thinkLevel := GetThinkLevel(messages[len(messages)-1])

	go func() {
		eventChan <- provider.Event{
			Type: provider.EventSending,
		}
		for {
			attempts++
			extendParams := map[string]interface{}{
				"system": []interface{}{
					map[string]interface{}{
						"cache_control": map[string]string{"type": "ephemeral"},
						"text":          o.options.SystemMessage,
						"type":          "text",
					},
				},
			}

			if thinkLevel.TokenCount > 0 {
				logging.Info("use thinking", "level", thinkLevel.Level, "tokens", thinkLevel.TokenCount)
				extendParams["thinking"] = map[string]interface{}{"budget_tokens": thinkLevel.TokenCount, "type": "enabled"}
			}

			openaiStream := o.client.Chat.Completions.NewStreaming(
				ctx,
				params,
				option.WithJSONSet("messages", o.convertAdaptedMessages(messages)),
				option.WithJSONSet("extendParams", extendParams),
			)

			acc := openai.ChatCompletionAccumulator{}
			currentContent := ""
			signature := ""
			toolCalls := make([]message.ToolCall, 0)
			receivingFlag := false
			for openaiStream.Next() {
				if !receivingFlag {
					eventChan <- provider.Event{
						Type: provider.EventReceiving,
					}
					receivingFlag = true
				}
				chunk := openaiStream.Current()

				logging.Info("IdeaLab LLM Data: " + chunk.RawJSON())

				acc.AddChunk(chunk)

				if len(chunk.Choices) == 0 {
					continue
				}

				if chunk.Choices[0].Delta.RawJSON() != "" {
					delta := map[string]interface{}{}
					err := json.Unmarshal([]byte(chunk.Choices[0].Delta.RawJSON()), &delta)
					if err != nil {
						logging.Error("Error unmarshalling choice delta", "chunk", chunk, "err", err)
						eventChan <- provider.Event{Type: provider.EventError, Error: err}
					}

					if delta["reasoning_content"] != nil {
						thinking := delta["reasoning_content"].(string)
						eventChan <- provider.Event{
							Type:    provider.EventThinkingDelta,
							Content: thinking,
						}
					}

					if delta["signature"] != nil {
						signature += delta["signature"].(string)
					}
				}

				for _, choice := range chunk.Choices {
					if choice.Delta.Content != "" {
						eventChan <- provider.Event{
							Type:    provider.EventContentDelta,
							Content: choice.Delta.Content,
						}
						currentContent += choice.Delta.Content
					}
				}
			}

			err := openaiStream.Err()
			if err == nil || errors.Is(err, io.EOF) {
				finishReason := o.finishReason(acc.ChatCompletion.Choices[0].FinishReason)
				if finishReason == message.FinishReasonToolUse {
					toolCalls = append(toolCalls, o.toolCalls(acc.ChatCompletion)...)
				} else if finishReason == message.FinishReasonMaxTokens {
					logging.Error("reaching max token")
					eventChan <- provider.Event{
						Type:  provider.EventError,
						Error: errors.New("reached max tokens limit"),
						Response: &provider.Response{
							Content:      currentContent,
							Signature:    signature,
							ToolCalls:    toolCalls,
							Usage:        o.usage(acc.ChatCompletion),
							FinishReason: finishReason,
						},
					}
					close(eventChan)
					return
				}

				eventChan <- provider.Event{
					Type: provider.EventComplete,
					Response: &provider.Response{
						Content:      currentContent,
						Signature:    signature,
						ToolCalls:    toolCalls,
						Usage:        o.usage(acc.ChatCompletion),
						FinishReason: finishReason,
					},
				}
				close(eventChan)
				return
			}

			logging.Warn("LLM returned error", "err", err)

			// If there is an error we are going to see if we can retry the call
			retry, after, retryErr := o.shouldRetry(attempts, err)
			if retryErr != nil {
				eventChan <- provider.Event{Type: provider.EventError, Error: retryErr}
				close(eventChan)
				return
			}
			if retry {
				logging.WarnPersist(fmt.Sprintf("Retrying due to rate limit... attempt %d of %d", attempts, maxRetries), logging.PersistTimeArg, time.Millisecond*time.Duration(after+100))
				select {
				case <-ctx.Done():
					if ctx.Err() == nil {
						eventChan <- provider.Event{Type: provider.EventError, Error: ctx.Err()}
					}
					close(eventChan)
					return
				case <-time.After(time.Duration(after) * time.Millisecond):
					continue
				}
			}
			eventChan <- provider.Event{Type: provider.EventError, Error: retryErr}
			close(eventChan)
			return
		}
	}()

	return eventChan
}

func (o *ideaLabClient) shouldRetry(attempts int, err error) (bool, int64, error) {
	var apierr *openai.Error
	if !errors.As(err, &apierr) {
		return false, 0, err
	}

	if apierr.StatusCode != 429 && apierr.StatusCode != 500 {
		return false, 0, err
	}

	if attempts > maxRetries {
		return false, 0, fmt.Errorf("maximum retry attempts reached for rate limit: %d retries", maxRetries)
	}

	retryMs := 0
	retryAfterValues := apierr.Response.Header.Values("Retry-After")

	backoffMs := 2000 * (1 << (attempts - 1))
	jitterMs := int(float64(backoffMs) * 0.2)
	retryMs = backoffMs + jitterMs
	if len(retryAfterValues) > 0 {
		if _, err := fmt.Sscanf(retryAfterValues[0], "%d", &retryMs); err == nil {
			retryMs = retryMs * 1000
		}
	}
	return true, int64(retryMs), nil
}

func (o *ideaLabClient) toolCalls(completion openai.ChatCompletion) []message.ToolCall {
	var toolCalls []message.ToolCall

	for i := range completion.Choices {
		for _, call := range completion.Choices[i].Message.ToolCalls {
			toolCall := message.ToolCall{
				Id:       call.ID,
				Name:     call.Function.Name,
				Input:    call.Function.Arguments,
				Type:     "function",
				Finished: true,
			}
			toolCalls = append(toolCalls, toolCall)
		}
	}

	return toolCalls
}

func (o *ideaLabClient) usage(completion openai.ChatCompletion) provider.TokenUsage {
	cachedTokens := completion.Usage.PromptTokensDetails.CachedTokens
	inputTokens := completion.Usage.PromptTokens - cachedTokens

	return provider.TokenUsage{
		InputTokens:         inputTokens,
		OutputTokens:        completion.Usage.CompletionTokens,
		CacheCreationTokens: 0, // OpenAI doesn't provide this directly
		CacheReadTokens:     cachedTokens,
	}
}

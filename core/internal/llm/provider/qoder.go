package provider

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/openai/openai-go"
	"github.com/qoder-ai/qodercli/core/internal/llm/converter"
	"github.com/qoder-ai/qodercli/core/llm/provider"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/qoder"
	"github.com/qoder-ai/qodercli/qoder/sse"
)

type QoderClient client

type qoderClient struct {
	options   *provider.ClientBuilder
	model     string
	reasoning bool
}

func newQoderClient(opts *provider.ClientBuilder) QoderClient {
	model := opts.Model.APIModel
	reasoning := opts.Model.CanReason

	return &qoderClient{
		options:   opts,
		model:     model,
		reasoning: reasoning,
	}
}

func (o *qoderClient) convertMessages(messages []message.Message) []*qoder.Message {
	var qoderMessages []*qoder.Message
	qoderMessages = append(qoderMessages, &qoder.Message{
		Role:    qoder.RoleTypeSystem,
		Content: o.options.SystemMessage,
	})

	for _, msg := range messages {
		qoderMessages = append(qoderMessages, converter.ConvertQoderLlmMessage(msg)...)
	}

	return qoderMessages
}

func (o *qoderClient) convertTools(tools []tools.BaseTool) []qoder.Tool {
	var qoderTools []qoder.Tool

	for _, tool := range tools {
		info := tool.Info()

		t := qoder.Tool{}
		t.Type = "function"
		t.Function = &qoder.FunctionDefinition{
			Name:        info.Name,
			Description: info.Description,
			Parameters: openai.FunctionParameters{
				"type":       "object",
				"properties": info.Parameters,
				"required":   info.Required,
			},
			Strict: false,
		}

		qoderTools = append(qoderTools, t)
	}

	return qoderTools
}

func (o *qoderClient) finishReason(reason string) message.FinishReason {
	switch reason {
	case "stop":
		return message.FinishReasonEndTurn
	case "length":
		return message.FinishReasonMaxTokens
	case "tool_calls":
		return message.FinishReasonToolUse
	default:
		return message.FinishReasonUnknown
	}
}

func (o *qoderClient) send(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (response *provider.Response, err error) {
	ch := o.stream(ctx, messages, tools)
	var pr *provider.Response
	for pe := range ch {
		pr = pe.Response
	}

	return pr, nil
}

func (o *qoderClient) stream(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan provider.Event {
	// 组装请求
	ask := &qoder.RemoteChatAsk{}
	var sessionId string
	var requestId string

	if messages[0].SessionId == "" {
		sessionId = uuid.NewString()
	} else {
		sessionId = messages[0].SessionId
	}

	requestId = uuid.NewString()
	ask.SessionId = sessionId
	ask.RequestId = requestId
	ask.RequestSetId = requestId
	ask.ChatRecordId = requestId

	ask.Stream = true
	ask.ChatTask = "FREE_INPUT"
	ask.ChatContext = map[string]interface{}{
		"text":               messages[len(messages)-1].Content(),
		"workspaceLanguages": []string{"java"},
		"language":           "",
		"localeLang":         "zh-cn",
		"preferredLanguage":  "zh-cn",
		"features":           []string{},
		"extra": map[string]interface{}{
			"context": []string{},
			"modelConfig": map[string]interface{}{
				"key":          o.model,
				"is_reasoning": o.reasoning,
			},
			"originalContent": messages[len(messages)-1].Content(),
		},
		"chatPrompt": "",
		"imageUrls":  nil,
	}
	ask.IsReply = true
	ask.Source = 1
	ask.Version = "3"
	ask.UserType = "personal_standard"
	ask.SessionType = "assistant"
	ask.AgentId = "agent_common"
	ask.TaskId = "common"
	ask.ModelConfig = qoder.ModelConfig{
		Key:            o.model,
		DisplayName:    o.model,
		Model:          "",
		Format:         "openai",
		IsVl:           false,
		IsReasoning:    o.reasoning,
		ApiKey:         "",
		Url:            "",
		Source:         "system",
		MaxInputTokens: 200000,
	}

	ask.Messages = o.convertMessages(messages)
	ask.Tools = o.convertTools(tools)

	// 发送请求
	req, err := qoder.BuildRequest(ask, nil)
	eventChan := make(chan provider.Event)
	processOver := false
	if err != nil {
		logging.Error("Fail to build qoder request", "err", err)
		return nil
	}

	logging.Info("Requesting: " + req.URL.String())
	logging.Info("RequestId: " + requestId)

	go func() {
		eventChan <- provider.Event{
			Type: provider.EventSending,
		}
		pe := provider.Event{}
		pr := &provider.Response{}
		signature := ""
		receivingFlag := false
		err = sse.NewClient("POST", nil).Subscribe(req, time.Second*280, func(msg *sse.Event) {
			logging.Info("Qoder LLM Data: " + string(msg.Data))
			logging.Info("Qoder LLM Event: " + string(msg.Event))
			if !receivingFlag {
				eventChan <- provider.Event{
					Type: provider.EventReceiving,
				}
				receivingFlag = true
			}

			if processOver {
				logging.Info("process is over, new message will be ignored")
				return
			}

			// error in event
			processOver = detectErrorEvent(msg, eventChan)
			if processOver {
				return
			}

			var response *qoder.ChatResponse
			response, processOver = parseResponse(msg, eventChan)
			if processOver {
				return
			}

			processOver = responseFailed(msg, response, eventChan)
			if processOver {
				return
			}

			bodyData := response.Body
			if bodyData == "[DONE]" || string(msg.Event) == "finish" {
				pr.Signature = signature
				eventChan <- provider.Event{
					Type:     provider.EventComplete,
					Response: pr,
				}
				close(eventChan)
				processOver = true
				return
			}

			var streamResponse qoder.StreamedChatResponsePayload
			err = json.NewDecoder(bytes.NewReader([]byte(bodyData))).Decode(&streamResponse)
			if err != nil {
				logging.Error("failed to decode stream payload", "err", err)
				return
			}

			processOver = streamResponseErrored(streamResponse, eventChan)
			if processOver {
				return
			}

			if streamResponse.Usage != nil {
				pr.Usage.OutputTokens = int64(streamResponse.Usage.CompletionTokens)
				pr.Usage.InputTokens = int64(streamResponse.Usage.PromptTokens)
			}

			if len(streamResponse.Choices) == 0 {
				return
			}
			choice := streamResponse.Choices[0]
			if choice.Delta.Content != "" {
				eventChan <- provider.Event{
					Type:    provider.EventContentDelta,
					Content: choice.Delta.Content,
				}
				pr.Content += choice.Delta.Content
			}

			if choice.Delta.ReasoningContent != "" {
				eventChan <- provider.Event{
					Type:    provider.EventThinkingDelta,
					Content: choice.Delta.ReasoningContent,
				}
				pe.Thinking += choice.Delta.ReasoningContent
			}

			if choice.FinishReason != "" {
				pr.FinishReason = o.finishReason(string(choice.FinishReason))
				if pr.FinishReason == message.FinishReasonMaxTokens {
					logging.Error("reaching max token")
					eventChan <- provider.Event{
						Type:     provider.EventError,
						Error:    errors.New("reached max tokens limit"),
						Response: pr,
					}
					processOver = true
					close(eventChan)
					return
				}
			}

			if choice.Delta.Signature != "" {
				signature += choice.Delta.Signature
			}

			if len(choice.Delta.ToolCalls) > 0 {
				ti := len(pr.ToolCalls) - 1
				call := choice.Delta.ToolCalls[0]
				if call.Id == "" {
					// lingma server可能有bug，在不返回id的情况下直接开始返回参数，这里先兼容下，少一个tool call 等后面的对话补偿
					if ti >= 0 {
						pr.ToolCalls[ti].Input = pr.ToolCalls[ti].Input + call.Function.Arguments
					}
				} else {
					pr.ToolCalls = append(pr.ToolCalls, message.ToolCall{
						Id:       call.Id,
						Name:     call.Function.Name,
						Input:    call.Function.Arguments,
						Type:     call.Type,
						Finished: true,
					})
				}

			}
		}, func() {
			logging.Error("request timed out", "requestId", requestId)
			if processOver {
				return
			}

			eventChan <- provider.Event{
				Type: provider.EventError, Error: errors.New("request timed out"),
			}

			processOver = true
			close(eventChan)
		})

		if err != nil {
			logging.Error("Fail to build qoder request", "err", err)
			if processOver {
				return
			}

			eventChan <- provider.Event{
				Type: provider.EventError, Error: err,
			}
			return
		}
	}()

	return eventChan
}

func detectErrorEvent(msg *sse.Event, eventChan chan provider.Event) bool {
	if string(msg.Event) == "error" {
		logging.Error("execute agent request error")

		eventChan <- provider.Event{
			Type: provider.EventError, Error: fmt.Errorf("execute agent request error"),
		}

		close(eventChan)
		return true
	}

	return false
}

func parseResponse(msg *sse.Event, eventChan chan provider.Event) (*qoder.ChatResponse, bool) {
	response := qoder.ChatResponse{}
	err := json.Unmarshal(msg.Data, &response)
	if err != nil {
		eventChan <- provider.Event{
			Type: provider.EventError, Error: fmt.Errorf("execute agent request unmarshal response error. error: %v, data: %s", err, msg.Data),
		}
		close(eventChan)
		return nil, true
	}
	return &response, false
}

func responseFailed(msg *sse.Event, response *qoder.ChatResponse, eventChan chan provider.Event) bool {
	if response.StatusCodeValue != 200 && string(msg.Event) != "finish" {
		m := response.Body
		eventChan <- provider.Event{
			Type: provider.EventError, Error: fmt.Errorf("execute agent request finished unexpected reason: %s, statusCode: %v", m, response.StatusCode),
		}
		close(eventChan)
		return true
	}

	return false
}

func streamResponseErrored(streamResponse qoder.StreamedChatResponsePayload, eventChan chan provider.Event) bool {
	if streamResponse.Error != nil {
		eventChan <- provider.Event{
			Type: provider.EventError, Error: streamResponse.Error,
		}
		close(eventChan)
		return true
	}

	return false
}

func (o *qoderClient) usage(completion openai.ChatCompletion) provider.TokenUsage {
	cachedTokens := completion.Usage.PromptTokensDetails.CachedTokens
	inputTokens := completion.Usage.PromptTokens - cachedTokens

	return provider.TokenUsage{
		InputTokens:         inputTokens,
		OutputTokens:        completion.Usage.CompletionTokens,
		CacheCreationTokens: 0, // OpenAI doesn't provide this directly
		CacheReadTokens:     cachedTokens,
	}
}

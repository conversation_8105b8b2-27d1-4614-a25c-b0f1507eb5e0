package provider

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/llm/provider"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	llmtools "github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/monitoring"
)

const maxRetries = 8

type client interface {
	send(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (*provider.Response, error)
	stream(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan provider.Event
}

type baseClient[C client] struct {
	client  C
	options *provider.ClientBuilder
}

func (c *baseClient[C]) cleanMessages(messages []message.Message) (cleaned []message.Message) {
	for _, msg := range messages {
		// The message has no content
		if len(msg.Parts) == 0 {
			continue
		}

		if len(msg.Parts) == 1 && msg.FinishPart() != nil {
			continue
		}
		cleaned = append(cleaned, msg)
	}
	return
}

func (c *baseClient[C]) SendMessages(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (*provider.Response, error) {
	messages = c.cleanMessages(messages)

	// 检查是否有有效的消息内容
	if len(messages) == 0 {
		return nil, fmt.Errorf("no valid messages to send")
	}

	start := time.Now()
	resp, err := c.client.send(ctx, messages, tools)
	// 统一 API 成功/错误埋点（运行级与会话级都在 monitoring 内处理）
	if sid, ok := ctx.Value(llmtools.SessionIDContextKey).(string); ok && sid != "" {
		if err == nil && resp != nil {
			usage := resp.Usage
			model := c.options.Model
			cost := model.CostPer1MInCached/1e6*float64(usage.CacheCreationTokens) +
				model.CostPer1MOutCached/1e6*float64(usage.CacheReadTokens) +
				model.CostPer1MIn/1e6*float64(usage.InputTokens) +
				model.CostPer1MOut/1e6*float64(usage.OutputTokens)
			monitoring.RecordAPIRequest(ctx, sid, model, usage, time.Since(start), cost)
		} else if err != nil {
			monitoring.RecordAPIError(ctx, sid, c.options.Model, err, 0, time.Since(start), 1)
		}
	}
	return resp, err
}

func (c *baseClient[C]) StreamResponse(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan provider.Event {
	messages = c.cleanMessages(messages)
	start := time.Now()
	inner := c.client.stream(ctx, messages, tools)
	out := make(chan provider.Event)
	go func() {
		defer close(out)
		for ev := range inner {
			// 透传
			out <- ev
			// 仅在完成/错误时埋点
			if sid, ok := ctx.Value(llmtools.SessionIDContextKey).(string); ok && sid != "" {
				switch ev.Type {
				case provider.EventComplete:
					if ev.Response != nil {
						usage := ev.Response.Usage
						model := c.options.Model
						cost := model.CostPer1MInCached/1e6*float64(usage.CacheCreationTokens) +
							model.CostPer1MOutCached/1e6*float64(usage.CacheReadTokens) +
							model.CostPer1MIn/1e6*float64(usage.InputTokens) +
							model.CostPer1MOut/1e6*float64(usage.OutputTokens)
						monitoring.RecordAPIRequest(ctx, sid, model, usage, time.Since(start), cost)
					}
				case provider.EventError:
					if ev.Error != nil {
						monitoring.RecordAPIError(ctx, sid, c.options.Model, ev.Error, 0, time.Since(start), 1)
					}
				}
			}
		}
	}()
	return out
}

func (c *baseClient[C]) GetModel() models.Model {
	return c.options.Model
}

type service struct {
}

func (s *service) NewClient(providerName models.ModelProvider, opts ...provider.ClientOption) (provider.Client, error) {
	builder := &provider.ClientBuilder{}
	for _, o := range opts {
		o(builder)
	}

	switch providerName {
	case models.ProviderOpenAI:
		builder.BaseUrl = os.Getenv("QODER_OPENAI_BASE_URL")
		return &baseClient[OpenAIClient]{
			options: builder,
			client:  newOpenAIClient(builder),
		}, nil
	case models.ProviderIdeaLab:
		return &baseClient[AnthropicClient]{
			options: builder,
			client:  newAnthropicClient(builder),
		}, nil
		//return &baseClient[IdeaLabClient]{
		//	options: builder,
		//	client:  newIdeaLabClient(builder),
		//}, nil
	case models.ProviderDashScope:
		// NOTE: 如果修改 DashScope Base URL，这里与 /status 的展示需要同步更新：
		// core/internal/llm/command/status.go -> buildStatusText()
		builder.BaseUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1"
		return &baseClient[OpenAIClient]{
			options: builder,
			client:  newOpenAIClient(builder),
		}, nil
	case models.ProviderQoder:
		return &baseClient[QoderClient]{
			options: builder,
			client:  newQoderClient(builder),
		}, nil
	default:
		panic("not implemented")
	}
}

func NewService() provider.Service {
	return &service{}
}

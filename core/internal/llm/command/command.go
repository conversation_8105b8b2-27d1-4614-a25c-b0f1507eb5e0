package command

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/llm/output_style"
	"github.com/qoder-ai/qodercli/core/llm/shell"

	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/pubsub"

	"github.com/adrg/frontmatter"
	"github.com/bmatcuk/doublestar/v4"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/config"
	internalpkg "github.com/qoder-ai/qodercli/core/internal"
	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/monitoring"
	"github.com/qoder-ai/qodercli/core/utils"
)

// https://docs.anthropic.com/en/docs/claude-code/slash-commands#custom-slash-commands

var commandCaveat = "Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."

var commandMessage = `<command-message>%s</command-message>
<command-name>%s</command-name>`

const initCmdPrompt = `Please analyze this codebase and create a ` + core.MemoryFileName + ` file, which will be given to future instances of ` + core.AppName + ` to operate in this repository.
            
What to add:
1. Commands that will be commonly used, such as how to build, lint, and run tests. Include the necessary commands to develop in this codebase, such as how to run a single test.
2. High-level code architecture and structure so that future instances can be productive more quickly. Focus on the "big picture" architecture that requires reading multiple files to understand

Usage notes:
- If there's already a ` + core.MemoryFileName + `, suggest improvements to it.
- When you make the initial ` + core.MemoryFileName + `, do not repeat yourself and do not include obvious instructions like "Provide helpful error messages to users", "Write unit tests for all new utilities", "Never include sensitive information (API keys, tokens) in code or commits" 
- Avoid listing every component or file structure that can be easily discovered
- Don't include generic development practices
- If there are Cursor rules (in .cursor/rules/ or .cursorrules) or Copilot rules (in .github/copilot-instructions.md), make sure to include the important parts.
- If there is a README.md, make sure to include the important parts. 
- Do not make up information such as "Common Development Tasks", "Tips for Development", "Support and Documentation" unless this is expressly included in other files that you read.
- Be sure to prefix the file with the following text:

` + "```" + `
# ` + core.MemoryFileName + `

This file provides guidance to ` + core.AppName + ` (` + core.MainDomainName + `) when working with code in this repository.
` + "```" + ``

type AgentGenerator interface {
	GenerateAgent(ctx context.Context, sessionId string, requirement string, location agent.SubAgentLocation) error
}

type CommandGenerator interface {
	GenerateCommand(ctx context.Context, sessionId string, requirement string, location command.Location) error
}
type tuple struct {
	location command.Location
	prefix   string
}

func loadFromDir(prefix string, location command.Location) ([]command.Command, error) {
	matches, err := doublestar.Glob(os.DirFS(prefix), "**/*.md")

	if err != nil {
		return nil, err
	}

	var commands []command.Command
	for _, match := range matches {
		cmd, err := parseSingleSlashCommand(location, filepath.Join(prefix, match), match)
		if err != nil {
			return nil, err
		}

		commands = append(commands, *cmd)
	}

	return commands, nil

}

func parseSingleSlashCommand(location command.Location, path string, relativePath string) (*command.Command, error) {
	f, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cmd := parseContent(f)
	cmd.Location = location
	cmd.Name = strings.ReplaceAll(
		strings.TrimSuffix(
			strings.TrimPrefix(relativePath, string(filepath.Separator)),
			filepath.Ext(relativePath)),
		string(filepath.Separator), ":")
	cmd.ID = "/" + cmd.Name
	cmd.Description = fmt.Sprintf("%s(%s)", cmd.Description, location)
	cmd.Type = command.TypePrompt
	return cmd, nil
}

func parseContent(reader io.Reader) *command.Command {
	cmd := &command.Command{}
	rest, err := frontmatter.Parse(reader, cmd)
	if err != nil {
		logging.Error("fail to parse command as frontmatter, treat it as string")
		cmd.Content = string(rest)
		return cmd
	}

	cmd.Content = string(rest)
	return cmd
}

type CommandService struct {
	*pubsub.Broker[command.Event]
	cfg              *config.Service
	Sessions         *internalpkg.SessionService
	agents           agent.SubAgentService
	agentGenerator   AgentGenerator
	commandGenerator CommandGenerator
	shells           shell.ShellService
	outputStyles     output_style.OutputStyleService
}

func NewCommandService(app llm.AppSupport, ag AgentGenerator, cg CommandGenerator) *CommandService {
	return &CommandService{
		Broker:           pubsub.NewBroker[command.Event](),
		cfg:              app.GetConfig(),
		agents:           app.GetAgentService(),
		shells:           app.GetShellService(),
		outputStyles:     app.GetOutputStyleService(),
		agentGenerator:   ag,
		commandGenerator: cg,
	}
}

func (c *CommandService) LoadAllCommands() ([]command.Command, error) {
	var commands []command.Command

	tuples := []tuple{{
		location: command.LocationProject,
		prefix:   filepath.Join(utils.GetWorkspaceStorageDir(c.cfg.WorkingDir), "commands"),
	}, {
		location: command.LocationUser,
		prefix:   filepath.Join(utils.GetUserStorageDir(), "commands"),
	}, {
		location: command.LocationClaudeCodeProject,
		prefix:   filepath.Join(utils.GetClaudeCodeWorkspaceStorageDir(c.cfg.WorkingDir), "commands"),
	}, {
		location: command.LocationClaudeCodeUser,
		prefix:   filepath.Join(utils.GetClaudeCodeUserStorageDir(), "commands"),
	}}

	for _, t := range tuples {
		cmds, err := loadFromDir(t.prefix, t.location)
		if err != nil {
			return nil, err
		}

		commands = append(commands, cmds...)
	}

	commands = append(commands, c.builtInSlashCommands()...)
	commands = append(commands, c.buildInPlusCommands()...)
	return commands, nil
}

func (c *CommandService) ListAllCommands() ([]command.Command, error) {
	// fixme 如果有性能问题，则需要考虑缓存
	// 因为重复从磁盘上读取了数据，实际上第一次读取后保存在内存里即可快速复用。 目前先不实现。
	return c.LoadAllCommands()
}

func (c *CommandService) builtInSlashCommands() []command.Command {
	return []command.Command{
		{
			ID:          "/init",
			Name:        "init",
			Description: fmt.Sprintf("Initialize a new %s file with codebase documentation", core.MemoryFileName),
			Content:     initCmdPrompt,
			Location:    command.LocationBuiltIn,
			Type:        command.TypePrompt,
		},
		{
			ID:          "/help",
			Name:        "help",
			Description: "Show help and available commands",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run:         c.ReturnHelpContent,
		},
		{
			ID:          "/output-style",
			Name:        "output-style",
			Description: "Set the output style directly or from a selection menu",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run:         c.ListOutputStyles,
		},
		{
			ID:          "/agents",
			Name:        "agents",
			Description: "Manage agent configurations",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run:         c.ManageAgents,
		},
		{
			ID:          "/bashes",
			Name:        "bashes",
			Description: "List and manage background bash shells",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run:         c.ListBashes,
		},
		{
			ID:           "/compact",
			Name:         "compact",
			Description:  "Summarize current session to compact the context (usage: /compact [instructions])",
			Content:      "",
			Location:     command.LocationBuiltIn,
			Type:         command.TypeEvent,
			RunWithEvent: c.executeCompactCommand,
		},
		{
			ID:          "/status",
			Name:        "status",
			Description: "Show qodercli status including version, model, account, API connectivity, and tool statuses",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run: func(ctx context.Context, sessionId string, args string) (string, error) {
				return buildStatusParts(c.cfg, sessionId)
			},
		},
		{
			ID:           "/clear",
			Name:         "clear",
			Description:  "Clear conversation history and free up context",
			Content:      "",
			Location:     command.LocationBuiltIn,
			Type:         command.TypeEvent,
			RunWithEvent: c.RunClearWithEvent,
		},
		{
			ID:           "/resume",
			Name:         "resume",
			Description:  "Resume a conversation (list sessions when no args)",
			Content:      "",
			Location:     command.LocationBuiltIn,
			Type:         command.TypeEvent,
			RunWithEvent: c.RunResumeWithEvent,
		},
		{
			ID:          "/cost",
			Name:        "cost",
			Description: "Show current run cost and usage summary",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run: func(ctx context.Context, sessionId string, args string) (string, error) {
				return buildRunSummaryParts(), nil
			},
		},
		{
			ID:           "/pr-review",
			Name:         "pr-review",
			Description:  "Launch PR reviewer agent to analyze pull requests (usage: /pr-review [PR_number])",
			Content:      "Please use the Task tool to launch the pr-reviewer agent for PR review. Pass any arguments (like PR numbers) directly to the subagent by including them in the prompt parameter.\n\nUse: Task(description=\"PR review\", prompt=\"Review pull request with arguments: $ARGUMENTS\", subagent_type=\"pr-reviewer\")",
			Location:     command.LocationBuiltIn,
			Type:         command.TypePrompt,
			AllowedTools: "Task", // 只允许使用Task工具，确保只能触发subagent
		},
		{
			ID:           "/login",
			Name:         "login",
			Description:  "Sign in with your Qoder account",
			Content:      "",
			Location:     command.LocationBuiltIn,
			Type:         command.TypeEvent,
			RunWithEvent: c.LoginWithEvent,
		},
		{
			ID:          "/bug",
			Name:        "bug",
			Description: "Submit feedback about Qoder CLI",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run:         c.Feedback,
		},
		{
			ID:          "/install-github-app",
			Name:        "install-github-app",
			Description: "Set up Qoder GitHub Actions for a repository",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeEvent,
			RunWithEvent: func(ctx context.Context, sessionId string, args string, output chan command.Event) {
				output <- command.Event{
					CommandName: "install-github-app",
					Args:        args,
					Payload:     "start",
					InputChan:   nil,
				}

				close(output)
			},
		},
		{
			ID:          "/release-notes",
			Name:        "release-notes",
			Description: "View release notes",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run:         c.ReleaseNotes,
		},
		{
			ID:           "/vim",
			Name:         "vim",
			Description:  "Open external editor for input",
			Content:      "",
			Location:     command.LocationBuiltIn,
			Type:         command.TypeEvent,
			RunWithEvent: c.executeVimCommand,
		},
		{
			ID:           "/memory",
			Name:         "memory",
			Description:  "Edit memory files",
			Content:      "",
			Location:     command.LocationBuiltIn,
			Type:         command.TypeEvent,
			RunWithEvent: c.executeMemoryCommand,
		},
		{
			ID:          "/pr-comments",
			Name:        "pr-comments",
			Description: "Guide the agent to fetch GitHub PR comments (usage: /pr-comments [owner/repo#PR|PR])",
			Content:     "Goal: Fetch GitHub Pull Request comments and present them in a concise Markdown table.\n\nInstructions:\n1) Resolve repository and PR number first:\n   - If input matches 'owner/repo#123', use that directly.\n   - Else if input is just a number like '123', infer 'owner/repo' from the current working directory using Bash:\n     a. Check if inside a git repo: git rev-parse --is-inside-work-tree; if not, respond '(not a git repository; provide owner/repo#PR)'.\n     b. Try to get remote: git remote get-url origin || git remote get-url upstream.\n     c. If both fail but gh exists, try: gh repo view --json owner,name --jq '.owner.login+\"/\"+.name'.\n     d. Normalize owner/repo from either '**************:owner/repo(.git)?' or 'https://github.com/owner/repo(.git)?'.\n     e. If still cannot infer, respond '(cannot infer repository; provide owner/repo#PR)'.\n\n2) Prefer Bash tool with gh if available:\n   - Detect gh availability via: command -v gh || type -p gh\n   - If available, run:\n     gh api repos/<owner>/<repo>/issues/<PR>/comments --paginate --jq \".[] | {author: .user.login, created_at: .created_at, body: .body}\"\n   - Parse JSON lines into rows: author, created_at, body.\n\n3) Fallback to WebFetch if gh is unavailable:\n   - GET https://api.github.com/repos/<owner>/<repo>/issues/<PR>/comments\n   - If env GITHUB_TOKEN exists, include header: Authorization: Bearer $GITHUB_TOKEN\n   - Accept: application/vnd.github+json\n   - Extract: .[].user.login, .[].created_at, .[].body\n\n4) Output formatting rules:\n   - Return only a Markdown table with header: | Author | Created | Comment |\n   - Escape '|' in body as '\\|' and compress newlines to spaces.\n   - Truncate body to ~200 chars per row, append '…' if truncated.\n   - If there are no comments, return '(no comments)'.\n   - Read-only operations only; do not mutate anything.\n\nReturn: Only the final table or the single-line status.",
			Location:    command.LocationBuiltIn,
			Type:        command.TypePrompt,
			GetPrompt:   c.getPrCommentsPrompt,
		},
	}
}

func (c *CommandService) buildInPlusCommands() []command.Command {
	return []command.Command{
		{
			ID:          "+agent",
			Name:        "agent",
			Description: "Add a new subagent (usage: +agent [user/project] \"Analyze the git changes and generate a standard git commit message\")",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run:         c.GenerateAgent,
		},
		{
			ID:          "+command",
			Name:        "command",
			Description: "Add a new slash command (usage: +command [user/project] \"Automatically view all git changes and make a good commit\")",
			Content:     "",
			Location:    command.LocationBuiltIn,
			Type:        command.TypeLocal,
			Run:         c.GenerateCommand,
		},
	}
}

func (c *CommandService) GetCaveat() []message.ContentPart {
	parts := []message.ContentPart{
		message.TextContent{Text: commandCaveat, Type: "text"},
	}
	return parts
}

func (c *CommandService) GetCommandInfoPart(cmd command.Command, msg, args string) message.ContentPart {
	cmdMsg := fmt.Sprintf(commandMessage, msg, cmd.ID)
	if args != "" {
		cmdMsg += fmt.Sprintf("\n<command-args>%s</command-args>", args)
	}
	return message.TextContent{Text: cmdMsg, Type: "text"}
}

func (c *CommandService) GetUserContentPartsForPrompt(ctx context.Context, cmd command.Command, sessionId string, args string) (string, []message.ContentPart) {
	content := ""
	parts := []message.ContentPart{
		c.GetCommandInfoPart(cmd, cmd.Name+" is running...", args),
	}
	if cmd.Name != "init" {
		parts = append([]message.ContentPart{
			message.TextContent{Text: commandCaveat, Type: "text"},
		}, parts...)
	}

	// 如果有写这个hook，优先从hook返回内容，否则直接用content和参数转换
	if cmd.GetPrompt != nil {
		content = cmd.GetPrompt(ctx, sessionId, args)
	} else {
		content = strings.ReplaceAll(cmd.Content, "$ARGUMENTS", args)
		cmdMsg := fmt.Sprintf(commandMessage, cmd.Name, cmd.ID)
		if args != "" {
			cmdMsg += fmt.Sprintf("\n<command-args>%s</command-args>", args)
		}
	}

	parts = append(parts, message.TextContent{Text: content, Type: "text"})

	return content, parts
}

// buildRunSummaryParts 生成"本次运行"统计信息（用于 /cost）
func buildRunSummaryParts() string {
	sum := monitoring.GetRunSummary()
	wall := monitoring.GetRunWallDuration()
	apiSecs := float64(sum.TotalAPIDurationMs) / 1000.0

	builder := &strings.Builder{}
	// 暂时隐藏cost信息，保留代码以备后用
	// fmt.Fprintf(builder, "Total cost:            $%.4f\n", sum.TotalCostUSD)
	fmt.Fprintf(builder, "Total duration (API):  %.1fs\n", apiSecs)
	fmt.Fprintf(builder, "Total duration (wall): %.1fs\n", wall.Seconds())
	fmt.Fprintf(builder, "Total code changes:    %d lines added, %d lines removed\n", sum.TotalLinesAdded, sum.TotalLinesRemoved)
	// 暂时隐藏Usage by model信息，保留代码以备后用
	// if len(sum.ModelUsage) > 0 {
	// 	fmt.Fprintln(builder, "Usage by model:")
	// 	keys := make([]string, 0, len(sum.ModelUsage))
	// 	for k := range sum.ModelUsage {
	// 		keys = append(keys, k)
	// 	}
	// 	sort.Strings(keys)
	// 	for _, model := range keys {
	// 		usage := sum.ModelUsage[model]
	// 		fmt.Fprintf(builder, "    %s:  %d input, %d output, %d cache read, %d cache write\n",
	// 			model,
	// 			usage.InputTokens,
	// 			usage.OutputTokens,
	// 			usage.CacheReadTokens,
	// 			usage.CacheCreationTokens,
	// 		)
	// 	}
	// }

	text := "```\n" + builder.String() + "```\n"
	return text
}

// executeCompactCommand executes compact command using RunWithEvent pattern
func (c *CommandService) executeCompactCommand(ctx context.Context, sessionId string, args string, output chan command.Event) {
	// 发送开始压缩的状态事件
	output <- command.Event{
		CommandName: "compact",
		Payload: map[string]interface{}{
			"type":    "status",
			"message": "Starting session context compression...",
		},
		SessionId: sessionId,
	}

	// 创建一个用于等待压缩完成的通道
	doneChan := make(chan bool)
	errorChan := make(chan error)

	// 启动后台压缩任务
	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("压缩过程出现异常: %v", r)
			}
		}()

		// 这里我们需要访问 agent，但是 CommandService 没有直接的 agent 引用
		// 我们通过发布一个特殊事件，让 agent 订阅者处理实际的压缩工作
		c.Publish(pubsub.CreatedEvent, command.Event{
			CommandName: "compact_internal",
			Args:        args,
			SessionId:   sessionId,
			Payload: map[string]interface{}{
				"done_chan":  doneChan,
				"error_chan": errorChan,
			},
		})
	}()

	// 等待压缩完成或出错，不进行定期推送
	startTime := time.Now()

	select {
	case <-ctx.Done():
		output <- command.Event{
			CommandName: "compact",
			Payload: map[string]interface{}{
				"type":    "error",
				"message": "Compression was cancelled",
			},
			SessionId: sessionId,
		}
		close(output)
		return

	case err := <-errorChan:
		output <- command.Event{
			CommandName: "compact",
			Payload: map[string]interface{}{
				"type":    "error",
				"message": err.Error(),
			},
			SessionId: sessionId,
		}
		close(output)
		return

	case <-doneChan:
		elapsed := time.Since(startTime)
		durationStr := elapsed.String()
		logging.Info("Compact command completed", "elapsed", durationStr, "seconds", int(elapsed.Seconds()))
		output <- command.Event{
			CommandName: "compact",
			Payload: map[string]interface{}{
				"type":     "complete",
				"message":  "Session context compression completed",
				"duration": durationStr,
			},
			SessionId: sessionId,
		}
		close(output)
		return
	}
}

func (c *CommandService) GenerateCommand(ctx context.Context, sessionId string, args string) (string, error) {
	example := "example: `+command [user/project] \"Automatically view all git changes and make a good commit\"`"
	sp := strings.SplitN(args, " ", 2)

	location := command.LocationUser
	requirement := ""
	if len(sp) == 0 {
		return "Insufficient args, " + example, nil
	} else if len(sp) == 1 {
		requirement = sp[0]
	} else if len(sp) == 2 {
		if sp[0] != "user" && sp[0] != "project" && !strings.Contains(sp[0], "\"") {
			return "Unknown location: **" + sp[0] + "**, accepted values are `user` or `project`.", nil
		} else if strings.Contains(sp[0], "\"") {
			requirement = strings.Join(sp[0:], " ")
		} else {
			location = command.Location(sp[0])
			requirement = sp[1]
		}
	}
	if strings.TrimSpace(requirement) == "" {
		return "Empty requirement, " + example, nil
	}
	_ = location
	if err := c.commandGenerator.GenerateCommand(ctx, sessionId, strings.Trim(requirement, "\""), location); err != nil {
		return "", err
	}
	return "", nil
}

// getPrCommentsPrompt 动态生成 pr-comments 命令的提示信息
func (c *CommandService) getPrCommentsPrompt(ctx context.Context, sessionId string, args string) string {
	baseContent := `You are an AI assistant integrated into a git-based version control system. Your task is to fetch and display comments from a GitHub pull request.

Follow these steps:

1. Use ` + "`" + `gh pr view --json number,headRepository` + "`" + ` to get the PR number and repository info
2. Use ` + "`" + `gh api /repos/{owner}/{repo}/issues/{number}/comments` + "`" + ` to get PR-level comments
3. Use ` + "`" + `gh api /repos/{owner}/{repo}/pulls/{number}/comments` + "`" + ` to get review comments. Pay particular attention to the following fields: ` + "`" + `body` + "`" + `, ` + "`" + `diff_hunk` + "`" + `, ` + "`" + `path` + "`" + `, ` + "`" + `line` + "`" + `, etc. If the comment references some code, consider fetching it using eg ` + "`" + `gh api /repos/{owner}/{repo}/contents/{path}?ref={branch} | jq .content -r | base64 -d` + "`" + `
4. Parse and format all comments in a readable way
5. Return ONLY the formatted comments, with no additional text

Format the comments as:

## Comments

[For each comment thread:]
- <AUTHOR>
  ` + "```" + `diff
  [diff_hunk from the API response]
  ` + "```" + `
  > quoted comment text
  
  [any replies indented]

If there are no comments, return "No comments found."

Remember:
1. Only show the actual comments, no explanatory text
2. Include both PR-level and code review comments
3. Preserve the threading/nesting of comment replies
4. Show the file and line number context for code review comments
5. Use jq to parse the JSON responses from the GitHub API
`

	// 如果有用户输入参数，添加 "Additional user input" 部分
	if strings.TrimSpace(args) != "" {
		baseContent += "Additional user input: " + args + "\n"
	}

	return baseContent
}

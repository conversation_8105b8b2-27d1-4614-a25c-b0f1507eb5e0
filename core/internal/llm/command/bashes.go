package command

import (
	"context"
	"fmt"
	"strings"
)

func (c *CommandService) ListBashes(ctx context.Context, sessionId string, args string) (string, error) {
	var lines []string
	bashId := strings.TrimSpace(args)
	if len(bashId) == 0 {
		list := c.shells.ListShells()
		for index, sh := range list {
			result := sh.GetResult()
			lines = append(lines, fmt.Sprintf("%d. %s (%s | %s)", index+1, result.Command, result.BashId, result.Status))
		}
		if len(lines) == 0 {
			lines = append(lines, "No background shells currently running")
		}
		lines = append(lines, "---\nYou can check the details of background tasks using the `/bashes {id}` command, such as `/bashes bash-1`")
		return strings.Join(lines, "\n"), nil
	} else if sh, exists := c.shells.GetShell(bashId); exists {
		result := sh.GetResult()
		lines = append(lines, fmt.Sprintf("BashId: %s", result.BashId))
		lines = append(lines, fmt.Sprintf(" | Status: %s", result.Status))
		lines = append(lines, fmt.Sprintf(" | Command: %s", result.Command))
		if result.ExitCode != nil {
			lines = append(lines, fmt.Sprintf(" | ExitCode: %d\n", *result.ExitCode))
		}
		lines = append(lines, "\n")
		lines = append(lines, fmt.Sprintf("Stdout: %s", result.Stdout))
		return strings.Join(lines, "\n"), nil
	}
	return fmt.Sprintf("Background shell with id %s not found", bashId), nil
}

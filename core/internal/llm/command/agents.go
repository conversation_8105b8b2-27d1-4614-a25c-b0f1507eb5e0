package command

import (
	"context"
	"fmt"
	"strings"

	"github.com/qoder-ai/qodercli/core/llm/agent"
)

func (c *CommandService) ManageAgents(ctx context.Context, sessionId string, args string) (string, error) {
	if strings.TrimSpace(args) == "" {
		content := ""
		agents, err := c.agents.List()
		if err != nil {
			return "", err
		}

		var userAgents []agent.SubAgent
		var projectAgents []agent.SubAgent
		var externalAgents []agent.SubAgent
		var builtinAgents []agent.SubAgent

		for i := range agents {
			switch agents[i].Location {
			case agent.UserSubAgentLocation:
				userAgents = append(userAgents, agents[i])
			case agent.ProjectSubAgentLocation:
				projectAgents = append(projectAgents, agents[i])
			case agent.ClaudeCodeUserSubAgentLocation, agent.ClaudeCodeProjectSubAgentLocation:
				externalAgents = append(externalAgents, agents[i])
			case agent.BuiltInSubAgentLocation:
				builtinAgents = append(builtinAgents, agents[i])
			}
		}

		if len(userAgents) > 0 {
			content += fmt.Sprintf("# User agents\n")
			for _, a := range userAgents {
				desc := a.Description
				if len(desc) > 120 {
					desc = desc[:120] + "..."
				}
				content += fmt.Sprintf("- **%s**: %s\n", a.Name, desc)
			}
		}

		if len(projectAgents) > 0 {
			content += fmt.Sprintf("# Project agents\n")
			for _, a := range projectAgents {
				desc := a.Description
				if len(desc) > 120 {
					desc = desc[:120] + "..."
				}
				content += fmt.Sprintf("- **%s**: %s\n", a.Name, desc)
			}
		}

		if len(externalAgents) > 0 {
			content += fmt.Sprintf("# External agents\n")
			for _, a := range externalAgents {
				desc := a.Description
				if len(desc) > 120 {
					desc = desc[:120] + "..."
				}
				content += fmt.Sprintf("- **%s**: %s\n", a.Name, desc)
			}
		}

		if len(builtinAgents) > 0 {
			content += fmt.Sprintf("# Builtin agents\n")
			for _, a := range builtinAgents {
				desc := a.Description
				if len(desc) > 120 {
					desc = desc[:120] + "..."
				}
				content += fmt.Sprintf("- **%s**: %s\n", a.Name, desc)
			}
		}

		content += "---\n"
		content += "You can automatically create new agents with Qoder using `/agents create` command, usage as below:\n"
		content += " - ```/agents create [user(default)|project] \"{description}\"``` "
		content += "| `description` is what this agent should do and when it should be used (be comprehensive for best results)\n\n"
		content += "Example:\n\n"
		content += " - ```/agents create \"Expert software engineer that helps review my code based on best practices…\"```"
		return content, nil
	} else {
		example := "example: `/agents create \"Expert software engineer that helps review my code based on best practices…\"`"
		sp := strings.SplitN(args, " ", 3)
		if len(sp) < 2 {
			return "Insufficient args, " + example, nil
		}

		if sp[0] != "create" {
			return "Unknown command: **" + sp[0] + "**, " + example, nil
		}

		location := agent.UserSubAgentLocation
		requirement := ""

		if len(sp) > 2 {
			if sp[1] != "user" && sp[1] != "project" && !strings.Contains(sp[1], "\"") {
				return "Unknown location: **" + sp[1] + "**, accepted values are `user` or `project`.", nil
			} else if strings.Contains(sp[1], "\"") {
				requirement = strings.Join(sp[1:], " ")
			} else {
				location = agent.SubAgentLocation(sp[1])
				requirement = sp[2]
			}
		} else {
			requirement = sp[1]
		}

		err := c.agentGenerator.GenerateAgent(ctx, sessionId, strings.Trim(requirement, "\""), location)
		if err != nil {
			return "", err
		}
	}
	return "", nil
}

func (c *CommandService) GenerateAgent(ctx context.Context, sessionId string, args string) (string, error) {
	example := "example: `+agent [user/project(Optional, default: user)] \"Expert software engineer that helps review my code based on best practices…\"`"
	sp := strings.SplitN(args, " ", 2)

	location := agent.UserSubAgentLocation
	requirement := ""
	if len(sp) == 0 {
		return "Insufficient args, " + example, nil
	} else if len(sp) == 1 {
		requirement = sp[0]
	} else if len(sp) == 2 {
		if sp[0] != "user" && sp[0] != "project" && !strings.Contains(sp[0], "\"") {
			return "Unknown location: **" + sp[0] + "**, accepted values are `user` or `project`.", nil
		} else if strings.Contains(sp[0], "\"") {
			requirement = strings.Join(sp[0:], " ")
		} else {
			location = agent.SubAgentLocation(sp[0])
			requirement = sp[1]
		}
	}
	if strings.TrimSpace(requirement) == "" {
		return "Empty requirement, " + example, nil
	}
	if err := c.agentGenerator.GenerateAgent(ctx, sessionId, strings.Trim(requirement, "\""), location); err != nil {
		return "", err
	}
	return "", nil
}

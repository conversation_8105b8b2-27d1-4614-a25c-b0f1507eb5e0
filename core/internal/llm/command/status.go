package command

import (
	"fmt"
	"strings"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/version"
	"github.com/qoder-ai/qodercli/qoder"
)

// buildStatusParts 将 /status 的输出按行拆分为多个 TextContent，方便 TUI 渲染
func buildStatusParts(cfg *config.Service, sessionId string) (string, error) {
	var b strings.Builder
	// Markdown 分段辅助：两个换行作为段落分隔
	br := func() { b.WriteString("\n\n") }

	// 顶部：版本 + Session ID 同一部分（同一行展示版本，下一行展示 Session ID）
	b.WriteString(fmt.Sprintf("**%s**   %s\n: ", strings.Title(core.ReleaseName), version.Version))
	if sessionId != "" {
		// 使用非代码文本，避免 Markdown 代码样式导致配色变化
		b.WriteString(fmt.Sprintf("`Session ID: %s`", sessionId))
	}
	br()

	// 工作目录（定义列表语法）
	b.WriteString("**Working Directory**\n: `")
	b.WriteString(cfg.WorkingDir)
	b.WriteString("`")
	br()

	// 账号（定义列表语法）
	b.WriteString("**Account**\n: ")
	agentCfg, ok := cfg.GetAgent(config.AgentCoder)
	if !ok {
		b.WriteString("`Default agent not configured`")
		br()
		b.WriteString("**Model**\n: `Default (unknown)`")
		text := b.String()
		return text, nil
	}

	model, exists := models.SupportedModels[agentCfg.Model]
	if !exists {
		b.WriteString("`Model not supported`")
		br()
		b.WriteString("**Model**\n: `Default (unknown)`")
		text := b.String()
		return text, nil
	}

	switch model.Provider {
	case models.ProviderOpenAI:
		b.WriteString("`Auth Token: QODER_OPENAI_API_KEY`")
	case models.ProviderQoder:
		if ui := qoder.GetCachedUserInfo(); ui != nil && ui.Name != "" {
			b.WriteString(fmt.Sprintf("`Qoder Account: %s`", ui.Name))
		} else {
			b.WriteString("`Qoder Account: not logged in`")
		}
	case models.ProviderDashScope:
		b.WriteString("`Auth Token: QODER_DASHSCOPE_API_KEY`")
	case models.ProviderIdeaLab:
		b.WriteString("`Auth Token: QODER_IDEALAB_API_KEY`")
	case models.ProviderAnthropic:
		b.WriteString("`Auth Token: ANTHROPIC_AUTH_TOKEN`")
	default:
		b.WriteString(fmt.Sprintf("`Provider: %s`", string(model.Provider)))
	}
	br()

	// API 配置（定义列表语法）
	b.WriteString("**API Configuration**\n: ")
	switch model.Provider {
	case models.ProviderQoder:
		endpoint := "https://api2.qoder.sh"
		if qc := qoder.GetConfig(); qc != nil && qc.RegionConfig.PreferredInferenceNode.Endpoint != "" {
			endpoint = qc.RegionConfig.PreferredInferenceNode.Endpoint
		}
		b.WriteString(fmt.Sprintf("`Qoder Endpoint: %s`", endpoint))
	case models.ProviderDashScope:
		b.WriteString("`DashScope Base URL: https://dashscope.aliyuncs.com/compatible-mode/v1`")
	case models.ProviderIdeaLab:
		b.WriteString("`IdeaLab Base URL: https://idealab-sg.alibaba-inc.com/api/openai/v1`")
	default:
		b.WriteString("`Base URL: unknown`")
	}
	br()

	// 模型（定义列表语法）
	b.WriteString("**Model**\n: ")
	b.WriteString(fmt.Sprintf("`Default (%s)`", model.APIModel))

	// (debug variants removed)

	text := b.String()
	return text, nil
}

package command

import (
	"context"
	"fmt"
	"strings"
)

func (c *CommandService) ListOutputStyles(ctx context.Context, sessionId string, args string) (string, error) {
	styles, err := c.outputStyles.List()
	if err != nil {
		return "", err
	}

	if len(args) > 0 {
		var names []string
		style := strings.TrimSpace(args)
		for _, s := range styles {
			names = append(names, s.Name)
			if s.Name == style {
				if err := c.cfg.SetOutputStyle(style); err != nil {
					return "", err
				}
				return fmt.Sprintf("Set output style to '%s'", style), nil
			}
		}
		return fmt.Sprintf("Output style '%s' is not valid, choose it from %s", style, strings.Join(names, "/")), nil
	}

	curStyle := c.cfg.GetOutputStyle()

	var lines []string
	for _, style := range styles {
		flag := ""
		if style.Name == curStyle {
			flag = "**✔**"
		}
		lines = append(lines, fmt.Sprintf("- **%s** : %s  %s", style.Name, style.Description, flag))
	}
	lines = append(lines, "---\nYou can set the output style using the `/output-style {name}` command, such as `/output-style Explanatory`")
	return strings.Join(lines, "\n"), nil
}

package command

import (
	"context"

	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/qoder"
)

func (c *CommandService) Login(ctx context.Context, sessionId string, args string) (string, error) {
	userInfo := qoder.GetCachedUserInfo()
	if userInfo != nil && userInfo.Name != "" {
		return "You are already logged in, account name: `" + userInfo.Name + "`", nil
	}
	return "Please login from `Qoder IDE`", nil
}

// LoginWithEvent 带事件处理的登录命令
func (c *CommandService) LoginWithEvent(ctx context.Context, sessionId string, args string, output chan command.Event) {
	// 首先发送开始事件，触发TUI的登录监听
	output <- command.Event{
		CommandName: "login",
		Args:        args,
		Payload:     "start",
		InputChan:   nil,
	}

	// 执行原有的登录逻辑
	result, err := c<PERSON><PERSON><PERSON>(ctx, sessionId, args)

	// 发送结果事件
	if err != nil {
		output <- command.Event{
			CommandName: "login",
			Payload: map[string]interface{}{
				"type":    "error",
				"message": err.Error(),
			},
		}
	} else {
		output <- command.Event{
			CommandName: "login",
			Payload: map[string]interface{}{
				"type":    "complete",
				"message": result,
			},
		}
	}

	close(output)
}

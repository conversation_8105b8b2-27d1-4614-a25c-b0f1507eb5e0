package command

import (
	"context"

	"github.com/qoder-ai/qodercli/core/internal"
	"github.com/qoder-ai/qodercli/core/llm/command"
)

// RunClearWithEvent implements /clear using event-driven approach
func (c *CommandService) RunClearWithEvent(ctx context.Context, sessionId string, args string, output chan command.Event) {
	// Create a new session in current workspace
	newSession, err := c.Sessions.CreateWithConfig(context.Background(), "New Session", &internal.SessionConfig{WorkingDir: c.cfg.WorkingDir})
	if err != nil {
		output <- command.Event{
			CommandName: "clear",
			Args:        args,
			SessionId:   sessionId,
			Payload: map[string]interface{}{
				"type":    "error",
				"message": "Failed to clear: " + err.Error(),
			},
		}
		close(output)
		return
	}

	// Create input channel for receiving results from TUI
	inputChan := make(chan interface{})

	// Send session switch event to TUI
	output <- command.Event{
		CommandName: "clear",
		Args:        args,
		SessionId:   sessionId,
		InputChan:   input<PERSON>han,
		Payload: map[string]interface{}{
			"type":           "session_switch",
			"new_session_id": newSession.Id,
		},
	}

	// Wait for result from TUI
	result := <-inputChan

	// Send final result
	if resultMap, ok := result.(map[string]interface{}); ok {
		if success, exists := resultMap["success"].(bool); exists && success {
			output <- command.Event{
				CommandName: "clear",
				Args:        args,
				SessionId:   sessionId,
				Payload: map[string]interface{}{
					"type":    "complete",
					"message": "Clear success",
				},
			}
		} else if errorMsg, exists := resultMap["error"].(string); exists {
			output <- command.Event{
				CommandName: "clear",
				Args:        args,
				SessionId:   sessionId,
				Payload: map[string]interface{}{
					"type":    "error",
					"message": errorMsg,
				},
			}
		}
	}

	close(output)
}

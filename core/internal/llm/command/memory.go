package command

import (
	"context"

	"github.com/qoder-ai/qodercli/core/llm/command"
)

// executeMemoryCommand 执行 memory 命令，编辑记忆文件
func (c *CommandService) executeMemoryCommand(ctx context.Context, sessionId string, args string, output chan command.Event) {
	// 发送状态事件，提示选择记忆文件
	output <- command.Event{
		CommandName: "memory",
		Payload: map[string]interface{}{
			"type":    "status",
			"message": "Please select memory file to edit...",
		},
		SessionId: sessionId,
	}

	// 创建用于双向通信的通道
	inputChan := make(chan any)

	// 发送打开记忆选择器的事件
	output <- command.Event{
		CommandName: "memory",
		Args:        args,
		Payload: map[string]interface{}{
			"type": "open_selector",
			"args": args,
		},
		InputChan: inputChan,
		SessionId: sessionId,
	}

	// 等待TUI处理完成
	select {
	case <-ctx.Done():
		output <- command.Event{
			CommandName: "memory",
			Payload: map[string]interface{}{
				"type":    "error",
				"message": "Memory edit operation was cancelled",
			},
			SessionId: sessionId,
		}
	case result := <-inputChan:
		// 处理从TUI返回的结果
		if resultMap, ok := result.(map[string]interface{}); ok {
			if success, ok := resultMap["success"].(bool); ok && success {
				// 选择成功完成，记忆文件已打开编辑
				output <- command.Event{
					CommandName: "memory",
					Payload: map[string]interface{}{
						"type":    "complete",
						"message": "Memory file opened for editing",
					},
					SessionId: sessionId,
				}
			} else if cancelled, ok := resultMap["cancelled"].(bool); ok && cancelled {
				// 用户取消了选择
				output <- command.Event{
					CommandName: "memory",
					Payload: map[string]interface{}{
						"type":    "info",
						"message": "Memory edit cancelled",
					},
					SessionId: sessionId,
				}
			} else {
				// 操作失败
				errorMsg := "Memory edit operation failed"
				if msg, ok := resultMap["error"].(string); ok {
					errorMsg = msg
				}
				output <- command.Event{
					CommandName: "memory",
					Payload: map[string]interface{}{
						"type":    "error",
						"message": errorMsg,
					},
					SessionId: sessionId,
				}
			}
		}
	}

	close(output)
}

package command

import (
	"github.com/stretchr/testify/assert"
	"strings"
	"testing"
)

func TestParseCommand(t *testing.T) {
	content := `---
allowed-tools: <PERSON><PERSON>(git add:*), <PERSON><PERSON>(git status:*), <PERSON>sh(git commit:*)
description: "Intelligent adaptive workflow that analyzes requirements and auto-selects optimal development path"
argument-hint: add [tagId] | remove [tagId] | list
model: haiku
---

Execute an intelligent, adaptive feature development workflow that automatically determines the optimal starting point based on requirement complexity and user intent.

**Adaptive Intelligence:**
Quest mode uses smart analysis to determine the most efficient path:
- **Simple/Clear Requirements**: Skip requirements phase, start with design or tasks
- **Complex/Ambiguous Requirements**: Begin with full requirements analysis
- **Implementation-Ready Features**: Jump directly to task planning
- **Technical Specifications**: Start with design document creation
`

	cmd := parseContent(strings.NewReader(content))
	assert.NotEmpty(t, cmd.Content)
	assert.NotEmpty(t, cmd.AllowedTools)
	assert.NotEmpty(t, cmd.ArgumentHint)
	assert.NotEmpty(t, cmd.Model)
}

func TestParseCommandSimple(t *testing.T) {
	content := `
Execute an intelligent, adaptive feature development workflow that automatically determines the optimal starting point based on requirement complexity and user intent.

**Adaptive Intelligence:**
Quest mode uses smart analysis to determine the most efficient path:
- **Simple/Clear Requirements**: Skip requirements phase, start with design or tasks
- **Complex/Ambiguous Requirements**: Begin with full requirements analysis
- **Implementation-Ready Features**: Jump directly to task planning
- **Technical Specifications**: Start with design document creation
`

	cmd := parseContent(strings.NewReader(content))
	assert.NotEmpty(t, cmd.Content)
}

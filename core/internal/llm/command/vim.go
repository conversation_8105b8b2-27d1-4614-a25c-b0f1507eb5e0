package command

import (
	"context"
	"strings"

	"github.com/qoder-ai/qodercli/core/llm/command"
)

// executeVimCommand 执行 vim 命令，打开外部编辑器
func (c *CommandService) executeVimCommand(ctx context.Context, sessionId string, args string, output chan command.Event) {
	// 发送开始编辑的状态事件
	output <- command.Event{
		CommandName: "vim",
		Payload: map[string]interface{}{
			"type":    "status",
			"message": "Opening external editor...",
		},
		SessionId: sessionId,
	}

	// 创建用于双向通信的通道
	inputChan := make(chan any)

	// 发送编辑器开启事件，等待TUI响应
	output <- command.Event{
		CommandName: "vim",
		Args:        args,
		Payload: map[string]interface{}{
			"type": "open_editor",
			"args": args,
		},
		InputChan: inputChan,
		SessionId: sessionId,
	}

	// 等待TUI处理完成
	select {
	case <-ctx.Done():
		output <- command.Event{
			CommandName: "vim",
			Payload: map[string]interface{}{
				"type":    "error",
				"message": "Editor operation was cancelled",
			},
			SessionId: sessionId,
		}
	case result := <-inputChan:
		// 处理从TUI返回的结果
		if resultMap, ok := result.(map[string]interface{}); ok {
			if success, ok := resultMap["success"].(bool); ok && success {
				// 编辑成功完成
				if content, ok := resultMap["content"].(string); ok && strings.TrimSpace(content) != "" {
					output <- command.Event{
						CommandName: "vim",
						Payload: map[string]interface{}{
							"type":    "complete",
							"content": content,
							"message": "Input received from external editor",
						},
						SessionId: sessionId,
					}
				}
				// 内容为空时静默处理，不发送任何事件
			} else {
				// 编辑失败或取消
				errorMsg := "Editor operation failed"
				if msg, ok := resultMap["error"].(string); ok {
					errorMsg = msg
				}
				output <- command.Event{
					CommandName: "vim",
					Payload: map[string]interface{}{
						"type":    "error",
						"message": errorMsg,
					},
					SessionId: sessionId,
				}
			}
		}
	}

	close(output)
}

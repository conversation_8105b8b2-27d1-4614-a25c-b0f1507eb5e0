package command

import (
	"context"
	"fmt"
	"github.com/qoder-ai/qodercli/core"
	"sort"
	"strings"

	"github.com/qoder-ai/qodercli/core/version"
)

// ReturnHelpContent 输出帮助文本
func (c *CommandService) ReturnHelpContent(ctx context.Context, sessionId string, args string) (string, error) {
	var b strings.Builder
	cliVersion := "dev"
	if version.Version != "" {
		cliVersion = version.Version
	}
	fmt.Fprintf(&b, "**%s CLI %s**\n\n", core.AppName, cliVersion)
	b.WriteString(fmt.Sprintf("> Always review %s's responses, especially when running code. %s can read and edit files in the current directory and can run commands with your permission.\n\n", core.AppName, core.AppName))

	// Usage
	b.WriteString("# Usage Modes\n\n")
	b.WriteString(fmt.Sprintf("- REPL: %s (interactive session)\n", core.ReleaseName))
	b.WriteString(fmt.Sprintf("- Non-interactive: %s -p \"question\"\n\n", core.ReleaseName))

	b.WriteString(fmt.Sprintf("Run %s -h for all command line options\n\n", core.ReleaseName))

	// Common tasks
	b.WriteString("# Common Tasks\n\n")
	b.WriteString("- Ask questions about your codebase `> How does foo.py work?`\n")
	b.WriteString("- Edit files `> Update bar.ts to...`\n")
	b.WriteString("- Fix errors `> build, test`\n")
	b.WriteString("- Run commands `> /help`\n")
	b.WriteString("- Run bash commands `> !ls`\n\n")

	// Commands 汇总，每行一个命令与描述
	b.WriteString("# Interactive Mode Commands\n\n")
	cmds, _ := c.LoadAllCommands()
	lines := make([]string, 0, len(cmds))
	for _, cmd := range cmds {
		lines = append(lines, fmt.Sprintf("- **/%s** - %s", cmd.Name, cmd.Description))
	}
	sort.Strings(lines)
	for _, l := range lines {
		b.WriteString(l + "\n")
	}

	b.WriteString("\n\n`Learn more at: https://docs.xxxx.com`\n\n")
	return b.String(), nil
}

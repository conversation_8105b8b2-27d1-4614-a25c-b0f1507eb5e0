package command

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/command"
)

// RunResumeWithEvent implements /resume using event-driven approach
func (c *CommandService) RunResumeWithEvent(ctx context.Context, sessionId string, args string, output chan command.Event) {
	args = strings.TrimSpace(args)

	if args != "" {
		// 有参数：切换到指定会话
		ses, err := c.Sessions.Get(context.Background(), args)
		if err != nil {
			output <- command.Event{
				CommandName: "resume",
				Args:        args,
				SessionId:   sessionId,
				Payload: map[string]interface{}{
					"type":    "error",
					"message": "Failed to resume: " + err.Error(),
				},
			}
			close(output)
			return
		}

		// 校验工作空间一致
		if strings.TrimSpace(ses.WorkingDir) == "" || strings.TrimSpace(c.cfg.WorkingDir) == "" || ses.WorkingDir != c.cfg.WorkingDir {
			text := fmt.Sprintf("Cannot resume: session workspace mismatch.\nSession: %s\nCurrent: %s", ses.WorkingDir, c.cfg.WorkingDir)
			output <- command.Event{
				CommandName: "resume",
				Args:        args,
				SessionId:   sessionId,
				Payload: map[string]interface{}{
					"type":    "error",
					"message": text,
				},
			}
			close(output)
			return
		}

		// Create input channel for receiving results from TUI
		inputChan := make(chan interface{})

		// 发送会话切换事件
		output <- command.Event{
			CommandName: "resume",
			Args:        args,
			SessionId:   sessionId,
			InputChan:   inputChan,
			Payload: map[string]interface{}{
				"type":           "session_switch",
				"new_session_id": args,
			},
		}

		// Wait for result from TUI
		result := <-inputChan

		// Send final result
		if resultMap, ok := result.(map[string]interface{}); ok {
			if success, exists := resultMap["success"].(bool); exists && success {
				output <- command.Event{
					CommandName: "resume",
					Args:        args,
					SessionId:   sessionId,
					Payload: map[string]interface{}{
						"type":    "complete",
						"message": "Resumed session: " + args,
					},
				}
			} else if errorMsg, exists := resultMap["error"].(string); exists {
				output <- command.Event{
					CommandName: "resume",
					Args:        args,
					SessionId:   sessionId,
					Payload: map[string]interface{}{
						"type":    "error",
						"message": errorMsg,
					},
				}
			}
		}

		close(output)
		return
	}

	// 无参数：获取会话列表并直接返回内容
	sessions, err := c.Sessions.List(context.Background())
	if err != nil {
		output <- command.Event{
			CommandName: "resume",
			Args:        args,
			SessionId:   sessionId,
			Payload: map[string]interface{}{
				"type":    "error",
				"message": "Failed to list sessions: " + err.Error(),
			},
		}
		close(output)
		return
	}

	// 过滤：仅同工作空间，且排除子会话
	filtered := make([]core.Session, 0, len(sessions))
	for _, s := range sessions {
		if s.ParentSessionId != "" {
			continue
		}
		// 排除当前会话
		if s.Id == sessionId {
			continue
		}
		if s.WorkingDir == c.cfg.WorkingDir {
			filtered = append(filtered, s)
		}
	}
	if len(filtered) == 0 {
		output <- command.Event{
			CommandName: "resume",
			Args:        args,
			SessionId:   sessionId,
			Payload: map[string]interface{}{
				"type":    "content",
				"message": "No sessions found in current workspace.",
			},
		}
		close(output)
		return
	}

	sort.Slice(filtered, func(i, j int) bool { return filtered[i].UpdatedAt < filtered[j].UpdatedAt })

	var b strings.Builder
	b.WriteString("# Recent Sessions\n\n")
	for _, s := range filtered {
		title := s.Title
		if strings.TrimSpace(title) == "" {
			title = "(no title)"
		}
		ts := time.UnixMilli(s.UpdatedAt).Local().Format("2006-01-02 15:04:05")
		fmt.Fprintf(&b, "- `%s` — %s — %s\n", s.Id, ts, title)
	}
	b.WriteString("\nCopy the session id and run **/resume 'session-id'** to resume.")

	// 直接发送会话列表内容
	output <- command.Event{
		CommandName: "resume",
		Args:        args,
		SessionId:   sessionId,
		Payload: map[string]interface{}{
			"type":    "complete",
			"message": b.String(),
		},
	}

	close(output)
}

package tools

import (
	"fmt"
	"strings"
	"testing"
)

func TestFormatLines_EmptyContent(t *testing.T) {
	result := formatLines("", 1)
	if result != "" {
		t.<PERSON><PERSON><PERSON>("Expected empty string for empty content, got %q", result)
	}
}

func TestFormatLines_SingleLine(t *testing.T) {
	content := "Hello World"
	result := formatLines(content, 1)
	expected := "     1→Hello World"
	
	if result != expected {
		t.<PERSON><PERSON><PERSON>("Expected %q, got %q", expected, result)
	}
}

func TestFormatLines_MultipleLines(t *testing.T) {
	content := "line 1\nline 2\nline 3"
	result := formatLines(content, 1)
	expected := "     1→line 1\n     2→line 2\n     3→line 3"
	
	if result != expected {
		t.<PERSON><PERSON>rf("Expected:\n%s\nGot:\n%s", expected, result)
	}
}

func TestFormatLines_WithStartLine(t *testing.T) {
	content := "line A\nline B"
	result := formatLines(content, 10)
	expected := "    10→line A\n    11→line B"
	
	if result != expected {
		t.<PERSON><PERSON><PERSON>("Expected:\n%s\nGot:\n%s", expected, result)
	}
}

func TestFormatLines_LongLineNumbers(t *testing.T) {
	content := "line 1\nline 2"
	result := formatLines(content, 100000)
	expected := "100000→line 1\n100001→line 2"
	
	if result != expected {
		t.Errorf("Expected:\n%s\nGot:\n%s", expected, result)
	}
}

func TestFormatLines_WindowsLineEndings(t *testing.T) {
	content := "line 1\r\nline 2\r\nline 3"
	result := formatLines(content, 1)
	expected := "     1→line 1\n     2→line 2\n     3→line 3"
	
	if result != expected {
		t.Errorf("Expected:\n%s\nGot:\n%s", expected, result)
	}
}

func TestFormatLines_MixedLineEndings(t *testing.T) {
	content := "line 1\nline 2\r\nline 3"
	result := formatLines(content, 1)
	expected := "     1→line 1\n     2→line 2\n     3→line 3"
	
	if result != expected {
		t.Errorf("Expected:\n%s\nGot:\n%s", expected, result)
	}
}

func TestFormatLines_EmptyLines(t *testing.T) {
	content := "line 1\n\nline 3"
	result := formatLines(content, 1)
	expected := "     1→line 1\n     2→\n     3→line 3"
	
	if result != expected {
		t.Errorf("Expected:\n%s\nGot:\n%s", expected, result)
	}
}

func TestFormatLines_OnlyNewlines(t *testing.T) {
	content := "\n\n"
	result := formatLines(content, 1)
	expected := "     1→\n     2→\n     3→"
	
	if result != expected {
		t.Errorf("Expected:\n%s\nGot:\n%s", expected, result)
	}
}

func TestFormatLines_TrailingNewline(t *testing.T) {
	content := "line 1\nline 2\n"
	result := formatLines(content, 1)
	expected := "     1→line 1\n     2→line 2\n     3→"
	
	if result != expected {
		t.Errorf("Expected:\n%s\nGot:\n%s", expected, result)
	}
}

func TestFormatLines_LineNumberPadding(t *testing.T) {
	tests := []struct {
		startLine int
		expected  string
	}{
		{1, "     1→test"},
		{10, "    10→test"},
		{100, "   100→test"},
		{1000, "  1000→test"},
		{10000, " 10000→test"},
		{100000, "100000→test"},
		{1000000, "1000000→test"},
	}
	
	for _, test := range tests {
		result := formatLines("test", test.startLine)
		if result != test.expected {
			t.Errorf("For startLine %d, expected %q, got %q", test.startLine, test.expected, result)
		}
	}
}

func TestFormatLines_SpecialCharacters(t *testing.T) {
	content := "line with\ttab\nline with spaces   \nline with unicode: 你好"
	result := formatLines(content, 1)
	expected := "     1→line with\ttab\n     2→line with spaces   \n     3→line with unicode: 你好"
	
	if result != expected {
		t.Errorf("Expected:\n%s\nGot:\n%s", expected, result)
	}
}

func TestFormatLines_LargeContent(t *testing.T) {
	// Test with a larger number of lines
	lines := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		lines[i] = fmt.Sprintf("line %d", i+1)
	}
	content := strings.Join(lines, "\n")
	
	result := formatLines(content, 1)
	resultLines := strings.Split(result, "\n")
	
	if len(resultLines) != 1000 {
		t.Errorf("Expected 1000 lines, got %d", len(resultLines))
	}
	
	// Check first and last lines
	if !strings.HasPrefix(resultLines[0], "     1→line 1") {
		t.Errorf("First line incorrect: %s", resultLines[0])
	}
	
	if !strings.HasPrefix(resultLines[999], "  1000→line 1000") {
		t.Errorf("Last line incorrect: %s", resultLines[999])
	}
}
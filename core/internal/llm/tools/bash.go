package tools

import (
	"context"
	"fmt"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/llm/shell"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/monitoring"
	"github.com/qoder-ai/qodercli/core/utils"
)

type bashTool struct {
	shells       shell.ShellService
	permissions  llm.PermissionTrigger
	snapshotFile string
}

// CommandExitCodeInterpreter 命令退出码解释器
type CommandExitCodeInterpreter func(exitCode int, stdout, stderr string) ExitCodeResult

type ExitCodeResult struct {
	IsError bool   `json:"is_error"`
	Message string `json:"message,omitempty"`
}

const (
	DefaultTimeout  = 2 * 60 * 1000  // 2 minutes in milliseconds
	MaxTimeout      = 10 * 60 * 1000 // 10 minutes in milliseconds
	MaxOutputLength = 30000

	// Tips: 2025.8.8 添加超时参数设置指引，避免模型在command参数中添加超时机制
	bashDescription = `Executes a given bash command in a persistent shell session with optional timeout, ensuring proper handling and security measures.

Before executing the command, please follow these steps:

1. Directory Verification:
   - If the command will create new directories or files, first use the LS tool to verify the parent directory exists and is the correct location
   - For example, before running "mkdir foo/bar", first use LS to check that "foo" exists and is the intended parent directory

2. Command Execution:
   - Always quote file paths that contain spaces with double quotes (e.g., cd "path with spaces/file.txt")
   - Examples of proper quoting:
     - cd "/Users/<USER>/My Documents" (correct)
     - cd /Users/<USER>/My Documents (incorrect - will fail)
     - python "/path/with spaces/script.py" (correct)
     - python /path/with spaces/script.py (incorrect - will fail)
   - After ensuring proper quoting, execute the command.
   - Capture the output of the command.

Usage notes:
  - The command argument is required.
  - You can specify an optional timeout in milliseconds (up to ` + string(rune(MaxTimeout)) + `ms / ` + string(rune(MaxTimeout/60000)) + ` minutes). If not specified, commands will timeout after ` + string(rune(DefaultTimeout)) + `ms (` + string(rune(DefaultTimeout/60000)) + ` minutes).
  - When specify timeout, remember to set the timeout argument, DO NOT add timeout operation in the command argument.
  - It is very helpful if you write a clear, concise description of what this command does in 5-10 words.
  - If the output exceeds ` + string(rune(MaxOutputLength)) + ` characters, output will be truncated before being returned to you.
  - VERY IMPORTANT: You MUST avoid using search commands like ` + "`find`" + ` and ` + "`grep`" + `. Instead use Grep, Glob, or Task to search. You MUST avoid read tools like ` + "`cat`" + `, ` + "`head`" + `, ` + "`tail`" + `, and ` + "`ls`" + `, and use Read and LS to read files.
  - If you _still_ need to run ` + "`grep`" + `, STOP. ALWAYS USE ripgrep at ` + "`rg`" + ` first, which all ` + core.AppName + ` users have pre-installed.
  - When issuing multiple commands, use the ';' or '&&' operator to separate them. DO NOT use newlines (newlines are ok in quoted strings).
  - Try to maintain your current working directory throughout the session by using absolute paths and avoiding usage of ` + "`cd`" + `. You may use ` + "`cd`" + ` if the User explicitly requests it.
    <good-example>
    pytest /foo/bar/tests
    </good-example>
    <bad-example>
    cd /foo/bar && pytest tests
    </bad-example>

# Committing changes with git

When the user asks you to create a new git commit, follow these steps carefully:

1. You have the capability to call multiple tools in a single response. When multiple independent pieces of information are requested, batch your tool calls together for optimal performance. ALWAYS run the following bash commands in parallel, each using the Bash tool:
  - Run a git status command to see all untracked files.
  - Run a git diff command to see both staged and unstaged changes that will be committed.
  - Run a git log command to see recent commit messages, so that you can follow this repository's commit message style.
2. Analyze all staged changes (both previously staged and newly added) and draft a commit message:
  - Summarize the nature of the changes (eg. new feature, enhancement to an existing feature, bug fix, refactoring, test, docs, etc.). Ensure the message accurately reflects the changes and their purpose (i.e. "add" means a wholly new feature, "update" means an enhancement to an existing feature, "fix" means a bug fix, etc.).
  - Check for any sensitive information that shouldn't be committed
  - Draft a concise (1-2 sentences) commit message that focuses on the "why" rather than the "what"
  - Ensure it accurately reflects the changes and their purpose
3. You have the capability to call multiple tools in a single response. When multiple independent pieces of information are requested, batch your tool calls together for optimal performance. ALWAYS run the following commands in parallel:
   - Add relevant untracked files to the staging area.
   - Create the commit with a message ending with: 🤖 Generated with [` + core.AppName + `][` + core.MainDomainURL + `]
   - Run git status to make sure the commit succeeded.
4. If the commit fails due to pre-commit hook changes, retry the commit ONCE to include these automated changes. If it fails again, it usually means a pre-commit hook is preventing the commit. If the commit succeeds but you notice that files were modified by the pre-commit hook, you MUST amend your commit to include them.

Important notes:
- NEVER update the git config
- NEVER run additional commands to read or explore code, besides git bash commands
- NEVER use the TodoWrite or Task tools
- DO NOT push to the remote repository unless the user explicitly asks you to do so
- IMPORTANT: Never use git commands with the -i flag (like git rebase -i or git add -i) since they require interactive input which is not supported.
- If there are no changes to commit (i.e., no untracked files and no modifications), do not create an empty commit
- In order to ensure good formatting, ALWAYS pass the commit message via a HEREDOC, a la this example:
<example>
git commit -m "$(cat <<'EOF'
   Commit message here.

   🤖 Generated with [` + core.AppName + `][` + core.MainDomainURL + `]
EOF
)"
</example>

# Creating pull requests
Use the gh command via the Bash tool for ALL GitHub-related tasks including working with issues, pull requests, checks, and releases. If given a Github URL use the gh command to get the information needed.

IMPORTANT: When the user asks you to create a pull request, follow these steps carefully:

1. You have the capability to call multiple tools in a single response. When multiple independent pieces of information are requested, batch your tool calls together for optimal performance. ALWAYS run the following bash commands in parallel using the ${eU} tool, in order to understand the current state of the branch since it diverged from the main branch:
   - Run a git status command to see all untracked files
   - Run a git diff command to see both staged and unstaged changes that will be committed
   - Check if the current branch tracks a remote branch and is up to date with the remote, so you know if you need to push to the remote
   - Run a git log command and ` + "`git diff [base-branch]...HEAD`" + ` to understand the full commit history for the current branch (from the time it diverged from the base branch)
2. Analyze all changes that will be included in the pull request, making sure to look at all relevant commits (NOT just the latest commit, but ALL commits that will be included in the pull request!!!), and draft a pull request summary
3. You have the capability to call multiple tools in a single response. When multiple independent pieces of information are requested, batch your tool calls together for optimal performance. ALWAYS run the following commands in parallel:
   - Create new branch if needed
   - Push to remote with -u flag if needed
   - Create PR using gh pr create with the format below. Use a HEREDOC to pass the body to ensure correct formatting.
<example>
gh pr create --title "the pr title" --body "$(cat <<'EOF'
## Summary
<1-3 bullet points>

## Test plan
[Checklist of TODOs for testing the pull request...]

	🤖 Generated with [` + core.AppName + `][` + core.MainDomainURL + `]
EOF
)"
</example>

Important:
- NEVER update the git config
- DO NOT use the TodoWrite or Task tools
- Return the PR URL when you're done, so the user can see it

# Other common operations
- View comments on a Github PR: gh api repos/foo/bar/pulls/123/comments`
)

// 智能退出码解释器映射 - 基于JavaScript版本的vQ4
var exitCodeInterpreters = map[string]CommandExitCodeInterpreter{
	"grep": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("grep failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "No matches found"}
		}
		return ExitCodeResult{IsError: false}
	},
	"rg": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("ripgrep failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "No matches found"}
		}
		return ExitCodeResult{IsError: false}
	},
	"find": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("find failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "Some directories were inaccessible"}
		}
		return ExitCodeResult{IsError: false}
	},
	"diff": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("diff failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "Files differ"}
		}
		return ExitCodeResult{IsError: false}
	},
	"test": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("test failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "Condition is false"}
		}
		return ExitCodeResult{IsError: false}
	},
	"[": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("test failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "Condition is false"}
		}
		return ExitCodeResult{IsError: false}
	},
}

// 默认退出码解释器 - 基于JavaScript版本的xQ4
var defaultExitCodeInterpreter = func(exitCode int, stdout, stderr string) ExitCodeResult {
	if exitCode != 0 {
		return ExitCodeResult{IsError: true, Message: fmt.Sprintf("Command failed with exit code %d", exitCode)}
	}
	return ExitCodeResult{IsError: false}
}

func NewBashTool(permissions llm.PermissionTrigger, shells shell.ShellService) tools.BaseTool {
	return &bashTool{
		shells:       shells,
		permissions:  permissions,
		snapshotFile: filepath.Join(utils.GetUserStorageDir(), "shell-snapshots", fmt.Sprintf("shell-snapshot-%d.sh", time.Now().UnixNano())),
	}
}

func (t *bashTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.BashToolName,
		Description: bashDescription,
		Parameters: map[string]any{
			"command": map[string]any{
				"type":        "string",
				"description": "The command to execute",
			},
			"timeout": map[string]any{
				"type":        "number",
				"description": "Optional timeout in milliseconds (max 600000)",
			},
			"description": map[string]any{
				"type": "string",
				"description": ` Clear, concise description of what this command does in 5-10 words. Examples:
Input: ls
Output: Lists files in current directory

Input: git status
Output: Shows working tree status

Input: npm install
Output: Installs package dependencies

Input: mkdir foo
Output: Creates directory 'foo'`,
			},
			"run_in_background": map[string]any{
				"type":        "boolean",
				"description": "Set to true to run this command in the background. Use BashOutput to read the output later.",
			},
		},
		Required: []string{"command"},
	}
}

func (t *bashTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.BashSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("invalid parameters"), nil
	}

	if params.Timeout > MaxTimeout {
		params.Timeout = MaxTimeout
	} else if params.Timeout <= 0 {
		params.Timeout = DefaultTimeout
	}

	if params.Command == "" {
		return tools.NewTextErrorResponse("missing command"), nil
	}

	// TODO 检查命令是否包含敏感操作
	// params.Command

	// 获取会话信息
	sessionId := ctx.GetSessionId()
	messageId := ctx.GetMessageId()
	if sessionId == "" || messageId == "" {
		return tools.ToolResponse{}, fmt.Errorf("session Id and message Id are required for creating a new file")
	}
	p := t.permissions.CreateRequestWithContext(ctx,
		core.CreatePermissionRequest{
			SessionId:   sessionId,
			Path:        ctx.GetWorkingDir(),
			ToolName:    tools.BashToolName,
			Action:      "execute",
			Description: fmt.Sprintf("Execute command: %s", params.Command),
			Params: specs.BashPermissionsParams{
				Command: params.Command,
			},
		},
	)
	if !p {
		return tools.ToolResponse{}, core.ErrorPermissionDenied
	}

	// 使用快照包装命令来保持shell状态
	startTime := time.Now()
	timeout := time.Duration(params.Timeout) * time.Millisecond
	if params.RunInBackground {
		timeout = -1 // 后台运行，永不超时
	}
	bashId, err := t.shells.Exec(ctx, ctx.GetWorkingDir(), params.Command, timeout)
	if err != nil {
		response := fmt.Sprintf("Command failed with error: %s", err.Error())
		metadata := specs.BashResponseMetadata{StartTime: startTime.UnixMilli()}
		return tools.WithResponseMetadata(tools.NewTextResponse(response), metadata), nil
	}

	sh, exists := t.shells.GetShell(bashId)
	if !exists || sh == nil {
		response := fmt.Sprintf("Shell for command not found: %s", params.Command)
		metadata := specs.BashResponseMetadata{StartTime: startTime.UnixMilli()}
		return tools.WithResponseMetadata(tools.NewTextResponse(response), metadata), nil
	}

	if params.RunInBackground {
		response := fmt.Sprintf("Command running in background with ID: %s", bashId)
		metadata := specs.BashResponseMetadata{StartTime: startTime.UnixMilli()}
		return tools.WithResponseMetadata(tools.NewTextResponse(response), metadata), nil
	}

	// 阻塞式获取命令执行结果
	result := sh.WaitForResult()

	// Git操作埋点
	t.trackGitOperations(sessionId, params.Command, result)

	// 退出码分析, 空白行清理、超长截断
	var isImage bool
	exitCodeResult := t.interpretExitCode(params.Command, result)
	isImage, result.Stdout = t.processContent(result.Stdout)
	_, result.Stderr = t.processContent(result.Stderr)

	// 构建响应内容
	response := t.buildNonBackgroundResponse(params, result)
	metadata := specs.BashResponseMetadata{
		StartTime:   startTime.UnixMilli(),
		EndTime:     time.Now().UnixMilli(),
		ExitCode:    result.ExitCode,
		IsImage:     isImage,
		ExitMessage: exitCodeResult.Message,
	}
	return tools.WithResponseMetadata(tools.NewTextResponse(response), metadata), nil
}

func (t *bashTool) parseCommandType(command string) string {
	// 按|分割，取最后一个部分
	parts := strings.Split(command, "|")
	lastPart := command
	if len(parts) > 1 {
		lastPart = parts[len(parts)-1]
	}

	// 去除首尾空格
	trimmed := strings.TrimSpace(lastPart)

	// 按空白字符分割，取第一个部分
	fields := strings.Fields(trimmed)
	if len(fields) > 0 {
		return fields[0]
	}

	return ""
}

// interpretExitCode 智能解释退出码
func (t *bashTool) interpretExitCode(command string, result shell.CommandResult) ExitCodeResult {
	if result.ExitCode == nil {
		if result.Error == nil {
			return ExitCodeResult{IsError: false}
		}
		return ExitCodeResult{IsError: true, Message: result.Error.Error()}
	}

	// 提取命令的第一个单词来确定命令类型
	commandType := t.parseCommandType(command)
	if interpreter, exists := exitCodeInterpreters[commandType]; exists {
		return interpreter(*result.ExitCode, result.Stdout, result.Stderr)
	}
	return defaultExitCodeInterpreter(*result.ExitCode, result.Stdout, result.Stderr)
}

// trackGitOperations 追踪Git操作
func (t *bashTool) trackGitOperations(sessionId string, command string, result shell.CommandResult) {
	if result.ExitCode != nil {
		return // 只追踪成功的Git操作
	}

	// 检测git commit操作
	if regexp.MustCompile(`^\s*git\s+commit\b`).MatchString(command) {
		monitoring.RecordCommit(context.Background(), sessionId)
	}

	// 检测gh pr create操作
	if regexp.MustCompile(`^\s*gh\s+pr\s+create\b`).MatchString(command) {
		monitoring.RecordPullRequest(context.Background(), sessionId)
	}
}

// processContent 智能内容处理
func (t *bashTool) processContent(content string) (bool, string) {
	if content == "" {
		return false, content
	}

	// 1. 去除前后空行，注意这里不能用trim
	content = t.trimEmptyLines(content)

	// 2. 检测和处理图像内容
	if t.isImageContent(content) {
		return true, content // 图像内容不截断
	}

	// 3. 智能截断 - 基于JavaScript版本的bP
	return false, t.truncateContent(content)
}

// trimEmptyLines 去除前后空行
func (t *bashTool) trimEmptyLines(content string) string {
	lines := strings.Split(content, "\n")
	start := 0
	end := len(lines) - 1

	// 找到第一个非空行
	for start < len(lines) && strings.TrimSpace(lines[start]) == "" {
		start++
	}

	// 找到最后一个非空行
	for end >= 0 && strings.TrimSpace(lines[end]) == "" {
		end--
	}

	if start > end {
		return ""
	}

	return strings.Join(lines[start:end+1], "\n")
}

// isImageContent 检测图像内容 - 基于JavaScript版本的bP
func (t *bashTool) isImageContent(content string) bool {
	// 检测base64编码的图像数据
	matched, _ := regexp.MatchString(`^data:image/[a-z0-9.+_-]+;base64,`, content)
	return matched
}

// truncateContent 智能截断内容
func (t *bashTool) truncateContent(content string) string {
	if len(content) <= MaxOutputLength {
		return content
	}

	start := content[:MaxOutputLength]

	// 计算被截断的行数
	truncatedPart := content[MaxOutputLength:]
	truncatedLines := len(strings.Split(truncatedPart, "\n"))

	return fmt.Sprintf("%s\n\n... [%d lines truncated] ...", start, truncatedLines)
}

// buildNonBackgroundResponse 构建最终响应 - 整合所有输出和错误信息
func (t *bashTool) buildNonBackgroundResponse(params *specs.BashParams, result shell.CommandResult) string {
	var parts []string

	// 添加标准输出
	if result.Stdout != "" {
		parts = append(parts, result.Stdout)
	}

	// 添加标准错误
	if result.Stderr != "" {
		parts = append(parts, strings.TrimSpace(result.Stderr))
	}

	// 添加退出码
	if result.ExitCode != nil {
		parts = append(parts, fmt.Sprintf("\nExit code %d", *result.ExitCode))
	}

	// 添加中断信息
	if result.Interrupted {
		parts = append(parts, "<error>Command was aborted before completion</error>")
	}

	// 添加超时信息
	if result.Timeout {
		parts = append(parts, fmt.Sprintf("<system-reminder>Command timed out in %ds, perhaps you can extend the timeout parameter for Bash tool</system-reminder>", params.Timeout/1000))
	}

	return strings.Join(parts, "\n")
}

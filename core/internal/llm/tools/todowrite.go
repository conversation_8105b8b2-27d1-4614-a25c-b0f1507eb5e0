package tools

import (
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/utils"
	"os"
	"path/filepath"
)

type todoWriteTool struct{}

const (
	todoWriteDescription = `Use this tool to create and manage a structured task list for your current coding session. This helps you track progress, organize complex tasks, and demonstrate thoroughness to the user.
It also helps the user understand the progress of the task and overall progress of their requests.

## When to Use This Tool
Use this tool proactively in these scenarios:

1. Complex multi-step tasks - When a task requires 3 or more distinct steps or actions
2. Non-trivial and complex tasks - Tasks that require careful planning or multiple operations
3. User explicitly requests todo list - When the user directly asks you to use the todo list
4. User provides multiple tasks - When users provide a list of things to be done (numbered or comma-separated)
5. After receiving new instructions - Immediately capture user requirements as todos
6. When you start working on a task - Mark it as in_progress BEFORE beginning work. Ideally you should only have one todo as in_progress at a time
7. After completing a task - Mark it as completed and add any new follow-up tasks discovered during implementation

## When NOT to Use This Tool

Skip using this tool when:
1. There is only a single, straightforward task
2. The task is trivial and tracking it provides no organizational benefit
3. The task can be completed in less than 3 trivial steps
4. The task is purely conversational or informational

NOTE that you should not use this tool if there is only one trivial task to do. In this case you are better off just doing the task directly.

## Examples of When to Use the Todo List

<example>
User: I want to add a dark mode toggle to the application settings. Make sure you run the tests and build when you're done!
Assistant: I'll help add a dark mode toggle to your application settings. Let me create a todo list to track this implementation.
*Creates todo list with the following items:*
1. Create dark mode toggle component in Settings page
2. Add dark mode state management (context/store)
3. Implement CSS-in-JS styles for dark theme
4. Update existing components to support theme switching
5. Run tests and build process, addressing any failures or errors that occur
*Begins working on the first task*

<reasoning>
The assistant used the todo list because:
1. Adding dark mode is a multi-step feature requiring UI, state management, and styling changes
2. The user explicitly requested tests and build be run afterward
3. The assistant inferred that tests and build need to pass by adding "Ensure tests and build succeed" as the final task
</reasoning>
</example>

<example>
User: Help me rename the function getCwd to getCurrentWorkingDirectory across my project
Assistant: Let me first search through your codebase to find all occurrences of 'getCwd'.
*Uses grep or search tools to locate all instances of getCwd in the codebase*
Assistant: I've found 15 instances of 'getCwd' across 8 different files. Let me create a todo list to track these changes.
*Creates todo list with specific items for each file that needs updating*

<reasoning>
The assistant used the todo list because:
1. First, the assistant searched to understand the scope of the task
2. Upon finding multiple occurrences across different files, it determined this was a complex task with multiple steps
3. The todo list helps ensure every instance is tracked and updated systematically
4. This approach prevents missing any occurrences and maintains code consistency
</reasoning>
</example>


<example>
User: I need to implement these features for my e-commerce site: user registration, product catalog, shopping cart, and checkout flow.
Assistant: I'll help implement these features. First, let's add all the features to the todo list.
*Creates a todo list breaking down each feature into specific tasks based on the project architecture*
Assistant: Let's start with user registration. This will involve creating database models, API endpoints, and frontend forms that integrate with your existing authentication system.

<reasoning>
The assistant used the todo list because:
1. The user provided multiple complex features to implement in a comma separated list
2. The todo list helps organize these large features into manageable tasks
3. This approach allows for tracking progress across the entire implementation
</reasoning>
</example>

<example>
User: Can you help optimize my React application? It's rendering slowly and has performance issues.</user>
Assistant: I'll help optimize your React application. First, let me examine your codebase to identify potential performance bottlenecks.
*Reviews component structure, render patterns, state management, and data fetching*
Assistant: After analyzing your codebase, I've identified several performance issues. Let me create a todo list to track our optimization efforts.
*Creates todo list with items like: 1) Implement memoization for expensive calculations in ProductList, 2) Add virtualization for long lists in Dashboard, 3) Optimize image loading in Gallery component, 4) Fix state update loops in ShoppingCart, 5) Review bundle size and implement code splitting*
Let's start by implementing memoization for the expensive calculations in your ProductList component.</assistant>

<reasoning>
The assistant used the todo list because:
1. First, the assistant examined the codebase to identify specific performance issues
2. Based on this analysis, it identified multiple optimization opportunities
3. Performance optimization is a non-trivial task requiring multiple steps
4. The todo list helps methodically track improvements across different components
5. This systematic approach ensures all performance bottlenecks are addressed
</reasoning>
</example>

## Examples of When NOT to Use the Todo List

<example>
User: How do I print 'Hello World' in Python?
Assistant: In Python, you can print "Hello World" with this simple code:

python
print("Hello World")

This will output the text "Hello World" to the console when executed.</assistant>

<reasoning>
The assistant did not use the todo list because this is a single, trivial task that can be completed in one step. There's no need to track multiple tasks or steps for such a straightforward request.
</reasoning>
</example>

<example>
User: What does the git status command do?
Assistant: The git status command shows the current state of your working directory and staging area. It displays which changes have been staged, which haven't, and which files aren't being tracked by Git. This is a fundamental command for understanding the current state of your repository before making commits.

<reasoning>
The assistant did not use the todo list because this is an informational request with no actual coding task to complete. The user is simply asking for an explanation, not for the assistant to perform multiple steps or tasks.
</reasoning>
</example>

<example>
User: Can you add a comment to the calculateTotal function to explain what it does?
Assistant: Sure, let me add a comment to the calculateTotal function to explain what it does.
* Uses the Edit tool to add a comment to the calculateTotal function *

<reasoning>
The assistant did not use the todo list because this is a single, straightforward task confined to one location in the code. Adding a comment doesn't require tracking multiple steps or systematic organization.
</reasoning>
</example>

<example>
User: Run npm install for me and tell me what happens.
Assistant: I'll run the npm install command for you.

*Executes: npm install*

The command completed successfully. Here's the output:
[Output of npm install command]

All dependencies have been installed according to your package.json file.

<reasoning>
The assistant did not use the todo list because this is a single command execution with immediate results. There are no multiple steps to track or organize, making the todo list unnecessary for this straightforward task.
</reasoning>
</example>

## Task States and Management

1. **Task States**: Use these states to track progress:
   - pending: Task not yet started
   - in_progress: Currently working on (limit to ONE task at a time)
   - completed: Task finished successfully

2. **Task Management**:
   - Update task status in real-time as you work
   - Mark tasks complete IMMEDIATELY after finishing (don't batch completions)
   - Only have ONE task in_progress at any time
   - Complete current tasks before starting new ones
   - Remove tasks that are no longer relevant from the list entirely

3. **Task Completion Requirements**:
   - ONLY mark a task as completed when you have FULLY accomplished it
   - If you encounter errors, blockers, or cannot finish, keep the task as in_progress
   - When blocked, create a new task describing what needs to be resolved
   - Never mark a task as completed if:
     - Tests are failing
     - Implementation is partial
     - You encountered unresolved errors
     - You couldn't find necessary files or dependencies

4. **Task Breakdown**:
   - Create specific, actionable items
   - Break complex tasks into smaller, manageable steps
   - Use clear, descriptive task names

When in doubt, use this tool. Being proactive with task management demonstrates attentiveness and ensures you complete all requirements successfully.`
	successReminder = `Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable`
)

func NewTodoWriteTool() tools.BaseTool {
	return &todoWriteTool{}
}

func (t *todoWriteTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.TodoWriteToolName,
		Description: todoWriteDescription,
		Parameters: map[string]any{
			"todos": map[string]any{
				"type": "array",
				"items": map[string]any{
					"type": "object",
					"properties": map[string]any{
						"content": map[string]any{
							"type":        "string",
							"description": "The content of the TODO item",
						},
						"status": map[string]any{
							"type":        "string",
							"enum":        []string{"pending", "in_progress", "completed"},
							"description": "The current status of the TODO item",
						},
						"priority": map[string]any{
							"type":        "string",
							"enum":        []string{"high", "medium", "low"},
							"description": "Priority level of the TODO item",
						},
						"id": map[string]any{
							"type":        "string",
							"description": "Sequence number of the TODO item (e.g. 1, 2, 3...)",
						},
					},
					"required": []string{"content", "status", "priority", "id"},
				},
				"description": "The updated todo list",
			},
		},
		Required: []string{"todos"},
	}
}

func (t *todoWriteTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.TodoWriteSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("invalid parameters"), nil
	}

	// 验证参数结构
	if err := t.validateTodos(params.Todos); err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("invalid todos: %s", err)), nil
	}

	// 获取会话ID
	sessionID := ctx.GetSessionId()
	if sessionID == "" {
		return tools.NewTextErrorResponse("session Id is required"), nil
	}

	// 获取当前任务列表
	oldTodos, err := t.loadTodos(sessionID)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("failed to load todos: %s", err)), nil
	}

	// 保存新的任务列表
	if err := t.saveTodos(sessionID, params.Todos); err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("failed to save todos: %s", err)), nil
	}

	// 返回成功消息
	return tools.WithResponseMetadata(tools.NewTextResponse(successReminder), specs.TodoWriteResult{
		OldTodos: oldTodos,
		NewTodos: params.Todos,
	}), nil
}

// 验证todo数据
func (t *todoWriteTool) validateTodos(todos []specs.Todo) error {
	for i, todo := range todos {
		if todo.Content == "" {
			return fmt.Errorf("todo[%d]: content cannot be empty", i)
		}
		if todo.ID == "" {
			return fmt.Errorf("todo[%d]: id cannot be empty", i)
		}
		if !isValidStatus(todo.Status) {
			return fmt.Errorf("todo[%d]: invalid status '%s'", i, todo.Status)
		}
		if !isValidPriority(todo.Priority) {
			return fmt.Errorf("todo[%d]: invalid priority '%s'", i, todo.Priority)
		}
	}
	return nil
}

// 验证状态是否有效
func isValidStatus(status specs.TodoStatus) bool {
	switch status {
	case specs.TodoStatusPending, specs.TodoStatusInProgress, specs.TodoStatusCompleted:
		return true
	default:
		return false
	}
}

// 验证优先级是否有效
func isValidPriority(priority specs.TodoPriority) bool {
	switch priority {
	case specs.TodoPriorityHigh, specs.TodoPriorityMedium, specs.TodoPriorityLow:
		return true
	default:
		return false
	}
}

// 获取单个会话的任务文件路径
func (t *todoWriteTool) getTodoFilePath(sessionID string) (string, error) {
	todosDir := filepath.Join(utils.GetUserStorageDir(), "todos")
	if err := os.MkdirAll(todosDir, 0755); err != nil {
		return "", err
	}

	// 文件名格式: {sessionID}-agent-{agentID}.json
	// 由于Go版本可能没有agentID概念，我们简化为 {sessionID}.json
	fileName := fmt.Sprintf("%s.json", sessionID)
	return filepath.Join(todosDir, fileName), nil
}

// 读取指定会话的任务文件路径
func (t *todoWriteTool) loadTodos(sessionID string) ([]specs.Todo, error) {
	filePath, err := t.getTodoFilePath(sessionID)
	if err != nil {
		return nil, err
	}

	// 如果文件不存在，返回空列表
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return []specs.Todo{}, nil
	}

	// 读取文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	// 解析JSON
	var todos []specs.Todo
	if err := json.Unmarshal(data, &todos); err != nil {
		return nil, err
	}

	return todos, nil
}

// 保存任务数据
func (t *todoWriteTool) saveTodos(sessionID string, todos []specs.Todo) error {
	filePath, err := t.getTodoFilePath(sessionID)
	if err != nil {
		return err
	}

	// 序列化为JSON
	data, err := json.MarshalIndent(todos, "", "  ")
	if err != nil {
		return err
	}

	// 写入文件
	return os.WriteFile(filePath, data, 0644)
}

package tools

import (
	"testing"
)

func TestBashKillTool_Info(t *testing.T) {
	tool := NewBashKillTool(&mockPermissionTrigger{shouldAllow: true}, &mockShellService{})
	info := tool.Info()

	if info.Name == "" {
		t.<PERSON>rror("Expected non-empty tool name")
	}

	if info.Description == "" {
		t.<PERSON>r("Expected non-empty description")
	}
}

func TestBashOutputTool_Info(t *testing.T) {
	tool := NewBashOutputTool(&mockPermissionTrigger{shouldAllow: true}, &mockShellService{})
	info := tool.Info()

	if info.Name == "" {
		t.<PERSON>rror("Expected non-empty tool name")
	}

	if info.Description == "" {
		t.<PERSON>rror("Expected non-empty description")
	}
}

func TestMultiEditTool_Info(t *testing.T) {
	tool := NewMultiEditTool(&mockPermissionTrigger{shouldAllow: true}, &mockHistoryService{})
	info := tool.Info()

	if info.Name == "" {
		t.Error("Expected non-empty tool name")
	}

	if info.Description == "" {
		t.<PERSON>rror("Expected non-empty description")
	}
}
package tools

import (
	"fmt"
	"github.com/qoder-ai/qodercli/core/llm/shell"
	"path/filepath"
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/utils"
)

type bashOutputTool struct {
	permissions  llm.PermissionTrigger
	snapshotFile string
	shells       shell.ShellService
}

const (
	maxOutputLength       = 30000
	bashOutputDescription = `
- Retrieves output from a running or completed background bash shell
- Takes a shell_id parameter identifying the shell
- Always returns only new output since the last check
- Returns stdout and stderr output along with shell status
- Supports optional regex filtering to show only lines matching a pattern
- Use this tool when you need to monitor or check the output of a long-running shell
- Shell IDs can be found using the /bashes command`
)

func NewBashOutputTool(permissions llm.PermissionTrigger, shells shell.ShellService) tools.BaseTool {
	return &bashOutputTool{
		permissions:  permissions,
		shells:       shells,
		snapshotFile: filepath.Join(utils.GetUserStorageDir(), "shell-snapshots", fmt.Sprintf("shell-snapshot-%d.sh", time.Now().UnixNano())),
	}
}

func (t *bashOutputTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.BashOutputToolName,
		Description: bashOutputDescription,
		Parameters: map[string]any{
			"bash_id": map[string]any{
				"type":        "string",
				"description": "The ID of the background shell to retrieve output from",
			},
			"filter": map[string]any{
				"type":        "string",
				"description": "Optional regular expression to filter the output lines. Only lines matching this regex will be included in the result. Any lines that do not match will no longer be available to read.",
			},
		},
		Required: []string{"bash_id"},
	}
}

func filterOutput(output, filter string) string {
	// 如果 filter 为空，或 output 去除空格后为空，直接返回 output
	if filter == "" || strings.TrimSpace(output) == "" {
		return output
	}

	// 编译不区分大小写的正则表达式
	regex, err := regexp.Compile("(?i)" + filter)
	if err != nil {
		// 如果正则表达式无效，返回原 output（可选：也可返回空或 panic，视需求而定）
		return output
	}

	// 按行分割
	lines := strings.Split(output, "\n")

	// 过滤出匹配的行
	var filteredLines []string
	for _, line := range lines {
		if regex.MatchString(line) {
			filteredLines = append(filteredLines, line)
		}
	}

	// 用换行符重新连接
	return strings.Join(filteredLines, "\n")
}

// trimBlankLines removes leading and trailing blank lines (including empty lines
// or lines containing only whitespace) from the input string.
// It preserves all content and whitespace in the middle.
func trimBlankLines(s string) string {
	lines := strings.Split(s, "\n")

	// Find first non-blank line
	start := 0
	for ; start < len(lines); start++ {
		if !isBlank(lines[start]) {
			break
		}
	}

	// Find last non-blank line
	end := len(lines) - 1
	for ; end >= 0; end-- {
		if !isBlank(lines[end]) {
			break
		}
	}

	if start > end {
		return ""
	}

	return strings.Join(lines[start:end+1], "\n")
}

// isBlank checks if a string is empty or contains only whitespace.
func isBlank(s string) bool {
	for _, r := range s {
		if !unicode.IsSpace(r) {
			return false
		}
	}
	return true
}

// truncateContent 实现：输入字符串 A，返回总行数和可能被截断的内容
func truncateContent(output string) string {
	if len(output) <= maxOutputLength {
		return output
	}

	content := output[:maxOutputLength]   // 前 B 个字符
	remaining := output[maxOutputLength:] // 剩余部分
	truncatedLineCount := len(strings.Split(remaining, "\n"))

	// 构造结果字符串
	var builder strings.Builder
	builder.WriteString(content)
	builder.WriteString("\n... [")
	builder.WriteString(fmt.Sprintf("%d", truncatedLineCount))
	builder.WriteString(" lines truncated] ...")

	return builder.String()
}

func (t *bashOutputTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.BashOutputSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("invalid parameters"), nil
	}

	// 获取会话信息
	sessionId := ctx.GetSessionId()
	messageId := ctx.GetMessageId()
	if sessionId == "" || messageId == "" {
		return tools.ToolResponse{}, fmt.Errorf("session Id and message Id are required for creating a new file")
	}
	p := t.permissions.CreateRequestWithContext(ctx,
		core.CreatePermissionRequest{
			SessionId:   sessionId,
			Path:        ctx.GetWorkingDir(),
			ToolName:    tools.BashOutputToolName,
			Action:      "execute",
			Description: fmt.Sprintf("Query bash output: %s", params.BashId),
			Params: specs.BashOutputPermissionsParams{
				BashId: params.BashId,
				Filter: params.Filter,
			},
		},
	)
	if !p {
		return tools.ToolResponse{}, core.ErrorPermissionDenied
	}

	// 查询 Background shell
	sh, exists := t.shells.GetShell(params.BashId)
	if !exists {
		metadata := specs.BashOutputResponseMetadata{BashId: params.BashId}
		message := fmt.Sprintf("Shell %s not found", params.BashId)
		return tools.WithResponseMetadata(tools.NewTextResponse(message), metadata), nil
	}

	result := sh.GetResult()
	stdout := filterOutput(result.Stdout, params.Filter)
	stdout = truncateContent(trimBlankLines(stdout))
	stderr := filterOutput(result.Stderr, params.Filter)
	stderr = truncateContent(trimBlankLines(stderr))
	exitCode := result.ExitCode

	var respParts []string
	respParts = append(respParts, fmt.Sprintf("<status>%s</status>", result.Status))
	if exitCode != nil {
		respParts = append(respParts, fmt.Sprintf("<exit_code>%d</exit_code>", *exitCode))
	}
	if len(stdout) > 0 {
		respParts = append(respParts, fmt.Sprintf("<stdout>\n%s\n</stdout>", stdout))
	}
	if len(stderr) > 0 {
		respParts = append(respParts, fmt.Sprintf("<stderr>\n%s\n</stderr>", stderr))
	}
	respParts = append(respParts, fmt.Sprintf("<timestamp>%s</timestamp>", time.Now().Format(time.RFC3339)))

	// 构建响应内容
	response := strings.Join(respParts, "\n\n")
	metadata := specs.BashOutputResponseMetadata{
		ExitCode: exitCode,
		Stdout:   stdout,
		Stderr:   stderr,
		BashId:   result.BashId,
		Status:   result.Status,
		Command:  result.Command,
	}
	return tools.WithResponseMetadata(tools.NewTextResponse(response), metadata), nil
}

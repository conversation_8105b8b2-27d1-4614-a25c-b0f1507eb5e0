package tools

import (
	"context"
	"encoding/json"
	"strings"
	"testing"
	"time"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/shell"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
)

// mockPermissionTrigger 模拟权限触发器
type mockPermissionTrigger struct {
	shouldAllow bool
}

func (m *mockPermissionTrigger) CreateRequestWithContext(ctx core.SessionContext, opts core.CreatePermissionRequest) bool {
	return m.shouldAllow
}

// mockShellService 模拟shell服务
type mockShellService struct{}

func (m *mockShellService) ListShells() []shell.Shell {
	return []shell.Shell{&mockShell{}}
}

func (m *mockShellService) GetShell(shellId string) (shell.Shell, bool) {
	return &mockShell{}, true
}

func (m *mockShellService) Exec(ctx context.Context, workingDir string, command string, timeout time.Duration) (string, error) {
	return "test output", nil
}

// mockShell 模拟shell
type mockShell struct{}

func (m *mockShell) GetStartTime() time.Time {
	return time.Now()
}

func (m *mockShell) IsExecuting() bool {
	return false
}

func (m *mockShell) GetResult() shell.CommandResult {
	exitCode := 0
	return shell.CommandResult{
		Stdout:   "test output",
		Stderr:   "",
		ExitCode: &exitCode,
		Error:    nil,
	}
}

func (m *mockShell) WaitForResult() shell.CommandResult {
	return m.GetResult()
}

func (m *mockShell) KillProcess() error {
	return nil
}

func TestBashTool_Run(t *testing.T) {
	// 创建bash工具
	tool := &bashTool{
		permissions: &mockPermissionTrigger{
			shouldAllow: true,
		},
		shells: &mockShellService{},
	}

	name := "echo命令成功"
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()
	t.Run(name, func(t *testing.T) {
		params := specs.BashParams{
			Command: "echo 'test'",
			Timeout: 5000,
		}
		input, err := json.Marshal(params)
		if err != nil {
			t.Fatalf("Failed to marshal params: %v", err)
		}

		call := tools.ToolCall{
			Id:    "test-call-789",
			Name:  tools.BashToolName,
			Input: string(input),
		}
		response, err := tool.Run(toolCtx, call)
		if err != nil {
			t.Fatalf("Run failed: %v", err)
		}

		t.Logf("=== 退出码解释测试: %s ===\n", name)
		t.Logf("是否错误: %t\n", response.IsError)
		t.Logf("结果消息: %s\n", response.Metadata)
	})
}

// Additional tests for edge cases and better coverage
func TestBashTool_parseCommandType(t *testing.T) {
	tool := &bashTool{}
	
	tests := []struct {
		command  string
		expected string
	}{
		{"git status", "git"},
		{"git commit -m 'test'", "git"},
		{"npm install", "npm"},
		{"npm run build", "npm"},
		{"go test", "go"},
		{"go build", "go"},
		{"python script.py", "python"},
		{"python3 script.py", "python"},
		{"echo hello", "default"},
		{"ls -la", "default"},
	}
	
	for _, tt := range tests {
		t.Run(tt.command, func(t *testing.T) {
			result := tool.parseCommandType(tt.command)
			// Just ensure the function doesn't panic
			_ = result
		})
	}
}

func TestBashTool_trackGitOperations(t *testing.T) {
	tool := &bashTool{}
	
	tests := []struct {
		name     string
		command  string
		exitCode *int
		shouldTrack bool
	}{
		{
			name:     "successful git commit",
			command:  "git commit -m 'test commit'",
			exitCode: nil, // nil means success
			shouldTrack: true,
		},
		{
			name:     "failed git commit",
			command:  "git commit -m 'test commit'",
			exitCode: bashIntPtr(1), // non-zero exit code
			shouldTrack: false,
		},
		{
			name:     "successful gh pr create",
			command:  "gh pr create --title 'test pr'",
			exitCode: nil,
			shouldTrack: true,
		},
		{
			name:     "non-git command",
			command:  "echo hello",
			exitCode: nil,
			shouldTrack: false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := shell.CommandResult{
				ExitCode: tt.exitCode,
				Stdout:   "test output",
				Stderr:   "",
			}
			
			// This test mainly ensures the function doesn't panic
			// In a real implementation, you'd mock the monitoring package
			tool.trackGitOperations("test-session", tt.command, result)
		})
	}
}

func TestBashTool_processContent(t *testing.T) {
	tool := &bashTool{}
	
	tests := []struct {
		name           string
		content        string
		expectedIsImg  bool
		expectedLength int // approximate expected length after processing
	}{
		{
			name:           "empty content",
			content:        "",
			expectedIsImg:  false,
			expectedLength: 0,
		},
		{
			name:           "simple text",
			content:        "hello world",
			expectedIsImg:  false,
			expectedLength: 11,
		},
		{
			name:           "content with empty lines",
			content:        "\n\nhello world\n\n",
			expectedIsImg:  false,
			expectedLength: 11,
		},
		{
			name:           "very long content",
			content:        strings.Repeat("a", 10000),
			expectedIsImg:  false,
			expectedLength: 5000, // should be truncated
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isImg, processed := tool.processContent(tt.content)
			
			if isImg != tt.expectedIsImg {
				t.Errorf("processContent(%q) isImg = %v, want %v", tt.name, isImg, tt.expectedIsImg)
			}
			
			if tt.expectedLength > 0 && len(processed) > tt.expectedLength*2 {
				t.Errorf("processContent(%q) length = %d, expected around %d", tt.name, len(processed), tt.expectedLength)
			}
		})
	}
}

func TestBashTool_truncateContent(t *testing.T) {
	tool := &bashTool{}
	
	tests := []struct {
		name     string
		content  string
		maxLen   int
	}{
		{
			name:    "short content",
			content: "hello world",
			maxLen:  100,
		},
		{
			name:    "long content",
			content: strings.Repeat("test line\n", 100), // Reduced size
			maxLen:  5000,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.truncateContent(tt.content)
			
			// Just ensure the function doesn't panic and returns something
			if result == "" && tt.content != "" {
				t.Error("Expected non-empty result for non-empty input")
			}
		})
	}
}

func TestBashTool_buildNonBackgroundResponse(t *testing.T) {
	tool := &bashTool{}
	
	tests := []struct {
		name     string
		result   shell.CommandResult
		command  string
	}{
		{
			name: "successful command",
			result: shell.CommandResult{
				ExitCode: nil,
				Stdout:   "success output",
				Stderr:   "",
			},
			command:  "echo hello",
		},
		{
			name: "failed command",
			result: shell.CommandResult{
				ExitCode: bashIntPtr(1),
				Stdout:   "",
				Stderr:   "error output",
			},
			command:  "false",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			params := &specs.BashParams{Command: tt.command}
			response := tool.buildNonBackgroundResponse(params, tt.result)
			
			// Just ensure the function doesn't panic and returns something
			if response == "" {
				t.Error("Expected non-empty response")
			}
		})
	}
}

// Helper function for bash tests
func bashIntPtr(i int) *int {
	return &i
}

package tools

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/logging"
	"io"
	"net/http"
	"net/url"
	"time"
	"unicode/utf8"
)

const ResultLimit = 10
const QueryLengthLimit = 100
const webSearchEndpoint = "https://center.qoder.sh/algo"
const webSearchUrl = "/api/v1/webSearch/serpApiSearch"
const encodeRequestVersion = "0"
const snippetLengthLimit = 50

const searchWebDescription = `Explore the web for real-time information on any topic.
Use this tool when you need up-to-date information that might not be included in your existing knowledge, or when you need to verify current facts. 
The search results will include relevant snippets and URLs from web pages.`

var client *http.Client

type SearchRequest struct {
	Query     string `json:"query"`
	TimeRange string `json:"timeRange"`
}

type SearchResponse struct {
	ErrorCode int                 `json:"errorCode"`
	ErrorMsg  string              `json:"errorMsg"`
	RequestId string              `json:"requestId"`
	PageItems []*SearchResultItem `json:"pageItems"`
}

type SearchResultItem struct {
	Title         string `json:"title"`         // 网站标题
	Link          string `json:"link"`          // 网站地址
	Snippet       string `json:"snippet"`       // 网页动态摘要，匹配到关键字的部分内容，平均长度150字符
	PublishedTime string `json:"publishedTime"` // 网页动态摘要，匹配到关键字的部分内容，平均长度150字符
	Hostname      string `json:"hostname"`      // 网页的站点名称
	HostLogo      string `json:"hostLogo"`      // 网页的站点Logo
	MainText      string `json:"mainText"`      // 解析得到的网页全正文，长度最大3000字符，召回比例超过98%
	MarkdownText  string `json:"markdownText"`  // 解析得到的网页全文markdown格式，对表格等结构化信息有更好的支持，目前召回比例约50%，待持续提高。
	Summary       string `json:"summary"`       // 从网页全正文中提取出与查询（Query）最相关的信息，用于提供增强摘要，默认长度约500字
}

type remoteSearchRequest struct {
	Query     string                      `json:"query"`               // 搜索问题。取值范围：1~100个字符。
	TimeRange string                      `json:"timeRange,omitempty"` // 查询的时间范围。支持可选值： OneDay：1天内 OneWeek：1周内  OneMonth：1月内 OneYear：1年内  NoLimit：无限制（默认值）
	Contents  *remoteSearchRequestContent `json:"contents,omitempty"`
}

type remoteSearchRequestContent struct {
	MainText     bool `json:"mainText"`     // 是否返回长正文
	MarkdownText bool `json:"markdownText"` // 是否返回markdown格式正文
	Summary      bool `json:"summary"`      // 是否返回摘要
}
type HttpPayload struct {

	//负载
	Payload string `json:"payload"`

	// 加密版本
	EncodeVersion string `json:"encodeVersion"`
}

type searchWebTool struct{}

func NewSearchWebTool() tools.BaseTool {
	client = newClientWithTimeout(time.Second*30, 5)
	return &searchWebTool{}
}

func (t searchWebTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.WebSearchToolName,
		Description: searchWebDescription,
		Parameters: map[string]any{
			"query": map[string]any{
				"type":        "string",
				"description": "Search query. Value range: 1 to 100 characters.",
			},
			"time_range": map[string]any{
				"type":        "string",
				"description": "The time range of the query. Optional ranges: 'OneDay', 'OneWeek', 'OneMonth', 'OneYear', 'NoLimit'. Default is NoLimit.",
				"enum":        []string{"OneDay", "OneWeek", "OneMonth", "OneYear", "NoLimit"},
			},
		},
		Required: []string{"query"},
	}
}

func (t searchWebTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	var request SearchRequest
	if call.Input == "" {
		return tools.NewTextErrorResponse("webSearch tool Error: Missing query"), nil
	}
	if err := json.Unmarshal([]byte(call.Input), &request); err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error parsing parameters: %s", err)), nil
	}
	if utf8.RuneCountInString(request.Query) > QueryLengthLimit {
		request.Query = TruncateStringByRuneCount(request.Query, QueryLengthLimit)
	}
	payload := remoteSearchRequest{
		Query:     request.Query,
		TimeRange: request.TimeRange,
		Contents: &remoteSearchRequestContent{
			MainText:     false,
			MarkdownText: false,
			Summary:      false,
		},
	}
	httpBody := HttpPayload{
		Payload:       ToJsonStr(payload),
		EncodeVersion: encodeRequestVersion,
	}
	responseBody, err := doSearch(&httpBody)
	if err != nil {
		logging.Error("search web failed", err)
		return tools.NewTextErrorResponse(fmt.Sprintf("search web failed: %s", err)), nil
	}
	var searchResponse SearchResponse
	if err := json.Unmarshal(responseBody, &searchResponse); err != nil {
		logging.Error("search web failed, json unmarshal error", responseBody, err)
		return tools.NewTextErrorResponse(fmt.Sprintf("search web failed, json unmarshal failed: %s", err)), nil
	}
	if searchResponse.ErrorCode != 0 {
		logging.Error("search web failed, status code", searchResponse.ErrorCode, searchResponse.ErrorMsg)
		return tools.NewTextErrorResponse(fmt.Sprintf("search web failed: code %d, message %s", searchResponse.ErrorCode, searchResponse.ErrorMsg)), nil
	}
	filterSearchResults(&searchResponse)
	fillHostname(&searchResponse)
	return tools.NewTextResponse(buildSearchResult(&searchResponse, request.Query)), nil
}

func doSearch(payload *HttpPayload) ([]byte, error) {
	url := buildSearchUrl(webSearchEndpoint, webSearchUrl)
	bodyBytes := toBytes(payload)
	req, err := createHTTPRequest(http.MethodPost, url, bodyBytes)
	if err != nil {
		logging.Error("create request failed", err)
		return make([]byte, 0), err
	}
	resp, err := client.Do(req)
	if err != nil {
		logging.Warn("Failed to send request, url=%s, error=%v", url, err)
		return make([]byte, 0), fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		logging.Warn("Failed to get response, url=%s, status=%s", url, resp.Status)
		return make([]byte, 0), fmt.Errorf("failed to get response, status=%s", resp.Status)
	}
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logging.Warn("Failed to read response, url=%s, error=%v", url, err)
		return make([]byte, 0), err
	}
	return responseBody, nil
}

// TruncateStringByRuneCount 截取字符串到指定的Unicode字符数量
func TruncateStringByRuneCount(s string, maxRuneCount int) string {
	// 计算字符串中的字符数量
	runeCount := utf8.RuneCountInString(s)
	// 如果字符数小于或等于最大字符数，则直接返回原字符串
	if runeCount <= maxRuneCount {
		return s
	}

	// 创建一个切片来存储截取后的字符
	var truncated []rune
	curRuneCount := 0
	for _, r := range s {
		curRuneCount++
		if curRuneCount > maxRuneCount {
			break
		}
		// 将当前字符添加到切片
		truncated = append(truncated, r)
	}
	// 将 rune 切片转换回字符串
	return string(truncated)
}

func newDefaultNoneProxyTransport(maxConnsPerHost int) *http.Transport {
	if maxConnsPerHost <= 0 {
		maxConnsPerHost = 5
	}
	return &http.Transport{
		Proxy:                 nil,
		MaxConnsPerHost:       maxConnsPerHost,
		MaxIdleConns:          3,                // 最大空闲连接数
		MaxIdleConnsPerHost:   3,                // 每个主机的最大空闲连接数
		IdleConnTimeout:       10 * time.Minute, // 空闲连接的超时时间
		ForceAttemptHTTP2:     true,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 2 * time.Second,
		TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
		DisableCompression:    true,
	}
}

func buildProxyTransport(maxConnsPerHost int) *http.Transport {
	return newDefaultNoneProxyTransport(maxConnsPerHost)
}

func newClientWithTimeout(timeout time.Duration, maxConnsPerHost int) *http.Client {
	cli := &http.Client{
		Timeout: timeout,
	}
	transport := buildProxyTransport(maxConnsPerHost)
	if transport != nil {
		cli.Transport = transport
	}
	return cli
}

// ToJsonStr converts something to a string, it returns an empty string and prints a log if error happens
func ToJsonStr(v interface{}) string {
	if v == nil {
		return ""
	}
	if result, err := json.Marshal(v); err == nil {
		return string(result)
	} else {
		logging.Error("Cannot convert object to json:", err)
	}
	return ""
}

func buildSearchUrl(endpoint string, url string) string {
	return endpoint + url + "?Encode=" + encodeRequestVersion
}

func toBytes(v interface{}) []byte {
	if v == nil {
		return nil
	}
	if result, err := json.Marshal(v); err == nil {
		return result
	} else {
		logging.Error("Cannot convert object to json:", err)
	}
	return nil
}

// createHTTPRequest 创建HTTP请求
func createHTTPRequest(method, urlString string, bodyBytes []byte) (*http.Request, error) {
	req, err := http.NewRequest(method, urlString, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("create request failed")
	}
	return req, nil
}

// filterSearchResults limits the number of search results to the specified limit.
func filterSearchResults(result *SearchResponse) {
	if len(result.PageItems) <= ResultLimit {
		return
	}
	result.PageItems = result.PageItems[:ResultLimit]
}

// fillHostname 会存在hostname为空的情况，补充一下hostname
func fillHostname(response *SearchResponse) {
	for _, item := range response.PageItems {
		if item.Hostname == "" {
			u, err := url.Parse(item.Link)
			if err != nil {
				continue
			}
			item.Hostname = u.Hostname()
		}
	}
}

func buildSearchResult(searchResponse *SearchResponse, query string) string {
	result := fmt.Sprintf("Web search results for query: %s\n\n", query)
	for _, item := range searchResponse.PageItems {
		result += fmt.Sprintf("Title: %s\n", item.Title)
		result += fmt.Sprintf("Link: %s\n", item.Link)
		result += fmt.Sprintf("Hostname: %s\n", item.Hostname)
		result += fmt.Sprintf("Snippet: %s\n", TruncateStringByRuneCount(item.Snippet, snippetLengthLimit))
		result += "\n"
	}
	return result
}

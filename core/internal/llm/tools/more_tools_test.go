package tools

import (
	"context"
	"strings"
	"testing"

	"github.com/qoder-ai/qodercli/core/llm/tools"
)

func TestWebSearchTool_Info(t *testing.T) {
	tool := NewSearchWebTool()
	info := tool.Info()

	if info.Name == "" {
		t.<PERSON>rror("Expected non-empty tool name")
	}

	if info.Description == "" {
		t.<PERSON>r("Expected non-empty description")
	}

	// Check required parameters
	if _, exists := info.Parameters["query"]; !exists {
		t.Error("Expected query parameter")
	}
}

func TestWebSearchTool_Run_Success(t *testing.T) {
	tool := NewSearchWebTool()
	
	ctx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session").
		WithMessageID("test-message").
		Build()
	
	call := tools.ToolCall{
		Input: `{"query": "golang testing"}`,
	}
	
	response, err := tool.Run(ctx, call)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	
	if response.Content == "" {
		t.Error("Expected non-empty response content")
	}
}

func TestWebSearchTool_Run_InvalidJSON(t *testing.T) {
	tool := NewSearchWebTool()
	
	ctx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session").
		WithMessageID("test-message").
		Build()
	
	call := tools.ToolCall{
		Input: `invalid json`,
	}
	
	response, err := tool.Run(ctx, call)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	
	if !strings.Contains(response.Content, "error parsing parameters") {
		t.Errorf("Expected error message about parsing parameters, got %s", response.Content)
	}
}

func TestDiagnosticsTool_Info(t *testing.T) {
	tool := NewDiagnosticsTool(nil)
	info := tool.Info()

	if info.Name == "" {
		t.Error("Expected non-empty tool name")
	}

	if info.Description == "" {
		t.Error("Expected non-empty description")
	}
}

func TestDiagnosticsTool_Run_Success(t *testing.T) {
	tool := NewDiagnosticsTool(nil)
	
	ctx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session").
		WithMessageID("test-message").
		Build()
	
	call := tools.ToolCall{
		Input: `{}`,
	}
	
	response, err := tool.Run(ctx, call)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	
	if response.Content == "" {
		t.Error("Expected non-empty response content")
	}
}

func TestPatchTool_Info(t *testing.T) {
	tool := NewPatchTool(nil, &mockPermissionTrigger{shouldAllow: true}, &mockHistoryService{
		files:    make(map[string]string),
		versions: make(map[string][]string),
	})
	info := tool.Info()

	if info.Name == "" {
		t.Error("Expected non-empty tool name")
	}

	if info.Description == "" {
		t.Error("Expected non-empty description")
	}
}

func TestPatchTool_Run_InvalidJSON(t *testing.T) {
	tool := NewPatchTool(nil, &mockPermissionTrigger{shouldAllow: true}, &mockHistoryService{
		files:    make(map[string]string),
		versions: make(map[string][]string),
	})
	
	ctx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session").
		WithMessageID("test-message").
		Build()
	
	call := tools.ToolCall{
		Input: `invalid json`,
	}
	
	response, err := tool.Run(ctx, call)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	
	if response.Content != "invalid parameters" {
		t.Errorf("Expected 'invalid parameters', got %s", response.Content)
	}
}
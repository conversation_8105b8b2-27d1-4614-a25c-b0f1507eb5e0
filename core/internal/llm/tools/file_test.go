package tools

import (
	"testing"
	"time"
)

func TestRecordFileRead(t *testing.T) {
	testPath := "/test/file.txt"
	
	// Clear any existing records
	fileRecordMutex.Lock()
	delete(fileRecords, testPath)
	fileRecordMutex.Unlock()
	
	// Record a file read
	beforeTime := time.Now()
	recordFileRead(testPath)
	afterTime := time.Now()
	
	// Check that the read time was recorded
	readTime := getLastReadTime(testPath)
	if readTime.IsZero() {
		t.Error("Expected non-zero read time")
	}
	
	if readTime.Before(beforeTime) || readTime.After(afterTime) {
		t.Errorf("Read time %v should be between %v and %v", readTime, beforeTime, afterTime)
	}
}

func TestGetLastReadTime_NonExistent(t *testing.T) {
	testPath := "/nonexistent/file.txt"
	
	// Clear any existing records
	fileRecordMutex.Lock()
	delete(fileRecords, testPath)
	fileRecordMutex.Unlock()
	
	readTime := getLastReadTime(testPath)
	if !readTime.IsZero() {
		t.<PERSON>("Expected zero time for non-existent file, got %v", readTime)
	}
}

func TestRecordFileWrite(t *testing.T) {
	testPath := "/test/write_file.txt"
	
	// Clear any existing records
	fileRecordMutex.Lock()
	delete(fileRecords, testPath)
	fileRecordMutex.Unlock()
	
	// Record a file write
	beforeTime := time.Now()
	recordFileWrite(testPath)
	afterTime := time.Now()
	
	// Check that the write time was recorded
	fileRecordMutex.RLock()
	record, exists := fileRecords[testPath]
	fileRecordMutex.RUnlock()
	
	if !exists {
		t.Error("Expected file record to exist")
	}
	
	if record.writeTime.IsZero() {
		t.Error("Expected non-zero write time")
	}
	
	if record.writeTime.Before(beforeTime) || record.writeTime.After(afterTime) {
		t.Errorf("Write time %v should be between %v and %v", record.writeTime, beforeTime, afterTime)
	}
}

func TestFileRecordUpdate(t *testing.T) {
	testPath := "/test/update_file.txt"
	
	// Clear any existing records
	fileRecordMutex.Lock()
	delete(fileRecords, testPath)
	fileRecordMutex.Unlock()
	
	// Record initial read
	recordFileRead(testPath)
	firstReadTime := getLastReadTime(testPath)
	
	// Wait a bit to ensure different timestamps
	time.Sleep(1 * time.Millisecond)
	
	// Record another read
	recordFileRead(testPath)
	secondReadTime := getLastReadTime(testPath)
	
	if !secondReadTime.After(firstReadTime) {
		t.Error("Second read time should be after first read time")
	}
	
	// Record a write
	recordFileWrite(testPath)
	
	// Check that both read and write times are recorded
	fileRecordMutex.RLock()
	record, exists := fileRecords[testPath]
	fileRecordMutex.RUnlock()
	
	if !exists {
		t.Error("Expected file record to exist")
	}
	
	if record.readTime.IsZero() {
		t.Error("Expected non-zero read time")
	}
	
	if record.writeTime.IsZero() {
		t.Error("Expected non-zero write time")
	}
	
	if record.path != testPath {
		t.Errorf("Expected path %s, got %s", testPath, record.path)
	}
}

func TestConcurrentFileOperations(t *testing.T) {
	testPath := "/test/concurrent_file.txt"
	
	// Clear any existing records
	fileRecordMutex.Lock()
	delete(fileRecords, testPath)
	fileRecordMutex.Unlock()
	
	// Run concurrent operations
	done := make(chan bool, 10)
	
	// Start multiple goroutines doing reads and writes
	for i := 0; i < 5; i++ {
		go func() {
			recordFileRead(testPath)
			done <- true
		}()
		
		go func() {
			recordFileWrite(testPath)
			done <- true
		}()
	}
	
	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}
	
	// Check that the record exists and has valid times
	fileRecordMutex.RLock()
	record, exists := fileRecords[testPath]
	fileRecordMutex.RUnlock()
	
	if !exists {
		t.Error("Expected file record to exist after concurrent operations")
	}
	
	if record.readTime.IsZero() {
		t.Error("Expected non-zero read time after concurrent operations")
	}
	
	if record.writeTime.IsZero() {
		t.Error("Expected non-zero write time after concurrent operations")
	}
}
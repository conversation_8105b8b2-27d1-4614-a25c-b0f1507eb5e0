package tools

import (
	"fmt"
	"github.com/qoder-ai/qodercli/core/internal/llm/tools/grep"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/utils"
	"github.com/qoder-ai/qodercli/core/utils/fileutil"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
)

type GrepResult struct {
	Mode       string   `json:"mode"`
	Filenames  []string `json:"filenames,omitempty"`
	NumFiles   int      `json:"numFiles"`
	Content    string   `json:"content,omitempty"`
	NumLines   int      `json:"numLines,omitempty"`
	NumMatches int      `json:"numMatches,omitempty"`
}

type GrepTool struct{}

const (
	grepDescription = `A powerful search tool built on ripgrep

  Usage:
  - ALWAYS use Grep for search tasks. NEVER invoke ` + "`grep`" + ` or ` + "`rg`" + ` as a Bash command. The Grep tool has been optimized for correct permissions and access.
  - Supports full regex syntax (e.g., "log.*Error", "function\\s+\\w+")
  - Filter files with glob parameter (e.g., "*.js", "**/*.tsx") or type parameter (e.g., "js", "py", "rust")
  - Output modes: "content" shows matching lines, "files_with_matches" shows only file paths (default), "count" shows match counts
  - Use Task tool for open-ended searches requiring multiple rounds
  - Pattern syntax: Uses ripgrep (not grep) - literal braces need escaping (use ` + "`interface\\{\\}`" + ` to find ` + "`interface{}`" + ` in Go code)
  - Multiline matching: By default patterns match within single lines only. For cross-line patterns like ` + "`struct \\{[\\s\\S]*?field`" + `, use ` + "`multiline: true`" + `
`
)

func NewGrepTool() tools.BaseTool {
	return &GrepTool{}
}

func (g *GrepTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.GrepToolName,
		Description: grepDescription,
		Parameters: map[string]any{
			"pattern": map[string]any{
				"type":        "string",
				"description": "The regex pattern to search for in file contents",
			},
			"path": map[string]any{
				"type":        "string",
				"description": "File or directory to search in (rg PATH). Defaults to current working directory",
			},
			"glob": map[string]any{
				"type":        "string",
				"description": `Glob pattern to filter files (e.g. "*.js", "*.{ts,tsx}") - maps to rg --glob`,
			},
			"output_mode": map[string]any{
				"type":        "string",
				"description": `Output mode: "content" shows matching lines (supports -A/-B/-C context, -n line numbers, head_limit), "files_with_matches" shows file paths (supports head_limit), "count" shows match counts (supports head_limit). Defaults to "files_with_matches".`,
				"enum":        []string{"content", "files_with_matches", "count"},
			},
			"-B": map[string]any{
				"type":        "integer",
				"description": `Number of lines to show before each match (rg -B). Requires output_mode: "content", ignored otherwise.`,
			},
			"-A": map[string]any{
				"type":        "integer",
				"description": `Number of lines to show after each match (rg -A). Requires output_mode: "content", ignored otherwise.`,
			},
			"-C": map[string]any{
				"type":        "integer",
				"description": `Number of lines to show before and after each match (rg -C). Requires output_mode: "content", ignored otherwise.`,
			},
			"-n": map[string]any{
				"type":        "boolean",
				"description": `Show line numbers in output (rg -n). Requires output_mode: "content", ignored otherwise.`,
			},
			"-i": map[string]any{
				"type":        "boolean",
				"description": "Case insensitive search (rg -i)",
			},
			"type": map[string]any{
				"type":        "string",
				"description": "File type to search (rg --type). Common types: js, py, rust, go, java, etc. More efficient than include for standard file types.",
			},
			"head_limit": map[string]any{
				"type":        "integer",
				"description": `Limit output to first N lines/entries, equivalent to "| head -N". Works across all output modes: content (limits output lines), files_with_matches (limits file paths), count (limits count entries). When unspecified, shows all results from ripgrep.`,
			},
			"multiline": map[string]any{
				"type":        "boolean",
				"description": "Enable multiline mode where . matches newlines and patterns can span lines (rg -U --multiline-dotall). Default: false.",
			},
		},
		Required: []string{"pattern"},
	}
}

func (g *GrepTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.GrepSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error parsing parameters: %s", err)), nil
	}

	if params.Pattern == "" {
		return tools.NewTextErrorResponse("pattern is required"), nil
	}

	// 设置默认值
	if params.OutputMode == "" {
		params.OutputMode = "files_with_matches"
	}

	// 解析搜索路径
	searchPath := g.resolveSearchPath(ctx, params.Path)

	// 构建ripgrep命令参数
	args, err := g.buildRipgrepArgs(params)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error building ripgrep arguments: %s", err)), nil
	}

	// 添加忽略模式
	args = g.addIgnorePatterns(args, searchPath)

	// 添加搜索路径
	args = append(args, searchPath)

	// 执行ripgrep命令
	output, err := g.executeRipgrep(ctx, args)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("ripgrep execution failed: %s", err)), nil
	}

	// 处理输出结果
	result, err := g.processOutput(output, params, searchPath)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error processing output: %s", err)), nil
	}

	// 生成响应
	return g.generateResponse(result), nil
}

// resolveSearchPath 解析搜索路径
func (g *GrepTool) resolveSearchPath(ctx tools.ToolExecutionContext, path string) string {
	if path == "" {
		return ctx.GetWorkingDir()
	}

	if filepath.IsAbs(path) {
		return path
	}

	return filepath.Join(ctx.GetWorkingDir(), path)
}

// buildRipgrepArgs 构建ripgrep命令参数
func (g *GrepTool) buildRipgrepArgs(params *specs.GrepParams) ([]string, error) {
	var args []string

	// 多行匹配
	if params.Multiline != nil && *params.Multiline {
		args = append(args, "-U", "--multiline-dotall")
	}

	// 忽略大小写
	if params.IgnoreCase != nil && *params.IgnoreCase {
		args = append(args, "-i")
	}

	// 输出模式
	switch params.OutputMode {
	case "files_with_matches":
		args = append(args, "-l")
	case "count":
		args = append(args, "-c")
	case "content":
		// 行号
		if params.LineNumbers != nil && *params.LineNumbers {
			args = append(args, "-n")
		}
		if params.ContextC != nil {
			args = append(args, "-C", strconv.Itoa(*params.ContextC))
		} else {
			if params.ContextB != nil {
				args = append(args, "-B", strconv.Itoa(*params.ContextB))
			}
			if params.ContextA != nil {
				args = append(args, "-A", strconv.Itoa(*params.ContextA))
			}
		}
	default:
		return nil, fmt.Errorf("invalid output_mode: %s", params.OutputMode)
	}

	// 搜索模式
	if strings.HasPrefix(params.Pattern, "-") {
		args = append(args, "-e", params.Pattern)
	} else {
		args = append(args, params.Pattern)
	}

	// 文件类型
	if params.Type != "" {
		args = append(args, "--type", params.Type)
	}

	// Glob模式
	if params.Glob != "" {
		globs := g.parseGlobPatterns(params.Glob)
		for _, glob := range globs {
			args = append(args, "--glob", glob)
		}
	}

	return args, nil
}

// parseGlobPatterns 解析glob模式
func (g *GrepTool) parseGlobPatterns(globStr string) []string {
	var globs []string

	// 按空格分割
	parts := strings.Fields(globStr)

	for _, part := range parts {
		// 如果包含花括号，保持原样
		if strings.Contains(part, "{") && strings.Contains(part, "}") {
			globs = append(globs, part)
		} else {
			// 按逗号分割
			subParts := strings.Split(part, ",")
			for _, subPart := range subParts {
				if strings.TrimSpace(subPart) != "" {
					globs = append(globs, strings.TrimSpace(subPart))
				}
			}
		}
	}

	return globs
}

// addIgnorePatterns 添加忽略模式
func (g *GrepTool) addIgnorePatterns(args []string, searchPath string) []string {
	// TODO 简化实现：添加常见的忽略模式
	commonIgnores := []string{
		"!.git/**",
		"!node_modules/**",
		"!.vscode/**",
		"!*.log",
		"!.DS_Store",
	}

	for _, ignore := range commonIgnores {
		args = append(args, "--glob", ignore)
	}

	return args
}

// getEmbeddedRgPath returns the path to the embedded ripgrep binary if available for the current platform
func (g *GrepTool) getEmbeddedRgPath() (string, error) {
	// Create an executable file for the embedded binary
	tmpFile := filepath.Join(utils.GetUserStorageDir(), "bin", "ripgrep", grep.GetBinaryFileName())

	// Check if executable ripgrep binary exists
	if _, err := os.Stat(tmpFile); err == nil {
		return tmpFile, nil
	}

	dir := filepath.Dir(tmpFile)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		// 创建目录
		if err := os.MkdirAll(dir, 0755); err != nil {
			return "", fmt.Errorf("failed to create directory: %w", err)
		}
	}

	// try to use the embedded binary
	if len(grep.RgBinary) > 0 {
		// Write the embedded binary to the temporary file, works well in windows
		err := os.WriteFile(tmpFile, grep.RgBinary, 0755)
		if err != nil {
			return "", fmt.Errorf("failed to write embedded ripgrep binary: %w", err)
		}

		// Make sure the file is executable
		err = grep.ChangeMode(tmpFile)
		if err != nil {
			return "", fmt.Errorf("failed to make ripgrep binary executable: %w", err)
		}

		return tmpFile, nil
	}

	// No embedded binary available for this platform
	return "", nil
}

// executeRipgrep 执行ripgrep命令
func (g *GrepTool) executeRipgrep(ctx tools.ToolExecutionContext, args []string) ([]string, error) {
	// First, try to use embedded ripgrep binary
	rgPath, err := g.getEmbeddedRgPath()
	if err != nil {
		return nil, fmt.Errorf("failed to get embedded ripgrep binary: %w", err)
	}

	// If no embedded binary is available, fall back to system ripgrep
	if rgPath == "" {
		rgPath = fileutil.GetRgPath()
		if rgPath == "" {
			return nil, fmt.Errorf("ripgrep (rg) not found in PATH. Please install ripgrep to use the grep tool")
		}
	}

	// 使用获取到的ripgrep路径执行命令
	cmd := exec.CommandContext(ctx, rgPath, args...)
	cmd.Dir = ctx.GetWorkingDir()

	output, err := cmd.Output()
	if err != nil {
		// 处理ripgrep的退出码
		if exitError, ok := err.(*exec.ExitError); ok {
			switch exitError.ExitCode() {
			case 1:
				// 没有匹配结果，这是正常情况
				return []string{}, nil
			case 2:
				// 有错误但可能有部分输出
				if len(output) > 0 {
					lines := strings.Split(strings.TrimSpace(string(output)), "\n")
					return g.filterNonEmpty(lines), nil
				}
				return []string{}, nil
			default:
				return nil, fmt.Errorf("ripgrep failed with exit code %d: %s", exitError.ExitCode(), string(exitError.Stderr))
			}
		}
		return nil, err
	}

	if len(output) == 0 {
		return []string{}, nil
	}

	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	return g.filterNonEmpty(lines), nil
}

// filterNonEmpty 过滤空行
func (g *GrepTool) filterNonEmpty(lines []string) []string {
	var result []string
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			result = append(result, line)
		}
	}
	return result
}

// processOutput 处理输出结果
func (g *GrepTool) processOutput(lines []string, params *specs.GrepParams, searchPath string) (*GrepResult, error) {
	result := &GrepResult{
		Mode: params.OutputMode,
	}

	// 应用结果限制
	if params.HeadLimit != nil && *params.HeadLimit > 0 {
		limit := *params.HeadLimit
		if len(lines) > limit {
			lines = lines[:limit]
		}
	}

	switch params.OutputMode {
	case "content":
		result.Content = strings.Join(lines, "\n")
		result.NumLines = len(lines)

	case "count":
		numMatches := 0
		numFiles := 0

		for _, line := range lines {
			// ripgrep -c 输出格式: file:count
			lastColon := strings.LastIndex(line, ":")
			if lastColon > 0 {
				countStr := line[lastColon+1:]
				if count, err := strconv.Atoi(countStr); err == nil {
					numMatches += count
					numFiles++
				}
			}
		}

		result.NumMatches = numMatches
		result.NumFiles = numFiles
		result.Content = strings.Join(lines, "\n")

	case "files_with_matches":
		// 按文件修改时间排序
		sortedFiles, err := g.sortFilesByModTime(lines)
		if err != nil {
			// 如果排序失败，使用原始顺序
			sortedFiles = lines
		}

		result.Filenames = sortedFiles
		result.NumFiles = len(sortedFiles)
	}

	return result, nil
}

// sortFilesByModTime 按修改时间排序文件
func (g *GrepTool) sortFilesByModTime(filenames []string) ([]string, error) {
	type fileInfo struct {
		path    string
		modTime time.Time
	}

	var files []fileInfo

	for _, filename := range filenames {
		stat, err := os.Stat(filename)
		if err != nil {
			// 如果无法获取文件信息，使用零时间
			files = append(files, fileInfo{
				path:    filename,
				modTime: time.Time{},
			})
			continue
		}

		files = append(files, fileInfo{
			path:    filename,
			modTime: stat.ModTime(),
		})
	}

	// 按修改时间降序排序（最新的在前）
	sort.Slice(files, func(i, j int) bool {
		if files[i].modTime.Equal(files[j].modTime) {
			// 如果时间相同，按路径名排序
			return files[i].path < files[j].path
		}
		return files[i].modTime.After(files[j].modTime)
	})

	// 提取排序后的文件路径
	var sortedPaths []string
	for _, file := range files {
		sortedPaths = append(sortedPaths, file.path)
	}

	return sortedPaths, nil
}

// generateResponse 生成响应
func (g *GrepTool) generateResponse(result *GrepResult) tools.ToolResponse {
	var responseText string

	switch result.Mode {
	case "content":
		if result.NumLines == 0 {
			responseText = "No matches found"
		} else {
			responseText = fmt.Sprintf("Found %d matching lines:\n\n%s",
				result.NumLines, result.Content)
		}

	case "count":
		if result.NumMatches == 0 {
			responseText = "No matches found"
		} else {
			responseText = fmt.Sprintf("Found %d total %s across %d %s",
				result.NumMatches,
				g.pluralize("occurrence", result.NumMatches),
				result.NumFiles,
				g.pluralize("file", result.NumFiles))

			if result.Content != "" {
				responseText += "\n\n" + result.Content
			}
		}

	case "files_with_matches":
		if result.NumFiles == 0 {
			responseText = "No files found"
		} else {
			responseText = fmt.Sprintf("Found %d %s:\n\n%s",
				result.NumFiles,
				g.pluralize("file", result.NumFiles),
				strings.Join(result.Filenames, "\n"))
		}
	}

	return tools.WithResponseMetadata(tools.NewTextResponse(responseText), result)
}

// pluralize 处理单复数
func (g *GrepTool) pluralize(word string, count int) string {
	if count == 1 {
		return word
	}
	return word + "s"
}

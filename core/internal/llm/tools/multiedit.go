package tools

import (
	"fmt"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"os"
	"path/filepath"
	"strings"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
)

type multiEditTool struct {
	permissions llm.PermissionTrigger
	files       core.HistoryService
}

const (
	multiEditDescription = `This is a tool for making multiple edits to a single file in one operation. It is built on top of the Edit tool and allows you to perform multiple find-and-replace operations efficiently. Prefer this tool over the Edit tool when you need to make multiple edits to the same file.

Before using this tool:

1. Use the Read tool to understand the file's contents and context
2. Verify the directory path is correct

To make multiple file edits, provide the following:
1. file_path: The absolute path to the file to modify (must be absolute, not relative)
2. edits: An array of edit operations to perform, where each edit contains:
   - old_string: The text to replace (must match the file contents exactly, including all whitespace and indentation)
   - new_string: The edited text to replace the old_string
   - replace_all: Replace all occurrences of old_string. This parameter is optional and defaults to false.

IMPORTANT:
- All edits are applied in sequence, in the order they are provided
- Each edit operates on the result of the previous edit
- All edits must be valid for the operation to succeed - if any edit fails, none will be applied
- This tool is ideal when you need to make several changes to different parts of the same file
- For Jupyter notebooks (.ipynb files), use the NotebookEdit instead

CRITICAL REQUIREMENTS:
1. All edits follow the same requirements as the single Edit tool
2. The edits are atomic - either all succeed or none are applied
3. Plan your edits carefully to avoid conflicts between sequential operations

WARNING:
- The tool will fail if edits.old_string doesn't match the file contents exactly (including whitespace)
- The tool will fail if edits.old_string and edits.new_string are the same
- Since edits are applied in sequence, ensure that earlier edits don't affect the text that later edits are trying to find

When making edits:
- Ensure all edits result in idiomatic, correct code
- Do not leave the code in a broken state
- Always use absolute file paths (starting with /)
- Only use emojis if the user explicitly requests it. Avoid adding emojis to files unless asked.
- Use replace_all for replacing and renaming strings across the file. This parameter is useful if you want to rename a variable for instance.

If you want to create a new file, use:
- A new file path, including dir name if needed
- First edit: empty old_string and the new file's contents as new_string
- Subsequent edits: normal edit operations on the created content`
)

func NewMultiEditTool(permissions llm.PermissionTrigger, files core.HistoryService) tools.BaseTool {
	return &multiEditTool{
		permissions: permissions,
		files:       files,
	}
}

func (t multiEditTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.MultiEditToolName,
		Description: multiEditDescription,
		Parameters: map[string]any{
			"file_path": map[string]any{
				"type":        "string",
				"description": "The absolute path to the file to modify",
			},
			"edits": map[string]any{
				"type": "array",
				"items": map[string]any{
					"type": "object",
					"properties": map[string]any{
						"old_string": map[string]any{
							"type":        "string",
							"description": "The text to replace",
						},
						"new_string": map[string]any{
							"type":        "string",
							"description": "The text to replace it with",
						},
						"replace_all": map[string]any{
							"type":        "boolean",
							"description": "Replace all occurrences of old_string (default false)",
						},
					},
					"required": []string{"old_string", "new_string"},
				},
				"minItems":    1,
				"description": "Array of edit operations to perform sequentially on the file",
			},
		},
		Required: []string{"file_path", "edits"},
	}
}

func (t multiEditTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.MultiEditSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("invalid parameters"), nil
	}

	if params.FilePath == "" {
		return tools.NewTextErrorResponse("file_path is required"), nil
	}

	if !filepath.IsAbs(params.FilePath) {
		// Get working directory from tool execution context
		wd := ctx.GetWorkingDir()
		params.FilePath = filepath.Join(wd, params.FilePath)
	}

	for _, edit := range params.Edits {
		edit.FilePath = params.FilePath
		if res := Validate(&edit); res.IsError {
			return res, nil
		} else if len(edit.OldString) == 0 {
			return tools.NewTextErrorResponse("Cannot create new files with MultiEdit. Please use the Write tool instead."), nil
		}
	}

	sessionID := ctx.GetSessionId()
	messageID := ctx.GetMessageId()
	if sessionID == "" || messageID == "" {
		return tools.ToolResponse{}, fmt.Errorf("session Id and message Id are required for editing a file")
	}

	content, err := os.ReadFile(params.FilePath)
	if err != nil {
		return tools.ToolResponse{}, fmt.Errorf("failed to read file: %w", err)
	}

	oldContent := string(content)
	newContent := oldContent
	for _, edit := range params.Edits {
		if edit.ReplaceAll {
			newContent = strings.ReplaceAll(newContent, edit.OldString, edit.NewString)
		} else {
			newContent = strings.Replace(newContent, edit.OldString, edit.NewString, 1)
		}
	}

	applier := FileApplier{
		ToolName:    tools.MultiEditToolName,
		permissions: t.permissions,
		files:       t.files,
		FilePath:    params.FilePath,
		OldContent:  oldContent,
		NewContent:  newContent,
	}
	return applier.Apply(ctx)
}

package tools

import (
	"fmt"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/llm/shell"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/utils"
	"path/filepath"
	"time"
)

type bashKillTool struct {
	permissions  llm.PermissionTrigger
	snapshotFile string
	shells       shell.ShellService
}

const (
	bashKillDescription = `
- Kills a running background bash shell by its ID
- Takes a shell_id parameter identifying the shell to kill
- Returns a success or failure status 
- Use this tool when you need to terminate a long-running shell
- Shell IDs can be found using the /bashes command`
)

func NewBashKillTool(permissions llm.PermissionTrigger, shells shell.ShellService) tools.BaseTool {
	return &bashKillTool{
		permissions:  permissions,
		shells:       shells,
		snapshotFile: filepath.Join(utils.GetUserStorageDir(), "shell-snapshots", fmt.Sprintf("shell-snapshot-%d.sh", time.Now().UnixNano())),
	}
}

func (t *bashKillTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.KillBashToolName,
		Description: bashKillDescription,
		Parameters: map[string]any{
			"bash_id": map[string]any{
				"type":        "string",
				"description": "The ID of the background shell to kill",
			},
		},
		Required: []string{"bash_id"},
	}
}

func (t *bashKillTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.BashKillSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("invalid parameters"), nil
	}

	// 获取会话信息
	sessionId := ctx.GetSessionId()
	messageId := ctx.GetMessageId()
	if sessionId == "" || messageId == "" {
		return tools.ToolResponse{}, fmt.Errorf("session Id and message Id are required for creating a new file")
	}
	p := t.permissions.CreateRequestWithContext(ctx,
		core.CreatePermissionRequest{
			SessionId:   sessionId,
			Path:        ctx.GetWorkingDir(),
			ToolName:    tools.KillBashToolName,
			Action:      "execute",
			Description: fmt.Sprintf("Kill bash: %s", params.BashId),
			Params: specs.BashKillPermissionsParams{
				BashId: params.BashId,
			},
		},
	)
	if !p {
		return tools.ToolResponse{}, core.ErrorPermissionDenied
	}

	// 获取正在执行的 Background shell
	sh, exists := t.shells.GetShell(params.BashId)
	metadata := specs.BashKillResponseMetadata{Success: false, BashId: params.BashId}
	if !exists || !sh.IsExecuting() {
		metadata.Message = fmt.Sprintf("Shell %s is not running", params.BashId)
		return tools.WithResponseMetadata(tools.NewTextResponse(metadata.Message), metadata), nil
	}

	// 尝试终止运行中的 Background shell
	if err := sh.KillProcess(); err != nil {
		metadata.Message = fmt.Sprintf("Failed to kill shell %s", params.BashId)
		return tools.WithResponseMetadata(tools.NewTextResponse(metadata.Message), metadata), nil
	}

	result := sh.GetResult()
	metadata.Message = fmt.Sprintf("Successfully killed shell: %s (%s)", result.BashId, result.Command)
	return tools.WithResponseMetadata(tools.NewTextResponse(metadata.Message), metadata), nil
}

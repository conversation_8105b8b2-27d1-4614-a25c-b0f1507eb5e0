package tools

import (
	"context"
	"encoding/json"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"strings"
	"testing"
)

// 创建一个模拟的权限服务，总是返回 true
type mockWebFetchPermissionService struct{}

func (m *mockWebFetchPermissionService) CreateRequestWithContext(ctx core.SessionContext, opts core.CreatePermissionRequest) bool {
	return true
}

func (m *mockWebFetchPermissionService) Subscribe(ctx context.Context) <-chan pubsub.Event[core.PermissionRequest] {
	ch := make(chan pubsub.Event[core.PermissionRequest])
	close(ch)
	return ch
}

func initWebFetchTestConfig(t *testing.T) {
}

func TestFetchTool_Run(t *testing.T) {
	// 初始化配置
	initWebFetchTestConfig(t)

	// 创建测试参数
	params := specs.FetchParams{
		URL:    "https://www.google.com",
		Prompt: "Extract the main heading and paragraph content from this page",
	}

	// 序列化参数
	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	// 创建模拟的 ChatRequest 函数
	mockChatRequest := func(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (string, error) {
		return "Mock AI Response: This is a simulated response from the AI model.", nil
	}

	// 创建工具调用
	call := tools.ToolCall{
		Input:       string(input),
		ChatRequest: mockChatRequest,
	}

	// 创建带有 session Id 和 message Id 的上下文
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-id").
		WithMessageID("test-message-id").
		Build()

	// 运行工具
	mockPermService := &mockWebFetchPermissionService{}
	tool := NewWebFetchTool(mockPermService)
	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.Type != tools.ToolResponseTypeText {
		t.Errorf("Expected response type %s, got %s", tools.ToolResponseTypeText, response.Type)
	}

	if response.IsError {
		t.Errorf("Expected no error, but got error response: %s", response.Content)
	}

	if response.Content == "" {
		t.Error("Response content should not be empty")
	}

	// 验证响应包含一些预期的内容
	if len(response.Content) < 10 {
		t.Errorf("Response content too short: %s", response.Content)
	}

	// 验证响应包含模拟的 AI 响应
	if !strings.Contains(response.Content, "Mock AI Response") {
		t.Errorf("Expected response to contain 'Mock AI Response', got: %s", response.Content)
	}

	t.Logf("Tool response: %s", response.Content)
}

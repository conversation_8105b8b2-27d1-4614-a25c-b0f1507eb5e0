package tools

import (
	"context"
	"fmt"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/message"
)

type fetchTool struct {
	client      *http.Client
	permissions llm.PermissionTrigger
}

const (
	webFetchToolDescription = `- Fetches content from a specified URL and processes it using an AI model
- Takes a URL and a prompt as input
- Fetches the URL content, converts HTML to markdown
- Processes the content with the prompt using a small, fast model
- Returns the model's response about the content
- Use this tool when you need to retrieve and analyze web content

Usage notes:
  - IMPORTANT: If an MCP-provided web fetch tool is available, prefer using that tool instead of this one, as it may have fewer restrictions. All MCP-provided tools start with "mcp__".
  - The URL must be a fully-formed valid URL
  - HTTP URLs will be automatically upgraded to HTTPS
  - The prompt should describe what information you want to extract from the page
  - This tool is read-only and does not modify any files
  - Results may be summarized if the content is very large
  - Includes a self-cleaning 15-minute cache for faster responses when repeatedly accessing the same URL`

	redirectPromptTemplate = `REDIRECT DETECTED: The URL redirects to a different host.

Original URL: %s
Redirect URL: %s
Status: %d %s

To complete your request, I need to fetch content from the redirected URL. Please use WebFetch again with these parameters:
- url: "%s"
- prompt: "%s"`

	processPromptTemplate = `
Web page content:
---
%s
---

%s

Provide a concise response based only on the content above. In your response:
 - Enforce a strict 125-character maximum for quotes from any source document. Open Source Software is ok as long as we respect the license.
 - Use quotation marks for exact language from articles; any language outside of the quotation should never be word-for-word the same.
 - You are not a lawyer and never comment on the legality of your own prompts and responses.
 - Never produce or reproduce exact song lyrics.
`
)

func NewWebFetchTool(permissions llm.PermissionTrigger) tools.BaseTool {
	return &fetchTool{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		permissions: permissions,
	}
}

func (t *fetchTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.WebFetchToolName,
		Description: webFetchToolDescription,
		Parameters: map[string]any{
			"url": map[string]any{
				"type":        "string",
				"description": "The URL to fetch content from",
			},
			"prompt": map[string]any{ // TODO 新增
				"type":        "string",
				"description": "The prompt to run on the fetched content",
			},
		},
		Required: []string{"url", "prompt"},
	}
}

func (t *fetchTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.WebFetchSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("Failed to parse fetch parameters: " + err.Error()), nil
	}

	u, err := url.Parse(params.URL)
	if err != nil || (u.Scheme != "https") || u.Host == "" {
		return tools.NewTextErrorResponse(fmt.Sprintf("Error: Invalid URL \"%s\". The URL provided could not be parsed.", params.URL)), nil
	}

	sessionId := ctx.GetSessionId()
	messageId := ctx.GetMessageId()
	if sessionId == "" || messageId == "" {
		return tools.ToolResponse{}, fmt.Errorf("session Id and message Id are required for creating a new file")
	}

	p := t.permissions.CreateRequestWithContext(ctx,
		core.CreatePermissionRequest{
			SessionId:   sessionId,
			Path:        ctx.GetWorkingDir(),
			ToolName:    tools.WebFetchToolName,
			Action:      "fetch",
			Description: fmt.Sprintf("Fetch markdown from URL: %s", params.URL),
			Params:      specs.FetchPermissionsParams(*params),
		},
	)

	if !p {
		return tools.ToolResponse{}, core.ErrorPermissionDenied
	}

	req, err := http.NewRequestWithContext(ctx, "GET", params.URL, nil)
	if err != nil {
		return tools.ToolResponse{}, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("User-Agent", fmt.Sprintf("%s/1.0", core.ReleaseName))
	resp, err := t.client.Do(req)
	if err != nil {
		return tools.ToolResponse{}, fmt.Errorf("failed to fetch URL: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// 检查是否为重定向（3xx），如果是则通过 redirectPrompt 返回
		if resp.StatusCode >= 300 && resp.StatusCode < 400 {
			return redirectPrompt(req, resp, params.Prompt), nil
		}
		return tools.NewTextErrorResponse(fmt.Sprintf("Request failed with status code: %d", resp.StatusCode)), nil
	}

	maxSize := int64(5 * 1024 * 1024) // 5MB
	body, err := io.ReadAll(io.LimitReader(resp.Body, maxSize))
	if err != nil {
		return tools.NewTextErrorResponse("Failed to read response body: " + err.Error()), nil
	}

	markdown, err := convertMarkdown(ctx, call.ChatRequest, string(body), params.Prompt)
	if err != nil {
		return tools.NewTextErrorResponse("Failed to convert to markdown: " + err.Error()), nil
	}
	return tools.NewTextResponse(markdown), nil
}

func redirectPrompt(req *http.Request, resp *http.Response, prompt string) tools.ToolResponse {
	statusText := "Found"
	if resp.StatusCode == http.StatusMovedPermanently {
		statusText = "Moved Permanently"
	} else if resp.StatusCode == http.StatusPermanentRedirect {
		statusText = "Permanent Redirect"
	} else if resp.StatusCode == http.StatusTemporaryRedirect {
		statusText = "Temporary Redirect"
	}

	location := resp.Header.Get("Location")
	result := fmt.Sprintf(redirectPromptTemplate, req.URL, location, resp.StatusCode, statusText, location, prompt)
	return tools.NewTextResponse(result)
}

func convertMarkdown(ctx context.Context, chatRequest tools.ChatRequest, html string, prompt string) (string, error) {
	userMessage := fmt.Sprintf(processPromptTemplate, html, prompt)
	return chatRequest(
		ctx,
		[]message.Message{
			{
				Role: message.User,
				Parts: []message.ContentPart{
					message.TextContent{Text: userMessage, Type: "text"},
				},
			},
		},
		make([]tools.BaseTool, 0),
	)
}

package tools

import (
	"fmt"
	"regexp"
	"strings"
)

// formatLines 为文本内容添加行号格式化
func formatLines(content string, startLine int) string {
	// 空内容检查
	if content == "" {
		return ""
	}

	// 使用正则表达式分割文本，支持不同操作系统的换行符
	re := regexp.MustCompile(`\r?\n`)
	lines := re.Split(content, -1)

	// 为每行添加行号
	var formattedLines []string
	for i, line := range lines {
		// 计算当前行号 = 数组索引 + 起始行号
		lineNumber := i + startLine
		lineNumberStr := fmt.Sprintf("%d", lineNumber)

		var formattedLine string
		// 行号格式化逻辑
		if len(lineNumberStr) >= 6 {
			// 长行号直接使用
			formattedLine = fmt.Sprintf("%s→%s", lineNumberStr, line)
		} else {
			// 短行号右对齐补空格到6位
			paddedLineNumber := fmt.Sprintf("%6s", lineNumberStr)
			formattedLine = fmt.Sprintf("%s→%s", paddedLineNumber, line)
		}
		formattedLines = append(formattedLines, formattedLine)
	}

	// 用换行符重新连接所有行
	return strings.Join(formattedLines, "\n")
}

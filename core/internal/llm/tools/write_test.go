package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

// mockHistoryService 模拟历史服务
type mockHistoryService struct {
	files    map[string]string
	versions map[string][]string
}

func (m *mockHistoryService) Create(ctx context.Context, sessionID, filePath, content string) (core.File, error) {
	key := fmt.Sprintf("%s:%s", sessionID, filePath)
	m.files[key] = content
	return core.File{
		Id:        "test-id",
		SessionId: sessionID,
		Path:      filePath,
		Content:   content,
		Version:   "v1",
		CreatedAt: time.Now().Unix(),
		UpdatedAt: time.Now().Unix(),
	}, nil
}

func (m *mockHistoryService) CreateVersion(ctx context.Context, sessionID, filePath, content string) (core.File, error) {
	key := fmt.Sprintf("%s:%s", sessionID, filePath)
	if m.versions[key] == nil {
		m.versions[key] = []string{}
	}
	m.versions[key] = append(m.versions[key], content)
	return core.File{
		Id:        "test-version-id",
		SessionId: sessionID,
		Path:      filePath,
		Content:   content,
		Version:   fmt.Sprintf("v%d", len(m.versions[key])),
		CreatedAt: time.Now().Unix(),
		UpdatedAt: time.Now().Unix(),
	}, nil
}

func (m *mockHistoryService) GetByPathAndSession(ctx context.Context, filePath, sessionID string) (core.File, error) {
	key := fmt.Sprintf("%s:%s", sessionID, filePath)
	if content, exists := m.files[key]; exists {
		return core.File{
			Id:        "test-id",
			SessionId: sessionID,
			Path:      filePath,
			Content:   content,
			Version:   "v1",
			CreatedAt: time.Now().Unix(),
			UpdatedAt: time.Now().Unix(),
		}, nil
	}
	return core.File{}, fmt.Errorf("not found")
}

func (m *mockHistoryService) Get(ctx context.Context, id string) (core.File, error) {
	return core.File{}, fmt.Errorf("not implemented")
}

func (m *mockHistoryService) ListBySession(ctx context.Context, sessionID string) ([]core.File, error) {
	return []core.File{}, nil
}

func (m *mockHistoryService) ListLatestSessionFiles(ctx context.Context, sessionID string) ([]core.File, error) {
	return []core.File{}, nil
}

func (m *mockHistoryService) Update(ctx context.Context, file core.File) (core.File, error) {
	return file, nil
}

func (m *mockHistoryService) Delete(ctx context.Context, id string) error {
	return nil
}

func (m *mockHistoryService) DeleteSessionFiles(ctx context.Context, sessionID string) error {
	return nil
}

// 实现pubsub.Suscriber接口
func (m *mockHistoryService) Subscribe(ctx context.Context) <-chan pubsub.Event[core.File] {
	ch := make(chan pubsub.Event[core.File])
	close(ch)
	return ch
}

func newMockHistoryService() *mockHistoryService {
	return &mockHistoryService{
		files:    make(map[string]string),
		versions: make(map[string][]string),
	}
}

func initWriteTestConfig(t *testing.T) {
}

func TestWriteTool_Info(t *testing.T) {
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)
	info := tool.Info()

	// 验证基本信息
	if info.Name != tools.WriteToolName {
		t.Errorf("Expected name %s, got %s", tools.WriteToolName, info.Name)
	}

	if info.Description == "" {
		t.Error("Description should not be empty")
	}

	// 验证参数定义
	if _, ok := info.Parameters["file_path"]; !ok {
		t.Error("file_path parameter should be defined")
	}

	if _, ok := info.Parameters["content"]; !ok {
		t.Error("content parameter should be defined")
	}

	// 验证必需参数
	if len(info.Required) != 2 {
		t.Errorf("Expected 2 required parameters, got %d", len(info.Required))
	}
}

func TestWriteTool_Run_CreateNewFile(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	// 创建临时目录
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.txt")

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.WriteParams{
		FilePath: testFile,
		Content:  "Hello, World!\nThis is a test file.",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证文件是否被创建
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Error("File was not created")
	}

	// 验证文件内容
	content, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read created file: %v", err)
	}

	if string(content) != params.Content {
		t.Errorf("File content mismatch. Expected %q, got %q", params.Content, string(content))
	}

	// 验证响应包含成功信息
	if !strings.Contains(response.Content, "successfully created") {
		t.Errorf("Response should indicate file was created: %s", response.Content)
	}
}

func TestWriteTool_Run_UpdateExistingFile(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	// 创建临时文件
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "existing.txt")
	initialContent := "Initial content\nLine 2"

	if err := os.WriteFile(testFile, []byte(initialContent), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 记录文件读取时间
	recordFileRead(testFile)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	newContent := "Updated content\nNew line 2\nNew line 3"
	params := specs.WriteParams{
		FilePath: testFile,
		Content:  newContent,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证文件内容
	content, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read updated file: %v", err)
	}

	if string(content) != newContent {
		t.Errorf("File content mismatch. Expected %q, got %q", newContent, string(content))
	}

	// 验证响应包含更新信息
	if !strings.Contains(response.Content, "successfully updated") {
		t.Errorf("Response should indicate file was updated: %s", response.Content)
	}
}

func TestWriteTool_Run_SameContent(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	// 创建临时文件
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "same.txt")
	content := "Same content"

	if err := os.WriteFile(testFile, []byte(content), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	recordFileRead(testFile)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.WriteParams{
		FilePath: testFile,
		Content:  content, // 相同内容
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应，因为内容相同
	if !response.IsError {
		t.Error("Expected error response for same content")
	}

	if !strings.Contains(response.Content, "already contains the exact content") {
		t.Errorf("Expected same content error, got: %s", response.Content)
	}
}

func TestWriteTool_Run_FileNotRead(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	// 创建临时文件但不记录读取
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "notread.txt")

	if err := os.WriteFile(testFile, []byte("content"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.WriteParams{
		FilePath: testFile,
		Content:  "new content",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for file not read")
	}

	if !strings.Contains(response.Content, "has not been read yet") {
		t.Errorf("Expected file not read error, got: %s", response.Content)
	}
}

func TestWriteTool_Run_Directory(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	// 使用目录作为文件路径
	tempDir := t.TempDir()

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.WriteParams{
		FilePath: tempDir,
		Content:  "content",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for directory")
	}

	if !strings.Contains(response.Content, "Path is a directory") {
		t.Errorf("Expected directory error, got: %s", response.Content)
	}
}

func TestWriteTool_Run_PermissionDenied(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: false} // 拒绝权限
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "denied.txt")

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.WriteParams{
		FilePath: testFile,
		Content:  "content",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: string(input),
	}

	_, err = tool.Run(toolCtx, call)

	// 应该返回权限拒绝错误
	if err == nil {
		t.Error("Expected permission denied error")
	}

	if err != core.ErrorPermissionDenied {
		t.Errorf("Expected permission denied error, got %v", err)
	}
}

func TestWriteTool_Run_InvalidJSON(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: "invalid json",
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for invalid JSON")
	}

	if !strings.Contains(response.Content, "error parsing parameters") {
		t.Errorf("Expected JSON parsing error, got: %s", response.Content)
	}
}

func TestWriteTool_Run_MissingFilePath(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.WriteParams{
		FilePath: "", // 空路径
		Content:  "content",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for missing file path")
	}

	if response.Content != "file_path is required" {
		t.Errorf("Expected file_path required error, got: %s", response.Content)
	}
}

func TestWriteTool_Run_MissingContent(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.txt")

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.WriteParams{
		FilePath: testFile,
		Content:  "", // 空内容
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for missing content")
	}

	if response.Content != "content is required" {
		t.Errorf("Expected content required error, got: %s", response.Content)
	}
}

func TestWriteTool_DetectFileEncoding(t *testing.T) {
	tool := &writeTool{}

	tests := []struct {
		name     string
		content  []byte
		expected string
	}{
		{
			name:     "UTF-8 with BOM",
			content:  []byte{0xEF, 0xBB, 0xBF, 'H', 'e', 'l', 'l', 'o'},
			expected: "utf-8",
		},
		{
			name:     "UTF-16 LE with BOM",
			content:  []byte{0xFF, 0xFE, 'H', 0x00, 'i', 0x00},
			expected: "utf-16le",
		},
		{
			name:     "Plain UTF-8",
			content:  []byte("Hello, 世界!"),
			expected: "utf-8",
		},
		{
			name:     "ASCII",
			content:  []byte("Hello, World!"),
			expected: "utf-8", // ASCII is valid UTF-8
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建临时文件
			tempFile := filepath.Join(t.TempDir(), "test.txt")
			if err := os.WriteFile(tempFile, tt.content, 0644); err != nil {
				t.Fatalf("Failed to create test file: %v", err)
			}

			encoding, err := tool.detectFileEncoding(tempFile)
			if err != nil {
				t.Fatalf("Failed to detect encoding: %v", err)
			}

			if encoding != tt.expected {
				t.Errorf("Expected encoding %s, got %s", tt.expected, encoding)
			}
		})
	}
}

func TestWriteTool_DetectLineEnding(t *testing.T) {
	tool := &writeTool{}

	tests := []struct {
		name     string
		content  string
		expected string
	}{
		{
			name:     "LF only",
			content:  "Line 1\nLine 2\nLine 3",
			expected: "LF",
		},
		{
			name:     "CRLF only",
			content:  "Line 1\r\nLine 2\r\nLine 3",
			expected: "CRLF",
		},
		{
			name:     "Mixed with CRLF majority",
			content:  "Line 1\r\nLine 2\r\nLine 3\nLine 4",
			expected: "CRLF",
		},
		{
			name:     "Mixed with LF majority",
			content:  "Line 1\nLine 2\nLine 3\r\nLine 4",
			expected: "LF",
		},
		{
			name:     "No line endings",
			content:  "Single line",
			expected: "LF",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.detectLineEnding(tt.content)
			if result != tt.expected {
				t.Errorf("Expected line ending %s, got %s", tt.expected, result)
			}
		})
	}
}

func TestWriteTool_ProcessLineEndings(t *testing.T) {
	tool := &writeTool{}

	tests := []struct {
		name         string
		content      string
		targetEnding string
		expected     string
	}{
		{
			name:         "Convert to CRLF",
			content:      "Line 1\nLine 2\nLine 3",
			targetEnding: "CRLF",
			expected:     "Line 1\r\nLine 2\r\nLine 3",
		},
		{
			name:         "Keep LF",
			content:      "Line 1\nLine 2\nLine 3",
			targetEnding: "LF",
			expected:     "Line 1\nLine 2\nLine 3",
		},
		{
			name:         "No line endings",
			content:      "Single line",
			targetEnding: "CRLF",
			expected:     "Single line",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.processLineEndings(tt.content, tt.targetEnding)
			if result != tt.expected {
				t.Errorf("Expected %q, got %q", tt.expected, result)
			}
		})
	}
}

func TestWriteTool_ResolveSymlink(t *testing.T) {
	tool := &writeTool{}

	tempDir := t.TempDir()

	// 创建目标文件
	targetFile := filepath.Join(tempDir, "target.txt")
	if err := os.WriteFile(targetFile, []byte("content"), 0644); err != nil {
		t.Fatalf("Failed to create target file: %v", err)
	}

	// 创建符号链接
	linkFile := filepath.Join(tempDir, "link.txt")
	if err := os.Symlink(targetFile, linkFile); err != nil {
		t.Skipf("Skipping symlink test: %v", err) // 在不支持符号链接的系统上跳过
	}

	resolved, err := tool.resolveSymlink(linkFile)
	if err != nil {
		t.Fatalf("Failed to resolve symlink: %v", err)
	}

	if resolved != targetFile {
		t.Errorf("Expected resolved path %s, got %s", targetFile, resolved)
	}

	// 测试非符号链接文件
	resolved, err = tool.resolveSymlink(targetFile)
	if err != nil {
		t.Fatalf("Failed to resolve regular file: %v", err)
	}

	if resolved != targetFile {
		t.Errorf("Expected original path for regular file %s, got %s", targetFile, resolved)
	}
}

func TestWriteTool_WriteFileAtomically(t *testing.T) {
	tool := &writeTool{}

	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "atomic.txt")
	content := "Atomic write test content"

	atomic, err := tool.writeFileAtomically(testFile, content, "utf-8")
	if err != nil {
		t.Fatalf("Failed to write file atomically: %v", err)
	}

	// 验证文件是否存在
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Error("File was not created")
	}

	// 验证文件内容
	fileContent, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read file: %v", err)
	}

	if string(fileContent) != content {
		t.Errorf("Expected content %q, got %q", content, string(fileContent))
	}

	// 验证是否使用了原子写入（这个测试可能因平台而异）
	t.Logf("Atomic write result: %v", atomic)
}

func TestWriteTool_ResponseMetadata(t *testing.T) {
	initWriteTestConfig(t)
	mockPermissions := &mockPermissionService{shouldApprove: true}
	mockHistory := newMockHistoryService()
	tool := NewWriteTool(mockPermissions, mockHistory)

	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "metadata.txt")

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.WriteParams{
		FilePath: testFile,
		Content:  "Line 1\r\nLine 2\nLine 3", // 混合行尾符
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.WriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证元数据
	if response.Metadata == "" {
		t.Error("Response should include metadata")
	}

	// 解析元数据
	var metadata specs.WriteResponseMetadata
	if err := json.Unmarshal([]byte(response.Metadata), &metadata); err != nil {
		t.Fatalf("Failed to parse metadata: %v", err)
	}

	// 验证元数据字段
	if metadata.Type != "create" {
		t.Errorf("Expected type 'create', got %s", metadata.Type)
	}

	if metadata.FilePath != testFile {
		t.Errorf("Expected file path %s, got %s", testFile, metadata.FilePath)
	}

	if metadata.Encoding != "utf-8" {
		t.Errorf("Expected encoding 'utf-8', got %s", metadata.Encoding)
	}

	if metadata.LineEnding != "LF" {
		t.Errorf("Expected line ending 'LF', got %s", metadata.LineEnding)
	}

	if metadata.Additions <= 0 {
		t.Error("Expected positive addition count")
	}
}

package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

// mockEditPermissionService 模拟权限服务用于测试
type mockEditPermissionService struct {
	shouldApprove bool
	lastRequest   *core.CreatePermissionRequest
}

func (m *mockEditPermissionService) CreateRequestWithContext(ctx core.SessionContext, opts core.CreatePermissionRequest) bool {
	m.lastRequest = &opts
	return m.shouldApprove
}

// 实现pubsub.Suscriber接口
func (m *mockEditPermissionService) Subscribe(ctx context.Context) <-chan pubsub.Event[core.PermissionRequest] {
	ch := make(chan pubsub.Event[core.PermissionRequest])
	close(ch)
	return ch
}

// mockEditHistoryService 模拟历史服务用于测试
type mockEditHistoryService struct {
	files    map[string]string
	versions map[string][]string
}

func (m *mockEditHistoryService) Create(ctx context.Context, sessionID, filePath, content string) (core.File, error) {
	key := fmt.Sprintf("%s:%s", sessionID, filePath)
	m.files[key] = content
	return core.File{
		Id:        "test-id",
		SessionId: sessionID,
		Path:      filePath,
		Content:   content,
		Version:   "v1",
		CreatedAt: time.Now().Unix(),
		UpdatedAt: time.Now().Unix(),
	}, nil
}

func (m *mockEditHistoryService) CreateVersion(ctx context.Context, sessionID, filePath, content string) (core.File, error) {
	key := fmt.Sprintf("%s:%s", sessionID, filePath)
	if m.versions[key] == nil {
		m.versions[key] = []string{}
	}
	m.versions[key] = append(m.versions[key], content)
	return core.File{
		Id:        "test-version-id",
		SessionId: sessionID,
		Path:      filePath,
		Content:   content,
		Version:   fmt.Sprintf("v%d", len(m.versions[key])),
		CreatedAt: time.Now().Unix(),
		UpdatedAt: time.Now().Unix(),
	}, nil
}

func (m *mockEditHistoryService) GetByPathAndSession(ctx context.Context, filePath, sessionID string) (core.File, error) {
	key := fmt.Sprintf("%s:%s", sessionID, filePath)
	if content, exists := m.files[key]; exists {
		return core.File{
			Id:        "test-id",
			SessionId: sessionID,
			Path:      filePath,
			Content:   content,
			Version:   "v1",
			CreatedAt: time.Now().Unix(),
			UpdatedAt: time.Now().Unix(),
		}, nil
	}
	return core.File{}, fmt.Errorf("not found")
}

func (m *mockEditHistoryService) Get(ctx context.Context, id string) (core.File, error) {
	return core.File{}, fmt.Errorf("not implemented")
}

func (m *mockEditHistoryService) ListBySession(ctx context.Context, sessionID string) ([]core.File, error) {
	return []core.File{}, nil
}

func (m *mockEditHistoryService) ListLatestSessionFiles(ctx context.Context, sessionID string) ([]core.File, error) {
	return []core.File{}, nil
}

func (m *mockEditHistoryService) Update(ctx context.Context, file core.File) (core.File, error) {
	return file, nil
}

func (m *mockEditHistoryService) Delete(ctx context.Context, id string) error {
	return nil
}

func (m *mockEditHistoryService) DeleteSessionFiles(ctx context.Context, sessionID string) error {
	return nil
}

// 实现pubsub.Suscriber接口
func (m *mockEditHistoryService) Subscribe(ctx context.Context) <-chan pubsub.Event[core.File] {
	ch := make(chan pubsub.Event[core.File])
	close(ch)
	return ch
}

func newMockEditHistoryService() *mockEditHistoryService {
	return &mockEditHistoryService{
		files:    make(map[string]string),
		versions: make(map[string][]string),
	}
}

func initEditTestConfig(t *testing.T) string {
	wd := t.TempDir()
	return wd
}

func TestEditTool_Run_CreateNewFile(t *testing.T) {
	tempDir := initEditTestConfig(t)
	mockPermissions := &mockEditPermissionService{shouldApprove: true}
	mockHistory := newMockEditHistoryService()
	tool := NewEditTool(mockPermissions, mockHistory)

	// 创建临时目录
	testFile := filepath.Join(tempDir, "new_file.txt")

	// Context creation moved to toolCtx creation below

	params := specs.EditParams{
		FilePath:  testFile,
		OldString: "",
		NewString: "Hello, World!\nThis is a new file.",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.EditToolName,
		Input: string(input),
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		WithWorkingDir(tempDir).
		Build()

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证文件是否被创建
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Error("File was not created")
	}

	// 验证文件内容
	content, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read created file: %v", err)
	}

	if string(content) != params.NewString {
		t.Errorf("File content mismatch. Expected %q, got %q", params.NewString, string(content))
	}

	// 验证响应包含成功信息
	if !strings.Contains(response.Content, "Content written in file:") {
		t.Errorf("Response should indicate content was written: %s", response.Content)
	}

	// 验证元数据
	if response.Metadata == "" {
		t.Error("Response should contain metadata")
	}
}

func TestEditTool_Run_ReplaceContent(t *testing.T) {
	tempDir := initEditTestConfig(t)
	mockPermissions := &mockEditPermissionService{shouldApprove: true}
	mockHistory := newMockEditHistoryService()
	tool := NewEditTool(mockPermissions, mockHistory)

	// 创建临时文件
	testFile := filepath.Join(tempDir, "existing.txt")
	initialContent := "Hello, World!\nThis is a test file.\nLine 3"

	if err := os.WriteFile(testFile, []byte(initialContent), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 记录文件读取时间
	recordFileRead(testFile)

	// Context creation moved to toolCtx creation below

	params := specs.EditParams{
		FilePath:  testFile,
		OldString: "Hello, World!",
		NewString: "Hello, Updated!",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.EditToolName,
		Input: string(input),
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		WithWorkingDir(tempDir).
		Build()

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证文件内容
	content, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read updated file: %v", err)
	}

	expectedContent := "Hello, Updated!\nThis is a test file.\nLine 3"
	if string(content) != expectedContent {
		t.Errorf("File content mismatch. Expected %q, got %q", expectedContent, string(content))
	}

	// 验证响应包含替换信息
	if !strings.Contains(response.Content, "Content written in file:") {
		t.Errorf("Response should indicate content was written: %s", response.Content)
	}

	// 验证元数据
	if response.Metadata == "" {
		t.Error("Response should contain metadata")
	}
}

func TestEditTool_Run_ReplaceAll(t *testing.T) {
	initEditTestConfig(t)
	mockPermissions := &mockEditPermissionService{shouldApprove: true}
	mockHistory := newMockEditHistoryService()
	tool := NewEditTool(mockPermissions, mockHistory)

	// 创建临时文件
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "multiple.txt")
	initialContent := "Hello, World!\nHello, World!\nHello, World!"

	if err := os.WriteFile(testFile, []byte(initialContent), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 记录文件读取时间
	recordFileRead(testFile)

	// Context creation moved to toolCtx creation below

	params := specs.EditParams{
		FilePath:   testFile,
		OldString:  "Hello, World!",
		NewString:  "Hello, Updated!",
		ReplaceAll: true,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.EditToolName,
		Input: string(input),
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证文件内容
	content, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read updated file: %v", err)
	}

	expectedContent := "Hello, Updated!\nHello, Updated!\nHello, Updated!"
	if string(content) != expectedContent {
		t.Errorf("File content mismatch. Expected %q, got %q", expectedContent, string(content))
	}
}

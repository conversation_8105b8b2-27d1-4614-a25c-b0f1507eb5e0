package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gobwas/glob"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func TestLsTool_Info(t *testing.T) {
	tool := NewLsTool()
	info := tool.Info()

	// 验证基本信息
	if info.Name != tools.LSToolName {
		t.Errorf("Expected name %s, got %s", tools.LSToolName, info.Name)
	}

	if info.Description == "" {
		t.Error("Description should not be empty")
	}

	// 验证参数定义
	if _, ok := info.Parameters["path"]; !ok {
		t.Error("path parameter should be defined")
	}

	if _, ok := info.Parameters["ignore"]; !ok {
		t.Error("ignore parameter should be defined")
	}

	// 验证必需参数
	if len(info.Required) == 0 || info.Required[0] != "path" {
		t.Error("path should be a required parameter")
	}
}

func TestLsTool_Run_BasicFunctionality(t *testing.T) {
	tool := NewLsTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()

	// 测试当前目录
	params := specs.LSParams{
		Path: ".",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.Type != tools.ToolResponseTypeText {
		t.Errorf("Expected response type %s, got %s", tools.ToolResponseTypeText, response.Type)
	}

	if response.Content == "" {
		t.Error("Response content should not be empty")
	}

	// 验证包含安全提示
	if !strings.Contains(response.Content, "malicious") {
		t.Error("Response should contain security warning")
	}

	// 验证元数据
	if response.Metadata == "" {
		t.Error("Response metadata should not be empty")
	}

	fmt.Printf("=== 基本功能测试输出 ===\n%s\n", response.Content)
}

func TestLsTool_Run_WithIgnorePatterns(t *testing.T) {
	tool := NewLsTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()

	params := specs.LSParams{
		Path:   ".",
		Ignore: []string{"*.go", "*.mod", "*.sum"},
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证Go文件被忽略
	if strings.Contains(response.Content, ".go") {
		t.Error("Go files should be ignored")
	}

	fmt.Printf("=== 忽略模式测试输出 ===\n%s\n", response.Content)
}

func TestLsTool_ValidateInput(t *testing.T) {
	tool := &lsTool{maxCharacters: listOutputLengthLimit}

	tests := []struct {
		name    string
		path    string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "空路径使用默认工作目录",
			path:    "",
			wantErr: false,
		},
		{
			name:    "不存在的路径",
			path:    "./nonexistent/path/12345",
			wantErr: true,
			errMsg:  "directory does not exist",
		},
		{
			name:    "文件而非目录",
			path:    createTempFile(t),
			wantErr: true,
			errMsg:  "path is not a directory",
		},
		{
			name:    "有效目录",
			path:    ".",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
			_, err := tool.searchPath(toolCtx, tt.path)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
				} else if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("Expected error containing '%s', got '%s'", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

func TestLsTool_Run_ErrorHandling(t *testing.T) {
	tool := NewLsTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()

	tests := []struct {
		name    string
		input   string
		wantErr bool
	}{
		{
			name:    "无效JSON",
			input:   "invalid json",
			wantErr: false, // 应该返回错误响应而不是错误
		},
		{
			name:    "缺少路径",
			input:   `{}`,
			wantErr: false, // 会使用默认路径
		},
		{
			name:    "不存在的路径",
			input:   `{"path": "./nonexistent/path/12345"}`,
			wantErr: false, // 应该返回错误响应
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			call := tools.ToolCall{Input: tt.input}
			response, err := tool.Run(toolCtx, call)

			if tt.wantErr && err == nil {
				t.Error("Expected error but got none")
			}

			if !tt.wantErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			// 对于错误情况，应该返回错误响应
			if tt.input == "invalid json" || strings.Contains(tt.input, "nonexistent") {
				if response.Type != tools.ToolResponseTypeText {
					t.Error("Should return text response for error cases")
				}
				if !response.IsError {
					t.Error("Error response should be marked as error")
				}
			}
		})
	}
}

func TestLsTool_ShouldIgnore(t *testing.T) {
	tool := &lsTool{maxCharacters: listOutputLengthLimit}
	rootPath := "."

	tests := []struct {
		name         string
		path         string
		patterns     []string
		shouldIgnore bool
	}{
		{
			name:         "隐藏文件",
			path:         "./.hidden",
			patterns:     []string{},
			shouldIgnore: true,
		},
		{
			name:         "Python缓存",
			path:         "./__pycache__/module.pyc",
			patterns:     []string{},
			shouldIgnore: true,
		},
		{
			name:         "普通文件",
			path:         "./file.txt",
			patterns:     []string{},
			shouldIgnore: false,
		},
		{
			name:         "匹配glob模式",
			path:         "./file.go",
			patterns:     []string{"*.go"},
			shouldIgnore: true,
		},
		{
			name:         "不匹配glob模式",
			path:         "./file.txt",
			patterns:     []string{"*.go"},
			shouldIgnore: false,
		},
		{
			name:         "点目录但不是隐藏",
			path:         "./.",
			patterns:     []string{},
			shouldIgnore: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 编译glob模式
			var ignoreGlobs []glob.Glob
			for _, pattern := range tt.patterns {
				if g, err := glob.Compile(pattern); err == nil {
					ignoreGlobs = append(ignoreGlobs, g)
				}
			}

			result := tool.shouldIgnore(tt.path, rootPath, ignoreGlobs)
			if result != tt.shouldIgnore {
				t.Errorf("shouldIgnore() = %v, want %v", result, tt.shouldIgnore)
			}
		})
	}
}

func TestLsTool_IsDefaultIgnoreDir(t *testing.T) {
	tool := &lsTool{maxCharacters: listOutputLengthLimit}

	tests := []struct {
		name       string
		currentDir string
		rootPath   string
		expected   bool
	}{
		{
			name:       "node_modules目录",
			currentDir: "./node_modules",
			rootPath:   ".",
			expected:   true,
		},
		{
			name:       "根目录本身就是node_modules",
			currentDir: "./node_modules",
			rootPath:   "./node_modules",
			expected:   false,
		},
		{
			name:       "普通目录",
			currentDir: "./src",
			rootPath:   ".",
			expected:   false,
		},
		{
			name:       "vendor目录",
			currentDir: "./vendor",
			rootPath:   ".",
			expected:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.isDefaultIgnoreDir(tt.currentDir, tt.rootPath)
			if result != tt.expected {
				t.Errorf("isDefaultIgnoreDir() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestLsTool_BuildTree(t *testing.T) {
	tool := &lsTool{maxCharacters: listOutputLengthLimit}

	paths := []string{
		"dir1/file1.txt",
		"dir1/file2.txt",
		"dir1/subdir/file3.txt",
		"dir2/file4.txt",
		"file5.txt",
	}

	tree := tool.buildTree(paths)

	// 验证根级别有3个节点
	if len(tree) != 3 {
		t.Errorf("Expected 3 root nodes, got %d", len(tree))
	}

	// 查找dir1节点
	var dir1Node *TreeNode
	for _, node := range tree {
		if node.Name == "dir1" {
			dir1Node = node
			break
		}
	}

	if dir1Node == nil {
		t.Fatal("dir1 node not found")
	}

	if dir1Node.Type != "directory" {
		t.Errorf("dir1 should be directory, got %s", dir1Node.Type)
	}

	// 验证dir1有3个子节点（file1.txt, file2.txt, subdir）
	if len(dir1Node.Children) != 3 {
		t.Errorf("dir1 should have 3 children, got %d", len(dir1Node.Children))
	}

	fmt.Printf("=== 树构建测试 ===\n")
	for _, node := range tree {
		printTreeForTest(node, 0)
	}
}

func TestLsTool_FormatTree(t *testing.T) {
	tool := &lsTool{maxCharacters: listOutputLengthLimit}

	// 创建简单的树结构
	tree := []*TreeNode{
		{
			Name: "dir1",
			Type: "directory",
			Children: []*TreeNode{
				{Name: "file1.txt", Type: "file"},
				{Name: "file2.txt", Type: "file"},
			},
		},
		{
			Name: "file3.txt",
			Type: "file",
		},
	}

	result := tool.formatTree(tree, ".")

	// 验证格式
	if !strings.Contains(result, "- ./") {
		t.Error("Result should contain root directory")
	}

	if !strings.Contains(result, "- dir1/") {
		t.Error("Result should contain directory with trailing slash")
	}

	if !strings.Contains(result, "- file1.txt") {
		t.Error("Result should contain file without trailing slash")
	}

	// 验证缩进
	if !strings.Contains(result, "  - file1.txt") {
		t.Error("Files in subdirectory should be indented")
	}

	fmt.Printf("=== 格式化测试输出 ===\n%s\n", result)
}

func TestLsTool_ContextCancellation(t *testing.T) {
	tool := NewLsTool()

	// 创建一个会被取消的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
	defer cancel()

	// 等待上下文过期
	time.Sleep(5 * time.Millisecond)

	params := specs.LSParams{Path: "."}
	input, _ := json.Marshal(params)
	call := tools.ToolCall{Input: string(input)}
	toolCtx := tools.NewToolExecutionContextBuilder(ctx).Build()
	response, err := tool.Run(toolCtx, call)

	// 应该返回错误响应或者部分结果
	if err != nil {
		t.Logf("Expected context cancellation error: %v", err)
	} else if response.Type == tools.ToolResponseTypeText {
		t.Logf("Got partial response due to context cancellation")
	}
}

func TestLsTool_LongOutput(t *testing.T) {
	// 创建临时目录结构用于测试
	tempDir := createTempDirStructure(t)
	defer os.RemoveAll(tempDir)

	tool := &lsTool{maxCharacters: 100} // 设置很小的限制用于测试
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()

	params := specs.LSParams{Path: tempDir}
	input, _ := json.Marshal(params)
	call := tools.ToolCall{Input: string(input)}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证输出被截断
	if !strings.Contains(response.Content, "more than") {
		t.Error("Long output should be truncated with warning")
	}

	// 验证元数据标记截断
	if response.Metadata != "" {
		var metadata specs.LSResponseMetadata
		if err := json.Unmarshal([]byte(response.Metadata), &metadata); err != nil {
			t.Errorf("Failed to unmarshal metadata: %v", err)
		}
		if !metadata.Truncated {
			t.Error("Metadata should indicate truncation")
		}
	}
}

// 辅助函数

func createTempFile(t *testing.T) string {
	tmpFile, err := os.CreateTemp("", "test_file_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tmpFile.Close()

	t.Cleanup(func() {
		os.Remove(tmpFile.Name())
	})

	return tmpFile.Name()
}

func createTempDirStructure(t *testing.T) string {
	tmpDir, err := os.MkdirTemp("", "test_dir_*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}

	// 创建多个文件和目录
	files := []string{
		"file1.txt", "file2.txt", "file3.txt",
		"dir1/file4.txt", "dir1/file5.txt",
		"dir2/file6.txt", "dir2/subdir/file7.txt",
	}

	for _, file := range files {
		fullPath := filepath.Join(tmpDir, file)
		dir := filepath.Dir(fullPath)

		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create dir %s: %v", dir, err)
		}

		if err := os.WriteFile(fullPath, []byte("test content"), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", fullPath, err)
		}
	}

	return tmpDir
}

func printTreeForTest(node *TreeNode, level int) {
	indent := strings.Repeat("  ", level)
	suffix := ""
	if node.Type == "directory" {
		suffix = "/"
	}
	fmt.Printf("%s- %s%s\n", indent, node.Name, suffix)

	for _, child := range node.Children {
		printTreeForTest(child, level+1)
	}
}

// 基准测试

func BenchmarkLsTool_Run(b *testing.B) {
	tool := NewLsTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()

	// 为基准测试初始化配置
	params := specs.LSParams{Path: "."}
	input, _ := json.Marshal(params)
	call := tools.ToolCall{Input: string(input)}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := tool.Run(toolCtx, call)
		if err != nil {
			b.Fatalf("Failed to run tool: %v", err)
		}
	}
}

func BenchmarkLsTool_BuildTree(b *testing.B) {
	tool := &lsTool{maxCharacters: listOutputLengthLimit}

	// 创建测试路径
	paths := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		paths[i] = fmt.Sprintf("dir%d/subdir%d/file%d.txt", i%10, i%5, i)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = tool.buildTree(paths)
	}
}

package filter

import (
	"context"
	"fmt"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/llm/memory"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"strings"
)

const maxLength = 1024
const memoryReminder = `<system-reminder>
As you answer the user's questions, you can use the following context:
%s

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context or otherwise consider it in your response unless it is highly relevant to your task. Most of the time, it is not relevant.
</system-reminder>`

const memoryHeader = `# markdown-formatted-instructions
Codebase and user instructions are shown below. Be sure to adhere to these instructions. IMPORTANT: These instructions OVERRIDE any default behavior and you MUST follow them exactly as written.`

const memoryChangedReminder = `<system-reminder>
Note: %s was modified, either by the user or by a linter. Don't tell the user this, since they are already aware. This change was intentional, so make sure to take it into account as you proceed (ie. don't revert it unless the user asks you to). So that you don't need to re-read the file, here's the result of running ` + "`cat -n`" + ` on a snippet of the edited file:
%s
</system-reminder>`

type MemoryFilter struct {
	memories llm.MemoryOperator
	messages llm.MessageOperator
}

// Refactor 添加记忆相关的 system-reminder，遵循如下规则：
// 1. 从后向前追溯，找到最近的记忆 reminder
// 2. 如果找不到 reminder，在第一条用户消息前追加当前记忆
// 3. 如果找到了，对比上一次内容与本次内容是否有差异，如果有差异，在最新的消息后追加记忆变更的 reminder
func (m *MemoryFilter) Refactor(ctx context.Context, sessionId string, messages []message.Message) []message.Message {
	if len(messages) == 0 {
		return messages
	}

	// 找到最后一条用户消息，通常是最后一条消息
	lastUserMessageIdx := len(messages) - 1
	for ; lastUserMessageIdx >= 0; lastUserMessageIdx-- {
		if messages[lastUserMessageIdx].Role == message.User {
			break
		}
	}

	// 如果最后一条消息已经有了记忆标识，不做处理
	if len(messages[lastUserMessageIdx].MemoryReminders()) > 0 {
		return messages
	}

	memories := m.memories.GetMemories()

	// 找到最近一条带记忆标识的用户消息
	lastWithMemoryIdx := lastUserMessageIdx
	for ; lastWithMemoryIdx >= 0; lastWithMemoryIdx-- {
		if messages[lastWithMemoryIdx].Role == message.User && len(messages[lastWithMemoryIdx].MemoryReminders()) > 0 {
			break
		}
	}

	// 所有消息都没有记忆标识，直接加在第一条用户消息上，返回
	if lastWithMemoryIdx == -1 {
		firstUserMessageIdx := 0

		for ; firstUserMessageIdx < len(messages); firstUserMessageIdx++ {
			if messages[firstUserMessageIdx].Role == message.User {
				break
			}
		}

		messages[firstUserMessageIdx].Parts = append([]message.ContentPart{message.MemoryReminderContent{
			Type:     "text",
			Content:  toMessageContent(memories),
			Memories: toContentMemories(memories),
		}}, messages[firstUserMessageIdx].Parts...)

		if err := m.messages.UpdateMessage(ctx, messages[firstUserMessageIdx]); err != nil {
			logging.Error("fail to update message after add memory reminder", "err", err)
		}

		return messages
	}

	// 记忆变更
	memoriesBefore := messages[lastWithMemoryIdx].MemoryReminders()[0].Memories

	for _, oldMem := range memoriesBefore {
		for _, newMem := range memories {
			if oldMem.Path == newMem.Path && oldMem.Content != newMem.Content {
				messages[lastUserMessageIdx].Parts = append(messages[lastUserMessageIdx].Parts, message.MemoryReminderContent{
					Type:     "text",
					Content:  toMemoryChangeContent(newMem),
					Memories: toContentMemories(memories),
				})

				if err := m.messages.UpdateMessage(ctx, messages[lastUserMessageIdx]); err != nil {
					logging.Error("fail to update message after add memory reminder", "err", err)
				}
			}
		}
	}

	return messages
}

func NewMemoryFilter(memories llm.MemoryOperator, messages llm.MessageOperator) *MemoryFilter {
	return &MemoryFilter{
		memories: memories,
		messages: messages,
	}
}

func toMessageContent(memories []memory.Memory) string {
	if len(memories) == 0 {
		return fmt.Sprintf(memoryReminder, "")
	}

	var builder strings.Builder
	builder.WriteString(memoryHeader)
	for _, m := range memories {
		description := ""
		switch m.Location {
		case memory.ProjectMemoryLocation:
			description = "project instructions, checked into the codebase"
		case memory.ProjectLocalMemoryLocation:
			description = "user's private project instructions, not checked in"
		case memory.UserMemoryLocation:
			description = "user's private global instructions for all projects"
		default:
			continue
		}
		builder.WriteString(fmt.Sprintf("\n\nContents of %s(%s)\n\n%s", m.Path, description, m.Content))
	}
	return fmt.Sprintf(memoryReminder, builder.String())
}

func toMemoryChangeContent(mem memory.Memory) string {
	content := mem.Content
	if len(content) > maxLength {
		content = content[:maxLength]
	}

	result := ""
	for i, line := range strings.Split(content, "\n") {
		result += fmt.Sprintf("\n%d...%s", i+1, line)
	}

	return fmt.Sprintf(memoryChangedReminder, mem.Path, result)
}

func toContentMemories(memories []memory.Memory) []message.Memory {
	results := make([]message.Memory, len(memories))
	for i, m := range memories {
		results[i] = message.Memory{
			Content: m.Content,
			Path:    m.Path,
		}
	}

	return results
}

package filter

import (
	"context"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/llm/filter"
	"github.com/qoder-ai/qodercli/core/message"
)

type Refactor<PERSON>hain struct {
	refactors []filter.MessageRefactor
}

func (chain RefactorChain) Refactor(ctx context.Context, sessionId string, messages []message.Message) []message.Message {
	for _, r := range chain.refactors {
		messages = r.Refactor(ctx, sessionId, messages)
	}

	return messages
}

func NewRefactorChain(app llm.AppSupport) *RefactorChain {
	return &RefactorChain{
		refactors: []filter.MessageRefactor{
			NewTodoFilter(app),
			NewMemoryFilter(app, app),
			NewToolResultFilter(),
		},
	}
}

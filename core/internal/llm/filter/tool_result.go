package filter

import (
	"context"
	"github.com/google/uuid"
	"github.com/qoder-ai/qodercli/core/message"
)

type ToolResultFilter struct {
}

// Refactor 用来补偿某个工具调用中断后，assistant message可能没有对应的tool result导致模型调用报错的问题
func (t *ToolResultFilter) Refactor(ctx context.Context, sessionId string, messages []message.Message) []message.Message {
	if len(messages) == 0 {
		return messages
	}

	var newMessages []message.Message
	for i, msg := range messages {
		newMessages = append(newMessages, messages[i])
		// 模型返回了工具调用
		if msg.Role == message.Assistant && len(msg.ToolCalls()) > 0 {
			// 校验工具调用结果是否缺失
			if i == len(messages)-1 || messages[i+1].Role != message.Tool {
				// 如果没有tool result，则做补偿
				toolResult := message.Message{
					Id:        uuid.New().String(),
					SessionId: sessionId,
					Role:      message.Tool,
					Parts:     GetFixedToolResults(msg.ToolCalls(), nil),
					Model:     "",
					CreatedAt: msg.UpdatedAt,
					UpdatedAt: msg.UpdatedAt,
					Provider:  "",
					ParentId:  msg.Id,
					Status:    message.StatusInit,
					IsMeta:    false,
				}
				newMessages = append(newMessages, toolResult)
				// 修改下一条消息的parentId（这里是不会落盘的）
				if i < len(messages)-1 {
					messages[i+1].ParentId = toolResult.Id
				}
			}
		}
	}

	return newMessages
}

func NewToolResultFilter() *ToolResultFilter {
	return &ToolResultFilter{}
}

func GetFixedToolResults(toolCalls []message.ToolCall, originalResults []message.ToolResult) []message.ContentPart {
	results := make([]message.ContentPart, len(toolCalls))
	for i := 0; i < len(toolCalls); i++ {
		missing := true
		for _, r := range originalResults {
			if toolCalls[i].Id == r.ToolCallId {
				missing = false
				results[i] = r
				break
			}
		}

		if missing {
			results[i] = message.ToolResult{
				ToolCallId: toolCalls[i].Id,
				Name:       toolCalls[i].Name,
				Content:    "tool call interrupted",
				Metadata:   "input: " + toolCalls[i].Input,
				IsError:    true,
			}
		}
	}
	return results
}

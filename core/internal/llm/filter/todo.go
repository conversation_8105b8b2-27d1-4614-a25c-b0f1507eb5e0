package filter

import (
	"context"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
)

const todoEmptyReminder = `<system-reminder>
This is a reminder that your todo list is currently empty. DO NOT mention this to the user explicitly because they are already aware. If you are working on tasks that would benefit from a todo list please use the TodoWrite tool to create one. If not, please feel free to ignore. Again do not mention this message to the user.
</system-reminder>`

type TodoFilter struct {
	messages llm.MessageOperator
}

// Refactor 在第一条用户消息后增加待办列表为空的提示
func (t *TodoFilter) Refactor(ctx context.Context, sessionId string, messages []message.Message) []message.Message {
	if len(messages) == 0 {
		return messages
	}

	// 找到第一条用户消息
	firstUserMessageIdx := 0
	for ; firstUserMessageIdx < len(messages); firstUserMessageIdx++ {
		if messages[firstUserMessageIdx].Role == message.User {
			break
		}
	}

	if firstUserMessageIdx == len(messages) {
		return messages
	}

	if messages[firstUserMessageIdx].TodoReminder() != nil {
		return messages
	}

	parts := messages[firstUserMessageIdx].Parts
	reminder := message.TodoReminderContent{
		Type:    "text",
		Content: todoEmptyReminder,
	}
	parts = append([]message.ContentPart{reminder}, parts...)
	messages[firstUserMessageIdx].Parts = parts

	if err := t.messages.UpdateMessage(ctx, messages[firstUserMessageIdx]); err != nil {
		logging.Error("fail to update message after add todo reminder", "err", err)
	}

	return messages
}

func NewTodoFilter(messages llm.MessageOperator) *TodoFilter {
	return &TodoFilter{
		messages: messages,
	}
}

package internal

import (
	"context"
	"time"

	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/monitoring"

	"github.com/google/uuid"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/internal/storage"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

type MessageService struct {
	*pubsub.Broker[message.Message]
	cache      *storage.Cache
	storageSvc *storage.Service
}

func NewMessageService(projSvc core.ProjectService) *MessageService {
	storageSvc := storage.NewStorageService(projSvc)
	return &MessageService{
		Broker:     pubsub.NewBroker[message.Message](),
		cache:      storage.NewCache(),
		storageSvc: storageSvc,
	}
}

func (s *MessageService) Create(ctx context.Context, sessionId string, params message.CreateMessageParams) (message.Message, error) {
	ts := time.Now().UnixMilli()

	if params.Role != message.Assistant {
		params.Parts = append(params.Parts, message.Finish{
			Reason: "stop",
			Time:   ts,
		})
	}

	msg := message.Message{
		Id:        uuid.New().String(),
		SessionId: sessionId,
		Role:      params.Role,
		Parts:     params.Parts,
		Model:     params.Model,
		CreatedAt: ts,
		UpdatedAt: ts,
		Provider:  models.SupportedModels[params.Model].Provider,
		ParentId:  params.ParentId,
		Status:    message.StatusInit,
		IsMeta:    params.IsMeta,
	}

	s.cache.PutMessage(msg)

	fp := params.FinishedPart()
	if fp != nil && fp.Time != 0 {
		if err := s.storageSvc.PersistentMessage(ctx, msg, fp.Time); err != nil {
			return message.Message{}, err
		}
	}

	monitoring.RecordNewMessage(ctx, msg)
	s.Publish(pubsub.CreatedEvent, msg)
	return msg, nil
}

func (s *MessageService) Update(ctx context.Context, msg message.Message) error {
	msg.UpdatedAt = time.Now().UnixMilli()
	finishedBefore := s.cache.UpdateMessage(msg)

	fp := msg.FinishPart()
	if fp != nil && fp.Time != 0 && !finishedBefore {
		if err := s.storageSvc.PersistentMessage(ctx, msg, fp.Time); err != nil {
			return err
		}
		monitoring.RecordMessageFinish(ctx, msg)
	}

	s.Publish(pubsub.UpdatedEvent, msg)
	return nil
}

func (s *MessageService) List(ctx context.Context, sessionId string) ([]message.Message, error) {
	messages := s.cache.ListMessages(sessionId)

	if len(messages) != 0 {
		return messages, nil
	}

	var err error
	messages, err = s.storageSvc.LoadSessionMessages(ctx, sessionId)
	if err != nil {
		return nil, err
	}

	s.cache.InitSessionMessages(sessionId, messages)

	return messages, nil
}

func (s *MessageService) GetLastMessageOfSession(ctx context.Context, sessionId string) (*message.Message, error) {
	messages, err := s.List(ctx, sessionId)
	if err != nil {
		return nil, err
	}

	if len(messages) == 0 {
		return nil, nil
	}
	return &messages[len(messages)-1], nil
}

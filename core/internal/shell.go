package internal

import (
	"bufio"
	"context"
	"fmt"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/tui/event"
	"os/exec"
	"sync"
)

// ShellManager Shell执行管理器
type ShellManager struct {
	activeExecutions map[string]*exec.Cmd
	cancelFuncs      map[string]context.CancelFunc // 存储每个执行的取消函数
	mu               sync.RWMutex
}

// NewShellManager 创建新的Shell管理器
func NewShellManager() *ShellManager {
	return &ShellManager{
		activeExecutions: make(map[string]*exec.Cmd),
		cancelFuncs:      make(map[string]context.CancelFunc),
	}
}

// ExecuteShell 执行 Shell 命令
func (s *ShellManager) ExecuteShell(ctx context.Context, command string, workingDir string, executionId string) (<-chan event.ShellEvent, error) {
	eventCh := make(chan event.ShellEvent, 256)

	logging.Debug("Starting shell execution", "command", command, "workingDir", workingDir, "executionId", executionId)

	// 为每个执行创建单独的context
	execCtx, cancel := context.WithCancel(ctx)
	s.mu.Lock()
	s.cancelFuncs[executionId] = cancel
	s.mu.Unlock()

	// 异步执行命令
	go s.executeShellAsync(execCtx, executionId, command, workingDir, eventCh)

	return eventCh, nil
}

// CancelShell 取消 Shell 执行
func (s *ShellManager) CancelShell(executionId string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 通过取消context来终止执行
	if cancelFunc, exists := s.cancelFuncs[executionId]; exists {
		logging.Debug("Canceling shell execution", "executionId", executionId)
		cancelFunc() // 这会触发context取消，从而发送ShellEventCanceled事件
		return nil
	}

	return fmt.Errorf("execution not found: %s", executionId)
}

// executeShellAsync 异步执行 Shell 命令
func (s *ShellManager) executeShellAsync(ctx context.Context, executionId, command, workingDir string, eventCh chan<- event.ShellEvent) {
	defer close(eventCh)

	// 发送开始事件
	startEvent := event.NewShellEvent(event.ShellEventStarted, executionId).WithContent(command)
	eventCh <- startEvent

	// 创建命令
	cmd := exec.CommandContext(ctx, "/bin/bash", "-l", "-c", command)
	cmd.Dir = workingDir

	// 注册到活跃执行列表
	s.mu.Lock()
	s.activeExecutions[executionId] = cmd
	s.mu.Unlock()

	// 确保执行完成后清理
	defer func() {
		s.mu.Lock()
		delete(s.activeExecutions, executionId)
		delete(s.cancelFuncs, executionId)
		s.mu.Unlock()
	}()

	// 设置输出管道
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		eventCh <- event.NewShellEvent(event.ShellEventError, executionId).WithError(err)
		return
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		eventCh <- event.NewShellEvent(event.ShellEventError, executionId).WithError(err)
		return
	}

	// 启动命令
	if err := cmd.Start(); err != nil {
		eventCh <- event.NewShellEvent(event.ShellEventError, executionId).WithError(err)
		return
	}

	var wg sync.WaitGroup
	wg.Add(2)

	// 处理标准输出
	go func() {
		defer wg.Done()
		scanner := bufio.NewScanner(stdout)
		scanner.Buffer(make([]byte, 0, 64*1024), 1024*1024)

		for scanner.Scan() {
			line := scanner.Text()
			if line != "" {
				eventCh <- event.NewShellEvent(event.ShellEventOutput, executionId).WithContent(line)
			}
		}

		if err := scanner.Err(); err != nil {
			logging.Debug("Error reading stdout", "error", err, "executionId", executionId)
		}
	}()

	// 处理标准错误
	go func() {
		defer wg.Done()
		scanner := bufio.NewScanner(stderr)
		scanner.Buffer(make([]byte, 0, 64*1024), 1024*1024)

		for scanner.Scan() {
			line := scanner.Text()
			if line != "" {
				eventCh <- event.NewShellEvent(event.ShellEventError, executionId).WithContent(line)
			}
		}

		if err := scanner.Err(); err != nil {
			logging.Debug("Error reading stderr", "error", err, "executionId", executionId)
		}
	}()

	// 等待命令完成
	err = cmd.Wait()
	wg.Wait()

	// 检查是否被cancel，优先检查context状态
	if ctx.Err() != nil {
		eventCh <- event.NewShellEvent(event.ShellEventCanceled, executionId).WithError(ctx.Err())
		return
	}

	// 发送完成事件
	exitCode := 0
	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			exitCode = exitError.ExitCode()
		} else {
			// 其他错误（不是取消也不是正常退出）
			eventCh <- event.NewShellEvent(event.ShellEventError, executionId).WithError(err)
			return
		}
	}

	eventCh <- event.NewShellEvent(event.ShellEventFinished, executionId).WithExitCode(exitCode)
	logging.Debug("Shell execution completed", "executionId", executionId, "exitCode", exitCode)
}

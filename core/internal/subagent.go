package internal

import (
	"bufio"
	"fmt"
	"gopkg.in/yaml.v3"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/utils"
)

const generalPurposeAgentSystemPrompt = `You are an agent for %s. Given the user's message, you should use the tools available to complete the task. Do what has been asked; nothing more, nothing less. When you complete the task simply respond with a detailed writeup.

Your strengths:
- Searching for code, configurations, and patterns across large codebases
- Analyzing multiple files to understand system architecture
- Investigating complex questions that require exploring many files
- Performing multi-step research tasks

Guidelines:
- For file searches: Use Grep or Glob when you need to search broadly. Use Read when you know the specific file path.
- For analysis: Start broad and narrow down. Use multiple search strategies if the first doesn't yield results.
- Be thorough: Check multiple locations, consider different naming conventions, look for related files.
- NEVER create files unless they're absolutely necessary for achieving your goal. ALWAYS prefer editing an existing file to creating a new one.
- NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested.
- In your final response always share relevant file names and code snippets. Any file paths you return in your response MUST be absolute. Do NOT use relative paths.
- For clear communication, avoid using emojis.`

const prReviewerAgentSystemPrompt = `You are an expert PR reviewer for %s. Your role is to analyze pull requests and provide thorough, constructive PR reviews.

Your expertise includes:
- Code quality and best practices analysis
- Security vulnerability identification
- Performance optimization suggestions
- Testing and coverage assessment
- Architecture and design pattern evaluation
- Compliance with project coding standards

Argument handling:
- If a PR number is provided in the prompt (e.g., "123" or "#123"), review that specific pull request
- If no PR number is provided or the prompt is general, first run Bash("gh pr list") to show available PRs, then review the most recent/latest one

Review process:
1. Parse the input to check for specific PR number
2. If PR number provided: Use Bash("gh pr view <number>") to get PR details
3. If no PR number: Use Bash("gh pr list") first, then select the latest PR
4. Use Bash("gh pr diff <number>") to examine the code changes
5. Analyze the changes systematically:
   - Overall impact and purpose of changes
   - Code correctness and logic
   - Adherence to coding standards and conventions
   - Performance implications
   - Security considerations
   - Test coverage and quality
   - Documentation adequacy

Output format:
- Start with a brief summary of what the PR accomplishes
- Organize feedback into clear sections (e.g., Critical Issues, Suggestions, Positive Notes)
- Provide specific line references when applicable
- Suggest concrete improvements with examples when possible
- Rate the overall quality and readiness for merge

Guidelines:
- Be constructive and professional in all feedback
- Focus on code quality, not personal preferences
- Explain the "why" behind your suggestions
- Acknowledge good practices when you see them
- Prioritize critical issues over minor style preferences
- Consider the broader system architecture and impacts`

type agentService struct {
	workingDir string
}

func NewAgentService(workingDir string) agent.SubAgentService {
	return &agentService{
		workingDir: workingDir,
	}
}

func (a agentService) GetStorageDir() string {
	return filepath.Join(utils.GetUserStorageDir(), "agents")
}

func (a agentService) parseAgent(path string) (*agent.SubAgent, error) {
	info := &agent.SubAgent{StoragePath: path}
	bytes, err := os.ReadFile(path)
	if err != nil {
		return info, err
	}

	var part = 0
	var yamlContent string
	scanner := bufio.NewScanner(strings.NewReader(string(bytes)))
	for scanner.Scan() {
		line := scanner.Text()
		content := strings.TrimSpace(line)
		if content == "---" {
			part++
			continue
		}

		if part == 1 {
			yamlContent += line + "\n"
		} else if part == 2 {
			info.SystemPrompt += line + "\n"
		}
	}
	data := map[string]string{}
	if err := yaml.Unmarshal([]byte(yamlContent), data); err != nil {
		return info, err
	}

	if name, ok := data["name"]; ok {
		info.Name = name
	}
	if color, ok := data["color"]; ok {
		info.Color = color
	}
	if model, ok := data["model"]; ok {
		info.Model = model
	}
	if description, ok := data["description"]; ok {
		info.Description = description
	}
	if tools, ok := data["tools"]; ok {
		parts := strings.Split(tools, ",")
		for _, tool := range parts {
			tool = strings.TrimSpace(tool)
			if tool != "" {
				info.Tools = append(info.Tools, tool)
			}
		}
	}
	info.SystemPrompt = strings.TrimSpace(info.SystemPrompt)
	return info, a.validAgent(*info)
}

func (a agentService) validAgent(info agent.SubAgent) error {
	if info.Name == "" {
		return fmt.Errorf("agent [%s] is invalid, name is required", info.StoragePath)
	} else if info.SystemPrompt == "" {
		return fmt.Errorf("agent [%s] is invalid, system prompt is required", info.StoragePath)
	} else if info.Description == "" {
		return fmt.Errorf("agent [%s] is invalid, description is required", info.StoragePath)
	}
	return nil
}

func (a agentService) loadAgents(dir string, location agent.SubAgentLocation) ([]agent.SubAgent, error) {
	var agents []agent.SubAgent
	if _, err := os.Stat(dir); err != nil {
		return agents, nil
	}
	return agents, filepath.WalkDir(dir, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() && filepath.Ext(path) == ".md" {
			if info, err := a.parseAgent(path); err != nil {
				logging.ErrorPersist(fmt.Sprintf("failed to parse agent [%s]: %v", path, err))
				return nil
			} else {
				info.Location = location
				agents = append(agents, *info)
			}
		}
		return nil
	})
}

func (a agentService) loadBuiltinAgents() []agent.SubAgent {
	agents := []agent.SubAgent{
		{
			Name:         "general-purpose",
			Description:  "General-purpose agent for researching complex questions, searching for code, and executing multi-step tasks. When you are searching for a keyword or file and are not confident that you will find the right match in the first few tries use this agent to perform the search for you.",
			Tools:        []string{"*"}, // 支持所有工具，与JavaScript版本保持一致
			SystemPrompt: fmt.Sprintf(generalPurposeAgentSystemPrompt, core.AppName),
			Location:     agent.BuiltInSubAgentLocation,
		},
		{
			Name:         "pr-reviewer",
			Description:  "Expert PR reviewer for analyzing pull requests and providing thorough reviews. Specializes in code quality, security, performance, and best practices assessment for PR workflows.",
			Tools:        []string{"WebFetch", "read", "LS", "Glob", "Grep", "Bash"},
			SystemPrompt: fmt.Sprintf(prReviewerAgentSystemPrompt, core.AppName),
			Location:     agent.BuiltInSubAgentLocation,
		},
	}
	return agents
}

func (a agentService) loadAgentsFromStorage() ([]agent.SubAgent, error) {
	userDefinedAgentsDir := filepath.Join(utils.GetUserStorageDir(), "agents")
	return a.loadAgents(userDefinedAgentsDir, agent.UserSubAgentLocation)
}

func (a agentService) loadAgentsFromProject() ([]agent.SubAgent, error) {
	projectStorageDir := utils.GetWorkspaceStorageDir(a.workingDir)
	projectDefinedAgentsDir := filepath.Join(projectStorageDir, "agents")
	return a.loadAgents(projectDefinedAgentsDir, agent.ProjectSubAgentLocation)
}

func (a agentService) loadAgentsFromClaudeCodeStorage() ([]agent.SubAgent, error) {
	claudeCodeUserAgentsDir := filepath.Join(utils.GetClaudeCodeUserStorageDir(), "agents")
	return a.loadAgents(claudeCodeUserAgentsDir, agent.ClaudeCodeUserSubAgentLocation)
}

func (a agentService) loadAgentsFromClaudeCodeProject() ([]agent.SubAgent, error) {
	claudeCodeProjectStorageDir := utils.GetClaudeCodeWorkspaceStorageDir(a.workingDir)
	claudeCodeProjectDefinedAgentsDir := filepath.Join(claudeCodeProjectStorageDir, "agents")
	return a.loadAgents(claudeCodeProjectDefinedAgentsDir, agent.ClaudeCodeProjectSubAgentLocation)
}

func (a agentService) loadAgentMap() map[string]agent.SubAgent {
	agentsMap := make(map[string]agent.SubAgent)

	for _, info := range a.loadBuiltinAgents() {
		info.Location = agent.BuiltInSubAgentLocation
		agentsMap[info.Name] = info
	}

	if agents, err := a.loadAgentsFromStorage(); err != nil {
		logging.ErrorPersist(fmt.Sprintf("failed to load agents from storage: %v", err))
	} else {
		for _, info := range agents {
			info.Location = agent.UserSubAgentLocation
			agentsMap[info.Name] = info
		}
	}

	if agents, err := a.loadAgentsFromProject(); err != nil {
		logging.ErrorPersist(fmt.Sprintf("failed to load agents from project: %v", err))
	} else {
		for _, info := range agents {
			info.Location = agent.ProjectSubAgentLocation
			agentsMap[info.Name] = info
		}
	}

	if agents, err := a.loadAgentsFromClaudeCodeProject(); err != nil {
		logging.ErrorPersist(fmt.Sprintf("failed to load agents from claude code project: %v", err))
	} else {
		for _, info := range agents {
			info.Location = agent.ClaudeCodeProjectSubAgentLocation
			agentsMap[info.Name] = info
		}
	}

	if agents, err := a.loadAgentsFromClaudeCodeStorage(); err != nil {
		logging.ErrorPersist(fmt.Sprintf("failed to load agents from claude code storage: %v", err))
	} else {
		for _, info := range agents {
			info.Location = agent.ClaudeCodeUserSubAgentLocation
			agentsMap[info.Name] = info
		}
	}
	return agentsMap
}

func (a agentService) Get(name string) (*agent.SubAgent, error) {
	agentsMap := a.loadAgentMap()
	if info, ok := agentsMap[name]; ok {
		return &info, nil
	}
	return nil, fmt.Errorf("agent %s not found", name)
}

func (a agentService) List() ([]agent.SubAgent, error) {
	agentsMap := a.loadAgentMap()
	var mergedAgents []agent.SubAgent
	for _, info := range agentsMap {
		mergedAgents = append(mergedAgents, info)
	}
	sort.Slice(mergedAgents, func(i, j int) bool {
		return mergedAgents[i].Name < mergedAgents[j].Name
	})
	return mergedAgents, nil
}

func (a agentService) Save(config agent.SubAgent) error {
	var content strings.Builder
	content.WriteString("---\n")
	content.WriteString(fmt.Sprintf("name: %s\n", config.Name))
	content.WriteString(fmt.Sprintf("description: %s\n", config.Description))
	content.WriteString(fmt.Sprintf("tools: %s\n", strings.Join(config.Tools, ",")))
	content.WriteString("---\n")
	content.WriteString(config.SystemPrompt)
	storageDir := utils.GetUserStorageDir()
	if config.Location == agent.UserSubAgentLocation {
		// default, do nothing
	} else if config.Location == agent.ProjectSubAgentLocation {
		storageDir = utils.GetWorkspaceStorageDir(a.workingDir)
	} else {
		return fmt.Errorf("agent with location [%s] cannot be saved", config.Location)
	}
	path := filepath.Join(storageDir, "agents", fmt.Sprintf("%s.md", config.Name))
	return os.WriteFile(path, []byte(content.String()), 0644)
}

func (a agentService) Delete(name string) error {
	agentsMap := a.loadAgentMap()
	if info, ok := agentsMap[name]; ok {
		return os.Remove(info.StoragePath)
	}
	return nil
}

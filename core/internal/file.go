package internal

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/internal/storage"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

const (
	InitialVersion = "initial"
)

type historyService struct {
	*pubsub.Broker[core.File]
	storageSvc *storage.Service
	lock       sync.RWMutex
}

func NewHistoryService(projSvc core.ProjectService) core.HistoryService {
	return &historyService{
		Broker:     pubsub.NewBroker[core.File](),
		storageSvc: storage.NewStorageService(projSvc),
	}
}

func (s *historyService) Create(ctx context.Context, sessionId, path, content string) (core.File, error) {
	return s.createWithVersion(ctx, sessionId, path, content, InitialVersion)
}

func (s *historyService) CreateVersion(ctx context.Context, sessionId, path, content string) (core.File, error) {
	s.lock.Lock()
	defer s.lock.Unlock()

	file, err := s.storageSvc.GetLatestVersion(ctx, sessionId, path)

	if err != nil {
		return core.File{}, err
	}

	if file == nil {
		return s.Create(ctx, sessionId, path, content)
	}

	latestVersion := file.Version
	var nextVersion string
	if latestVersion == InitialVersion {
		nextVersion = "v1"
	} else if strings.HasPrefix(latestVersion, "v") {
		versionNum, err := strconv.Atoi(latestVersion[1:])
		if err != nil {
			// If we can't parse the version, just use a timestamp-based version
			nextVersion = fmt.Sprintf("v%d", file.CreatedAt)
		} else {
			nextVersion = fmt.Sprintf("v%d", versionNum+1)
		}
	} else {
		// If the version format is unexpected, use a timestamp-based version
		nextVersion = fmt.Sprintf("v%d", file.CreatedAt)
	}

	return s.createWithVersion(ctx, sessionId, path, content, nextVersion)
}

func (s *historyService) createWithVersion(ctx context.Context, sessionId, path, content, version string) (core.File, error) {
	ts := time.Now().UnixMilli()
	file := core.File{
		Id:        uuid.New().String(),
		SessionId: sessionId,
		Path:      path,
		Content:   content,
		Version:   version,
		CreatedAt: ts,
		UpdatedAt: ts,
	}

	err := s.storageSvc.CreateFileVersion(ctx, file)
	if err != nil {
		return core.File{}, err
	}

	s.Publish(pubsub.CreatedEvent, file)
	return file, nil
}

func (s *historyService) GetByPathAndSession(ctx context.Context, path, sessionId string) (core.File, error) {
	file, err := s.storageSvc.GetLatestVersion(ctx, sessionId, path)
	if err != nil {
		return core.File{}, err
	}
	if file == nil {
		return core.File{}, nil
	}

	return *file, nil
}

func (s *historyService) ListBySession(ctx context.Context, sessionId string) ([]core.File, error) {
	return s.storageSvc.ListSessionModifiedFiles(ctx, sessionId)
}

func (s *historyService) ListLatestSessionFiles(ctx context.Context, sessionID string) ([]core.File, error) {
	return s.storageSvc.ListLatestVersion(ctx, sessionID)
}

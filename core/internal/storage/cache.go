package storage

import (
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/message"
	"sync"
	"time"
)

type Cache struct {
	msgCache map[string][]message.Message
	sesCache []core.Session
	msgLock  sync.RWMutex
	sesLock  sync.RWMutex
}

func (c *Cache) ListMessages(sessionId string) []message.Message {
	c.msgLock.RLock()
	defer c.msgLock.RUnlock()

	return c.msgCache[sessionId]
}

func (c *Cache) InitSessionMessages(sessionId string, messages []message.Message) {
	c.msgLock.Lock()
	defer c.msgLock.Unlock()
	c.msgCache[sessionId] = messages
}

func (c *Cache) PutMessage(msg message.Message) {
	c.msgLock.Lock()
	defer c.msgLock.Unlock()

	sessionId := msg.SessionId
	if messages, ok := c.msgCache[sessionId]; ok {
		c.msgCache[sessionId] = append(messages, msg)
	} else {
		c.msgCache[sessionId] = []message.Message{msg}
	}
}

func (c *Cache) UpdateMessage(msg message.Message) bool {
	c.msgLock.Lock()
	defer c.msgLock.Unlock()

	finishedBefore := false
	sessionId := msg.SessionId
	for i, m := range c.msgCache[sessionId] {
		if m.Id == msg.Id {
			finishedBefore = c.msgCache[sessionId][i].IsFinished()
			c.msgCache[sessionId][i].UpdatedAt = time.Now().UnixMilli()
			c.msgCache[sessionId][i].Parts = msg.Parts
			break
		}
	}

	return finishedBefore
}

func (c *Cache) ListSessions() []core.Session {
	c.sesLock.RLock()
	defer c.sesLock.RUnlock()

	return c.sesCache
}

func (c *Cache) InitSessions(sessions []core.Session) {
	c.sesLock.Lock()
	defer c.sesLock.Unlock()
	c.sesCache = sessions
}

func (c *Cache) PutSession(ses core.Session) {
	c.sesLock.Lock()
	defer c.sesLock.Unlock()

	c.sesCache = append(c.sesCache, ses)
}

func (c *Cache) GetSession(sessionId string) *core.Session {
	c.sesLock.RLock()
	defer c.sesLock.RUnlock()

	for i, s := range c.sesCache {
		if s.Id == sessionId {
			return &c.sesCache[i]
		}
	}

	return nil
}

func (c *Cache) UpdateSession(ses core.Session) {
	c.sesLock.Lock()
	defer c.sesLock.Unlock()

	for i, s := range c.sesCache {
		if s.Id == ses.Id {
			c.sesCache[i].UpdatedAt = time.Now().UnixMilli()
			c.sesCache[i].Title = ses.Title
			c.sesCache[i].PromptTokens = ses.PromptTokens
			c.sesCache[i].CompletionTokens = ses.CompletionTokens
			c.sesCache[i].SummaryMessageId = ses.SummaryMessageId
			c.sesCache[i].Cost = ses.Cost
			break
		}
	}
}

func NewCache() *Cache {
	return &Cache{
		msgLock:  sync.RWMutex{},
		msgCache: make(map[string][]message.Message),
	}
}

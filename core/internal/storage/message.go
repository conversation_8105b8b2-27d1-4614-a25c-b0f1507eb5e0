package storage

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/internal/llm/converter"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/utils"
)

type PartType string

const (
	reasoningType  PartType = "reasoning"
	textType       PartType = "text"
	imageURLType   PartType = "image_url"
	binaryType     PartType = "binary"
	toolCallType   PartType = "tool_call"
	toolResultType PartType = "tool_result"
	finishType     PartType = "finish"
)

type PartWrapper struct {
	Type PartType            `json:"type"`
	Data message.ContentPart `json:"data"`
}

func (w *PartWrapper) UnmarshalJSON(data []byte) error {
	var wrapper struct {
		Type PartType        `json:"type"`
		Data json.RawMessage `json:"data"`
	}

	err := json.Unmarshal(data, &wrapper)
	if err != nil {
		return err
	}

	switch wrapper.Type {
	case reasoningType:
		part := message.ReasoningContent{}
		if err = json.Unmarshal(wrapper.Data, &part); err != nil {
			return err
		}
		w.Data = part
	case textType:
		part := message.TextContent{}
		if err = json.Unmarshal(wrapper.Data, &part); err != nil {
			return err
		}
		w.Data = part
	case imageURLType:
		part := message.ImageUrlContent{}
		if err = json.Unmarshal(wrapper.Data, &part); err != nil {
			return err
		}
		w.Data = part
	case binaryType:
		part := message.BinaryContent{}
		if err = json.Unmarshal(wrapper.Data, &part); err != nil {
			return err
		}
		w.Data = part
	case toolCallType:
		part := message.ToolCall{}
		if err = json.Unmarshal(wrapper.Data, &part); err != nil {
			return err
		}
		w.Data = part
	case toolResultType:
		part := message.ToolResult{}
		if err = json.Unmarshal(wrapper.Data, &part); err != nil {
			return err
		}
		w.Data = part
	case finishType:
		part := message.Finish{}
		if err = json.Unmarshal(wrapper.Data, &part); err != nil {
			return err
		}
		w.Data = part
	default:
		return fmt.Errorf("unknown part type: %s", wrapper.Type)
	}

	return nil
}

type storedMessage struct {
	Id         string              `json:"id"`
	SessionId  string              `json:"session_id"`
	Role       string              `json:"role"`
	Parts      []PartWrapper       `json:"parts"`
	Model      string              `json:"model"`
	CreatedAt  int64               `json:"created_at"`
	UpdatedAt  int64               `json:"updated_at"`
	FinishedAt int64               `json:"finished_at"`
	ParentId   string              `json:"parent_id"`
	Provider   string              `json:"provider"`
	IsMeta     bool                `json:"is_meta"`
	Usage      *message.TokenUsage `json:"usage,omitempty"`
}

func (s storedMessage) toMessage() (message.Message, error) {
	//parts, err := unmarshallParts([]byte(s.Parts))
	//if err != nil {
	//	return message.Message{}, err
	//}
	return message.Message{
		Id:        s.Id,
		SessionId: s.SessionId,
		Role:      message.Role(s.Role),
		Parts:     transParts(s.Parts),
		Model:     models.ModelId(s.Model),
		CreatedAt: s.CreatedAt,
		UpdatedAt: s.UpdatedAt,
		ParentId:  s.ParentId,
		Provider:  models.ModelProvider(s.Provider),
		IsMeta:    s.IsMeta,
		Usage:     s.Usage,
	}, nil
}

type UpdateMessageParams struct {
	Parts      string `json:"parts"`
	FinishedAt int64  `json:"finished_at"`
	ID         string `json:"id"`
}

type Service struct {
	projSvc core.ProjectService
	msgLock sync.RWMutex
	sesLock sync.RWMutex
}

func NewStorageService(projSvc core.ProjectService) *Service {
	return &Service{projSvc: projSvc}
}

func (s *Service) getSessionMessagesFile(path, sessionId string) string {
	return filepath.Join(path, sessionId+".jsonl")
}

func (s *Service) LoadSessionMessages(ctx context.Context, sessionId string) ([]message.Message, error) {
	s.msgLock.RLock()
	defer s.msgLock.RUnlock()

	if !s.projSvc.StorageDirExists() {
		return []message.Message{}, nil
	}

	f := s.getSessionMessagesFile(s.projSvc.GetStorageDir(), sessionId)
	if !fileExists(f) {
		return []message.Message{}, nil
	}

	return readMessagesFromFile(f)
}

func (s *Service) PersistentMessage(ctx context.Context, msg message.Message, finishedAt int64) error {
	parts, err := marshallParts(msg.Parts)
	if err != nil {
		return err
	}

	sm := storedMessage{
		Id:         msg.Id,
		SessionId:  msg.SessionId,
		Role:       string(msg.Role),
		Parts:      parts,
		Model:      string(msg.Model),
		Provider:   string(msg.Provider),
		ParentId:   msg.ParentId,
		CreatedAt:  msg.CreatedAt,
		UpdatedAt:  msg.UpdatedAt,
		FinishedAt: finishedAt,
		IsMeta:     msg.IsMeta,
		Usage:      msg.Usage,
	}

	am := converter.ConvertAnthropicStoredMessage(msg, s.projSvc.GetWorkDir(), "")

	s.msgLock.Lock()
	defer s.msgLock.Unlock()

	s.projSvc.EnsureStorageDir()

	if s.saveToClaudeCode() {
		claudePath := filepath.Join(utils.GetClaudeCodeUserStorageDir(), "projects", s.projSvc.GetId())
		_ = appendMessagesToFile(s.getSessionMessagesFile(claudePath, msg.SessionId), am)
	}

	return appendMessagesToFile(s.getSessionMessagesFile(s.projSvc.GetStorageDir(), msg.SessionId), sm)
}

func (s *Service) saveToClaudeCode() bool {
	if _, err := os.Stat(utils.GetClaudeCodeUserStorageDir()); err != nil {
		if os.IsNotExist(err) {
			return false
		}
		logging.Error("fail to stats claude storage directory", "err", err)
		return false
	}

	projDir := filepath.Join(utils.GetClaudeCodeUserStorageDir(), "projects", s.projSvc.GetId())
	if _, err := os.Stat(projDir); err != nil {
		if os.IsNotExist(err) {
			if err = os.MkdirAll(projDir, 0755); err != nil {
				logging.Error("fail to create claude projects directory", "err", err)
				return false
			}
		}
		logging.Error("fail to stats claude projects directory", "err", err)
		return false
	}

	return true
}

func readMessagesFromFile(filename string) ([]message.Message, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var messages []message.Message
	reader := bufio.NewReader(file)

	for {
		var sm storedMessage
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		if err := json.Unmarshal([]byte(line), &sm); err != nil {
			return nil, err
		}

		msg, err := sm.toMessage()
		if err != nil {
			return nil, err
		}

		messages = append(messages, msg)
	}

	return messages, nil
}

func appendMessagesToFile(filename string, msg interface{}) error {
	file, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	//for _, msg := range messages {
	data, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	if _, err := writer.Write(data); err != nil {
		return err
	}
	if err := writer.WriteByte('\n'); err != nil {
		return err
	}
	//}

	return nil
}

func marshallParts(parts []message.ContentPart) ([]PartWrapper, error) {
	wrappedParts := make([]PartWrapper, len(parts))

	for i, part := range parts {
		var typ PartType

		switch part.(type) {
		case message.ReasoningContent:
			typ = reasoningType
		case message.TextContent:
			typ = textType
		case message.ImageUrlContent:
			typ = imageURLType
		case message.BinaryContent:
			typ = binaryType
		case message.ToolCall:
			typ = toolCallType
		case message.ToolResult:
			typ = toolResultType
		case message.Finish:
			typ = finishType
		default:
			return nil, fmt.Errorf("unknown part type: %T", part)
		}

		wrappedParts[i] = PartWrapper{
			Type: typ,
			Data: part,
		}
	}
	return wrappedParts, nil
}

func transParts(partWrappers []PartWrapper) []message.ContentPart {
	parts := make([]message.ContentPart, len(partWrappers))

	for i := range partWrappers {
		parts[i] = partWrappers[i].Data
	}

	return parts
}

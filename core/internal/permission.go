package internal

import (
	"context"
	"path/filepath"
	"slices"
	"sync"

	"github.com/google/uuid"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/monitoring"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"github.com/qoder-ai/qodercli/lsp"
)

type PermissionService struct {
	*pubsub.Broker[core.PermissionRequest]
	sessionPermissions  []core.PermissionRequest
	autoApproveAll      bool
	pendingRequests     sync.Map
	autoApproveSessions []string
}

func (s *PermissionService) GrantPersistent(request core.PermissionRequest) {
	respCh, ok := s.pendingRequests.Load(request.Id)
	if ok {
		respCh.(chan bool) <- true
	}
	s.sessionPermissions = append(s.sessionPermissions, request)
	// 用户选择永久允许
	monitoring.RecordToolDecision(context.Background(), request.SessionId, request.ToolName, "accept", "user_permanent")
	monitoring.RecordToolDecisionMetric(context.Background(), request.SessionId, request.ToolName, "accept", detectLanguageFromParams(request.Params))
}

func (s *PermissionService) Grant(request core.PermissionRequest) {
	respCh, ok := s.pendingRequests.Load(request.Id)
	if ok {
		respCh.(chan bool) <- true
	}
	// 用户临时允许
	monitoring.RecordToolDecision(context.Background(), request.SessionId, request.ToolName, "accept", "user_temporary")
	monitoring.RecordToolDecisionMetric(context.Background(), request.SessionId, request.ToolName, "accept", detectLanguageFromParams(request.Params))
}

func (s *PermissionService) Deny(request core.PermissionRequest) {
	respCh, ok := s.pendingRequests.Load(request.Id)
	if ok {
		respCh.(chan bool) <- false
	}
	// 用户拒绝
	monitoring.RecordToolDecision(context.Background(), request.SessionId, request.ToolName, "reject", "user_reject")
	monitoring.RecordToolDecisionMetric(context.Background(), request.SessionId, request.ToolName, "reject", detectLanguageFromParams(request.Params))
}

func (s *PermissionService) CreateRequestWithContext(ctx core.SessionContext, opts core.CreatePermissionRequest) bool {
	if s.autoApproveAll || slices.Contains(s.autoApproveSessions, opts.SessionId) {
		// 自动批准：来源为 config
		monitoring.RecordToolDecision(context.Background(), opts.SessionId, opts.ToolName, "accept", "config")
		monitoring.RecordToolDecisionMetric(context.Background(), opts.SessionId, opts.ToolName, "accept", detectLanguageFromParams(opts.Params))
		return true
	}
	dir := filepath.Dir(opts.Path)
	if dir == "." && ctx != nil {
		dir = ctx.GetWorkingDir()
	}

	request := core.PermissionRequest{
		Id:          uuid.New().String(),
		Path:        dir,
		SessionId:   opts.SessionId,
		ToolName:    opts.ToolName,
		Description: opts.Description,
		Action:      opts.Action,
		Params:      opts.Params,
	}

	for _, p := range s.sessionPermissions {
		if p.ToolName == request.ToolName && p.Action == request.Action && p.SessionId == request.SessionId && p.Path == request.Path {
			// 命中会话级永久允许：来源为 user_permanent
			monitoring.RecordToolDecision(context.Background(), request.SessionId, request.ToolName, "accept", "user_permanent")
			monitoring.RecordToolDecisionMetric(context.Background(), request.SessionId, request.ToolName, "accept", detectLanguageFromParams(request.Params))
			return true
		}
	}

	respCh := make(chan bool, 1)

	s.pendingRequests.Store(request.Id, respCh)
	defer s.pendingRequests.Delete(request.Id)

	s.Publish(pubsub.CreatedEvent, request)

	resp := <-respCh
	return resp
}

func (s *PermissionService) AutoApproveSession(sessionId string) {
	s.autoApproveSessions = append(s.autoApproveSessions, sessionId)
}

func (s *PermissionService) SetAutoApproveAll(v bool) {
	s.autoApproveAll = v
}

func (s *PermissionService) IsAutoApproveAll() bool {
	return s.autoApproveAll
}

func NewPermissionService(autoApproveAll bool) *PermissionService {
	return &PermissionService{
		Broker:             pubsub.NewBroker[core.PermissionRequest](),
		autoApproveAll:     autoApproveAll,
		sessionPermissions: make([]core.PermissionRequest, 0),
	}
}

func detectLanguageFromParams(params any) string {
	switch v := params.(type) {
	case specs.EditPermissionsParams:
		return languageFromPath(v.FilePath)
	case specs.WritePermissionsParams:
		return languageFromPath(v.FilePath)
	default:
		return "unknown"
	}
}

func languageFromPath(path string) string {
	if path == "" {
		return "unknown"
	}
	lang := lsp.DetectLanguageID(path)
	if string(lang) == "" {
		return "unknown"
	}
	return string(lang)
}

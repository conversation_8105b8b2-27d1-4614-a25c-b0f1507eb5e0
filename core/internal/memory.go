package internal

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/memory"
	"github.com/qoder-ai/qodercli/core/utils"
)

const memoryReminder = `<system-reminder>
As you answer the user's questions, you can use the following context:
%s

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context or otherwise consider it in your response unless it is highly relevant to your task. Most of the time, it is not relevant.
</system-reminder>`

const memoryHeader = `# markdown-formatted-instructions
Codebase and user instructions are shown below. Be sure to adhere to these instructions. IMPORTANT: These instructions OVERRIDE any default behavior and you MUST follow them exactly as written.`

type MemoryService struct {
	workingDir string
}

func NewMemoryService(workingDir string) *MemoryService {
	return &MemoryService{
		workingDir: workingDir,
	}
}

func appendMemory(memories []memory.Memory, location memory.MemoryLocation, path string) []memory.Memory {
	bytes, err := os.ReadFile(path)
	if err != nil {
		return memories
	}
	return append(memories, memory.Memory{
		Location: location,
		Path:     path,
		Content:  string(bytes),
	})
}

func (s *MemoryService) memoryFiles(location memory.MemoryLocation, workingDir string) []memory.Memory {
	var memories []memory.Memory
	switch location {
	case memory.UserMemoryLocation:
		memories = appendMemory(memories, memory.UserMemoryLocation, filepath.Join(utils.GetClaudeCodeUserStorageDir(), "CLAUDE.md"))
		memories = appendMemory(memories, memory.UserMemoryLocation, s.GetUserMemoryFilePath())
	case memory.ProjectLocalMemoryLocation:
		memories = appendMemory(memories, memory.ProjectLocalMemoryLocation, filepath.Join(workingDir, "CLAUDE.local.md"))
		memories = appendMemory(memories, memory.ProjectLocalMemoryLocation, s.GetProjectLocalMemoryFilePath())
	case memory.ProjectMemoryLocation:
		memories = appendMemory(memories, memory.UserMemoryLocation, filepath.Join(workingDir, "CLAUDE.md"))
		memories = appendMemory(memories, memory.UserMemoryLocation, s.GetProjectMemoryFilePath())
	case memory.ManagedMemoryLocation:
		//     switch (zQ()) {
		//      case "macos":
		//        return "/Library/Application Support/ClaudeCode/CLAUDE.md";
		//      case "windows":
		//        return "C:\\ProgramData\\ClaudeCode/CLAUDE.md";
		//      default:
		//        return "/etc/claude-code/CLAUDE.md";
		//    }
	}
	return memories
}

func toMessageContent(memories []memory.Memory) []string {
	if len(memories) == 0 {
		return []string{fmt.Sprintf(memoryReminder, "")}
	}

	var builder strings.Builder
	builder.WriteString(memoryHeader)
	for _, m := range memories {
		description := ""
		switch m.Location {
		case memory.ProjectMemoryLocation:
			description = "project instructions, checked into the codebase"
		case memory.ProjectLocalMemoryLocation:
			description = "user's private project instructions, not checked in"
		case memory.UserMemoryLocation:
			description = "user's private global instructions for all projects"
		default:
			continue
		}
		builder.WriteString(fmt.Sprintf("\n\nContents of %s(%s)\n\n%s", m.Path, description, m.Content))
	}
	return []string{fmt.Sprintf(memoryReminder, builder.String())}
}

func (s *MemoryService) GetMemories() []memory.Memory {
	var memories []memory.Memory
	memories = append(memories, s.memoryFiles(memory.ProjectMemoryLocation, s.workingDir)...)
	memories = append(memories, s.memoryFiles(memory.ProjectLocalMemoryLocation, s.workingDir)...)
	memories = append(memories, s.memoryFiles(memory.UserMemoryLocation, s.workingDir)...)
	return memories
}

func (s *MemoryService) GetMemoriesContent() []string {
	memories := s.GetMemories()
	return toMessageContent(memories)
}

func (s *MemoryService) GetUserMemoryFilePath() string {
	return filepath.Join(utils.GetUserStorageDir(), core.MemoryFileName)
}

func (s *MemoryService) GetProjectMemoryFilePath() string {
	return filepath.Join(s.workingDir, core.MemoryFileName)
}

func (s *MemoryService) GetProjectLocalMemoryFilePath() string {
	return filepath.Join(s.workingDir, core.MemoryLocalFileName)
}

// Append 将内容追加到指定位置的记忆文件
func (s *MemoryService) Append(location memory.MemoryLocation, content string) error {
	var path string
	switch location {
	case memory.UserMemoryLocation:
		path = s.GetUserMemoryFilePath()
	case memory.ProjectLocalMemoryLocation:
		path = s.GetProjectLocalMemoryFilePath()
	case memory.ProjectMemoryLocation:
		path = s.GetProjectMemoryFilePath()
	default:
		return fmt.Errorf("unsupported memory location: %s", location)
	}
	if err := os.MkdirAll(filepath.Dir(path), 0o755); err != nil {
		return err
	}
	f, err := os.OpenFile(path, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0o644)
	if err != nil {
		return err
	}
	defer f.Close()

	if _, err := f.WriteString(strings.TrimRight("- "+content, "\n") + "\n"); err != nil {
		return err
	}
	return nil
}

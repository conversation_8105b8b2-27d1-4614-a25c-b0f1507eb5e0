package internal

import (
	"context"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/internal/storage"
	"github.com/qoder-ai/qodercli/core/monitoring"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

type SessionMonitor interface {
	EnsureSessionMonitor(sessionId string)
}
type SessionConfig struct {
	WorkingDir string `json:"working_dir"`
}

type SessionService struct {
	*pubsub.Broker[core.Session]
	storageSvc    *storage.Service
	cache         *storage.Cache
	monitor       SessionMonitor
	sessionLoaded bool

	// session级别的锁，防止并发字段更新
	sessionMutexes map[string]*sync.Mutex
	mutexLock      sync.RWMutex
}

func NewSessionService(projSvc core.ProjectService, monitor SessionMonitor) *SessionService {
	storageSvc := storage.NewStorageService(projSvc)

	return &SessionService{
		Broker:         pubsub.NewBroker[core.Session](),
		cache:          storage.NewCache(),
		monitor:        monitor,
		storageSvc:     storageSvc,
		sessionMutexes: make(map[string]*sync.Mutex),
	}
}

func (s *SessionService) CreateWithConfig(ctx context.Context, title string, config *SessionConfig) (core.Session, error) {
	ts := time.Now().UnixMilli()
	ses := core.Session{
		Id:        uuid.New().String(),
		Title:     title,
		CreatedAt: ts,
		UpdatedAt: ts,
	}

	if config != nil {
		ses.WorkingDir = config.WorkingDir
	}

	if err := s.storageSvc.PersistentSession(ctx, ses); err != nil {
		return ses, err
	}

	s.cache.PutSession(ses)
	s.monitor.EnsureSessionMonitor(ses.Id)
	// metrics: session.count += 1
	monitoring.RecordSessionStart(ctx, ses.Id)
	s.Publish(pubsub.CreatedEvent, ses)
	return ses, nil
}

func (s *SessionService) CreateTaskSession(ctx context.Context, toolCallId, parentId, title string) (core.Session, error) {
	parent, err := s.Get(ctx, parentId)
	if err != nil {
		return core.Session{}, err
	}

	ts := time.Now().UnixMilli()
	ses := core.Session{
		Id:              toolCallId,
		ParentSessionId: parentId,
		Title:           title,
		WorkingDir:      parent.WorkingDir,
		ContextPaths:    parent.ContextPaths,
		CreatedAt:       ts,
		UpdatedAt:       ts,
	}

	if err := s.storageSvc.PersistentSession(ctx, ses); err != nil {
		return ses, err
	}

	s.cache.PutSession(ses)
	// 任务会话也计一次，以统计工具子会话的使用量（如不需要可移除）
	s.monitor.EnsureSessionMonitor(ses.Id)
	monitoring.RecordSessionStart(ctx, ses.Id)
	s.Publish(pubsub.CreatedEvent, ses)
	return ses, nil
}

func (s *SessionService) Get(ctx context.Context, id string) (core.Session, error) {
	ses := s.cache.GetSession(id)
	if ses != nil {
		return *ses, nil
	}

	ses, err := s.storageSvc.LoadSession(ctx, id)

	if err != nil {
		return core.Session{}, err
	}

	s.cache.PutSession(*ses)
	return *ses, nil
}

// getSessionMutex 获取session锁
func (s *SessionService) getSessionMutex(sessionId string) *sync.Mutex {
	s.mutexLock.RLock()
	if mutex, exists := s.sessionMutexes[sessionId]; exists {
		s.mutexLock.RUnlock()
		return mutex
	}
	s.mutexLock.RUnlock()

	s.mutexLock.Lock()
	defer s.mutexLock.Unlock()

	// 双重检查
	if mutex, exists := s.sessionMutexes[sessionId]; exists {
		return mutex
	}

	s.sessionMutexes[sessionId] = &sync.Mutex{}
	return s.sessionMutexes[sessionId]
}

// UpdateTitle 更新session的title字段
func (s *SessionService) UpdateTitle(ctx context.Context, sessionId string, title string) error {
	mutex := s.getSessionMutex(sessionId)
	mutex.Lock()
	defer mutex.Unlock()

	ses, err := s.Get(ctx, sessionId)
	if err != nil {
		return err
	}

	ses.Title = title
	ses.UpdatedAt = time.Now().UnixMilli()

	s.cache.UpdateSession(ses)
	if err := s.storageSvc.PersistentSession(ctx, ses); err != nil {
		return err
	}
	s.Publish(pubsub.UpdatedEvent, ses)
	return nil
}

// UpdateTokens 更新session的token相关字段
func (s *SessionService) UpdateTokens(ctx context.Context, sessionId string, promptTokens int64, completionTokens int64, costDelta float64) error {
	mutex := s.getSessionMutex(sessionId)
	mutex.Lock()
	defer mutex.Unlock()

	ses, err := s.Get(ctx, sessionId)
	if err != nil {
		return err
	}

	ses.PromptTokens = promptTokens
	ses.CompletionTokens = completionTokens
	ses.Cost += costDelta
	ses.UpdatedAt = time.Now().UnixMilli()

	s.cache.UpdateSession(ses)
	if err := s.storageSvc.PersistentSession(ctx, ses); err != nil {
		return err
	}
	s.Publish(pubsub.UpdatedEvent, ses)
	return nil
}

func (s *SessionService) Save(ctx context.Context, ses core.Session) error {
	s.cache.UpdateSession(ses)
	if err := s.storageSvc.PersistentSession(ctx, ses); err != nil {
		return err
	}
	s.Publish(pubsub.UpdatedEvent, ses)
	return nil
}

func (s *SessionService) List(ctx context.Context) ([]core.Session, error) {
	if s.sessionLoaded {
		return s.cache.ListSessions(), nil
	}

	sessions, err := s.storageSvc.LoadAllSessions(ctx)
	if err != nil {
		return nil, err
	}

	s.cache.InitSessions(sessions)
	s.sessionLoaded = true
	return sessions, nil
}

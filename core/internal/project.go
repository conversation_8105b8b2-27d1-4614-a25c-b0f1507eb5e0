package internal

import (
	"errors"
	"fmt"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/utils"
	"os"
	"path/filepath"
	"regexp"
)

type ProjectService struct {
	workDir     string
	id          string
	initialized bool
}

func NewProjectService(workDir string) *ProjectService {
	p := &ProjectService{
		workDir: workDir,
		id:      parseProjectId(workDir),
	}

	_, err := os.Stat(p.GetStorageDir())
	if err != nil {
		if os.IsNotExist(err) {
			p.initialized = false
		} else {
			panic(errors.New(fmt.Sprintf("project service initialization failed: %s", err)))
		}
	} else {
		p.initialized = true
	}

	return p
}

func (s *ProjectService) GetId() string {
	return s.id
}

func (s *ProjectService) GetWorkDir() string {
	return s.workDir
}

func (s *ProjectService) GetStorageDir() string {
	return filepath.Join(utils.GetUserStorageDir(), "projects", s.GetId())
}

func (s *ProjectService) StorageDirExists() bool {
	dir := s.GetStorageDir()
	if _, err := os.Stat(dir); err != nil {
		if os.IsNotExist(err) {
			return false
		}
		logging.Error("fail to stats project storage directory", "dir", dir, "err", err)
		panic(err)
	}
	return true
}

func (s *ProjectService) EnsureStorageDir() string {
	projectDir := s.GetStorageDir()
	if s.StorageDirExists() {
		return projectDir
	}

	if err := os.MkdirAll(projectDir, 0755); err != nil {
		logging.Error("fail to create project storage directory", "dir", projectDir, "err", err)
		panic(err)
	}

	return projectDir
}

func (s *ProjectService) Initialized() bool {
	return s.initialized
}

func (s *ProjectService) Initialize() error {
	s.EnsureStorageDir()
	s.initialized = true
	return nil
}

func parseProjectId(dir string) string {
	re := regexp.MustCompile(`[^a-zA-Z0-9]`)
	return re.ReplaceAllString(dir, "-")
}

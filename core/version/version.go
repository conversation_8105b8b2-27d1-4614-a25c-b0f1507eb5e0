package version

import "runtime/debug"

const defaultVersion = "pre-20250808"

// Version 会在发布时通过 -ldflags 注入，例如 v1.2.3。
// 若未注入，则默认为 defaultVersion，并在 init() 中尝试从构建信息读取。
var Version string

// UpdateBaseURL 用于自更新与安装脚本指向的公共基址，由发布脚本通过 -X 注入。
// 例如：https://cdn.example.com/qodercli
// 待改造：使用云上的服务获取更新地址，而不是构建时注入，此地址仅用于开发态
var UpdateBaseURL = ""

func init() {
	// 如果通过 -ldflags 注入了版本号，则不再用 debug 信息覆盖
	if Version != "" {
		return
	}
	info, ok := debug.ReadBuildInfo()
	if !ok {
		// < go v1.18
		return
	}
	mainVersion := info.Main.Version
	if mainVersion == "" || mainVersion == "(devel)" {
		// bin not built using `go install`
		return
	}
	Version = mainVersion
}

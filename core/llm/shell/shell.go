package shell

import (
	"context"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"time"
)

const (
	ShellCommandStarted   pubsub.EventType = "ShellCommandStarted"
	ShellCommandCompleted pubsub.EventType = "ShellCommandCompleted"
	ShellCommandFailed    pubsub.EventType = "ShellCommandFailed"
	ShellCommandTimeout   pubsub.EventType = "ShellCommandTimeout"
)

// Event represents a shell execution event
type Event struct {
	WorkingDir    string // 工作目录
	CommandResult        // 执行结果
}

type ShellStatus string

const (
	ShellStatusRunning   ShellStatus = "running"
	ShellStatusCompleted ShellStatus = "completed"
	ShellStatusFailed    ShellStatus = "failed"
	ShellStatusKilled    ShellStatus = "killed"
)

// CommandResult represents the result of a shell command execution
type CommandResult struct {
	BashId      string
	Command     string // 执行的命令
	Stdout      string
	Stderr      string
	ExitCode    *int
	Timeout     bool
	Interrupted bool
	Error       error
	Status      ShellStatus
	StartTime   time.Time // 开始时间
	EndTime     time.Time // 结束时间
}

type Shell interface {
	GetStartTime() time.Time
	IsExecuting() bool
	GetResult() CommandResult
	WaitForResult() CommandResult
	KillProcess() error
}

type ShellService interface {
	ListShells() []Shell
	GetShell(shellId string) (Shell, bool)
	Exec(ctx context.Context, workingDir string, command string, timeout time.Duration) (string, error)
}

package command

import (
	"context"
)

type Location string

const (
	LocationUser              Location = "user"
	LocationProject           Location = "project"
	LocationBuiltIn           Location = "built-in"
	LocationClaudeCodeUser    Location = "claude-code-user"
	LocationClaudeCodeProject Location = "claude-code-project"
)

type Type string

const (
	TypeLocal  Type = "local"
	TypePrompt Type = "prompt"
	TypeEvent  Type = "event"
)

type Command struct {
	// ID 需要全局唯一，保持前后端一致
	ID string
	// Name 命令的展示名称，可以重复但不建议。Name应该仅包含有效语意字符。
	Name         string
	AllowedTools string `yaml:"allowed-tools"`
	ArgumentHint string `yaml:"argument-hint"`
	Description  string
	Model        string
	Content      string
	Location     Location
	Type         Type
	GetPrompt    func(ctx context.Context, sessionId string, args string) string
	Run          func(ctx context.Context, sessionId string, args string) (string, error)
	RunWithEvent func(ctx context.Context, sessionId string, args string, output chan Event)
}

type Event struct {
	CommandName string   `json:"commandName"`
	Args        string   `json:"args"`
	Payload     any      `json:"payload"`
	InputChan   chan any `json:"-"`
	SessionId   string   `json:"sessionId"`
}

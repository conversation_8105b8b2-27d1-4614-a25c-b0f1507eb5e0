package provider

import (
	"github.com/qoder-ai/qodercli/core/llm/models"
	"strings"
)

type ClientBuilder struct {
	ApiKey          string
	Model           models.Model
	MaxTokens       int64
	SystemMessage   string
	ReasoningEffort ReasoningEffort
	Debug           bool
	BaseUrl         string
	DisableCache    bool
	ShouldThink     func(string) bool
}

type ClientOption func(*ClientBuilder)

func WithAPIKey(apiKey string) ClientOption {
	return func(builder *ClientBuilder) {
		builder.ApiKey = apiKey
	}
}

func WithModel(model models.Model) ClientOption {
	return func(builder *ClientBuilder) {
		builder.Model = model
	}
}

func WithMaxTokens(maxTokens int64) ClientOption {
	return func(builder *ClientBuilder) {
		builder.MaxTokens = maxTokens
	}
}

func WithSystemMessage(systemMessage string) ClientOption {
	return func(builder *ClientBuilder) {
		builder.SystemMessage = systemMessage
	}
}

func WithDebug(debug bool) ClientOption {
	return func(builder *ClientBuilder) {
		builder.Debug = debug
	}
}

func WithReasoningEffort(reasoningEffort ReasoningEffort) ClientOption {
	return func(builder *ClientBuilder) {
		builder.ReasoningEffort = reasoningEffort
	}
}

func WithBaseUrl(baseUrl string) ClientOption {
	return func(builder *ClientBuilder) {
		builder.BaseUrl = baseUrl
	}
}

func WithDisableCache(disableCache bool) ClientOption {
	return func(builder *ClientBuilder) {
		builder.DisableCache = disableCache
	}
}

func DefaultShouldThinkFn(s string) bool {
	return strings.Contains(strings.ToLower(s), "think")
}

func WithShouldThinkFn(fn func(string) bool) ClientOption {
	return func(builder *ClientBuilder) {
		builder.ShouldThink = fn
	}
}

package agent

import (
	"context"
	"errors"

	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

var (
	ErrRequestCancelled = errors.New("Request cancelled by user")
	ErrSessionBusy      = errors.New("Session is currently processing another request")
	ErrAgentBusy        = errors.New("Agent is currently processing another request")
)

type EnvType string

const (
	AgentEventTypeError     EnvType = "error"
	AgentEventTypeSystem    EnvType = "system"
	AgentEventTypeUser      EnvType = "user"
	AgentEventTypeAssistant EnvType = "assistant"
	AgentEventTypeResult    EnvType = "result"
	AgentEventTypeSummarize EnvType = "summarize"
)

type Config struct {
	Tools          []string       `json:"tools,omitempty"`
	McpServers     []string       `json:"mcp_servers,omitempty"`
	Model          models.ModelId `json:"model,omitempty"`
	PermissionMode string         `json:"permission_mode,omitempty"`
	ApiKeySource   string         `json:"api_key_source,omitempty"`
}

type Event struct {
	*Config   `json:",omitempty"`
	Type      EnvType          `json:"type"`
	Subtype   string           `json:"subtype,omitempty"`
	Message   *message.Message `json:"message,omitempty"`
	Error     error            `json:"error,omitempty"`
	SessionId string           `json:"session_id"`
	Progress  string           `json:"progress,omitempty"`
	Done      bool             `json:"done"`
}

type Agent interface {
	pubsub.Subscriber[Event]
	Model() models.Model
	Run(ctx context.Context, sessionId string, content string, attachments ...message.Attachment) (<-chan Event, error)
	Cancel(sessionId string)
	IsSessionBusy(sessionId string) bool
	IsBusy() bool
	UpdateModel(agentName config.AgentName, modelId models.ModelId) error
	Summarize(ctx context.Context, sessionId string) error
	SummarizeWithInstructions(ctx context.Context, sessionId string, instructions string) error
}

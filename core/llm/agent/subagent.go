package agent

type SubAgentLocation string

const (
	BuiltInSubAgentLocation           SubAgentLocation = "built-in"
	UserSubAgentLocation              SubAgentLocation = "user"
	ProjectSubAgentLocation           SubAgentLocation = "project"
	ClaudeCodeProjectSubAgentLocation SubAgentLocation = "claude-code-project"
	ClaudeCodeUserSubAgentLocation    SubAgentLocation = "claude-code-user"
)

type SubAgent struct {
	Name         string
	Description  string
	Model        string
	Tools        []string
	SystemPrompt string
	Location     SubAgentLocation
	StoragePath  string
	Color        string
}

type SubAgentService interface {
	Get(name string) (*SubAgent, error)
	List() ([]SubAgent, error)
	Save(info SubAgent) error
	Delete(name string) error
}

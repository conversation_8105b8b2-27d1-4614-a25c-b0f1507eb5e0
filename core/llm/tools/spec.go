package tools

import (
	"encoding/json"
	"errors"
)

const (
	TaskToolName         = "Task"
	BashToolName         = "Bash"
	BashOutputToolName   = "BashOutput"
	KillBashToolName     = "KillBash"
	DiagnosticsToolName  = "diagnostics"
	EditToolName         = "Edit"
	ExitPlanModeToolName = "ExitPlanMode"
	GlobToolName         = "Glob"
	GrepToolName         = "Grep"
	LSToolName           = "LS"
	MultiEditToolName    = "MultiEdit"
	PatchToolName        = "patch"
	ReadToolName         = "Read"
	TodoWriteToolName    = "TodoWrite"
	WebFetchToolName     = "WebFetch"
	WebSearchToolName    = "WebSearch"
	WriteToolName        = "Write"
)

type IsPresent interface {
	IsPresent() bool
}

type NotPresent struct{}

func (n NotPresent) IsPresent() bool {
	return false
}

type Spec[T1, T2, T3 IsPresent] struct {
	params           T1
	permissionParams T2
	responseMetadata T3
}

func (t *Spec[T1, T2, T3]) GetParams(input string) (*T1, error) {
	if !t.params.IsPresent() {
		return nil, nil
	}

	p := new(T1)
	err := json.Unmarshal([]byte(input), p)

	return p, err
}

func (t *Spec[T1, T2, T3]) ParsePermissionParams(params any) T2 {
	if !t.permissionParams.IsPresent() {
		return t.permissionParams
	}

	return params.(T2)
}

func (t *Spec[T1, T2, T3]) LoadResponseMetadata(metadata string) (*T3, error) {
	if !t.responseMetadata.IsPresent() {
		return nil, errors.New("no response metadata attached to spec")
	}

	m := new(T3)
	err := json.Unmarshal([]byte(metadata), m)
	return m, err
}

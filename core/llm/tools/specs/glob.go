package specs

import "github.com/qoder-ai/qodercli/core/llm/tools"

var GlobSpec = tools.Spec[GlobParams, tools.NotPresent, GlobResponseMetadata]{}

type GlobParams struct {
	Pattern string `json:"pattern"`
	Path    string `json:"path"`
}

func (g GlobParams) IsPresent() bool {
	return true
}

type GlobResponseMetadata struct {
	NumberOfFiles int    `json:"number_of_files"`
	Truncated     bool   `json:"truncated"`
	SearchPath    string `json:"search_path"`
	Pattern       string `json:"pattern"`
	ElapsedMs     int64  `json:"elapsed_ms"`
}

func (g GlobResponseMetadata) IsPresent() bool {
	return true
}

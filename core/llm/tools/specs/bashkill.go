package specs

import "github.com/qoder-ai/qodercli/core/llm/tools"

var BashKillSpec = tools.Spec[BashKillParams, BashKillPermissionsParams, BashKillResponseMetadata]{}

type BashKillParams struct {
	BashId string `json:"bash_id"`
}

func (b BashKillParams) IsPresent() bool {
	return true
}

type BashKillPermissionsParams struct {
	BashId string `json:"bash_id"`
}

func (b BashKillPermissionsParams) IsPresent() bool {
	return true
}

type BashKillResponseMetadata struct {
	Success bool   `json:"result"`
	Message string `json:"message"`
	BashId  string `json:"bash_id"`
}

func (b BashKillResponseMetadata) IsPresent() bool {
	return true
}

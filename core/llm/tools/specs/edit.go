package specs

import "github.com/qoder-ai/qodercli/core/llm/tools"

var EditSpec = tools.Spec[EditParams, EditPermissionsParams, EditResponseMetadata]{}

type EditParams struct {
	FilePath   string `json:"file_path,omitempty"`
	OldString  string `json:"old_string"`
	NewString  string `json:"new_string"`
	ReplaceAll bool   `json:"replace_all,omitempty"`
}

func (e EditParams) IsPresent() bool {
	return true
}

type EditPermissionsParams struct {
	FilePath string `json:"file_path"`
	Diff     string `json:"diff"`
}

func (e EditPermissionsParams) IsPresent() bool {
	return true
}

type EditResponseMetadata struct {
	Diff      string `json:"diff"`
	Additions int    `json:"additions"`
	Removals  int    `json:"removals"`
}

func (e EditResponseMetadata) IsPresent() bool {
	return true
}

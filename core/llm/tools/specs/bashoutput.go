package specs

import (
	"github.com/qoder-ai/qodercli/core/llm/shell"
	"github.com/qoder-ai/qodercli/core/llm/tools"
)

var BashOutputSpec = tools.Spec[BashOutputParams, BashOutputPermissionsParams, BashOutputResponseMetadata]{}

type BashOutputParams struct {
	BashId string `json:"bash_id"`
	Filter string `json:"filter"`
}

func (b BashOutputParams) IsPresent() bool {
	return true
}

type BashOutputPermissionsParams struct {
	BashId string `json:"bash_id"`
	Filter string `json:"filter"`
}

func (b BashOutputPermissionsParams) IsPresent() bool {
	return true
}

type BashOutputResponseMetadata struct {
	ExitCode *int              `json:"exit_code"`
	Stdout   string            `json:"stdout"`
	Stderr   string            `json:"stderr"`
	BashId   string            `json:"bash_id"`
	Command  string            `json:"command"`
	Status   shell.ShellStatus `json:"status"`
}

func (b BashOutputResponseMetadata) IsPresent() bool {
	return true
}

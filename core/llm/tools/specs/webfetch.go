package specs

import "github.com/qoder-ai/qodercli/core/llm/tools"

var WebFetchSpec = tools.Spec[FetchParams, FetchPermissionsParams, tools.NotPresent]{}

type FetchParams struct {
	URL    string `json:"url"`
	Prompt string `json:"prompt"`
}

func (f FetchParams) IsPresent() bool {
	return true
}

type FetchPermissionsParams struct {
	URL    string `json:"url"`
	Prompt string `json:"prompt"`
}

func (f FetchPermissionsParams) IsPresent() bool {
	return true
}

package hook

import "github.com/qoder-ai/qodercli/core/config"

type HookDecision string

const (
	HookDecisionApprove HookDecision = "approve"
	HookDecisionBlock   HookDecision = "block"
)

type PreToolUseSpecificOutput struct {
	HookEventName            string       `json:"hookEventName"`
	PermissionDecision       HookDecision `json:"permissionDecision,omitempty"`
	PermissionDecisionReason string       `json:"permissionDecisionReason,omitempty"`
}

type SessionStartSpecificOutput struct {
	HookEventName     string `json:"hookEventName"`
	AdditionalContext string `json:"additionalContext,omitempty"`
}

type PostToolUseSpecificOutput SessionStartSpecificOutput

type DecisionControl struct {
	Continue           *bool        `json:"continue,omitempty"`
	SuppressOutput     *bool        `json:"suppressOutput,omitempty"`
	StopReason         string       `json:"stopReason,omitempty"`
	Decision           HookDecision `json:"decision,omitempty"`
	Reason             string       `json:"reason,omitempty"`
	SystemMessage      string       `json:"systemMessage,omitempty"`
	HookSpecificOutput any          `json:"hookSpecificOutput,omitempty"`
}

type Input struct {
	Tool string
	Args any
}

// Result refer https://docs.anthropic.com/en/docs/claude-code/hooks#hook-output
type Result struct {
	ExitCode        int
	Stdout          string
	Stderr          string
	DecisionControl *DecisionControl
}

type Executor interface {
	Execute() (*Result, error)
}

type HookService interface {
	GetHookExecutors(event config.HookEvent, input Input) []Executor
	GetPreToolUseHookExecutor(input Input) []Executor
	GetPostToolUseHookExecutor(input Input) []Executor
	GetNotificationHookExecutor(input Input) []Executor
	GetUserPromptSubmitHookExecutor(input Input) []Executor
	GetStopHookExecutor(input Input) []Executor
	GetSubagentStopHookExecutor(input Input) []Executor
	GetPreCompactHookExecutor(input Input) []Executor
	GetSessionStartHookExecutor(input Input) []Executor
}

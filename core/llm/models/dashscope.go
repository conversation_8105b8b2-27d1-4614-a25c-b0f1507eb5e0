package models

const (
	ProviderDashScope ModelProvider = "dashscope"
	Qwen3CoderPlus    ModelId       = "qwen3-coder-plus"
)

var DashScopeModels = map[ModelId]Model{
	Qwen3CoderPlus: {
		Id:                  Qwen3CoderPlus,
		Name:                "Qwen3 Coder Plus",
		Provider:            ProviderDashScope,
		APIModel:            "qwen3-coder-plus",
		CostPer1MIn:         2.00,
		CostPer1MInCached:   0.50,
		CostPer1MOutCached:  0.0,
		CostPer1MOut:        8.00,
		ContextWindow:       1000000,
		DefaultMaxTokens:    50000,
		SupportsAttachments: true,
	},
}

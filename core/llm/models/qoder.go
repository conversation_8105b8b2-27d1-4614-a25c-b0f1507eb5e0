package models

const (
	ProviderQoder        ModelProvider = "qoder"
	QoderSonnet4Thinking ModelId       = "qoder_claude_sonnet4_thinking"
	QoderSonnet4                       = "qoder_claude_sonnet4"
	QoderAuto                          = "auto"
)

var QoderModels = map[ModelId]Model{
	QoderAuto: {
		Id:                  QoderAuto,
		Name:                "Qoder Auto",
		Provider:            ProviderQoder,
		APIModel:            "auto",
		CostPer1MIn:         2.00,
		CostPer1MInCached:   0.50,
		CostPer1MOutCached:  0.0,
		CostPer1MOut:        8.00,
		ContextWindow:       200000,
		DefaultMaxTokens:    50000,
		SupportsAttachments: true,
	},
	QoderSonnet4Thinking: {
		Id:                  QoderSonnet4Thinking,
		Name:                "Qoder Sonnet 4 Thinking",
		Provider:            ProviderQoder,
		APIModel:            "idealab_claude_sonnet4_thinking",
		CostPer1MIn:         2.00,
		CostPer1MInCached:   0.50,
		CostPer1MOutCached:  0.0,
		CostPer1MOut:        8.00,
		ContextWindow:       200000,
		DefaultMaxTokens:    50000,
		CanReason:           true,
		SupportsAttachments: true,
	},
	QoderSonnet4: {
		Id:                  QoderSonnet4,
		Name:                "Qoder Sonnet 4",
		Provider:            ProviderQoder,
		APIModel:            "idealab_claude_sonnet4",
		CostPer1MIn:         2.00,
		CostPer1MInCached:   0.50,
		CostPer1MOutCached:  0.0,
		CostPer1MOut:        8.00,
		ContextWindow:       200000,
		DefaultMaxTokens:    50000,
		SupportsAttachments: true,
	},
}

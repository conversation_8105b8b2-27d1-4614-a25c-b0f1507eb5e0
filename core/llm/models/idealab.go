package models

const (
	ProviderIdeaLab        ModelProvider = "idealab"
	IdeaLabSonnet4Thinking ModelId       = "idealab_claude_sonnet4"
)

var IdeaLabModels = map[ModelId]Model{
	IdeaLabSonnet4Thinking: {
		Id:                  IdeaLabSonnet4Thinking,
		Name:                "Idealab Sonnet 4 Thinking",
		Provider:            ProviderIdeaLab,
		APIModel:            "claude_sonnet4",
		CostPer1MIn:         2.00,
		CostPer1MInCached:   0.50,
		CostPer1MOutCached:  0.0,
		CostPer1MOut:        8.00,
		ContextWindow:       200000,
		DefaultMaxTokens:    50000,
		CanReason:           true,
		SupportsAttachments: true,
	},
}

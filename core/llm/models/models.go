package models

import "maps"

type ModelId string
type ModelProvider string

type Model struct {
	Id                  ModelId       `json:"id"`
	Name                string        `json:"name"`
	Provider            ModelProvider `json:"provider"`
	APIModel            string        `json:"api_model"`
	CostPer1MIn         float64       `json:"cost_per_1m_in"`
	CostPer1MOut        float64       `json:"cost_per_1m_out"`
	CostPer1MInCached   float64       `json:"cost_per_1m_in_cached"`
	CostPer1MOutCached  float64       `json:"cost_per_1m_out_cached"`
	ContextWindow       int64         `json:"context_window"`
	DefaultMaxTokens    int64         `json:"default_max_tokens"`
	CanReason           bool          `json:"can_reason"`
	SupportsAttachments bool          `json:"supports_attachments"`
}

var ProviderPopularity = map[ModelProvider]int{
	ProviderIdeaLab:   1,
	ProviderDashScope: 2,
	ProviderQoder:     3,
	ProviderAnthropic: 4,
	ProviderOpenAI:    5,
}

var SupportedModels = map[ModelId]Model{}

func init() {
	maps.Copy(SupportedModels, IdeaLabModels)
	maps.Copy(SupportedModels, DashScopeModels)
	maps.Copy(SupportedModels, QoderModels)
	maps.Copy(SupportedModels, AnthropicModels)
	maps.Copy(SupportedModels, OpenAIModels)
}

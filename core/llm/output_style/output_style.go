package output_style

type OutputStyleSource string

const (
	OutputStyleSourceUser           OutputStyleSource = "user"
	OutputStyleSourceBuiltIn        OutputStyleSource = "built-in"
	OutputStyleSourceClaudeCodeUser OutputStyleSource = "claude-code-user"
)

type OutputStyle struct {
	Name            string
	Source          OutputStyleSource
	Description     string
	Prompt          string
	IsCodingRelated bool
	StoragePath     string
}

type OutputStyleService interface {
	Get(name string) (*OutputStyle, error)
	List() ([]OutputStyle, error)
}

package core

type Session struct {
	Id               string   `json:"id"`
	ParentSessionId  string   `json:"parent_session_id"`
	Title            string   `json:"title"`
	MessageCount     int64    `json:"message_count"`
	PromptTokens     int64    `json:"prompt_tokens"`
	CompletionTokens int64    `json:"completion_tokens"`
	SummaryMessageId string   `json:"summary_message_id"`
	Cost             float64  `json:"cost"`
	CreatedAt        int64    `json:"created_at"`
	UpdatedAt        int64    `json:"updated_at"`
	WorkingDir       string   `json:"working_dir"`
	ContextPaths     []string `json:"context_paths,omitempty"`
}

type SessionContext interface {
	GetWorkingDir() string
	GetContextPaths() []string
}

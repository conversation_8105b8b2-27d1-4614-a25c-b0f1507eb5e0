package monitoring

import (
	"context"
	"errors"
	"fmt"
	"path"
	"sync"
	"time"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/models"
	providerapi "github.com/qoder-ai/qodercli/core/llm/provider"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/version"
	"github.com/qoder-ai/qodercli/qoder"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/log"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/trace"
)

var sessionMap = make(map[string]*sessionMonitor)
var sessionMapLock sync.Mutex
var cachedSessionMonitor *sessionMonitor

// contextKey 用于在context中传递用户信息的key类型
type contextKey string

const userInfoKey contextKey = "user_info"

// UserInfo 用户信息结构
type UserInfo struct {
	AccountID string
	UserID    string
	UserName  string
}

// WithUserInfo 将用户信息添加到context中
func WithUserInfo(ctx context.Context) context.Context {
	aid, uid, name := qoder.GetUserIdAndName()
	userInfo := UserInfo{
		AccountID: aid,
		UserID:    uid,
		UserName:  name,
	}
	return context.WithValue(ctx, userInfoKey, userInfo)
}

// getUserInfoFromContext 从context中获取用户信息
func getUserInfoFromContext(ctx context.Context) UserInfo {
	if userInfo, ok := ctx.Value(userInfoKey).(UserInfo); ok {
		return userInfo
	}
	return UserInfo{}
}

// buildMetricAttributes 构建包含用户信息的metric属性集
func buildMetricAttributes(ctx context.Context, baseAttrs ...attribute.KeyValue) attribute.Set {
	attrs := make([]attribute.KeyValue, 0, len(baseAttrs)+1)
	attrs = append(attrs, baseAttrs...)

	userInfo := getUserInfoFromContext(ctx)
	if userInfo.AccountID != "" {
		attrs = append(attrs, attribute.String("user.account_id", userInfo.AccountID))
	}

	return attribute.NewSet(attrs...)
}

// buildLogAttributes 构建包含用户信息和会话信息的log属性
func buildLogAttributes(ctx context.Context, record *log.Record, sessionId string) {
	userInfo := getUserInfoFromContext(ctx)
	if userInfo.AccountID != "" {
		record.AddAttributes(log.String("user.account_id", userInfo.AccountID))
	}
	if userInfo.UserID != "" {
		record.AddAttributes(log.String("user.id", userInfo.UserID))
	}
	if userInfo.UserName != "" {
		record.AddAttributes(log.String("user.name", userInfo.UserName))
	}
	if sessionId != "" {
		record.AddAttributes(log.String("session.id", sessionId))
	}
}

type sessionMonitor struct {
	sessionId string
	provider  *Provider

	// meters
	messageCounter      metric.Int64Counter
	sessionCounter      metric.Int64Counter
	linesOfCodeCounter  metric.Int64Counter
	pullRequestCounter  metric.Int64Counter
	commitCounter       metric.Int64Counter
	costCounter         metric.Float64Counter
	tokenCounter        metric.Int64Counter
	toolDecisionCounter metric.Int64Counter
	activeTimeCounter   metric.Int64Counter

	logger log.Logger
	tracer trace.Tracer

	// accumulators for session summary
	totalsMu sync.Mutex
	summary  SessionSummary
}

// ModelUsageTotal aggregates token usage per model
type ModelUsageTotal struct {
	InputTokens         int64 `json:"input_tokens"`
	OutputTokens        int64 `json:"output_tokens"`
	CacheReadTokens     int64 `json:"cache_read_tokens"`
	CacheCreationTokens int64 `json:"cache_creation_tokens"`
}

// SessionSummary stores human-readable session totals for exit page
type SessionSummary struct {
	TotalCostUSD       float64                    `json:"total_cost_usd"`
	TotalAPIDurationMs int64                      `json:"total_api_duration_ms"`
	TotalLinesAdded    int64                      `json:"total_lines_added"`
	TotalLinesRemoved  int64                      `json:"total_lines_removed"`
	ModelUsage         map[string]ModelUsageTotal `json:"model_usage"`
}

func EnsureSessionMonitor(mode Mode, proj core.ProjectService, sessionId string, register ShutdownRegister) error {
	s := getSessionMonitor(sessionId)
	if s != nil {
		return nil
	}

	sessionMapLock.Lock()
	defer sessionMapLock.Unlock()

	if _, ok := sessionMap[sessionId]; ok {
		return nil
	}

	provider, err := NewProvider(mode, path.Join(proj.GetStorageDir(), sessionId), register)
	if err != nil {
		return err
	}

	meter := provider.meterProvider.Meter("qodercli", metric.WithInstrumentationVersion(version.Version))
	messageCounter, _ := meter.Int64Counter("qodercli.message.count", metric.WithUnit("1"))
	sessionCounter, _ := meter.Int64Counter("qodercli.session.count", metric.WithUnit("1"))
	linesOfCodeCounter, _ := meter.Int64Counter("qodercli.lines_of_code.count", metric.WithUnit("1"))
	pullRequestCounter, _ := meter.Int64Counter("qodercli.pull_request.count", metric.WithUnit("1"))
	commitCounter, _ := meter.Int64Counter("qodercli.commit.count", metric.WithUnit("1"))
	costCounter, _ := meter.Float64Counter("qodercli.cost.usage", metric.WithUnit("usd"))
	tokenCounter, _ := meter.Int64Counter("qodercli.token.usage", metric.WithUnit("1"))
	toolDecisionCounter, _ := meter.Int64Counter("qodercli.tool_decision.count", metric.WithUnit("1"))
	activeTimeCounter, _ := meter.Int64Counter("qodercli.active_time.count", metric.WithUnit("s"))

	logger := provider.logProvider.Logger("qodercli", log.WithInstrumentationVersion(version.Version))
	tracer := provider.traceProvider.Tracer("qodercli", trace.WithInstrumentationVersion(version.Version))

	monitor := &sessionMonitor{
		sessionId:           sessionId,
		provider:            provider,
		messageCounter:      messageCounter,
		sessionCounter:      sessionCounter,
		linesOfCodeCounter:  linesOfCodeCounter,
		pullRequestCounter:  pullRequestCounter,
		commitCounter:       commitCounter,
		costCounter:         costCounter,
		tokenCounter:        tokenCounter,
		toolDecisionCounter: toolDecisionCounter,
		activeTimeCounter:   activeTimeCounter,
		logger:              logger,
		tracer:              tracer,
	}

	// init summary map
	monitor.summary = SessionSummary{ModelUsage: make(map[string]ModelUsageTotal)}

	sessionMap[sessionId] = monitor
	cachedSessionMonitor = monitor
	return nil
}

func getSessionMonitor(sessionId string) *sessionMonitor {
	if cachedSessionMonitor != nil && cachedSessionMonitor.sessionId == sessionId {
		return cachedSessionMonitor
	}

	sessionMapLock.Lock()
	defer sessionMapLock.Unlock()

	return sessionMap[sessionId]
}

func RecordStart() {
	// no-op for tracing (短根方案)
}

// RecordSessionStart 在新会话创建时自增一次 session.count
func RecordSessionStart(ctx context.Context, sessionId string) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		logging.Error("session monitor not found: " + sessionId)
		return
	}
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}
	attrs := buildMetricAttributes(ctx, attribute.String("session.id", sessionId))
	monitor.sessionCounter.Add(ctx, 1, metric.WithAttributeSet(attrs))
}

// RecordLinesOfCode 上报新增/删除的行数
func RecordLinesOfCode(ctx context.Context, sessionId string, added int, removed int) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		logging.Error("session monitor not found: " + sessionId)
		return
	}
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}
	addedAttrs := buildMetricAttributes(ctx, attribute.String("session.id", sessionId), attribute.String("type", "added"))
	removedAttrs := buildMetricAttributes(ctx, attribute.String("session.id", sessionId), attribute.String("type", "removed"))
	monitor.linesOfCodeCounter.Add(ctx, int64(added), metric.WithAttributeSet(addedAttrs))
	monitor.linesOfCodeCounter.Add(ctx, int64(removed), metric.WithAttributeSet(removedAttrs))

	monitor.totalsMu.Lock()
	monitor.summary.TotalLinesAdded += int64(added)
	monitor.summary.TotalLinesRemoved += int64(removed)
	monitor.totalsMu.Unlock()

	// 运行级别累计
	addRunLines(added, removed)
}

// RecordPullRequest PR 计数
func RecordPullRequest(ctx context.Context, sessionId string) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		return
	}
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}
	attrs := buildMetricAttributes(ctx, attribute.String("session.id", sessionId))
	monitor.pullRequestCounter.Add(ctx, 1, metric.WithAttributeSet(attrs))
}

// RecordCommit 提交计数
func RecordCommit(ctx context.Context, sessionId string) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		return
	}
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}
	attrs := buildMetricAttributes(ctx, attribute.String("session.id", sessionId))
	monitor.commitCounter.Add(ctx, 1, metric.WithAttributeSet(attrs))
}

// RecordCost 成本计数（USD）
func RecordCost(ctx context.Context, sessionId, model string, usd float64) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		return
	}
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}
	attrs := buildMetricAttributes(ctx,
		attribute.String("session.id", sessionId),
		attribute.String("model", model))
	monitor.costCounter.Add(ctx, usd, metric.WithAttributeSet(attrs))

	monitor.totalsMu.Lock()
	monitor.summary.TotalCostUSD += usd
	monitor.totalsMu.Unlock()
}

// RecordTokens Token 计数（按类型）
func RecordTokens(ctx context.Context, sessionId, model string, usage providerapi.TokenUsage) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		return
	}
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}
	baseAttrs := []attribute.KeyValue{
		attribute.String("session.id", sessionId),
		attribute.String("model", model),
	}
	if usage.InputTokens > 0 {
		attrs := buildMetricAttributes(ctx, append(baseAttrs, attribute.String("type", "input"))...)
		monitor.tokenCounter.Add(ctx, int64(usage.InputTokens), metric.WithAttributeSet(attrs))
	}
	if usage.OutputTokens > 0 {
		attrs := buildMetricAttributes(ctx, append(baseAttrs, attribute.String("type", "output"))...)
		monitor.tokenCounter.Add(ctx, int64(usage.OutputTokens), metric.WithAttributeSet(attrs))
	}
	if usage.CacheReadTokens > 0 {
		attrs := buildMetricAttributes(ctx, append(baseAttrs, attribute.String("type", "cacheRead"))...)
		monitor.tokenCounter.Add(ctx, int64(usage.CacheReadTokens), metric.WithAttributeSet(attrs))
	}
	if usage.CacheCreationTokens > 0 {
		attrs := buildMetricAttributes(ctx, append(baseAttrs, attribute.String("type", "cacheCreation"))...)
		monitor.tokenCounter.Add(ctx, int64(usage.CacheCreationTokens), metric.WithAttributeSet(attrs))
	}

	monitor.totalsMu.Lock()
	agg := monitor.summary.ModelUsage[model]
	agg.InputTokens += int64(usage.InputTokens)
	agg.OutputTokens += int64(usage.OutputTokens)
	agg.CacheReadTokens += int64(usage.CacheReadTokens)
	agg.CacheCreationTokens += int64(usage.CacheCreationTokens)
	monitor.summary.ModelUsage[model] = agg
	monitor.totalsMu.Unlock()
}

// RecordToolDecisionMetric 工具决策计数
func RecordToolDecisionMetric(ctx context.Context, sessionId, tool, decision, language string) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		return
	}
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}
	attrs := buildMetricAttributes(ctx,
		attribute.String("session.id", sessionId),
		attribute.String("tool", tool),
		attribute.String("decision", decision),
		attribute.String("language", language))
	monitor.toolDecisionCounter.Add(ctx, 1, metric.WithAttributeSet(attrs))
}

// AddActiveTimeSeconds 活跃时间（秒）计数
func AddActiveTimeSeconds(ctx context.Context, sessionId string, seconds int) {
	if seconds <= 0 {
		return
	}
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		return
	}
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}
	attrs := buildMetricAttributes(ctx, attribute.String("session.id", sessionId))
	monitor.activeTimeCounter.Add(ctx, int64(seconds), metric.WithAttributeSet(attrs))
}

func RecordNewMessage(ctx context.Context, msg message.Message) {
	monitor := getSessionMonitor(msg.SessionId)
	if monitor == nil {
		logging.Error("session monitor not found: " + msg.SessionId)
		return
	}

	// 使用传入的context，如果没有用户信息则添加
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}

	// incr counter
	attrs := buildMetricAttributes(ctx,
		attribute.String("role", string(msg.Role)),
		attribute.String("session.id", msg.SessionId))
	monitor.messageCounter.Add(ctx, 1, metric.WithAttributeSet(attrs))

	// 短根方案下，不在会话层面记录事件，保留在请求 span 内按需记录

	// user message recorded at start
	// https://docs.anthropic.com/en/docs/claude-code/monitoring-usage#user-prompt-event
	if msg.Role == message.User {
		content := msg.Content().String()
		record := log.Record{}
		record.SetEventName("qodercli.user_prompt")
		record.SetTimestamp(time.UnixMilli(msg.CreatedAt))
		// 固定记录原始 prompt 内容
		record.AddAttributes(log.String("prompt", content))
		record.AddAttributes(log.Int("prompt_length", len([]rune(content))))
		// 添加用户ID和会话信息
		buildLogAttributes(ctx, &record, msg.SessionId)

		monitor.logger.Emit(ctx, record)
	}
}

func RecordMessageFinish(ctx context.Context, msg message.Message) {
	monitor := getSessionMonitor(msg.SessionId)
	if monitor == nil {
		logging.Error("session monitor not found: " + msg.SessionId)
		return
	}

	if msg.Role == message.User {
		content := msg.Content().String()
		record := log.Record{}
		record.SetEventName("qodercli.user_prompt")
		record.SetTimestamp(time.UnixMilli(msg.CreatedAt))
		// 固定记录原始 prompt 内容
		record.AddAttributes(log.String("prompt", content))
		record.AddAttributes(log.Int("prompt_length", len([]rune(content))))
		// 添加用户ID和会话信息
		buildLogAttributes(ctx, &record, msg.SessionId)

		monitor.logger.Emit(ctx, record)
	}

	// 短根方案下，不在会话层面记录事件
}

// EmitSessionEvent for general event sending
func EmitSessionEvent(ctx context.Context, sessionId string, record log.Record) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		logging.Error("session monitor not found: " + sessionId)
		return
	}

	monitor.logger.Emit(ctx, record)
}

func RecordToolCalling(ctx context.Context, sessionId string, toolCall tools.ToolCall, response tools.ToolResponse, duration time.Duration, err error) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		logging.Error("session monitor not found: " + sessionId)
		return
	}
	record := log.Record{}
	record.SetEventName("qodercli.tool_result")
	record.SetTimestamp(time.Now())

	success := !(err != nil || response.IsError)

	record.AddAttributes(
		log.String("tool_name", toolCall.Name),
		log.String("success", fmt.Sprintf("%t", success)),
		log.Int64("duration_ms", duration.Milliseconds()),
		log.String("tool_parameters", toolCall.Input),
	)
	// 添加用户ID信息
	buildLogAttributes(ctx, &record, sessionId)

	if err != nil && errors.Is(err, core.ErrorPermissionDenied) {
		record.AddAttributes(
			log.String("decision", "reject"),
			log.String("source", "user_reject"),
		)
	} else {
		record.AddAttributes(
			log.String("decision", "accept"),
			log.String("source", "user_temporary"),
		)
	}

	if err != nil {
		record.AddAttributes(log.String("error", err.Error()))
	}

	monitor.logger.Emit(ctx, record)
}

// RecordToolDecision 记录工具权限决策事件（tool_decision）
func RecordToolDecision(ctx context.Context, sessionId, toolName, decision, source string) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		logging.Error("session monitor not found: " + sessionId)
		return
	}
	record := log.Record{}
	record.SetEventName("qodercli.tool_decision")
	record.SetTimestamp(time.Now())
	record.AddAttributes(
		log.String("tool_name", toolName),
		log.String("decision", decision),
		log.String("source", source),
	)
	// 添加用户ID信息
	buildLogAttributes(ctx, &record, sessionId)
	monitor.logger.Emit(ctx, record)
}

// RecordAPIRequest 记录 Claude API 请求成功事件（api_request）
func RecordAPIRequest(ctx context.Context, sessionId string, model models.Model, usage providerapi.TokenUsage, duration time.Duration, costUSD float64) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		logging.Error("session monitor not found: " + sessionId)
		return
	}
	record := log.Record{}
	record.SetEventName("qodercli.api_request")
	record.SetTimestamp(time.Now())
	record.AddAttributes(
		log.String("model", model.APIModel),
		log.Float64("cost_usd", costUSD),
		log.Int64("duration_ms", duration.Milliseconds()),
		log.Int64("input_tokens", usage.InputTokens),
		log.Int64("output_tokens", usage.OutputTokens),
		log.Int64("cache_read_tokens", usage.CacheReadTokens),
		log.Int64("cache_creation_tokens", usage.CacheCreationTokens),
	)
	// 添加用户ID信息
	buildLogAttributes(ctx, &record, sessionId)
	monitor.logger.Emit(ctx, record)

	// 同步累加成本与token计数器
	RecordCost(ctx, sessionId, model.APIModel, costUSD)
	RecordTokens(ctx, sessionId, model.APIModel, usage)

	monitor.totalsMu.Lock()
	monitor.summary.TotalAPIDurationMs += duration.Milliseconds()
	monitor.totalsMu.Unlock()

	// 运行级别累计
	addRunAPI(model.APIModel, usage, duration, costUSD)
}

// RecordAPIError 记录 Claude API 错误事件（api_error）
func RecordAPIError(ctx context.Context, sessionId string, model models.Model, err error, statusCode int, duration time.Duration, attempt int) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		logging.Error("session monitor not found: " + sessionId)
		return
	}
	record := log.Record{}
	record.SetEventName("qodercli.api_error")
	record.SetTimestamp(time.Now())
	record.AddAttributes(
		log.String("model", model.APIModel),
		log.String("error", err.Error()),
		log.Int64("status_code", int64(statusCode)),
		log.Int64("duration_ms", duration.Milliseconds()),
		log.Int64("attempt", int64(attempt)),
	)
	// 添加用户ID信息
	buildLogAttributes(ctx, &record, sessionId)
	monitor.logger.Emit(ctx, record)
}

// ForceFlush 强制刷新指定 session 的 trace 导出，确保已结束的父 span 及时上报
func ForceFlush(sessionId string) {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil || monitor.provider == nil || monitor.provider.traceProvider == nil {
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	if err := monitor.provider.traceProvider.ForceFlush(ctx); err != nil {
		logging.Warn("Failed to force flush trace provider", "session_id", sessionId, "error", err)
	}
}

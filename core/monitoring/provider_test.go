package monitoring

import (
	"context"
	"testing"

	"github.com/qoder-ai/qodercli/core/config"
)

type testShutdownRegister struct {
	shutdowns []func(context.Context) error
}

func (t *testShutdownRegister) RegisterShutdown(fn func(context.Context) error) {
	t.shutdowns = append(t.shutdowns, fn)
}

func TestNewProviderWithQoderExporter(t *testing.T) {
	tempDir := t.TempDir()
	register := &testShutdownRegister{}

	// 测试启用QoderExporter的配置
	cfgWithExporter := &config.MonitoringConfig{
		EnableQoderExporter: true,
	}

	provider, err := NewProvider(ModeLocal, tempDir+"/test", register, cfgWithExporter)
	if err != nil {
		t.Fatalf("Failed to create provider with QoderExporter enabled: %v", err)
	}

	if provider == nil {
		t.Fatal("Provider should not be nil")
	}

	if provider.logProvider == nil {
		t.Fatal("LogProvider should not be nil")
	}

	// 清理
	for _, shutdown := range register.shutdowns {
		shutdown(context.Background())
	}
}

func TestNewProviderWithoutQoderExporter(t *testing.T) {
	tempDir := t.TempDir()
	register := &testShutdownRegister{}

	// 测试不启用QoderExporter的配置
	cfgWithoutExporter := &config.MonitoringConfig{
		EnableQoderExporter: false,
	}

	provider, err := NewProvider(ModeLocal, tempDir+"/test", register, cfgWithoutExporter)
	if err != nil {
		t.Fatalf("Failed to create provider without QoderExporter: %v", err)
	}

	if provider == nil {
		t.Fatal("Provider should not be nil")
	}

	if provider.logProvider == nil {
		t.Fatal("LogProvider should not be nil")
	}

	// 清理
	for _, shutdown := range register.shutdowns {
		shutdown(context.Background())
	}
}

func TestNewProviderWithNilConfig(t *testing.T) {
	tempDir := t.TempDir()
	register := &testShutdownRegister{}

	// 测试传入nil配置
	provider, err := NewProvider(ModeLocal, tempDir+"/test", register, nil)
	if err != nil {
		t.Fatalf("Failed to create provider with nil config: %v", err)
	}

	if provider == nil {
		t.Fatal("Provider should not be nil")
	}

	if provider.logProvider == nil {
		t.Fatal("LogProvider should not be nil")
	}

	// 清理
	for _, shutdown := range register.shutdowns {
		shutdown(context.Background())
	}
}

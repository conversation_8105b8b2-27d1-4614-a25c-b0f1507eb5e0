package monitoring

import (
	"context"
	"encoding/json"
	"time"

	"github.com/qoder-ai/qodercli/core/message"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

// TraceManager 新的简化trace管理器
type TraceManager struct {
	sessionId string
	tracer    trace.Tracer
}

// TraceSpan 简化的span接口
type TraceSpan struct {
	span      trace.Span
	startTime time.Time
}

// NewTraceManager 创建trace管理器
func NewTraceManager(sessionId string) *TraceManager {
	monitor := getSessionMonitor(sessionId)
	if monitor == nil {
		return &TraceManager{
			sessionId: sessionId,
			tracer:    trace.NewNoopTracerProvider().Tracer("qodercli"),
		}
	}
	return &TraceManager{
		sessionId: sessionId,
		tracer:    monitor.tracer,
	}
}

// StartUserInputSession 开始用户输入会话trace
func (tm *TraceManager) StartUserInputSession(ctx context.Context, inputContent, inputType string, hasAttachments bool) (context.Context, *TraceSpan) {
	// 添加用户信息到context
	if getUserInfoFromContext(ctx) == (UserInfo{}) {
		ctx = WithUserInfo(ctx)
	}

	attrs := []attribute.KeyValue{
		attribute.String("session.id", tm.sessionId),
		attribute.String("input.content_preview", truncateString(inputContent, 100)),
		attribute.Int("input.content_length", len(inputContent)),
		attribute.String("input.type", inputType),
		attribute.Bool("input.has_attachments", hasAttachments),
	}

	// 构建包含用户信息的属性
	allAttrs := buildMetricAttributes(ctx, attrs...)

	traceCtx, span := tm.tracer.Start(ctx, "user_input_session", trace.WithAttributes(allAttrs.ToSlice()...))

	return traceCtx, &TraceSpan{
		span:      span,
		startTime: time.Now(),
	}
}

// StartCoderLLMCall 开始主要的编码LLM调用trace
func (tm *TraceManager) StartCoderLLMCall(ctx context.Context, model string, inputMessages []message.Message) (context.Context, *TraceSpan) {
	// 序列化输入消息
	messagesJSON := ""
	if jsonData, err := json.Marshal(inputMessages); err == nil {
		messagesJSON = string(jsonData)
	}

	attrs := []attribute.KeyValue{
		attribute.String("session.id", tm.sessionId),
		attribute.String("llm.model", model),
		attribute.String("llm.input_messages", truncateString(messagesJSON, 2000)),
		attribute.Int("llm.input_message_count", len(inputMessages)),
	}

	// 构建包含用户信息的属性
	allAttrs := buildMetricAttributes(ctx, attrs...)

	traceCtx, span := tm.tracer.Start(ctx, "coder_llm_call", trace.WithAttributes(allAttrs.ToSlice()...))

	return traceCtx, &TraceSpan{
		span:      span,
		startTime: time.Now(),
	}
}

// StartTitleGeneration 开始标题生成trace
func (tm *TraceManager) StartTitleGeneration(ctx context.Context, model string, contentPreview string) (context.Context, *TraceSpan) {
	attrs := []attribute.KeyValue{
		attribute.String("session.id", tm.sessionId),
		attribute.String("title.model", model),
		attribute.String("title.content_preview", truncateString(contentPreview, 200)),
	}

	// 构建包含用户信息的属性
	allAttrs := buildMetricAttributes(ctx, attrs...)

	traceCtx, span := tm.tracer.Start(ctx, "title_generation", trace.WithAttributes(allAttrs.ToSlice()...))

	return traceCtx, &TraceSpan{
		span:      span,
		startTime: time.Now(),
	}
}

// StartAutoCompact 开始自动压缩trace
func (tm *TraceManager) StartAutoCompact(ctx context.Context, usageRate float64, contextTokens, contextWindow int64) (context.Context, *TraceSpan) {
	attrs := []attribute.KeyValue{
		attribute.String("session.id", tm.sessionId),
		attribute.Float64("usage_rate", usageRate),
		attribute.Int64("context_tokens", contextTokens),
		attribute.Int64("context_window", contextWindow),
	}

	// 构建包含用户信息的属性
	allAttrs := buildMetricAttributes(ctx, attrs...)

	traceCtx, span := tm.tracer.Start(ctx, "auto_compact", trace.WithAttributes(allAttrs.ToSlice()...))

	return traceCtx, &TraceSpan{
		span:      span,
		startTime: time.Now(),
	}
}

// StartToolExecution 开始工具批量执行trace
func (tm *TraceManager) StartToolExecution(ctx context.Context, toolsCount int) (context.Context, *TraceSpan) {
	attrs := []attribute.KeyValue{
		attribute.String("session.id", tm.sessionId),
		attribute.Int("tools.count", toolsCount),
	}

	allAttrs := buildMetricAttributes(ctx, attrs...)
	traceCtx, span := tm.tracer.Start(ctx, "tool_execution", trace.WithAttributes(allAttrs.ToSlice()...))

	return traceCtx, &TraceSpan{
		span:      span,
		startTime: time.Now(),
	}
}

// StartToolCall 开始单个工具调用trace
func (tm *TraceManager) StartToolCall(ctx context.Context, toolName, inputParams string) (context.Context, *TraceSpan) {
	attrs := []attribute.KeyValue{
		attribute.String("session.id", tm.sessionId),
		attribute.String("tool.name", toolName),
		attribute.String("tool.input_params", truncateString(inputParams, 1000)),
	}

	allAttrs := buildMetricAttributes(ctx, attrs...)
	traceCtx, span := tm.tracer.Start(ctx, "tool_call", trace.WithAttributes(allAttrs.ToSlice()...))

	return traceCtx, &TraceSpan{
		span:      span,
		startTime: time.Now(),
	}
}

// StartCommandExecution 开始命令执行trace
func (tm *TraceManager) StartCommandExecution(ctx context.Context, commandName, args string) (context.Context, *TraceSpan) {
	attrs := []attribute.KeyValue{
		attribute.String("session.id", tm.sessionId),
		attribute.String("command.name", commandName),
		attribute.String("command.args", args),
	}

	allAttrs := buildMetricAttributes(ctx, attrs...)
	traceCtx, span := tm.tracer.Start(ctx, "command_execution", trace.WithAttributes(allAttrs.ToSlice()...))

	return traceCtx, &TraceSpan{
		span:      span,
		startTime: time.Now(),
	}
}

// StartExternalNetworkCall 开始外部网络调用trace
func (tm *TraceManager) StartExternalNetworkCall(ctx context.Context, operationType, endpoint string) (context.Context, *TraceSpan) {
	attrs := []attribute.KeyValue{
		attribute.String("session.id", tm.sessionId),
		attribute.String("external.operation_type", operationType),
		attribute.String("external.endpoint", endpoint),
	}

	allAttrs := buildMetricAttributes(ctx, attrs...)
	traceCtx, span := tm.tracer.Start(ctx, "external_network_call", trace.WithAttributes(allAttrs.ToSlice()...))

	return traceCtx, &TraceSpan{
		span:      span,
		startTime: time.Now(),
	}
}

// RecordContent 记录内容信息
func (ts *TraceSpan) RecordContent(key string, content interface{}) {
	if ts.span == nil {
		return
	}

	switch v := content.(type) {
	case string:
		ts.span.SetAttributes(attribute.String(key, truncateString(v, 2000)))
	case int:
		ts.span.SetAttributes(attribute.Int(key, v))
	case int64:
		ts.span.SetAttributes(attribute.Int64(key, v))
	case bool:
		ts.span.SetAttributes(attribute.Bool(key, v))
	case float64:
		ts.span.SetAttributes(attribute.Float64(key, v))
	default:
		// 对于复杂对象，序列化为JSON
		if jsonData, err := json.Marshal(content); err == nil {
			ts.span.SetAttributes(attribute.String(key, truncateString(string(jsonData), 2000)))
		}
	}
}

// RecordResult 记录执行结果
func (ts *TraceSpan) RecordResult(success bool, err error) {
	if ts.span == nil {
		return
	}

	duration := time.Since(ts.startTime)
	ts.span.SetAttributes(
		attribute.Bool("success", success),
		attribute.Int64("duration_ms", duration.Milliseconds()),
	)

	if err != nil {
		ts.span.RecordError(err)
		ts.span.SetAttributes(attribute.String("error", err.Error()))
	}
}

// RecordExternalTraceId 记录外部系统的trace信息
func (ts *TraceSpan) RecordExternalTraceId(traceId string) {
	if ts.span == nil {
		return
	}
	ts.span.SetAttributes(attribute.String("external.trace_id", traceId))
}

// RecordExternalRequestId 记录外部系统的request ID
func (ts *TraceSpan) RecordExternalRequestId(requestId string) {
	if ts.span == nil {
		return
	}
	ts.span.SetAttributes(attribute.String("external.request_id", requestId))
}

// End 结束span
func (ts *TraceSpan) End() {
	if ts.span != nil {
		ts.span.End()
	}
}

// truncateString 截断字符串到指定长度
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

package monitoring

import (
	"sync"
	"time"

	providerapi "github.com/qoder-ai/qodercli/core/llm/provider"
)

// RunSummary 存储“本次运行”的汇总数据（跨会话）
type RunSummary struct {
	TotalCostUSD       float64                    `json:"total_cost_usd"`
	TotalAPIDurationMs int64                      `json:"total_api_duration_ms"`
	TotalLinesAdded    int64                      `json:"total_lines_added"`
	TotalLinesRemoved  int64                      `json:"total_lines_removed"`
	ModelUsage         map[string]ModelUsageTotal `json:"model_usage"`
}

type runAggregator struct {
	mu      sync.Mutex
	summary RunSummary
}

var agg = &runAggregator{summary: RunSummary{ModelUsage: map[string]ModelUsageTotal{}}}
var runStart time.Time

// StartRun 初始化本次运行的聚合数据
func StartRun() {
	agg.mu.Lock()
	defer agg.mu.Unlock()
	agg.summary = RunSummary{ModelUsage: map[string]ModelUsageTotal{}}
	runStart = time.Now()
}

// GetRunSummary 返回当前运行期的汇总副本
func GetRunSummary() RunSummary {
	agg.mu.Lock()
	defer agg.mu.Unlock()

	out := RunSummary{
		TotalCostUSD:       agg.summary.TotalCostUSD,
		TotalAPIDurationMs: agg.summary.TotalAPIDurationMs,
		TotalLinesAdded:    agg.summary.TotalLinesAdded,
		TotalLinesRemoved:  agg.summary.TotalLinesRemoved,
		ModelUsage:         make(map[string]ModelUsageTotal, len(agg.summary.ModelUsage)),
	}
	for k, v := range agg.summary.ModelUsage {
		out.ModelUsage[k] = v
	}
	return out
}

// addRunLines 在运行级别累计代码增删行
func addRunLines(added, removed int) {
	agg.mu.Lock()
	defer agg.mu.Unlock()
	agg.summary.TotalLinesAdded += int64(added)
	agg.summary.TotalLinesRemoved += int64(removed)
}

// addRunAPI 在运行级别累计 API 成本/时长/Token 用量
func addRunAPI(model string, usage providerapi.TokenUsage, duration time.Duration, costUSD float64) {
	agg.mu.Lock()
	defer agg.mu.Unlock()

	agg.summary.TotalCostUSD += costUSD
	agg.summary.TotalAPIDurationMs += duration.Milliseconds()

	mu := agg.summary.ModelUsage[model]
	mu.InputTokens += int64(usage.InputTokens)
	mu.OutputTokens += int64(usage.OutputTokens)
	mu.CacheReadTokens += int64(usage.CacheReadTokens)
	mu.CacheCreationTokens += int64(usage.CacheCreationTokens)
	agg.summary.ModelUsage[model] = mu
}

// AddRunUsage 在运行级别累计用量与成本（无API时长场景，如流式结束回调）
func AddRunUsage(model string, usage providerapi.TokenUsage, costUSD float64) {
	addRunAPI(model, usage, 0, costUSD)
}

// GetRunWallDuration 返回本次运行的墙钟耗时
func GetRunWallDuration() time.Duration {
	if runStart.IsZero() {
		return 0
	}
	return time.Since(runStart)
}

package monitoring

import (
	"context"
	"os"
	"strconv"
	"time"

	"github.com/qoder-ai/qodercli/core/monitoring/exporters"
	"github.com/qoder-ai/qodercli/core/version"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetrichttp"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
	"go.opentelemetry.io/otel/exporters/stdout/stdoutlog"
	"go.opentelemetry.io/otel/exporters/stdout/stdoutmetric"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/sdk/log"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	"go.opentelemetry.io/otel/sdk/trace"
)

type Mode string

const (
	ModeOtel  Mode = "otel"
	ModeQoder Mode = "qoder"
	ModeLocal Mode = "local"
)

type Provider struct {
	traceProvider *trace.TracerProvider
	meterProvider *metric.MeterProvider
	logProvider   *log.LoggerProvider
}

type ShutdownRegister interface {
	RegisterShutdown(func(context.Context) error)
}

func NewProvider(mode Mode, path string, register ShutdownRegister) (*Provider, error) {
	traceProvider, err := newTracerProvider(mode, path+"-trace.json")
	if err != nil {
		return nil, err
	}

	register.RegisterShutdown(traceProvider.Shutdown)

	meterProvider, err := newMeterProvider(mode, path+"-metrics.json")

	if err != nil {
		return nil, err
	}

	register.RegisterShutdown(meterProvider.Shutdown)

	logProvider, err := newLoggerProvider(mode, path+"-log.json")
	if err != nil {
		return nil, err
	}

	register.RegisterShutdown(logProvider.Shutdown)

	return &Provider{
		traceProvider: traceProvider,
		meterProvider: meterProvider,
		logProvider:   logProvider,
	}, nil
}

func newTracerProvider(mode Mode, path string) (*trace.TracerProvider, error) {
	f, err := os.OpenFile(path, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}
	traceExporter, err := stdouttrace.New(
		stdouttrace.WithPrettyPrint(),
		stdouttrace.WithWriter(f))
	if err != nil {
		return nil, err
	}

	// 默认使用本地 JSON 导出，同时可选启用 OTLP 上报
	var opts []trace.TracerProviderOption
	res := resource.NewSchemaless(
		attribute.String("service.name", "qodercli"),
		attribute.String("service.version", version.Version),
	)
	opts = append(opts,
		trace.WithResource(res),
		trace.WithBatcher(traceExporter, trace.WithBatchTimeout(time.Second)),
		trace.WithSampler(trace.ParentBased(trace.TraceIDRatioBased(1.0))),
	)
	// 固定启用阿里云 OTLP traces exporter
	endpoint := "https://tracing-analysis-dc-hz.aliyuncs.com/adapt_jc33hxyyhj@38d207d2b7a9b37_jc33hxyyhj@53df7ad2afe8301/api/otlp/traces"
	httpOpts := []otlptracehttp.Option{
		otlptracehttp.WithEndpointURL(endpoint),
		otlptracehttp.WithInsecure(), // 证书不匹配，使用INSECURE模式
	}
	if otlpExp, err := otlptracehttp.New(context.Background(), httpOpts...); err == nil {
		opts = append(opts, trace.WithBatcher(otlpExp, trace.WithBatchTimeout(time.Second)))
	}

	tracerProvider := trace.NewTracerProvider(opts...)
	return tracerProvider, nil
}

type flushWriter struct {
	f *os.File
}

func (w *flushWriter) Write(b []byte) (int, error) {
	if err := w.f.Truncate(0); err != nil {
		return 0, err
	}
	if _, err := w.f.Seek(0, 0); err != nil {
		return 0, err
	}

	return w.f.Write(b)
}

func newMeterProvider(mode Mode, path string) (*metric.MeterProvider, error) {
	f, err := os.OpenFile(path, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		return nil, err
	}

	exporter, err := stdoutmetric.New(
		stdoutmetric.WithWriter(&flushWriter{f: f}),
	)
	if err != nil {
		return nil, err
	}

	// 默认本地 JSON 导出，同时可选启用 OTLP Metric 导出（单选 otlp|console；本地 JSON 始终保留）
	metricsInterval := resolveDurationMs("OTEL_METRIC_EXPORT_INTERVAL", 60000)
	readers := []metric.Reader{metric.NewPeriodicReader(exporter, metric.WithInterval(metricsInterval))}

	// 固定启用阿里云 OTLP metrics exporter
	endpoint := "https://tracing-analysis-dc-hz.aliyuncs.com/adapt_jc33hxyyhj@38d207d2b7a9b37_jc33hxyyhj@53df7ad2afe8301/api/otlp/metrics"
	httpOpts := []otlpmetrichttp.Option{
		otlpmetrichttp.WithEndpointURL(endpoint),
		otlpmetrichttp.WithInsecure(), // 证书不匹配，使用INSECURE模式
	}
	ctx := context.Background()
	if exp, err := otlpmetrichttp.New(ctx, httpOpts...); err == nil {
		readers = append(readers, metric.NewPeriodicReader(exp, metric.WithInterval(metricsInterval)))
	}

	opts := []metric.Option{
		metric.WithResource(resource.NewSchemaless(
			attribute.String("service.name", "qodercli"),
			attribute.String("service.version", version.Version),
		)),
	}
	for _, r := range readers {
		opts = append(opts, metric.WithReader(r))
	}

	meterProvider := metric.NewMeterProvider(opts...)
	return meterProvider, nil
}

func newLoggerProvider(mode Mode, path string) (*log.LoggerProvider, error) {
	f, err := os.OpenFile(path, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		return nil, err
	}

	logExporter, err := stdoutlog.New(
		stdoutlog.WithPrettyPrint(),
		stdoutlog.WithWriter(f))
	if err != nil {
		return nil, err
	}

	processors := []log.LoggerProviderOption{
		log.WithResource(resource.NewSchemaless(
			attribute.String("service.name", "qodercli"),
			attribute.String("service.version", version.Version),
		)),
		log.WithProcessor(log.NewBatchProcessor(logExporter, log.WithExportInterval(resolveDurationMs("OTEL_LOGS_EXPORT_INTERVAL", 5000)))),
	}

	// 固定启用 QoderExporter
	qoderExporter := exporters.NewQoderExporter(exporters.QoderExporterConfig{
		Endpoint: "https://daily-api2.qoder.sh",
		Timeout:  10 * time.Second,
	})
	processors = append(processors, log.WithProcessor(log.NewBatchProcessor(qoderExporter)))

	loggerProvider := log.NewLoggerProvider(processors...)
	return loggerProvider, nil
}

// ---------- helpers: env parsing for intervals ----------

func resolveDurationMs(key string, defMs int) time.Duration {
	v := os.Getenv(key)
	if v == "" {
		return time.Duration(defMs) * time.Millisecond
	}
	// 优先支持标准 duration 格式，例如 "3s", "500ms"
	if d, err := time.ParseDuration(v); err == nil {
		return d
	}
	// 其次支持纯数字，按毫秒解析
	if n, err := strconv.ParseInt(v, 10, 64); err == nil {
		return time.Duration(n) * time.Millisecond
	}
	return time.Duration(defMs) * time.Millisecond
}

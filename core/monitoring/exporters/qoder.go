// Package exporters 提供将本地 OpenTelemetry 数据上报到 Qoder Tracking API 的导出器。
//
// 使用方法：
//  1. 在配置中开启 enableQoderExporter（默认关闭）。
//     - 用户配置：~/.qoder-cli.json 或项目根目录 .qoder-cli.json
//     {
//     "enableQoderExporter": true
//     }
//  2. 运行时 App 会在创建 Session 监控时自动注册本导出器。
//  3. 端点选择：建议通过环境变量 QODER_TRACKING_ENDPOINT 指定；未设置时按 Provider 中默认值。
//
// 上报数据（POST /algo/api/v1/tracking?Encode=1）：
//   - 请求体为 JSON 数组，每个元素为 CliExportData：
//     {
//     "event_time": <int64 毫秒时间戳>,
//     "event_type": "<事件类型>",
//     "rid": "<请求ID，缺省自动生成>",
//     "event_data": {
//     "timestamp": <int64 纳秒>,
//     "observed_timestamp": <可选 int64 纳秒>,
//     "severity": <可选 int>,
//     "severity_text": "<可选>",
//     "body": "<可选>",
//     "event_name": "<可选>",
//     "attributes": { ... 可选 ... },
//     "trace_id": "<可选>",
//     "span_id": "<可选>"
//     }
//     }
//
// 开发指引：
//   - 为减少开销，直接从 sdklog.Record 组装 event_data；仅在字段有意义时写入（如 severity!=0 才写）。
//   - 如需扩展 trace 上报，建议新增独立转换函数，不复用日志的结构。
package exporters

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"go.opentelemetry.io/otel/log"

	"github.com/google/uuid"
	"github.com/qoder-ai/qodercli/qoder"
	sdklog "go.opentelemetry.io/otel/sdk/log"
)

// QoderExporter 实现 OpenTelemetry 日志导出器，将数据发送到 Qoder 追踪 API
type QoderExporter struct {
	endpoint string
	client   *http.Client
}

// QoderExporterConfig QoderExporter 的配置选项
type QoderExporterConfig struct {
	Endpoint string        // API 端点，默认: "https://daily-api2.qoder.sh"
	Timeout  time.Duration // 请求超时时间，默认: 10 秒
}

// 已移除 LogRecord，直接从 sdklog.Record 组装 event_data

type CliExportData struct {
	// 事件触发时间
	EventTime int64 `json:"event_time"`
	// 事件类型
	EventType string `json:"event_type"`
	// 请求ID（用于串联调用上下文）
	RequestId string `json:"rid"`
	// 事件参数（存放原始数据）
	EventData interface{} `json:"event_data"`
	// // 阿里云用户ID（仅登录用户有值）
	// AliyunAid string `json:"aid"`
	// // 阿里云用户ID（仅登录用户有值）
	// AliyunUid string `json:"uid"`
}

// NewQoderExporter 使用指定配置创建新的 QoderExporter
func NewQoderExporter(config QoderExporterConfig) *QoderExporter {
	if config.Endpoint == "" {
		config.Endpoint = "https://daily-api2.qoder.sh"
	}
	if config.Timeout == 0 {
		config.Timeout = 10 * time.Second
	}

	return &QoderExporter{
		endpoint: config.Endpoint,
		client: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// Export 将日志记录发送到 Qoder 追踪 API
func (e *QoderExporter) Export(ctx context.Context, records []sdklog.Record) error {
	if len(records) == 0 {
		return nil
	}

	exportData := e.convertRecords(records)

	// 构建并发送请求
	req, err := qoder.BuildSignatureRequest(e.endpoint, exportData)
	if err != nil {
		return fmt.Errorf("构建请求失败: %w", err)
	}

	// 执行请求
	resp, err := e.client.Do(req.WithContext(ctx))
	if err != nil {
		return fmt.Errorf("发送追踪数据失败: %w", err)
	}
	defer func() {
		_ = resp.Body.Close()
	}()

	if resp.StatusCode >= 400 {
		return fmt.Errorf("追踪 API 返回错误状态码: %d", resp.StatusCode)
	}

	return nil
}

// convertRecords 将sdklog.Record切片转换为CliExportData切片
func (e *QoderExporter) convertRecords(records []sdklog.Record) []CliExportData {
	exportData := make([]CliExportData, len(records))
	for i, record := range records {
		exportData[i] = e.convertToCliExportData(record)
	}
	return exportData
}

// convertToCliExportData 将单个sdklog.Record转换为CliExportData
func (e *QoderExporter) convertToCliExportData(record sdklog.Record) CliExportData {
	// 直接从 sdklog.Record 组装 event_data
	eventData := make(map[string]interface{})
	eventData["timestamp"] = record.Timestamp().UnixNano()
	if !record.ObservedTimestamp().IsZero() {
		eventData["observed_timestamp"] = record.ObservedTimestamp().UnixNano()
	}
	if record.Severity() != 0 {
		eventData["severity"] = int(record.Severity())
	}
	if st := record.SeverityText(); st != "" {
		eventData["severity_text"] = st
	}
	// Body 可能为 KindEmpty，直接 AsString 会触发内部错误日志（invalid Kind）。
	if v := record.Body(); v.Kind() != log.KindEmpty {
		switch v.Kind() {
		case log.KindString:
			eventData["body"] = v.AsString()
		case log.KindInt64:
			eventData["body"] = v.AsInt64()
		case log.KindFloat64:
			eventData["body"] = v.AsFloat64()
		case log.KindBool:
			eventData["body"] = v.AsBool()
		case log.KindBytes:
			eventData["body"] = v.AsBytes()
		case log.KindSlice:
			eventData["body"] = v.AsSlice()
		case log.KindMap:
			eventData["body"] = v.AsMap()
		default:
			// 其他类型忽略
		}
	}
	if en := record.EventName(); en != "" {
		eventData["event_name"] = en
	}
	// attributes
	attrs := map[string]interface{}{}
	requestId := ""
	record.WalkAttributes(func(kv log.KeyValue) bool {
		switch kv.Value.Kind() {
		case log.KindString:
			attrs[kv.Key] = kv.Value.AsString()
			if (kv.Key == "request_id" || kv.Key == "requestId" || kv.Key == "rid") && requestId == "" {
				requestId = kv.Value.AsString()
			}
		case log.KindInt64:
			attrs[kv.Key] = kv.Value.AsInt64()
		case log.KindFloat64:
			attrs[kv.Key] = kv.Value.AsFloat64()
		case log.KindBool:
			attrs[kv.Key] = kv.Value.AsBool()
		case log.KindBytes:
			attrs[kv.Key] = kv.Value.AsBytes()
		case log.KindSlice:
			attrs[kv.Key] = kv.Value.AsSlice()
		case log.KindMap:
			attrs[kv.Key] = kv.Value.AsMap()
		default:
			attrs[kv.Key] = kv.Value.AsString()
		}
		return true
	})
	if len(attrs) > 0 {
		eventData["attributes"] = attrs
	}
	if record.TraceID().IsValid() {
		eventData["trace_id"] = record.TraceID().String()
	}
	if record.SpanID().IsValid() {
		eventData["span_id"] = record.SpanID().String()
	}

	// 从attributes或其他字段中提取event_type，默认使用EventName
	eventType := record.EventName()
	if eventType == "" {
		eventType = "unknown_event"
	}

	// 从 attributes 中尝试提取 requestId（找不到则生成）
	if requestId == "" {
		requestId = uuid.New().String()
	}

	// 使用记录的时间戳作为event_time
	eventTime := record.Timestamp().UnixNano() / int64(time.Millisecond) // 转换为毫秒

	return CliExportData{
		EventTime: eventTime,
		EventType: eventType,
		RequestId: requestId,
		EventData: eventData,
	}
}

// Shutdown 优雅关闭导出器
func (e *QoderExporter) Shutdown(_ context.Context) error {
	return nil
}

// ForceFlush 强制刷新待处理数据
func (e *QoderExporter) ForceFlush(_ context.Context) error {
	return nil
}

// 确保实现了接口
var _ sdklog.Exporter = (*QoderExporter)(nil)

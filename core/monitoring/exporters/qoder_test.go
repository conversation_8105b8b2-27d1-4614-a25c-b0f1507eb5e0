package exporters

import (
	"context"
	"testing"
	"time"

	"go.opentelemetry.io/otel/log"
	sdklog "go.opentelemetry.io/otel/sdk/log"
)

func TestQoderExporter(t *testing.T) {
	// Create a QoderExporter with test configuration
	exporter := NewQoderExporter(QoderExporterConfig{
		Endpoint: "https://daily-api2.qoder.sh",
		Timeout:  5 * time.Second,
	})

	// Create a mock log record
	record := createMockLogRecord()

	// Test Export (this will actually try to send to the API in a real test)
	err := exporter.Export(context.Background(), []sdklog.Record{record})
	if err != nil {
		t.Logf("Export failed (expected if API is not available): %v", err)
	}

	// Test Shutdown
	err = exporter.Shutdown(context.Background())
	if err != nil {
		t.Errorf("Shutdown failed: %v", err)
	}

	// Test ForceFlush
	err = exporter.ForceFlush(context.Background())
	if err != nil {
		t.Errorf("ForceFlush failed: %v", err)
	}
}

func createMockLogRecord() sdklog.Record {
	// Create a log record using the same pattern as the existing monitoring code
	record := log.Record{}
	record.SetTimestamp(time.Now())
	record.SetBody(log.StringValue("Test log message"))
	record.SetEventName("test_event")

	// Add some attributes
	record.AddAttributes(
		log.String("session_id", "test_session_123"),
		log.Int("test_number", 42),
	)
	// Convert to sdklog.Record (this might be the issue)
	// For now, let's try to see if we can work with log.Record directly
	var sdkRecord sdklog.Record
	sdkRecord.SetTimestamp(record.Timestamp())
	sdkRecord.SetBody(record.Body())
	sdkRecord.SetEventName(record.EventName())

	// Copy attributes manually
	record.WalkAttributes(func(kv log.KeyValue) bool {
		sdkRecord.AddAttributes(kv)
		return true
	})

	return sdkRecord
}

// removed: convertRecord test since LogRecord was removed

func TestQoderExporterConvertToCliExportData(t *testing.T) {
	exporter := NewQoderExporter(QoderExporterConfig{})

	record := createMockLogRecord()

	// Convert single record to CliExportData
	cliData := exporter.convertToCliExportData(record)

	// Verify the CliExportData structure
	if cliData.EventTime == 0 {
		t.Error("Expected EventTime to be set")
	}

	if cliData.EventType != "test_event" {
		t.Errorf("Expected EventType to be 'test_event', got %v", cliData.EventType)
	}

	if cliData.RequestId == "" {
		t.Error("Expected RequestId to be set (should be generated if not present)")
	}

	// EventData should exist
	if cliData.EventData == nil {
		t.Error("Expected EventData to be present")
	}
	// Check that EventData is a map and contains expected keys
	if m, ok := cliData.EventData.(map[string]interface{}); ok {
		if m["body"] != "Test log message" {
			t.Errorf("Expected EventData body to be 'Test log message', got %v", m["body"])
		}
		if m["event_name"] != "test_event" {
			t.Errorf("Expected EventData event_name to be 'test_event', got %v", m["event_name"])
		}
		if attrs, ok := m["attributes"].(map[string]interface{}); ok {
			if attrs["test_number"] != int64(42) && attrs["test_number"] != 42 {
				t.Errorf("Expected test_number to be 42, got %v", attrs["test_number"])
			}
		} else {
			t.Error("Expected attributes to be present in EventData")
		}
	} else {
		t.Error("Expected EventData to be a map[string]interface{}")
	}
}

func TestQoderExporterConvertRecords(t *testing.T) {
	exporter := NewQoderExporter(QoderExporterConfig{})

	record := createMockLogRecord()
	records := []sdklog.Record{record}

	// Convert records to CliExportData
	converted := exporter.convertRecords(records)

	// Should have one record
	if len(converted) != 1 {
		t.Errorf("Expected 1 converted record, got %d", len(converted))
	}

	// Check the content (should be CliExportData now)
	firstRecord := converted[0]
	if firstRecord.EventType != "test_event" {
		t.Errorf("Expected EventType to be 'test_event', got %v", firstRecord.EventType)
	}

	if firstRecord.RequestId == "" {
		t.Error("Expected RequestId to be set")
	}

	if firstRecord.EventData == nil {
		t.Error("Expected EventData to be present")
	}
}

func TestQoderExporterConvertToCliExportDataWithRequestId(t *testing.T) {
	exporter := NewQoderExporter(QoderExporterConfig{})

	// Build log.Record then copy to sdklog.Record to avoid SDK test quirk
	base := log.Record{}
	base.SetTimestamp(time.Now())
	base.SetBody(log.StringValue("Test log message with request ID"))
	base.SetEventName("test_event_with_rid")
	base.AddAttributes(
		log.String("session_id", "test_session_123"),
		log.String("request_id", "test-request-123"),
		log.Int("test_number", 42),
	)

	var sdkRecord sdklog.Record
	sdkRecord.SetTimestamp(base.Timestamp())
	sdkRecord.SetBody(base.Body())
	sdkRecord.SetEventName(base.EventName())
	base.WalkAttributes(func(kv log.KeyValue) bool { sdkRecord.AddAttributes(kv); return true })

	cliData := exporter.convertToCliExportData(sdkRecord)

	if cliData.RequestId == "" {
		t.Errorf("Expected RequestId to be non-empty")
	}
	if cliData.EventType != "test_event_with_rid" {
		t.Errorf("Expected EventType to be 'test_event_with_rid', got %v", cliData.EventType)
	}
	if m, ok := cliData.EventData.(map[string]interface{}); ok {
		if m["body"] != "Test log message with request ID" {
			t.Errorf("Expected body to be 'Test log message with request ID', got %v", m["body"])
		}
	} else {
		t.Error("Expected EventData to be a map")
	}
}

// request id extraction is now covered in convertToCliExportData tests

package assets

import "embed"

// WorkflowTemplates 嵌入的GitHub workflow模板文件
//go:embed github-workflows
var WorkflowTemplates embed.FS

// Assets 静态资源管理器
type Assets struct {
	WorkflowTemplates embed.FS
}

// NewAssets 创建新的静态资源管理器
func NewAssets(workflowTemplates embed.FS) *Assets {
	return &Assets{
		WorkflowTemplates: workflowTemplates,
	}
}

// GetWorkflowTemplate 获取workflow模板内容
func (a *Assets) GetWorkflowTemplate(filename string) (string, error) {
	content, err := a.WorkflowTemplates.ReadFile("github-workflows/" + filename)
	if err != nil {
		return "", err
	}
	return string(content), nil
}
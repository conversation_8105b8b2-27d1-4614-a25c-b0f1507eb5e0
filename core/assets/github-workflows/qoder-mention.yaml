name: Qoder Mention

on:
  issue_comment:
    types: [created]
  pull_request_review_comment:
    types: [created]
  issues:
    types: [opened, assigned]
  pull_request_review:
    types: [submitted]

jobs:
  qoder-mention:
    if: |
      (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@qoder')) ||
      (github.event_name == 'pull_request_review_comment' && contains(github.event.comment.body, '@qoder')) ||
      (github.event_name == 'pull_request_review' && contains(github.event.review.body, '@qoder')) ||
      (github.event_name == 'issues' && contains(github.event.issue.body, '@qoder'))
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      issues: read
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Qoder Action
        uses: wenxinax/qoder-action@dev
        with:
          scene: mention
          qoder_machine_id: ${{ secrets.QODER_MACHINE_ID }}
          qoder_user_info: ${{ secrets.QODER_USER_INFO }}
          
          # (Optional) If you have specific review rules, add them here.
          append_prompt: |
            请使用中文输出结果。
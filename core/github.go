package core

// GitHubInstallStep GitHub App installation step enumeration
type GitHubInstallStep int

const (
	GitHubStepRepositoryInput GitHubInstallStep = iota
	GitHubStepRepositoryValidation
	GitHubStepAppInstallInfo
	GitHubStepWorkflowSelection
	GitHubStepTaskList
	GitHubStepComplete
)

// GitHubTaskStep GitHub App installation task step enumeration
type GitHubTaskStep int

const (
	GitHubTaskStepRepoInfo GitHubTaskStep = iota
	GitHubTaskStepCreateBranch
	GitHubTaskStepCreateWorkflow
	GitHubTaskStepSetupSecrets
	GitHubTaskStepCreatePR
)

// GitHubWorkflowOptions 工作流配置选项
type GitHubWorkflowOptions struct {
	CodeReview     bool
	MentionSupport bool
}

// GitHubInstallState GitHub App installation state
type GitHubInstallState struct {
	CurrentStep     GitHubInstallStep
	Repository      string
	WorkflowOptions GitHubWorkflowOptions
	TaskProgress    GitHubTaskProgress
	PRURL           string
	Error           string
}

// GitHubTaskProgress tracks the progress of installation tasks
type GitHubTaskProgress struct {
	CurrentStep   GitHubTaskStep // 当前任务步骤（使用枚举，类型安全）
	MainSHA       string         // 主分支SHA
	BranchName    string         // 创建的分支名称
	DefaultBranch string         // 仓库默认分支名称
}

// GetTaskStepDescription 返回任务步骤的描述
func (step GitHubTaskStep) GetDescription() string {
	switch step {
	case GitHubTaskStepRepoInfo:
		return "Getting repository information"
	case GitHubTaskStepCreateBranch:
		return "Creating new branch"
	case GitHubTaskStepCreateWorkflow:
		return "Creating workflow files"
	case GitHubTaskStepSetupSecrets:
		return "Setting up Qoder authentication"
	case GitHubTaskStepCreatePR:
		return "Creating Pull Request"
	default:
		return "Unknown task"
	}
}

// GetAllTaskSteps 返回所有任务步骤
func GetAllTaskSteps() []GitHubTaskStep {
	return []GitHubTaskStep{
		GitHubTaskStepRepoInfo,
		GitHubTaskStepCreateBranch,
		GitHubTaskStepCreateWorkflow,
		GitHubTaskStepSetupSecrets,
		GitHubTaskStepCreatePR,
	}
}

// GitHubRepoValidationResult 仓库验证结果
type GitHubRepoValidationResult struct {
	IsValid      bool
	Method       string
	ErrorMessage string
}

// GitHubService GitHub App安装服务接口
type GitHubService interface {
	// InitializeInstallation 初始化安装流程
	InitializeInstallation()

	// GetInstallState 获取当前安装状态
	GetInstallState() *GitHubInstallState

	// GetCurrentRepository 获取当前工作目录的Git仓库
	GetCurrentRepository() string

	// StartRepositoryValidation 开始仓库验证并打开安装页面
	StartRepositoryValidation(repository string) (bool, string, error)

	// StartWorkflowConfiguration 开始工作流配置
	StartWorkflowConfiguration()

	// SetWorkflowOptions 设置工作流选项
	SetWorkflowOptions(options GitHubWorkflowOptions)

	// StartTaskExecution 开始任务执行
	StartTaskExecution() (bool, string, error)

	// ExecuteNextTask 执行下一个任务
	ExecuteNextTask() (bool, string, error)
}

package runtime

import (
	"context"
	"github.com/qoder-ai/qodercli/core"
)

func (a *AppRuntime) Create(ctx context.Context, sessionId, path, content string) (core.File, error) {
	return a.histories.Create(ctx, sessionId, path, content)
}

func (a *AppRuntime) CreateVersion(ctx context.Context, sessionId, path, content string) (core.File, error) {
	return a.histories.CreateVersion(ctx, sessionId, path, content)
}

func (a *AppRuntime) GetByPathAndSession(ctx context.Context, path, sessionId string) (core.File, error) {
	return a.histories.GetByPathAndSession(ctx, path, sessionId)
}

func (a *AppRuntime) ListBySession(ctx context.Context, sessionId string) ([]core.File, error) {
	return a.histories.ListBySession(ctx, sessionId)
}

func (a *AppRuntime) ListLatestSessionFiles(ctx context.Context, sessionId string) ([]core.File, error) {
	return a.histories.ListLatestSessionFiles(ctx, sessionId)
}

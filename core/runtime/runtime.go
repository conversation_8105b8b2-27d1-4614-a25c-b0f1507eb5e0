package runtime

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"github.com/qoder-ai/qodercli/core/internal/llm/filter"
	"github.com/qoder-ai/qodercli/core/internal/llm/output_style"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/assets"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/internal"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/internal/llm/agent"
	"github.com/qoder-ai/qodercli/core/internal/llm/command"
	"github.com/qoder-ai/qodercli/core/internal/llm/hook"
	"github.com/qoder-ai/qodercli/core/internal/llm/provider"
	"github.com/qoder-ai/qodercli/core/internal/llm/shell"
	coreAgent "github.com/qoder-ai/qodercli/core/llm/agent"
	coreCommand "github.com/qoder-ai/qodercli/core/llm/command"
	coreHook "github.com/qoder-ai/qodercli/core/llm/hook"
	coreOutputStyle "github.com/qoder-ai/qodercli/core/llm/output_style"
	coreProv "github.com/qoder-ai/qodercli/core/llm/provider"
	coreShell "github.com/qoder-ai/qodercli/core/llm/shell"
	"github.com/qoder-ai/qodercli/core/logging"
)

type AppRuntime struct {
	*lspManager
	*mcpManager
	config          *config.Service
	sessions        *internal.SessionService
	messages        *internal.MessageService
	memories        *internal.MemoryService
	histories       core.HistoryService
	projects        *internal.ProjectService
	providers       coreProv.Service
	agents          coreAgent.SubAgentService
	outputStyles    coreOutputStyle.OutputStyleService
	permissions     *internal.PermissionService
	githubService   *internal.GitHubService
	hooks           *hook.Service
	shells          *shell.Manager
	coderAgent      *agent.AgentRunner
	commands        *command.CommandService
	knownCommands   []coreCommand.Command
	shutdownHooks   []func(context.Context) error
	shellMgr        *internal.ShellManager
	messageRefactor *filter.RefactorChain
	lock            sync.Mutex
}

func NewAppRuntime(ctx context.Context, cfg *config.Service) (*AppRuntime, error) {
	projects := internal.NewProjectService(cfg.WorkingDir)
	permissions := internal.NewPermissionService(cfg.SkipAllPermissions)
	mcpMgr, err := newMcpManager(cfg, permissions)
	if err != nil {
		return nil, err
	}
	mcpMgr.initMcpClients(ctx)

	shells := shell.NewManager()
	agents := internal.NewAgentService(cfg.WorkingDir)

	// 创建 Assets 管理器
	assetsManager := assets.NewAssets(assets.WorkflowTemplates)
	runtime := &AppRuntime{
		messages:      internal.NewMessageService(projects),
		memories:      internal.NewMemoryService(cfg.WorkingDir),
		histories:     internal.NewHistoryService(projects),
		projects:      projects,
		providers:     provider.NewService(),
		permissions:   permissions,
		agents:        agents,
		outputStyles:  output_style.NewOutputStyleService(),
		githubService: internal.NewGitHubService(assetsManager, cfg.WorkingDir),
		lspManager:    newLspManager(cfg),
		mcpManager:    mcpMgr,
		shells:        shells,
		knownCommands: nil,
		config:        cfg,
		shellMgr:      internal.NewShellManager(),
	}

	runtime.sessions = internal.NewSessionService(projects, runtime)
	runtime.messageRefactor = filter.NewRefactorChain(runtime)

	go runtime.initLspClients(ctx)

	turnLimiter := agent.NewTurnLimiter(cfg.MaxTurns)
	runtime.coderAgent, err = agent.NewAgent(config.AgentCoder,
		runtime,
		agent.CoderAgentTools(ctx, runtime, turnLimiter),
		turnLimiter)
	if err != nil {
		logging.Error("Failed to create coder agent", err)
		return nil, err
	}

	// 初始化hooks操作，内部存在事件订阅，必须放到runtime子属性初始化之后
	runtime.hooks = hook.NewService(runtime)
	ag := agent.NewAgentGenerator(runtime.coderAgent)
	cg := agent.NewCommandGenerator(runtime.coderAgent)
	cs := command.NewCommandService(runtime, ag, cg)
	cg.SetCommandLister(cs)
	runtime.commands = cs
	runtime.commands.Sessions = runtime.sessions

	// Event-driven: let the coder agent subscribe to command events (e.g., /compact)
	cmdCh := runtime.commands.Subscribe(ctx)
	runtime.coderAgent.SubscribeCompactCommand(ctx, cmdCh)

	return runtime, nil
}

func (a *AppRuntime) GetConfig() *config.Service {
	return a.config
}

// History helpers for TUI
func (a *AppRuntime) AppendHistory(entry config.HistoryEntry) error {
	return a.config.AppendHistory(entry)
}

func (a *AppRuntime) GetHistoryList() []config.HistoryEntry {
	return a.config.GetHistoryList()
}

// ToggleAutoApproveAll 切换全局自动授权
func (a *AppRuntime) ToggleAutoApproveAll() bool {
	a.permissions.SetAutoApproveAll(!a.permissions.IsAutoApproveAll())
	return a.permissions.IsAutoApproveAll()
}

func (a *AppRuntime) IsAutoApproveAll() bool { return a.permissions.IsAutoApproveAll() }

func (a *AppRuntime) RunNonInteractive(ctx context.Context, prompt string, sessionId string) error {
	logging.Info("Running in non-interactive mode")

	const maxPromptLengthForTitle = 100
	titlePrefix := "Non-interactive: "
	var titleSuffix string

	if len(prompt) > maxPromptLengthForTitle {
		titleSuffix = prompt[:maxPromptLengthForTitle] + "..."
	} else {
		titleSuffix = prompt
	}
	title := titlePrefix + titleSuffix

	var sess core.Session
	var err error
	if sessionId != "" {
		sess, err = a.sessions.Get(ctx, sessionId)
		if err != nil {
			return fmt.Errorf("failed to get session for non-interactive mode: %w", err)
		}
		logging.Info("Get session for non-interactive run", "session_id", sess.Id)
		// resume 旧会话时，确保监控初始化
		a.EnsureSessionMonitor(sess.Id)
	} else {
		sess, err = a.CreateSession(ctx, title)
		if err != nil {
			return fmt.Errorf("failed to create session for non-interactive mode: %w", err)
		}
		logging.Info("Created session for non-interactive run", "session_id", sess.Id)
	}

	a.permissions.AutoApproveSession(sess.Id)

	done, err := a.coderAgent.Run(ctx, sess.Id, prompt)
	if err != nil {
		return fmt.Errorf("failed to start agent processing stream: %w", err)
	}

	result := <-done
	if result.Error != nil {
		if errors.Is(result.Error, context.Canceled) || errors.Is(result.Error, coreAgent.ErrRequestCancelled) {
			logging.Info("Agent processing cancelled", "session_id", sess.Id)
			return nil
		}
		return fmt.Errorf("agent processing failed: %w", result.Error)
	}

	logging.Info("Non-interactive run completed", "session_id", sess.Id)
	return nil
}

func (a *AppRuntime) GetProjectService() core.ProjectService {
	return a.projects
}

func (a *AppRuntime) GetProviderService() coreProv.Service {
	return a.providers
}

func (a *AppRuntime) GetHistoryService() core.HistoryService {
	return a.histories
}

func (a *AppRuntime) GetWorkingDir() string {
	return a.projects.GetWorkDir()
}

func (a *AppRuntime) GetGitHubService() core.GitHubService {
	return a.githubService
}

func (a *AppRuntime) GetLspManager() llm.LspManager {
	return a.lspManager
}

func (a *AppRuntime) GetHookService() coreHook.HookService {
	return a.hooks
}

func (a *AppRuntime) GetOutputStyleService() coreOutputStyle.OutputStyleService {
	return a.outputStyles
}

func (a *AppRuntime) GetShellService() coreShell.ShellService {
	return a.shells
}

func (a *AppRuntime) RegisterShutdown(shutdown func(ctx context.Context) error) {
	a.lock.Lock()
	defer a.lock.Unlock()

	a.shutdownHooks = append(a.shutdownHooks, shutdown)
}

func (a *AppRuntime) Shutdown() {
	a.lspManager.Shutdown()
	a.mcpManager.Shutdown()

	for _, h := range a.shutdownHooks {
		if err := h(context.Background()); err != nil {
			logging.Error("Failed to trigger shutdown hooks", "error", err)
		}
	}
}

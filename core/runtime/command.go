package runtime

import (
	"context"
	"errors"
	"fmt"

	"github.com/qoder-ai/qodercli/core/pubsub"

	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
)

var ErrCommandInterrupted = errors.New("command interrupted by session creation hook")

func (a *AppRuntime) LoadAllCommands() ([]command.Command, error) {
	return a.commands.LoadAllCommands()
}

func (a *AppRuntime) sendCommandFailedMsg(ctx context.Context, sessionId string, err error, parentId string) {
	content := "Fail to execute command:\n" + err.Error()
	_, err = a.messages.Create(ctx, sessionId, message.CreateMessageParams{
		Role:     message.User,
		Parts:    []message.ContentPart{message.TextContent{Type: "text", Text: content}},
		ParentId: parentId,
	})
}

func (a *AppRuntime) CommandRun(ctx context.Context, sessionId string, cmd command.Command, args string, attachments []message.Attachment) (<-chan agent.Event, error) {
	switch cmd.Type {
	case command.TypeLocal:
		// 常规本地命令
		parentId := ""
		lastMsg, err := a.messages.GetLastMessageOfSession(ctx, sessionId)
		if err != nil {
			a.sendCommandFailedMsg(ctx, sessionId, err, "")
		}

		if lastMsg != nil {
			parentId = lastMsg.Id
		}

		preMsg1, err := a.messages.Create(ctx, sessionId, message.CreateMessageParams{
			Role:     message.User,
			Parts:    a.commands.GetCaveat(),
			ParentId: parentId,
			IsMeta:   true,
		})

		if err != nil {
			logging.Error("fail to create command caveat")
			return nil, err
		}

		preMsg2, err := a.messages.Create(ctx, sessionId, message.CreateMessageParams{
			Role:     message.User,
			Parts:    []message.ContentPart{a.commands.GetCommandInfoPart(cmd, cmd.Name, args)},
			ParentId: preMsg1.ParentId,
		})

		if err != nil {
			logging.Error("fail to create command message")
			return nil, err
		}

		output, err := cmd.Run(ctx, sessionId, args)

		// 总是创建结果消息，即使输出为空
		var parts []message.ContentPart
		if err != nil {
			parts = append(parts, message.TextContent{Type: "text", Text: "Error: " + err.Error()})
			// 如果有错误但仍有输出，也包含输出内容
			if output != "" {
				parts = append(parts, message.TextContent{Type: "text", Text: fmt.Sprintf("<local-command-stdout>%s</local-command-stdout>", output)})
			} else {
				parts = append(parts, message.TextContent{Type: "text", Text: "<local-command-stdout>(no content)</local-command-stdout>"})
			}
		} else {
			// 无错误情况：有输出显示输出，无输出显示 "(no content)"
			if output != "" {
				parts = append(parts, message.TextContent{Type: "text", Text: fmt.Sprintf("<local-command-stdout>%s</local-command-stdout>", output)})
			} else {
				parts = append(parts, message.TextContent{Type: "text", Text: "<local-command-stdout>(no content)</local-command-stdout>"})
			}
		}

		if _, err2 := a.messages.Create(ctx, sessionId, message.CreateMessageParams{
			Role:     message.User,
			Parts:    parts,
			ParentId: preMsg2.Id,
		}); err2 != nil {
			logging.Error("fail to create command response message")
		}

		// 如果原始命令执行有错误，仍然返回错误
		if err != nil {
			return nil, err
		}
	case command.TypeEvent:
		parentId := ""
		ml, err := a.messages.List(ctx, sessionId)
		if len(ml) > 0 {
			parentId = ml[len(ml)-1].Id
		}

		if err != nil {
			a.sendCommandFailedMsg(ctx, sessionId, err, "")
		}

		preMsg1, err := a.messages.Create(ctx, sessionId, message.CreateMessageParams{
			Role:     message.User,
			Parts:    a.commands.GetCaveat(),
			ParentId: parentId,
			IsMeta:   true,
		})

		if err != nil {
			logging.Error("fail to create command caveat")
			return nil, err
		}

		preMsg2, err := a.messages.Create(ctx, sessionId, message.CreateMessageParams{
			Role:     message.User,
			Parts:    []message.ContentPart{a.commands.GetCommandInfoPart(cmd, cmd.Name, args)},
			ParentId: preMsg1.ParentId,
		})

		if err != nil {
			logging.Error("fail to create command message")
		}

		outputChan := make(chan command.Event)
		go cmd.RunWithEvent(ctx, sessionId, args, outputChan)

		// 启动结果处理协程
		go func() {
			var resultContent string
			var hasError bool
			var errorMsg string

			for e := range outputChan {
				// 转发事件给TUI处理
				a.commands.Publish(pubsub.UpdatedEvent, e)

				// 收集结果信息
				if payloadMap, ok := e.Payload.(map[string]interface{}); ok {
					switch payloadMap["type"] {
					case "complete", "info":
						// 命令成功完成
						if message, ok := payloadMap["message"].(string); ok && resultContent == "" {
							resultContent = message
						}
					case "error":
						// 命令执行错误
						hasError = true
						if message, ok := payloadMap["message"].(string); ok {
							errorMsg = message
						}
					case "result":
						// 直接的结果内容
						if content, ok := payloadMap["content"].(string); ok {
							resultContent = content
						}
					}
				}
			}

			// 创建结果消息
			var parts []message.ContentPart
			if hasError {
				parts = append(parts, message.TextContent{Type: "text", Text: "Error: " + errorMsg})
				if resultContent != "" {
					parts = append(parts, message.TextContent{Type: "text", Text: fmt.Sprintf("<local-command-stdout>%s</local-command-stdout>", resultContent)})
				} else {
					parts = append(parts, message.TextContent{Type: "text", Text: "<local-command-stdout>(no content)</local-command-stdout>"})
				}
			} else {
				// 无错误情况
				if resultContent != "" {
					parts = append(parts, message.TextContent{Type: "text", Text: fmt.Sprintf("<local-command-stdout>%s</local-command-stdout>", resultContent)})
				} else {
					parts = append(parts, message.TextContent{Type: "text", Text: "<local-command-stdout>(no content)</local-command-stdout>"})
				}
			}

			if _, err := a.messages.Create(ctx, sessionId, message.CreateMessageParams{
				Role:     message.User,
				Parts:    parts,
				ParentId: preMsg2.Id,
			}); err != nil {
				logging.Error("fail to create event command response message")
			}
		}()
	case command.TypePrompt:
		cmdContent, parts := a.commands.GetUserContentPartsForPrompt(ctx, cmd, sessionId, args)
		for _, attachment := range attachments {
			parts = append(parts, message.BinaryContent{Path: attachment.FilePath, MIMEType: attachment.MimeType, Data: attachment.Content})
		}

		return a.coderAgent.RunWithContentParts(ctx, sessionId, cmdContent, parts...)
	}

	return nil, nil
}

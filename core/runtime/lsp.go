package runtime

import (
	"context"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/lsp"
	"github.com/qoder-ai/qodercli/lsp/watcher"
	"sync"
	"time"
)

type lspManager struct {
	lspClient          *lsp.SolidLspClient
	clientsMutex       sync.RWMutex
	watcherCancelFuncs []context.CancelFunc
	cancelFuncsMutex   sync.Mutex
	watcherWG          sync.WaitGroup
	config             *config.Service
	workingDir         string
}

func newLspManager(config *config.Service) *lspManager {
	return &lspManager{
		lspClient:  nil, // Will be initialized in initLspClients
		config:     config,
		workingDir: config.WorkingDir,
	}
}

func (l *lspManager) initLspClients(ctx context.Context) {
	lspConfig := lsp.ConfigFromApp(l.config, l.workingDir)

	// Create and start a single solid LSP client
	go l.createAndStartSolidLspClient(ctx, lspConfig)

	logging.Info("Solid LSP client initialization started in background")
}

func (l *lspManager) createAndStartSolidLspClient(ctx context.Context, lspConfig *lsp.Config) {
	logging.Info("Creating Solid LSP client")

	lspClient, err := lsp.NewSolidLspClient(ctx, lspConfig)
	if err != nil {
		logging.Error("Failed to create Solid LSP client", err)
		return
	}

	initCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	_, err = lspClient.InitializeLspClient(initCtx, lspConfig.WorkingDirectory)
	if err != nil {
		logging.Error("Initialize failed for Solid LSP client", "error", err)
		lspClient.Close()
		return
	}

	if err := lspClient.WaitForServerReady(initCtx); err != nil {
		logging.Error("Solid LSP server failed to become ready", "error", err)
		lspClient.SetServerState(lsp.StateError)
	} else {
		logging.Info("Solid LSP server is ready")
		lspClient.SetServerState(lsp.StateReady)
	}

	logging.Info("Solid LSP client initialized")

	l.clientsMutex.Lock()
	l.lspClient = lspClient
	l.clientsMutex.Unlock()
}

func (l *lspManager) createAndStartLspClient(ctx context.Context, lspConfig *lsp.Config, name string, command string, args ...string) {
	// This function is kept for compatibility but not used with the new solid client approach
	logging.Info("Creating LSP client", "name", name, "command", command, "args", args)
	lspClient, err := lsp.NewClient(ctx, lspConfig, command, args...)
	if err != nil {
		logging.Error("Failed to create LSP client for", name, err)
		return
	}

	initCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	_, err = lspClient.InitializeLspClient(initCtx, lspConfig.WorkingDirectory)
	if err != nil {
		logging.Error("Initialize failed", "name", name, "error", err)
		lspClient.Close()
		return
	}

	if err := lspClient.WaitForServerReady(initCtx); err != nil {
		logging.Error("Server failed to become ready", "name", name, "error", err)
		lspClient.SetServerState(lsp.StateError)
	} else {
		logging.Info("LSP server is ready", "name", name)
		lspClient.SetServerState(lsp.StateReady)
	}

	logging.Info("LSP client initialized", "name", name)

	watchCtx, cancelFunc := context.WithCancel(ctx)
	watchCtx = context.WithValue(watchCtx, "serverName", name)
	workspaceWatcher := watcher.NewWorkspaceWatcher(lspClient, lspConfig.DebugLsp)

	l.cancelFuncsMutex.Lock()
	l.watcherCancelFuncs = append(l.watcherCancelFuncs, cancelFunc)
	l.cancelFuncsMutex.Unlock()

	l.watcherWG.Add(1)

	l.clientsMutex.Lock()
	// Create a temporary map for backward compatibility
	tempMap := make(map[string]*lsp.Client)
	tempMap[name] = lspClient
	// We're not actually storing this since we're using the solid client
	l.clientsMutex.Unlock()

	go l.runWorkspaceWatcher(watchCtx, name, workspaceWatcher, lspConfig)
}

func (l *lspManager) runWorkspaceWatcher(ctx context.Context, name string, workspaceWatcher *watcher.WorkspaceWatcher, lspConfig *lsp.Config) {
	defer l.watcherWG.Done()
	defer logging.RecoverPanic("LSP-"+name, func() {
		l.restartLspClient(ctx, name)
	})

	workspaceWatcher.WatchWorkspace(ctx, lspConfig.WorkingDirectory)
	logging.Info("Workspace watcher stopped", "client", name)
}

func (l *lspManager) restartLspClient(ctx context.Context, name string) {
	lspConfig := lsp.ConfigFromApp(l.config, l.workingDir)
	_, exists := lspConfig.ClientConfigs[name]
	if !exists {
		logging.Error("Cannot restart client, configuration not found", "client", name)
		return
	}

	l.clientsMutex.Lock()
	oldClient := l.lspClient
	l.lspClient = nil
	l.clientsMutex.Unlock()

	if oldClient != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		_ = oldClient.Shutdown(shutdownCtx)
		cancel()
		oldClient.Close()
	}

	l.createAndStartSolidLspClient(ctx, lspConfig)
	logging.Info("Successfully restarted Solid LSP client", "client", name)
}

func (l *lspManager) GetLspClients() map[string]*lsp.Client {
	// Return an empty map for backward compatibility
	return make(map[string]*lsp.Client)
}

func (l *lspManager) GetSolidLspClient() *lsp.SolidLspClient {
	l.clientsMutex.RLock()
	defer l.clientsMutex.RUnlock()
	return l.lspClient
}

func (l *lspManager) Shutdown() {
	l.cancelFuncsMutex.Lock()
	for _, cancel := range l.watcherCancelFuncs {
		cancel()
	}
	l.cancelFuncsMutex.Unlock()
	l.watcherWG.Wait()
	l.clientsMutex.RLock()
	client := l.lspClient
	l.clientsMutex.RUnlock()

	if client != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		if err := client.Shutdown(shutdownCtx); err != nil {
			logging.Error("Failed to shutdown Solid LSP client", "error", err)
		}
		cancel()
		client.Close()
	}
}

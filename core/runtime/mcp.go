package runtime

import (
	"context"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/internal/llm"
	"github.com/qoder-ai/qodercli/core/internal/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/logging"
)

type mcpManager struct {
	permissions llm.PermissionTrigger
	servers     []config.McpServer
	clients     map[string]*client.Client
	mcpTools    map[string][]tools.BaseTool
}

func newMcpManager(cfg *config.Service, permissions llm.PermissionTrigger) (*mcpManager, error) {
	servers, err := cfg.ListMcpServers()
	if err != nil {
		logging.Error("Failed to list mcp servers", err)
		return nil, err
	}
	return &mcpManager{permissions: permissions, servers: servers}, nil
}

func (m *mcpManager) initMcpClients(ctx context.Context) {
	m.clients = make(map[string]*client.Client)
	for _, server := range m.servers {
		cli, err := server.InitClient(ctx)
		if err != nil {
			logging.Error("error creating mcp client", "name", server.Name, "error", err)
			continue
		}

		m.clients[server.Name] = cli
		cli.OnConnectionLost(func(err error) {
			delete(m.clients, server.Name)
			delete(m.mcpTools, server.Name)
			// TODO 尝试重建 Mcp client
			logging.Error("mcp client connection lost", "name", server.Name, "error", err)
			_ = cli.Close()
		})
	}
}

func (m *mcpManager) Shutdown() {
	for _, cli := range m.clients {
		_ = cli.Close()
	}
}

func (m *mcpManager) ListMcpTools(ctx context.Context) []tools.BaseTool {
	if m.mcpTools == nil {
		m.mcpTools = make(map[string][]tools.BaseTool)
		for name, cli := range m.clients {
			toolsRequest := mcp.ListToolsRequest{}
			res, err := cli.ListTools(ctx, toolsRequest)
			if err != nil {
				logging.Error("error listing tools", "name", name, "error", err)
				continue
			}
			var baseTools []tools.BaseTool
			for _, tool := range res.Tools {
				baseTools = append(baseTools, agent.NewMcpTool(name, tool, cli, m.permissions))
			}
			m.mcpTools[name] = baseTools
		}
	}

	var list []tools.BaseTool
	for _, ts := range m.mcpTools {
		list = append(list, ts...)
	}
	return list
}

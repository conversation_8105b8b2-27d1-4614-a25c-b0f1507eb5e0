package runtime

import (
	"context"
	"strings"

	"github.com/qoder-ai/qodercli/core"
	coreAgent "github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/monitoring"
)

func (a *AppRuntime) GetCoderAgent() coreAgent.Agent {
	return a.coderAgent
}

func (a *AppRuntime) GetAgentService() coreAgent.SubAgentService {
	return a.agents
}

func (a *AppRuntime) RunAgent(ctx context.Context, sessionId string, content string, attachments ...message.Attachment) (<-chan coreAgent.Event, error) {
	if a.coderAgent.IsBusy() {
		return nil, coreAgent.ErrAgentBusy
	}

	var ses core.Session
	var err error

	if sessionId == "" {
		ses, err = a.CreateSession(ctx, "New Session")
		if err != nil {
			return nil, err
		}
		sessionId = ses.Id
	} else {
		// resume 已有会话时，确保监控初始化
		a.EnsureSessionMonitor(sessionId)
	}

	content = strings.TrimSpace(content)
	cmd, args := a.detectCommand(content)

	// 创建用户输入会话trace
	tm := monitoring.NewTraceManager(sessionId)
	inputType := "chat"
	if cmd != nil {
		inputType = "command:" + cmd.Name
	}

	traceCtx, userInputSpan := tm.StartUserInputSession(ctx, content, inputType, len(attachments) > 0)
	defer func() {
		// 记录会话信息
		userInputSpan.RecordContent("session.new", sessionId == ses.Id)
		if cmd != nil {
			userInputSpan.RecordContent("detected_command", cmd.Name)
			userInputSpan.RecordContent("command_args", args)
		}
		userInputSpan.End()
	}()

	if cmd != nil {
		return a.CommandRun(traceCtx, sessionId, *cmd, args, attachments)
	} else {
		return a.coderAgent.Run(traceCtx, sessionId, content, attachments...)
	}
}

func (a *AppRuntime) detectCommand(content string) (*command.Command, string) {
	isCmd := false
	if strings.HasPrefix(content, "/") {
		isCmd = true
	}
	if strings.HasPrefix(content, "+") {
		isCmd = true
	}
	if !isCmd {
		return nil, ""
	}

	fields := strings.Fields(content)

	if a.knownCommands == nil {
		commands, err := a.commands.LoadAllCommands()
		if err == nil {
			a.knownCommands = commands
		}
	}

	for i, cmd := range a.knownCommands {
		if cmd.ID == fields[0] {
			if len(fields) > 1 {
				return &a.knownCommands[i], strings.Join(fields[1:], " ")
			}

			return &a.knownCommands[i], ""
		}
	}

	return nil, ""
}

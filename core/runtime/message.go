package runtime

import (
	"context"
	"github.com/qoder-ai/qodercli/core/message"
)

func (a *AppRuntime) CreateMessage(ctx context.Context, sessionId string, params message.CreateMessageParams) (message.Message, error) {
	return a.messages.Create(ctx, sessionId, params)
}

func (a *AppRuntime) UpdateMessage(ctx context.Context, message message.Message) error {
	return a.messages.Update(ctx, message)
}

func (a *AppRuntime) ListMessages(ctx context.Context, sessionId string) ([]message.Message, error) {
	return a.messages.List(ctx, sessionId)
}

func (a *AppRuntime) GetLastMessageOfSession(ctx context.Context, sessionId string) (*message.Message, error) {
	return a.messages.GetLastMessageOfSession(ctx, sessionId)
}

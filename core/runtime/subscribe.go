package runtime

import (
	"context"
	"github.com/qoder-ai/qodercli/core/llm/shell"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

func (a *AppRuntime) SubscribeSession(ctx context.Context) <-chan pubsub.Event[core.Session] {
	return a.sessions.Subscribe(ctx)
}

func (a *AppRuntime) SubscribeHistory(ctx context.Context) <-chan pubsub.Event[core.File] {
	return a.histories.Subscribe(ctx)
}

func (a *AppRuntime) SubscribeMessage(ctx context.Context) <-chan pubsub.Event[message.Message] {
	return a.messages.Subscribe(ctx)
}

func (a *AppRuntime) SubscribePermission(ctx context.Context) <-chan pubsub.Event[core.PermissionRequest] {
	return a.permissions.Subscribe(ctx)
}

func (a *AppRuntime) SubscribeCoderAgent(ctx context.Context) <-chan pubsub.Event[agent.Event] {
	return a.coderAgent.Subscribe(ctx)
}

func (a *AppRuntime) SubscribeCommand(ctx context.Context) <-chan pubsub.Event[command.Event] {
	return a.commands.Subscribe(ctx)
}

func (a *AppRuntime) SubscribeShell(ctx context.Context) <-chan pubsub.Event[shell.Event] {
	return a.shells.Subscribe(ctx)
}

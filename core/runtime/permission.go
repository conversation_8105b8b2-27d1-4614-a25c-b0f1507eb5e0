package runtime

import (
	"github.com/qoder-ai/qodercli/core"
)

func (a *AppRuntime) GrantPersistent(permission core.PermissionRequest) {
	a.permissions.GrantPersistent(permission)
}

func (a *AppRuntime) Grant(permission core.PermissionRequest) {
	a.permissions.Grant(permission)
}

func (a *AppRuntime) Deny(permission core.PermissionRequest) {
	a.permissions.Deny(permission)
}

func (a *AppRuntime) AutoApproveSession(sessionId string) {
	a.permissions.AutoApproveSession(sessionId)
}

func (a *AppRuntime) CreateRequestWithContext(ctx core.SessionContext, opts core.CreatePermissionRequest) bool {
	return a.permissions.CreateRequestWithContext(ctx, opts)
}

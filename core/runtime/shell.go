package runtime

import (
	"context"
	"fmt"
	coreShell "github.com/qoder-ai/qodercli/core/llm/shell"
	"github.com/qoder-ai/qodercli/tui/event"
)

//======================================== go shell executor ========================================

// ExecuteShell 执行Shell命令
func (a *AppRuntime) ExecuteShell(ctx context.Context, command string, workingDir string, executionId string) (<-chan event.ShellEvent, error) {
	if a.shellMgr == nil {
		return nil, fmt.Errorf("shell manager not initialized")
	}
	return a.shellMgr.ExecuteShell(ctx, command, workingDir, executionId)
}

// CancelShell 取消Shell执行
func (a *AppRuntime) CancelShell(executionId string) error {
	if a.shellMgr == nil {
		return fmt.Errorf("shell manager not initialized")
	}
	return a.shellMgr.CancelShell(executionId)
}

//======================================== llm shells ========================================

// ListShells 获取当前执行的shells列表
func (a *AppRuntime) ListShells() []coreShell.Shell {
	return a.shells.ListShells()
}

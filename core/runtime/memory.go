package runtime

import (
	"github.com/qoder-ai/qodercli/core/llm/memory"
)

func (a *AppRuntime) GetMemories() []memory.Memory {
	return a.memories.GetMemories()
}

func (a *AppRuntime) GetMemoriesContent() []string {
	return a.memories.GetMemoriesContent()
}

func (s *AppRuntime) GetUserMemoryFilePath() string {
	return s.memories.GetUserMemoryFilePath()
}

func (s *AppRuntime) GetProjectMemoryFilePath() string {
	return s.memories.GetProjectMemoryFilePath()
}

func (s *AppRuntime) GetProjectLocalMemoryFilePath() string {
	return s.memories.GetProjectLocalMemoryFilePath()
}

func (a *AppRuntime) AppendMemory(location memory.MemoryLocation, content string) error {
	return a.memories.Append(location, content)
}

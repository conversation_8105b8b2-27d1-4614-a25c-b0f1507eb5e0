package runtime

import (
	"context"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/internal"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/monitoring"
)

func (a *AppRuntime) EnsureSessionMonitor(sessionId string) {
	if err := monitoring.EnsureSessionMonitor(monitoring.ModeLocal, a.projects, sessionId, a); err != nil {
		logging.Error("failed to ensure session monitoring: %v", err)
		panic(err)
	}
}

func (a *AppRuntime) CreateSession(ctx context.Context, title string) (core.Session, error) {
	return a.sessions.CreateWithConfig(ctx, title, &internal.SessionConfig{
		WorkingDir: a.workingDir,
	})
}

func (a *AppRuntime) ListSessions(ctx context.Context) ([]core.Session, error) {
	return a.sessions.List(ctx)
}

func (a *AppRuntime) CreateTaskSession(ctx context.Context, toolCallId, parentId, title string) (core.Session, error) {
	return a.sessions.CreateTaskSession(ctx, toolCallId, parentId, title)
}

func (a *AppRuntime) GetSession(ctx context.Context, id string) (core.Session, error) {
	return a.sessions.Get(ctx, id)
}

func (a *AppRuntime) SaveSession(ctx context.Context, session core.Session) error {
	return a.sessions.Save(ctx, session)
}

func (a *AppRuntime) UpdateSessionTitle(ctx context.Context, sessionId string, title string) error {
	return a.sessions.UpdateTitle(ctx, sessionId, title)
}

func (a *AppRuntime) UpdateSessionTokens(ctx context.Context, sessionId string, promptTokens int64, completionTokens int64, costDelta float64) error {
	return a.sessions.UpdateTokens(ctx, sessionId, promptTokens, completionTokens, costDelta)
}

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.log

# Binary output directory
/bin/
/dist/
qodercli
/.build/
/.dist/

# Local environment variables
.env
.env.local

.claude/
CLAUDE.md

# mcp config
.mcp.json

# other
.qoder
QODER.md
AGENTS.md
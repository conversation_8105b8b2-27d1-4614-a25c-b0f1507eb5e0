package utils

import (
	"encoding/json"
	"fmt"
	"os/exec"
	"strings"
)

// PodOptions represents options for creating a Kubernetes pod
type PodOptions struct {
	Name          string
	Namespace     string
	Image         string
	Labels        map[string]string
	Annotations   map[string]string
	EnvVars       map[string]string
	Volumes       map[string]string // configmap/secret name:mount_path
	Command       []string
	Args          []string
	RestartPolicy string // Never, OnFailure, Always
}

// K8sClient represents a Kubernetes client with kubeconfig support
type K8sClient struct {
	kubeconfig string
}

// NewK8sClient creates a new Kubernetes client with optional kubeconfig
func NewK8sClient(kubeconfig string) *K8sClient {
	return &K8sClient{
		kubeconfig: kubeconfig,
	}
}

// buildKubectlCommand builds a kubectl command with kubeconfig if specified
func (k *K8sClient) buildKubectlCommand(args ...string) *exec.Cmd {
	if k.kubeconfig != "" {
		args = append([]string{"--kubeconfig", k.kubeconfig}, args...)
	}
	return exec.Command("kubectl", args...)
}

// CreatePod creates a new Kubernetes pod with the given options
func (k *K8sClient) CreatePod(opts PodOptions) (string, error) {
	// Generate pod YAML
	podYAML := k.generatePodYAML(opts)

	// Apply the pod using kubectl
	cmd := k.buildKubectlCommand("apply", "-f", "-")
	cmd.Stdin = strings.NewReader(podYAML)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// DeletePod deletes a Kubernetes pod
func (k *K8sClient) DeletePod(podName, namespace string) (string, error) {
	args := []string{"delete", "pod", podName}
	if namespace != "" {
		args = append(args, "-n", namespace)
	}

	cmd := k.buildKubectlCommand(args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// GetPodStatus gets the status of a Kubernetes pod
func (k *K8sClient) GetPodStatus(podName, namespace string) (string, error) {
	args := []string{"get", "pod", podName, "-o", "jsonpath={.status.phase}"}
	if namespace != "" {
		args = append(args, "-n", namespace)
	}

	cmd := k.buildKubectlCommand(args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// GetPod gets a specific Kubernetes pod
func (k *K8sClient) GetPod(podName, namespace string) (string, error) {
	args := []string{"get", "pod", podName, "-o", "name"}
	if namespace != "" {
		args = append(args, "-n", namespace)
	}

	cmd := k.buildKubectlCommand(args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// ExecInPod executes a command in a Kubernetes pod
func (k *K8sClient) ExecInPod(podName, namespace string, interactive, tty bool, command []string, timeout string) (string, error) {
	args := []string{"exec"}

	if timeout != "" {
		args = append(args, "--request-timeout="+timeout)
	}
	if interactive {
		args = append(args, "-i")
	}
	if tty {
		args = append(args, "-t")
	}

	if namespace != "" {
		args = append(args, "-n", namespace)
	}

	args = append(args, podName)
	args = append(args, "--")
	args = append(args, command...)

	cmd := k.buildKubectlCommand(args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// ExecInPodInteractive executes an interactive command in a Kubernetes pod
func (k *K8sClient) ExecInPodInteractive(podName, namespace string, command []string) *exec.Cmd {
	args := []string{"exec", "-it"}

	if namespace != "" {
		args = append(args, "-n", namespace)
	}

	args = append(args, podName)
	args = append(args, "--")
	args = append(args, command...)

	return k.buildKubectlCommand(args...)
}

// CopyToPod copies files/folders from the host to a Kubernetes pod
func (k *K8sClient) CopyToPod(podName, namespace, srcPath, destPath string) (string, error) {
	target := podName + ":" + destPath
	if namespace != "" {
		target = namespace + "/" + target
	}

	cmd := k.buildKubectlCommand("cp", srcPath, target)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// CopyFromPod copies files/folders from a Kubernetes pod to the host
func (k *K8sClient) CopyFromPod(podName, namespace, srcPath, destPath string) (string, error) {
	source := podName + ":" + srcPath
	if namespace != "" {
		source = namespace + "/" + source
	}

	cmd := k.buildKubectlCommand("cp", source, destPath)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// IsPodRunning checks if a Kubernetes pod is running
func (k *K8sClient) IsPodRunning(podName, namespace string) bool {
	status, err := k.GetPodStatus(podName, namespace)
	return err == nil && status == "Running"
}

// IsPodCompleted checks if a Kubernetes pod is completed
func (k *K8sClient) IsPodCompleted(podName, namespace string) bool {
	status, err := k.GetPodStatus(podName, namespace)
	return err == nil && (status == "Succeeded" || status == "Failed")
}

// KubernetesPodList represents the kubectl get pods -o json output
type KubernetesPodList struct {
	Items []KubernetesPod `json:"items"`
}

// KubernetesPod represents a single pod from kubectl output
type KubernetesPod struct {
	Metadata KubernetesPodMetadata `json:"metadata"`
	Spec     KubernetesPodSpec     `json:"spec"`
	Status   KubernetesPodStatus   `json:"status"`
}

// KubernetesPodMetadata represents pod metadata
type KubernetesPodMetadata struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	Labels            map[string]string `json:"labels"`
	Annotations       map[string]string `json:"annotations"`
	CreationTimestamp string            `json:"creationTimestamp"`
}

// KubernetesPodSpec represents pod spec
type KubernetesPodSpec struct {
	Containers []KubernetesContainer `json:"containers"`
}

// KubernetesContainer represents a container spec
type KubernetesContainer struct {
	Image string `json:"image"`
}

// KubernetesPodStatus represents pod status
type KubernetesPodStatus struct {
	Phase string `json:"phase"`
}

// ListPods lists Kubernetes pods with optional label selector and namespace
func (k *K8sClient) ListPods(namespace, labelSelector string) ([]KubernetesPod, error) {
	args := []string{"get", "pods", "-o", "json"}
	args = append(args, "--request-timeout=10s")

	if namespace != "" {
		args = append(args, "-n", namespace)
	} else {
		args = append(args, "--all-namespaces")
	}

	if labelSelector != "" {
		args = append(args, "-l", labelSelector)
	}

	cmd := k.buildKubectlCommand(args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, err
	}

	if len(output) == 0 {
		return []KubernetesPod{}, nil
	}

	// 解析 JSON 输出
	var podList KubernetesPodList
	if err := json.Unmarshal(output, &podList); err != nil {
		return nil, fmt.Errorf("failed to parse kubernetes pods JSON: %v", err)
	}
	return podList.Items, nil
}

// WaitForPodReady 等待pod就绪
func (k *K8sClient) WaitForPodReady(podName, namespace, timeout string) error {
	args := []string{"wait", "--for=condition=Ready", "pod/" + podName, "--timeout=" + timeout}

	// 如果指定了namespace，添加namespace参数
	if namespace != "" {
		args = append(args, "--namespace", namespace)
	}

	cmd := k.buildKubectlCommand(args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("pod not ready: %s", strings.TrimSpace(string(output)))
	}

	return nil
}

// generatePodYAML generates the YAML for a Kubernetes pod
func (k *K8sClient) generatePodYAML(opts PodOptions) string {
	restartPolicy := opts.RestartPolicy
	if restartPolicy == "" {
		restartPolicy = "Never"
	}

	namespace := opts.Namespace
	if namespace == "" {
		namespace = "default"
	}

	yaml := fmt.Sprintf(`apiVersion: v1
kind: Pod
metadata:
  name: %s
  namespace: %s`, opts.Name, namespace)

	// Add labels
	if len(opts.Labels) > 0 {
		yaml += "\n  labels:"
		for key, value := range opts.Labels {
			yaml += fmt.Sprintf("\n    %s: \"%s\"", key, value)
		}
	}

	// Add annotations
	if len(opts.Annotations) > 0 {
		yaml += "\n  annotations:"
		for key, value := range opts.Annotations {
			yaml += fmt.Sprintf("\n    %s: \"%s\"", key, value)
		}
	}

	yaml += fmt.Sprintf(`
spec:
  restartPolicy: %s
  containers:
  - name: %s
    image: %s
    imagePullPolicy: IfNotPresent`, restartPolicy, opts.Name, opts.Image)

	// Add command and args
	if len(opts.Command) > 0 {
		yaml += "\n    command:"
		for _, cmd := range opts.Command {
			yaml += fmt.Sprintf("\n    - %q", cmd)
		}
	}

	if len(opts.Args) > 0 {
		yaml += "\n    args:"
		for _, arg := range opts.Args {
			yaml += fmt.Sprintf("\n    - %q", arg)
		}
	}

	// Add environment variables
	if len(opts.EnvVars) > 0 {
		yaml += "\n    env:"
		for key, value := range opts.EnvVars {
			yaml += fmt.Sprintf(`
    - name: %s
      value: %q`, key, value)
		}
	}

	// Add volume mounts
	if len(opts.Volumes) > 0 {
		yaml += "\n    volumeMounts:"
		for volumeName, mountPath := range opts.Volumes {
			yaml += fmt.Sprintf(`
    - name: %s
      mountPath: %s`, volumeName, mountPath)
		}

		yaml += "\n  volumes:"
		for volumeName := range opts.Volumes {
			yaml += fmt.Sprintf(`
  - name: %s
    configMap:
      name: %s`, volumeName, volumeName)
		}
	}

	return yaml
}

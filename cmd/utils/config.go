package utils

import (
	"fmt"
	"github.com/qoder-ai/qodercli/core/utils"
	"os"
	"path/filepath"

	"github.com/qoder-ai/qodercli/core/config"
	"github.com/spf13/cobra"
)

func validConfig(cmd *cobra.Command) error {
	isContainer, _ := cmd.Flags().GetBool("container")
	isWorktree, _ := cmd.Flags().GetBool("worktree")
	if isContainer && isWorktree {
		return fmt.Errorf("--container and --worktree can not be used together")
	}

	systemPrompt, _ := cmd.Flags().GetString("system-prompt")
	prompt, _ := cmd.Flags().GetString("print")
	if systemPrompt != "" && prompt == "" {
		return fmt.Errorf("--system-prompt can only be used with --print (-p)")
	}

	cont, _ := cmd.Flags().GetBool("continue")
	sessionId, _ := cmd.Flags().GetString("resume")
	if cont && sessionId != "" {
		return fmt.Errorf("--resume can not be used with --continue")
	}

	if v, err := cmd.Flags().GetInt("max-turns"); err == nil {
		if v < 0 {
			return fmt.Errorf("--max-turns must be non negative")
		}
		if v > 0 && prompt == "" {
			return fmt.Errorf("--max-turns can only be used with --print (-p)")
		}
	}

	return nil
}

func parseWorkingDirOption(cmd *cobra.Command) (config.Option, error) {
	workingDir, _ := cmd.Flags().GetString("workspace")
	if workingDir != "" {
		err := os.Chdir(workingDir)
		if err != nil {
			return nil, fmt.Errorf("failed to change directory: %v", err)
		}
	} else {
		c, err := os.Getwd()
		if err != nil {
			return nil, fmt.Errorf("failed to get current working directory: %v", err)
		}
		workingDir = c
	}
	return config.WithWorkingDir(workingDir), nil
}

func parseWorkModeOptions(cmd *cobra.Command) ([]config.Option, error) {
	var options []config.Option
	isContainer, _ := cmd.Flags().GetBool("container")
	isWorktree, _ := cmd.Flags().GetBool("worktree")
	isKubernetes, _ := cmd.Flags().GetBool("kubernetes")
	if isKubernetes {
		image, _ := cmd.Flags().GetString("image")
		if len(image) == 0 { // 参数为空，使用默认值
			image = "ubuntu:latest"
		}
		options = append(options, config.WithKubernetesImage(image))
	} else if isContainer { // 使用了--image参数
		image, _ := cmd.Flags().GetString("image")
		if len(image) == 0 { // 参数为空，使用默认值
			image = "ubuntu:latest"
		}
		options = append(options, config.WithContainerImage(image))
	} else if isWorktree { // 使用了--worktree参数
		path, _ := cmd.Flags().GetString("path")
		if len(path) == 0 { // 参数为空，随机创建一个目录
			dir := filepath.Join(utils.GetUserStorageDir(), "worktrees")
			if err := os.MkdirAll(dir, os.ModePerm); err != nil {
				return nil, fmt.Errorf("failed to create worktree directory: %v", err)
			}
			path = filepath.Join(dir, "$ID")
		}
		options = append(options, config.WithWorktreePath(path))
	}
	return options, nil
}

func ParseConfig(cmd *cobra.Command) (*config.Service, error) {
	if err := validConfig(cmd); err != nil {
		return nil, err
	}

	var options []config.Option

	// 设定的工作路径
	if option, err := parseWorkingDirOption(cmd); err != nil {
		return nil, err
	} else if option != nil {
		options = append(options, option)
	}

	// 判断是 Local/Worktree/Container 哪种运行模式
	if opts, err := parseWorkModeOptions(cmd); err != nil {
		return nil, err
	} else if opts != nil {
		options = append(options, opts...)
	}

	// 两个特殊的参数，在create/fetch/attach/rm/stop Pod时都需要使用
	namespace, _ := cmd.Flags().GetString("namespace")
	if len(namespace) == 0 {
		namespace = "default"
	}
	options = append(options, config.WithKubernetesNamespace(namespace))

	kubeconfig, _ := cmd.Flags().GetString("kubeconfig")
	if len(kubeconfig) > 0 {
		options = append(options, config.WithKubernetesConfig(kubeconfig))
	}

	// 是否开启 Debug 模式
	debug, _ := cmd.Flags().GetBool("debug")
	options = append(options, config.WithDebug(debug))

	// 最大对话轮次
	maxTurns, _ := cmd.Flags().GetInt("max-turns")
	options = append(options, config.WithMaxTurns(maxTurns))

	// 沙箱内、Worktree情况下使用的分支
	branch, _ := cmd.Flags().GetString("branch")
	options = append(options, config.WithBranch(branch))

	// 非交互模式下的用户提示词
	prompt, _ := cmd.Flags().GetString("print")
	options = append(options, config.WithPrint(prompt))

	// 非交互模式下的输出格式类型
	outputFormat, _ := cmd.Flags().GetString("output-format")
	options = append(options, config.WithOutputFormat(outputFormat))

	// 自定义的系统提示词
	systemPrompt, _ := cmd.Flags().GetString("system-prompt")
	options = append(options, config.WithSystemPrompt(systemPrompt))

	// 是否继续运行最后一次会话
	isContinue, _ := cmd.Flags().GetBool("continue")
	options = append(options, config.WithContinue(isContinue))

	// 是否恢复指定的会话
	resume, _ := cmd.Flags().GetString("resume")
	options = append(options, config.WithResume(resume))

	// 打开跳过权限检查切换的开关
	dangerouslySkipPermissions, _ := cmd.Flags().GetBool("dangerously-skip-permissions")
	options = append(options, config.WithSkipAllPermissions(dangerouslySkipPermissions))

	return config.NewService(options...)
}

package utils

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// CreateCompressedArchive 创建包含多个目录的压缩zip文件
func CreateCompressedArchive(archivePath string, dirMappings map[string]string) error {
	// 创建zip文件
	file, err := os.Create(archivePath)
	if err != nil {
		return err
	}
	defer file.Close()

	zipWriter := zip.NewWriter(file)
	defer zipWriter.Close()

	// 遍历目录映射，将每个源目录添加到指定的目标路径
	for srcDir, destPrefix := range dirMappings {
		if srcDir == "" {
			continue
		}

		// 检查源目录是否存在
		if _, err := os.Stat(srcDir); err != nil {
			if os.IsNotExist(err) {
				continue // 跳过不存在的目录
			}
			return fmt.Errorf("failed to access directory %s: %v", srcDir, err)
		}

		err = addDirToZip(zipWriter, srcDir, destPrefix)
		if err != nil {
			return fmt.Errorf("failed to add directory %s to archive: %v", srcDir, err)
		}
	}

	return nil
}

// addDirToZip 递归添加目录到zip archive
func addDirToZip(zipWriter *zip.Writer, srcDir, destPrefix string) error {
	return filepath.Walk(srcDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 获取相对路径
		relPath, err := filepath.Rel(srcDir, path)
		if err != nil {
			return err
		}

		// 构建zip中的文件路径
		zipPath := filepath.Join(destPrefix, relPath)
		// 在zip中使用正斜杠
		zipPath = strings.ReplaceAll(zipPath, "\\", "/")

		// 如果是目录，确保以"/"结尾
		if info.IsDir() {
			if !strings.HasSuffix(zipPath, "/") {
				zipPath += "/"
			}
			// 创建目录条目
			_, err := zipWriter.Create(zipPath)
			return err
		}

		// 如果是普通文件，添加文件内容
		if info.Mode().IsRegular() {
			// 创建文件条目
			writer, err := zipWriter.Create(zipPath)
			if err != nil {
				return err
			}

			// 打开源文件
			file, err := os.Open(path)
			if err != nil {
				return err
			}
			defer file.Close()

			// 拷贝文件内容
			_, err = io.Copy(writer, file)
			if err != nil {
				return err
			}
		}

		return nil
	})
}

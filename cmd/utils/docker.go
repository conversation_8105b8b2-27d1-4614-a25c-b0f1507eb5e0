package utils

import (
	"os/exec"
	"strings"
)

// DockerRunOptions represents options for running a docker container
type DockerRunOptions struct {
	Name        string
	Image       string
	Detached    bool
	Interactive bool
	TTY         bool
	Labels      map[string]string
	EnvVars     map[string]string
	Volumes     map[string]string // host_path:container_path
	Command     []string
}

// RunContainer starts a new docker container with the given options
func RunContainer(opts DockerRunOptions) (string, error) {
	args := []string{"run"}

	if opts.Detached {
		args = append(args, "-d")
	}
	if opts.Interactive {
		args = append(args, "-i")
	}
	if opts.TTY {
		args = append(args, "-t")
	}

	if opts.Name != "" {
		args = append(args, "--name", opts.Name)
	}

	for key, value := range opts.Labels {
		args = append(args, "--label", key+"="+value)
	}

	for key, value := range opts.EnvVars {
		args = append(args, "-e", key+"="+value)
	}

	for hostPath, containerPath := range opts.Volumes {
		args = append(args, "-v", hostPath+":"+containerPath)
	}

	args = append(args, opts.Image)
	args = append(args, opts.Command...)

	cmd := exec.Command("docker", args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// StopContainer stops a docker container
func StopContainer(containerName string) (string, error) {
	cmd := exec.Command("docker", "stop", containerName)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// RemoveContainer removes a docker container
func RemoveContainer(containerName string) (string, error) {
	cmd := exec.Command("docker", "rm", containerName)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// ListContainers lists docker containers with the given filters
func ListContainers(all bool, filters []string, format string) (string, error) {
	args := []string{"ps"}

	if all {
		args = append(args, "-a")
	}

	for _, filter := range filters {
		args = append(args, "--filter", filter)
	}

	if format != "" {
		args = append(args, "--format", format)
	}

	cmd := exec.Command("docker", args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// ListContainerIDs lists only container IDs with the given filters
func ListContainerIDs(all bool, filters []string) (string, error) {
	args := []string{"ps", "-q"}

	if all {
		args = append(args, "-a")
	}

	for _, filter := range filters {
		args = append(args, "--filter", filter)
	}

	cmd := exec.Command("docker", args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// CopyFromContainer copies files/folders from a docker container to the host
func CopyFromContainer(containerName, srcPath, destPath string) (string, error) {
	cmd := exec.Command("docker", "cp", containerName+":"+srcPath, destPath)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// CopyToContainer copies files/folders from the host to a docker container
func CopyToContainer(containerName, srcPath, destPath string) (string, error) {
	cmd := exec.Command("docker", "cp", srcPath, containerName+":"+destPath)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// ExecInContainer executes a command in a running docker container
func ExecInContainer(containerName string, interactive, tty, detached bool, command []string) (string, error) {
	args := []string{"exec"}

	if interactive {
		args = append(args, "-i")
	}
	if tty {
		args = append(args, "-t")
	}
	if detached {
		args = append(args, "-d")
	}

	args = append(args, containerName)
	args = append(args, command...)

	cmd := exec.Command("docker", args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// ExecInteractiveCommand executes an interactive command in a running docker container
// This is useful for commands that need stdin/stdout/stderr redirection
func ExecInteractiveCommand(containerName string, command []string) *exec.Cmd {
	args := []string{"exec", "-it", containerName}
	args = append(args, command...)
	return exec.Command("docker", args...)
}

// IsContainerRunning checks if a container is running
func IsContainerRunning(containerName string) bool {
	filters := []string{"name=" + containerName, "status=running"}
	output, err := ListContainerIDs(false, filters)
	return err == nil && output != ""
}

// IsContainerExists checks if a container exists (running or stopped)
func IsContainerExists(containerName string) bool {
	filters := []string{"name=" + containerName}
	output, err := ListContainerIDs(true, filters)
	return err == nil && output != ""
}

// StopAndRemoveContainer stops and removes a docker container
func StopAndRemoveContainer(containerName string) error {
	// Stop the container (ignore errors as it might already be stopped)
	StopContainer(containerName)

	// Remove the container
	_, err := RemoveContainer(containerName)
	return err
}

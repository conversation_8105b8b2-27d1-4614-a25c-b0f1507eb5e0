package utils

import (
	"os/exec"
	"strings"
)

func IsGitRepo(workingDir string) bool {
	cmd := exec.Command("git", "-C", workingDir, "rev-parse", "--git-dir")
	return cmd.Run() == nil
}

func IsBranchExists(workingDir, branchName string) bool {
	cmd := exec.Command("git", "-C", workingDir, "rev-parse", "--verify", branchName)
	return cmd.Run() == nil
}

func CreateBranch(workingDir, branchName string, sourceBranch string) (string, error) {
	args := []string{"-C", workingDir, "branch", branchName}
	if sourceBranch != "" {
		args = append(args, sourceBranch)
	}
	cmd := exec.Command("git", args...)
	bytes, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(bytes)), err
}

func GetCurrentBranch(workingDir string) (string, error) {
	cmd := exec.Command("git", "-C", workingDir, "rev-parse", "--abbrev-ref", "HEAD")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(output)), nil
}

func ListWorktrees(workingDir string) (string, error) {
	cmd := exec.Command("git", "-C", workingDir, "worktree", "list", "--porcelain")
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func AddWorktree(workingDir, worktreePath, branch string) (string, error) {
	cmd := exec.Command("git", "-C", workingDir, "worktree", "add", worktreePath, branch)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func RemoveWorktree(workingDir, worktreePath string, force bool) (string, error) {
	args := []string{"-C", workingDir, "worktree", "remove"}
	if force {
		args = append(args, "--force")
	}
	args = append(args, worktreePath)
	cmd := exec.Command("git", args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func DeleteBranch(workingDir, branchName string, force bool) (string, error) {
	arg := "-d"
	if force {
		arg = "-D"
	}
	cmd := exec.Command("git", "-C", workingDir, "branch", arg, branchName)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// CreateCommit adds all changes, checks status, and commits if there are changes
// Returns (output, hasChanges, error)
func CreateCommit(workingDir, message string) (string, bool, error) {
	// 1. 添加所有修改到staging area
	addCmd := exec.Command("git", "-C", workingDir, "add", ".")
	if output, err := addCmd.CombinedOutput(); err != nil {
		return strings.TrimSpace(string(output)), false, err
	}

	// 2. 检查是否有修改需要提交
	statusCmd := exec.Command("git", "-C", workingDir, "status", "--porcelain")
	statusOutput, err := statusCmd.CombinedOutput()
	if err != nil {
		return strings.TrimSpace(string(statusOutput)), false, err
	}

	// 如果没有修改，直接返回
	if strings.TrimSpace(string(statusOutput)) == "" {
		return "No changes to commit", false, nil
	}

	// 3. 提交修改
	commitCmd := exec.Command("git", "-C", workingDir, "commit", "-m", message)
	commitOutput, err := commitCmd.CombinedOutput()
	return strings.TrimSpace(string(commitOutput)), true, err
}

func CheckoutBranch(workingDir, branch string) (string, error) {
	cmd := exec.Command("git", "-C", workingDir, "checkout", branch)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func AddRemote(workingDir, remoteName, remoteURL string) (string, error) {
	cmd := exec.Command("git", "-C", workingDir, "remote", "add", remoteName, remoteURL)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func RemoveRemote(workingDir, remoteName string) (string, error) {
	cmd := exec.Command("git", "-C", workingDir, "remote", "remove", remoteName)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func FetchFromRemote(workingDir, remoteName, refSpec string) (string, error) {
	args := []string{"-C", workingDir, "fetch", remoteName}
	if refSpec != "" {
		args = append(args, refSpec)
	}
	cmd := exec.Command("git", args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func SetGitConfig(workingDir, key, value string) (string, error) {
	cmd := exec.Command("git", "-C", workingDir, "config", key, value)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func GetGitConfig(workingDir, key string) (string, error) {
	cmd := exec.Command("git", "-C", workingDir, "config", key)
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(output)), nil
}

package cmd

import (
	"fmt"
	"io"
	"os"
	"slices"
	"strings"

	"github.com/qoder-ai/qodercli/cmd/jobs"
	"github.com/qoder-ai/qodercli/cmd/mcp"
	"github.com/qoder-ai/qodercli/cmd/start"
	"github.com/qoder-ai/qodercli/cmd/update"
	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/version"
	"github.com/spf13/cobra"
	"github.com/spf13/pflag"
)

const (
	descriptionTemplate = `%s is a powerful terminal-based AI assistant that helps with software development tasks.
It provides an interactive chat interface with AI capabilities, code analysis, and LSP integration
to assist developers in writing, debugging, and understanding code directly from the terminal.`

	exampleTemplate = `
  # Run in interactive mode
  %s

  # Run with debug logging
  %s -d

  # Run with debug logging in a specific directory
  %s -d -c /path/to/project

  # Print version
  %s -v

  # Run a single non-interactive prompt
  %s -p "Explain the use of context in Go"

  # Run a single non-interactive prompt with JSON output format
  %s -p "Explain the use of context in Go" -f json

  # Run a single non-interactive prompt with limited agent iterations
  %s -p "Explain the use of context in Go" --max-turns 25`
)

var initPromptWithTui string

var rootCmd = &cobra.Command{
	Use:     core.ReleaseName,
	Short:   "Terminal-based AI assistant for software development",
	Long:    fmt.Sprintf(descriptionTemplate, core.AppName),
	Example: fmt.Sprintf(exampleTemplate, core.ReleaseName, core.ReleaseName, core.ReleaseName, core.ReleaseName, core.ReleaseName, core.ReleaseName, core.ReleaseName),
	RunE: func(cmd *cobra.Command, args []string) error {
		if cmd.Flag("version").Changed {
			fmt.Println(version.Version)
			return nil
		}

		// 设置信号处理，Ctrl+C时候清理资源
		start.SetupSignalHandling()

		// 解析命令行参数，读取配置文件
		cfg, err := utils.ParseConfig(cmd)
		if err != nil {
			fmt.Printf("[Error] Failed to parse config: %v\n", err)
			return err
		}
		cfg.InitPromptWithTui = initPromptWithTui

		// 在后台调度自动更新（静默，无输出）
		update.ScheduleAutoUpdateIfEnabled(cfg)

		// 启动主程序
		ctx := cmd.Context()
		if len(cfg.ContainerImage) > 0 {
			// 创建本地容器方式运行
			if err := start.RunInContainer(ctx, cfg); err != nil {
				fmt.Printf("[Error] Failed to run in container: %v\n", err)
				start.ExecuteCleanup(start.ContainerCleaner)
			}
		} else if len(cfg.WorktreePath) > 0 {
			// 创建工作树方式运行
			if err := start.RunInWorktree(ctx, cfg); err != nil {
				fmt.Printf("[Error] Failed to run in worktree: %v\n", err)
				start.ExecuteCleanup(start.WorktreeCleaner)
			}
		} else if len(cfg.KubernetesImage) > 0 {
			// 在Kubernetes中运行
			if err := start.RunInKubernetes(ctx, cfg); err != nil {
				fmt.Printf("[Error] Failed to run in kubernetes: %v\n", err)
				start.ExecuteCleanup(start.KubernetesCleaner)
			}
		} else {
			// 本地直接运行
			return start.RunInLocal(ctx, cfg)
		}
		return nil
	},
}

func preprocessArgsForPiping() {
	if utils.IsTerminal(os.Stdin) {
		return
	}

	for i, arg := range os.Args {
		if arg == "-p" || arg == "--print" {
			if i+1 >= len(os.Args) || strings.HasPrefix(os.Args[i+1], "-") {
				content, _ := io.ReadAll(os.Stdin)
				newArgs := make([]string, 0, len(os.Args)+1)
				newArgs = append(newArgs, os.Args[:i+1]...)
				newArgs = append(newArgs, string(content))
				newArgs = append(newArgs, os.Args[i+1:]...)
				os.Args = newArgs
				break
			}
		}
	}
}

// 支持形如 qodercli "query" 启动 TUI
func processInitPrompt() {
	var knownCommands []string
	for _, cmd := range rootCmd.Commands() {
		use := strings.ToLower(cmd.Use)
		if strings.Contains(use, " ") {
			use = strings.Split(use, " ")[0]
		}
		knownCommands = append(knownCommands, use)
	}

	var boolFlags []string
	rootCmd.Flags().VisitAll(func(f *pflag.Flag) {
		if f.Value.Type() == "bool" {
			boolFlags = append(boolFlags, f.Name)
			if f.Shorthand != "" {
				boolFlags = append(boolFlags, f.Shorthand)
			}
		}
	})

	isFlagName := func(arg string) bool {
		return strings.HasPrefix(arg, "-")
	}

	notMyFlagName := func(arg string) bool {
		return !isFlagName(arg) || // 非-开头Flag名称
			strings.Contains(arg, "=") || // 含=的已赋值Flag名称
			slices.Contains(boolFlags, strings.TrimLeft(arg, "-")) // 无需赋值的Flag名称
	}

	for i, arg := range os.Args {
		if i == 0 || isFlagName(arg) {
			continue
		}

		if i == 1 || notMyFlagName(os.Args[i-1]) {
			// 命中子命令，直接返回
			if slices.Contains(knownCommands, strings.ToLower(arg)) {
				return
			}

			initPromptWithTui = arg
			newArgs := make([]string, 0, len(os.Args)-1)
			newArgs = append(newArgs, os.Args[:i]...)
			newArgs = append(newArgs, os.Args[i+1:]...)
			os.Args = newArgs
			return
		}
	}
}

func Execute() {
	preprocessArgsForPiping()
	processInitPrompt()

	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	rootCmd.PersistentFlags().BoolP("version", "v", false, "Version")
	rootCmd.PersistentFlags().BoolP("debug", "d", false, "Debug")
	rootCmd.PersistentFlags().StringP("workspace", "w", "", "Current working directory")

	// 运行环境相关参数
	rootCmd.Flags().BoolP("worktree", "", false, "Start a concurrent job via git worktree")
	rootCmd.Flags().StringP("path", "", "", "Worktree path of the worktree job (only works with --worktree)")
	rootCmd.Flags().BoolP("container", "", false, "Start a concurrent job via container")
	rootCmd.Flags().StringP("image", "", "ubuntu:latest", "Image name of the container or kubernetes job (only works with --image or --kubernetes)")
	rootCmd.Flags().BoolP("kubernetes", "k", false, "Start a concurrent job via kubernetes")
	rootCmd.Flags().StringP("kubeconfig", "", "", "Kubeconfig of the kubernetes job (only works with --kubernetes)")
	rootCmd.Flags().StringP("namespace", "", "", "Namespace of the kubernetes job (only works with --kubernetes)")

	rootCmd.Flags().StringP("branch", "", "", "Branch name (only works with --worktree, --image, or --k8s)")
	rootCmd.Flags().StringP("resume", "r", "", "Resume a conversation - provide a session Id to resume")
	rootCmd.Flags().BoolP("continue", "c", false, "Continue the most recent conversation")
	rootCmd.Flags().StringP("print", "p", "", "Print response and exit (useful for pipes)")
	rootCmd.Flags().String("system-prompt", "", "Custom system prompt (only works with --print)")
	rootCmd.Flags().BoolP("dangerously-skip-permissions", "", false, "Bypass all permission checks. Recommended only for sandboxes with no internet access")
	rootCmd.Flags().IntP("max-turns", "", 0, "Maximum agent loop cycles (0 for unlimited) - only effective in --print mode")
	rootCmd.Flags().StringP("output-format", "f", start.Text.String(), "Output format for non-interactive mode (text, json, stream-json)")
	rootCmd.Flags().BoolP("quiet", "q", false, "Hide spinner in non-interactive mode")
	_ = rootCmd.RegisterFlagCompletionFunc("output-format", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return start.SupportedFormats, cobra.ShellCompDirectiveNoFileComp
	})

	jobs.AddSubCommand(rootCmd)
	mcp.AddSubCommand(rootCmd)
	rootCmd.AddCommand(serverCmd)
	update.AddUpdateCommand(rootCmd)
}

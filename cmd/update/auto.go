package update

import (
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/version"
)

// ScheduleAutoUpdateIfEnabled schedules background auto-update check.
// Runs silently without any output, failures are ignored.
func ScheduleAutoUpdateIfEnabled(cfg *config.Service) {
	if cfg == nil {
		return
	}
	prefs := cfg.GetPreferences()
	if prefs.AutoUpdates == nil || !*prefs.AutoUpdates {
		return
	}
	if runtime.GOOS == "windows" {
		return
	}

	baseURL := version.UpdateBaseURL
	if env := os.Getenv("QODERCLI_PUBLIC_BASE_URL"); env != "" {
		baseURL = env
	}
	if baseURL == "" {
		return
	}

	// Delay execution to avoid affecting startup experience
	time.AfterFunc(90*time.Second, func() {
		_ = autoUpdateSilently(baseURL)
	})
}

func autoUpdateSilently(baseURL string) error {
	manifestURL := strings.TrimRight(baseURL, "/") + "/channels/manifest.json"
	manifest, err := fetchManifest(manifestURL)
	if err != nil {
		return err
	}
	if manifest.Latest == version.Version {
		return nil
	}

	file, ok := manifest.pick(runtime.GOOS, runtime.GOARCH)
	if !ok {
		return nil
	}

	tmpZip, err := os.CreateTemp("", "qodercli-auto-update-*.zip")
	if err != nil {
		return err
	}
	defer os.Remove(tmpZip.Name())
	defer tmpZip.Close()

	if err := httpDownload(file.URL, tmpZip); err != nil {
		return err
	}
	if file.SHA256 != "" {
		if err := verifySHA256(tmpZip.Name(), file.SHA256); err != nil {
			return err
		}
	}

	tmpDir, err := os.MkdirTemp("", "qodercli-unzip-")
	if err != nil {
		return err
	}
	defer os.RemoveAll(tmpDir)
	binaryPath, err := unzipSingleBinary(tmpZip.Name(), tmpDir)
	if err != nil {
		return err
	}

	selfPath, err := os.Executable()
	if err != nil {
		return err
	}

	// 检查是否为软链接，如果是则获取真实路径
	realSelfPath := selfPath
	if stat, err := os.Lstat(selfPath); err == nil && stat.Mode()&os.ModeSymlink != 0 {
		if target, err := os.Readlink(selfPath); err == nil {
			if filepath.IsAbs(target) {
				realSelfPath = target
			} else {
				realSelfPath = filepath.Join(filepath.Dir(selfPath), target)
			}
		}
	}

	selfDir := filepath.Dir(realSelfPath)

	src, err := os.Open(binaryPath)
	if err != nil {
		return err
	}
	defer src.Close()

	tmpTarget, err := os.CreateTemp(selfDir, "qodercli-new-")
	if err != nil {
		return err
	}
	tmpTargetPath := tmpTarget.Name()
	if _, err := io.Copy(tmpTarget, src); err != nil {
		tmpTarget.Close()
		os.Remove(tmpTargetPath)
		return err
	}
	if err := tmpTarget.Sync(); err != nil {
		tmpTarget.Close()
		os.Remove(tmpTargetPath)
		return err
	}
	if err := tmpTarget.Close(); err != nil {
		os.Remove(tmpTargetPath)
		return err
	}
	_ = os.Chmod(tmpTargetPath, 0o755)
	if err := os.Rename(tmpTargetPath, realSelfPath); err != nil {
		os.Remove(tmpTargetPath)
		return err
	}

	return nil
}

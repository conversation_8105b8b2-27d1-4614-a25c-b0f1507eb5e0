package update

import (
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/qoder-ai/qodercli/core/version"
	"github.com/spf13/cobra"
)

var updateCmd = &cobra.Command{
	Use:                   "update",
	Short:                 "Check remote version and self-update to the latest release",
	DisableFlagsInUseLine: true,
	DisableFlagParsing:    true,
	Hidden:                false,
	RunE: func(cmd *cobra.Command, args []string) error {
		// 如果用户输入了任何参数（包括-h, --help等），直接提示用法并退出
		if len(args) > 0 {
			fmt.Println("Usage: qodercli update")
			fmt.Println("This command doesn't accept any arguments or flags.")
			return nil
		}
		if runtime.GOOS == "windows" {
			return fmt.Errorf("self-update on Windows is not supported yet")
		}

		baseURL := version.UpdateBaseURL
		if env := os.Getenv("QODERCLI_PUBLIC_BASE_URL"); env != "" {
			baseURL = env
		}
		if baseURL == "" {
			return fmt.Errorf("no update source configured. Please set environment variable: export QODERCLI_PUBLIC_BASE_URL=<your-update-oss-source-url>")
		}

		// Ensure baseURL has a protocol scheme, default to https://
		if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
			baseURL = "https://" + baseURL
		}

		manifestURL := strings.TrimRight(baseURL, "/") + "/channels/manifest.json"
		manifest, err := fetchManifest(manifestURL)
		if err != nil {
			return err
		}

		fmt.Printf("Current version: %s\n", version.Version)
		fmt.Printf("Latest version: %s\n", manifest.Latest)
		if manifest.Latest == version.Version {
			fmt.Println("Already up to date.")
			return nil
		}

		// Interactive confirmation (skip with QODERCLI_UPDATE_YES=1)
		if os.Getenv("QODERCLI_UPDATE_YES") != "1" {
			var resp string
			fmt.Printf("Update from %s to %s? [y/N] ", version.Version, manifest.Latest)
			if _, err := fmt.Scanln(&resp); err != nil && !errors.Is(err, io.EOF) {
				return err
			}
			resp = strings.TrimSpace(strings.ToLower(resp))
			if resp != "y" && resp != "yes" {
				fmt.Println("Canceled")
				return nil
			}
		}

		file, ok := manifest.pick(runtime.GOOS, runtime.GOARCH)
		if !ok {
			return fmt.Errorf("manifest 中未找到当前平台构建: %s/%s", runtime.GOOS, runtime.GOARCH)
		}

		tmpZip, err := os.CreateTemp("", "qodercli-update-*.zip")
		if err != nil {
			return err
		}
		defer os.Remove(tmpZip.Name())
		defer tmpZip.Close()

		if err := httpDownload(file.URL, tmpZip); err != nil {
			return err
		}
		if file.SHA256 != "" {
			if err := verifySHA256(tmpZip.Name(), file.SHA256); err != nil {
				return err
			}
		}

		tmpDir, err := os.MkdirTemp("", "qodercli-unzip-")
		if err != nil {
			return err
		}
		defer os.RemoveAll(tmpDir)
		binPath, err := unzipSingleBinary(tmpZip.Name(), tmpDir)
		if err != nil {
			return err
		}

		// Atomic replacement of current executable: copy new file to temp file in same directory, then rename
		selfPath, err := os.Executable()
		if err != nil {
			return err
		}

		// 检查是否为软链接，如果是则获取真实路径
		realSelfPath := selfPath
		if stat, err := os.Lstat(selfPath); err == nil && stat.Mode()&os.ModeSymlink != 0 {
			if target, err := os.Readlink(selfPath); err == nil {
				if filepath.IsAbs(target) {
					realSelfPath = target
				} else {
					realSelfPath = filepath.Join(filepath.Dir(selfPath), target)
				}
			}
		}

		selfDir := filepath.Dir(realSelfPath)

		src, err := os.Open(binPath)
		if err != nil {
			return err
		}
		defer src.Close()
		tmpTarget, err := os.CreateTemp(selfDir, "qodercli-new-")
		if err != nil {
			return err
		}
		tmpTargetPath := tmpTarget.Name()
		if _, err := io.Copy(tmpTarget, src); err != nil {
			tmpTarget.Close()
			os.Remove(tmpTargetPath)
			return err
		}
		if err := tmpTarget.Sync(); err != nil {
			tmpTarget.Close()
			os.Remove(tmpTargetPath)
			return err
		}
		if err := tmpTarget.Close(); err != nil {
			os.Remove(tmpTargetPath)
			return err
		}
		_ = os.Chmod(tmpTargetPath, 0o755)
		// Rename to overwrite (use the real path, not the symlink path)
		if err := os.Rename(tmpTargetPath, realSelfPath); err != nil {
			os.Remove(tmpTargetPath)
			return fmt.Errorf("failed to replace executable: %w", err)
		}

		fmt.Println("Updated to:", manifest.Latest)
		return nil
	},
}

// AddUpdateCommand adds the update command to the provided root command
func AddUpdateCommand(rootCmd *cobra.Command) {
	// 完全禁用所有标志和帮助，让命令极简
	updateCmd.SetHelpFunc(func(cmd *cobra.Command, args []string) {
		// 自定义简化的帮助信息
		fmt.Println("Usage: qodercli update")
		fmt.Println("Check remote version and self-update to the latest release.")
		fmt.Println("This command doesn't accept any arguments or flags.")
	})

	// 禁用所有默认行为
	updateCmd.SilenceUsage = true
	updateCmd.SilenceErrors = false

	rootCmd.AddCommand(updateCmd)
}

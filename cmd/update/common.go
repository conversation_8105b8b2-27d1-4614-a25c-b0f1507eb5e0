package update

import (
	"archive/zip"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// manifestFile represents a single release file in the manifest
type manifestFile struct {
	OS     string `json:"os"`
	Arch   string `json:"arch"`
	URL    string `json:"url"`
	SHA256 string `json:"sha256"`
}

// manifestJSON represents the release manifest structure
type manifestJSON struct {
	Latest      string         `json:"latest"`
	PublishedAt string         `json:"published_at"`
	Files       []manifestFile `json:"files"`
}

// pick finds the appropriate file for the given OS and architecture
func (m *manifestJSON) pick(goos, goarch string) (*manifestFile, bool) {
	// Normalize architecture naming
	if goarch == "x86_64" {
		goarch = "amd64"
	}
	if goarch == "aarch64" {
		goarch = "arm64"
	}
	for _, f := range m.Files {
		if f.OS == goos && f.Arch == goarch {
			return &f, true
		}
	}
	return nil, false
}

// fetchManifest downloads and parses the release manifest
func fetchManifest(url string) (*manifestJSON, error) {
	// Ensure URL has a protocol scheme, default to https://
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		url = "https://" + url
	}

	client := &http.Client{Timeout: 20 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to fetch manifest: %s", resp.Status)
	}
	var m manifestJSON
	if err := json.NewDecoder(resp.Body).Decode(&m); err != nil {
		return nil, err
	}
	if m.Latest == "" {
		return nil, errors.New("manifest missing latest field")
	}
	return &m, nil
}

// httpDownload downloads a file from the given URL to the writer
func httpDownload(url string, w io.Writer) error {
	// Ensure URL has a protocol scheme, default to https://
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		url = "https://" + url
	}

	client := &http.Client{Timeout: 0}
	resp, err := client.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("download failed: %s", resp.Status)
	}
	_, err = io.Copy(w, resp.Body)
	return err
}

// verifySHA256 verifies the SHA-256 checksum of a file
func verifySHA256(path, expected string) error {
	f, err := os.Open(path)
	if err != nil {
		return err
	}
	defer f.Close()
	h := sha256.New()
	if _, err := io.Copy(h, f); err != nil {
		return err
	}
	got := hex.EncodeToString(h.Sum(nil))
	if !strings.EqualFold(got, expected) {
		return fmt.Errorf("SHA256 mismatch: expected %s got %s", expected, got)
	}
	return nil
}

// unzipSingleBinary extracts the qodercli binary from a zip file
func unzipSingleBinary(zipPath, dstDir string) (string, error) {
	zr, err := zip.OpenReader(zipPath)
	if err != nil {
		return "", err
	}
	defer zr.Close()
	var cand string
	for _, f := range zr.File {
		base := filepath.Base(f.Name)
		if base == "qodercli" || base == "qodercli.exe" {
			cand = base
			rc, err := f.Open()
			if err != nil {
				return "", err
			}
			defer rc.Close()
			out := filepath.Join(dstDir, base)
			of, err := os.Create(out)
			if err != nil {
				return "", err
			}
			if _, err := io.Copy(of, rc); err != nil {
				of.Close()
				return "", err
			}
			of.Close()
			return out, nil
		}
	}
	if cand == "" {
		return "", fmt.Errorf("no qodercli binary found inside zip")
	}
	return "", fmt.Errorf("failed to extract qodercli from zip")
}

package jobs

import (
	"fmt"
	"strings"

	"github.com/qoder-ai/qodercli/cmd/start"

	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/spf13/cobra"
)

var rmCmd = &cobra.Command{
	Use:   "rm [id]",
	Short: "Remove concurrent job(s)",
	Long:  `Remove concurrent job(s) for current workspace.`,
	Args:  cobra.MinimumNArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		jobId := args[0]

		cfg, err := utils.ParseConfig(cmd)
		if err != nil {
			return err
		}

		if _, err := checkJobExistence(jobId, cfg); err != nil {
			fmt.Printf(err.Error() + "\n")
			return nil
		}

		return removeJob(jobId, cfg)
	},
}

// removeJob 删除指定名称的 job
func removeJob(jobId string, cfg *config.Service) error {
	jobName := start.GetJobName(jobId)
	jobType := start.JobType(jobId[0:1])
	switch jobType {
	case start.WorktreeJobType:
		if _, err := removeWorktreeJob(jobName, cfg); err != nil {
			fmt.Printf("Failed to remove worktree job: %v\n", err)
			return err
		}
	case start.ContainerJobType:
		if err := removeContainerJob(jobName); err != nil {
			fmt.Printf("Failed to remove container job: %v\n", err)
			return nil
		}
	case start.KubernetesJobType:
		if err := removePodJob(jobName, cfg); err != nil {
			fmt.Printf("Failed to remove kubernetes job: %v\n", err)
			return err
		}
	default:
		fmt.Printf("Invalid job id: %s", jobId)
		return nil
	}
	fmt.Printf("Job '%s' removed successfully\n", jobId)
	return nil
}

// removeWorktreeJob 尝试删除 worktree job
func removeWorktreeJob(jobName string, cfg *config.Service) (bool, error) {
	// 检查是否是 git 仓库
	if !utils.IsGitRepo(cfg.WorkingDir) {
		return false, nil
	}

	// 列出所有 worktree 并查找匹配的
	jobs, _ := getWorktreeJobs(cfg.WorkingDir, jobName)
	branch, path := jobs[0].Branch, jobs[0].WorktreePath
	fmt.Printf("Removing worktree job: %s (branch: %s)...\n", jobName, branch)

	// 删除 worktree
	if output, err := utils.RemoveWorktree(cfg.WorkingDir, path, true); err != nil {
		return false, fmt.Errorf("failed to remove worktree: %s", output)
	}

	// 删除分支（如果是自动生成的分支）
	if strings.HasPrefix(branch, "qoder-job-") {
		_, _ = utils.DeleteBranch(cfg.WorkingDir, branch, true) // 忽略错误，分支可能已经被删除
	}

	fmt.Printf("Worktree '%s' removed\n", jobName)
	return true, nil
}

// removeContainerJob 尝试删除容器 job
func removeContainerJob(containerName string) error {
	fmt.Printf("Removing container job: %s...\n", containerName)
	if err := utils.StopAndRemoveContainer(containerName); err != nil {
		return fmt.Errorf("failed to remove container: %s", err)
	}

	fmt.Printf("Container job '%s' removed\n", containerName)
	return nil
}

// removePodJob 删除指定名称的 pod
func removePodJob(podName string, cfg *config.Service) error {
	// 获取 kubernetes 配置
	namespace := cfg.KubernetesNamespace
	k8sClient := utils.NewK8sClient(cfg.KubernetesConfig)

	// 删除 pod
	fmt.Printf("Removing pod job: %s...\n", podName)
	if output, err := k8sClient.DeletePod(podName, namespace); err != nil {
		return fmt.Errorf("failed to remove pod: %s", output)
	}

	fmt.Printf("Pod '%s' removed\n", podName)
	return nil
}

func init() {
	rmCmd.Flags().StringP("kubeconfig", "", "", "Kubeconfig of the kubernetes job")
	rmCmd.Flags().StringP("namespace", "", "", "Namespace of the kubernetes job")
}

package jobs

import (
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qodercli/cmd/start"
	"os"
	"strings"
	"time"

	"github.com/charmbracelet/lipgloss"
	"github.com/mattn/go-runewidth"
	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/spf13/cobra"
)

// Container<PERSON>ob represents a qoder job container with all relevant information
type ContainerJob struct {
	ID          string `json:"ID"`
	Name        string `json:"Name"`
	Image       string `json:"Image"`
	Status      string `json:"Status"`
	CreatedAt   string `json:"CreatedAt"`
	Interactive bool   // derived from qoder.interactive label
	InitPrompt  string // derived from qoder.init-prompt label
	WorkingDir  string // derived from qoder.working-dir label
	Branch      string
}

// WorktreeJob represents a qoder worktree job
type WorktreeJob struct {
	ID           string
	Name         string
	Branch       string
	WorktreePath string
	InitPrompt   string
	Interactive  bool
	CreatedAt    string
	Status       string // "active", "stopped", etc.
}

// KubernetesJob represents a qoder kubernetes job
type KubernetesJob struct {
	ID          string
	Name        string
	Namespace   string
	Image       string
	Status      string
	CreatedAt   string
	Interactive bool
	InitPrompt  string
	WorkingDir  string
	Branch      string
}

// DockerContainer represents the raw docker container info from JSON output
type DockerContainer struct {
	ID      string `json:"ID"`
	Names   string `json:"Names"`
	Image   string `json:"Image"`
	State   string `json:"state"`
	Created string `json:"CreatedAt"`
	Labels  string `json:"Labels"`
}

var jobsCmd = &cobra.Command{
	Use:   "jobs",
	Short: "List concurrent job(s)",
	Long:  `List concurrent job(s) for current workspace.`,
	RunE: func(cmd *cobra.Command, args []string) error {
		cfg, err := utils.ParseConfig(cmd)
		if err != nil {
			return err
		}
		return showJobs(cfg)
	},
}

// formatTimeAgo 将时间字符串转换为 "time ago" 格式
func formatTimeAgo(timeStr string) string {
	if timeStr == "" {
		return "-"
	}

	var t time.Time
	var err error

	// 尝试多种时间格式
	timeFormats := []string{
		"2006-01-02 15:04:05 -0700 MST",       // Docker格式: 2025-08-24 19:01:18 +0800 CST
		"2006-01-02 15:04:05 -0700",           // Git格式
		time.RFC3339,                          // Docker常用格式: 2023-12-01T15:30:45Z
		"2006-01-02T15:04:05.999999999Z07:00", // Docker完整格式
		"2006-01-02 15:04:05",                 // 简单格式
	}

	for _, format := range timeFormats {
		if t, err = time.Parse(format, timeStr); err == nil {
			break
		}
	}

	if err != nil {
		// 如果所有格式都解析失败，返回原始字符串
		return timeStr
	}

	now := time.Now()
	duration := now.Sub(t)

	switch {
	case duration < time.Minute:
		return "just now"
	case duration < time.Hour:
		minutes := int(duration.Minutes())
		if minutes == 1 {
			return "1 minute ago"
		}
		return fmt.Sprintf("%d minutes ago", minutes)
	case duration < 24*time.Hour:
		hours := int(duration.Hours())
		if hours == 1 {
			return "1 hour ago"
		}
		return fmt.Sprintf("%d hours ago", hours)
	case duration < 30*24*time.Hour:
		days := int(duration.Hours() / 24)
		if days == 1 {
			return "1 day ago"
		}
		return fmt.Sprintf("%d days ago", days)
	case duration < 365*24*time.Hour:
		months := int(duration.Hours() / (24 * 30))
		if months == 1 {
			return "1 month ago"
		}
		return fmt.Sprintf("%d months ago", months)
	default:
		years := int(duration.Hours() / (24 * 365))
		if years == 1 {
			return "1 year ago"
		}
		return fmt.Sprintf("%d years ago", years)
	}
}

func AddSubCommand(rootCmd *cobra.Command) {
	// Add job management command group
	groupID := "jobs"
	rootCmd.AddGroup(&cobra.Group{
		ID:    groupID,
		Title: "Job Management Commands:",
	})

	// Set group ID for all job-related commands
	jobsCmd.GroupID = groupID
	rmCmd.GroupID = groupID
	stopCmd.GroupID = groupID
	attachCmd.GroupID = groupID
	fetchCmd.GroupID = groupID

	rootCmd.AddCommand(jobsCmd)
	rootCmd.AddCommand(rmCmd)
	rootCmd.AddCommand(stopCmd)
	rootCmd.AddCommand(attachCmd)
	rootCmd.AddCommand(fetchCmd)
}

// parseLabels 解析Docker标签字符串为map
func parseLabels(labelStr string) map[string]string {
	labels := make(map[string]string)
	if labelStr == "" {
		return labels
	}

	// Docker标签格式：key1=value1,key2=value2
	pairs := strings.Split(labelStr, ",")
	for _, pair := range pairs {
		pair = strings.TrimSpace(pair)
		if pair == "" {
			continue
		}

		// 分割key=value
		parts := strings.SplitN(pair, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			labels[key] = value
		}
	}

	return labels
}

// getContainerJobs 获取当前工作目录的qoder job列表
func getContainerJobs(workingDir string, jobName string) ([]ContainerJob, error) {
	// 构建过滤条件，根据working-dir进行过滤
	workingDirFilter := fmt.Sprintf("label=qoder.working-dir=%s", workingDir)
	containerNameFilter := "name=qoder-job-"
	if jobName != "" {
		containerNameFilter = fmt.Sprintf("name=%s", jobName)
	}

	// 执行docker命令获取JSON格式的完整容器信息
	filters := []string{containerNameFilter, workingDirFilter}
	output, err := utils.ListContainers(true, filters, "json")
	if err != nil {
		return nil, fmt.Errorf("failed to list docker containers: %s", output)
	}

	if output == "" {
		return []ContainerJob{}, nil
	}

	// 解析JSON输出并转换为Job对象
	var jobs []ContainerJob
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		var container DockerContainer
		if err := json.Unmarshal([]byte(line), &container); err != nil {
			return nil, fmt.Errorf("failed to parse docker container JSON: %v", err)
		}

		// 转换DockerContainer到Job
		job := ContainerJob{
			ID:        container.ID,
			Image:     container.Image,
			Status:    container.State,
			CreatedAt: formatTimeAgo(container.Created), // Docker JSON中已经是格式化的时间字符串
		}

		// 处理容器名称 - Docker JSON中Names是字符串，不需要数组处理
		job.Name = container.Names

		// 从标签字符串中解析并提取qoder特定信息
		labels := parseLabels(container.Labels)
		if id, ok := labels["qoder.id"]; ok {
			job.ID = id
		}

		if branch, ok := labels["qoder.branch"]; ok {
			job.Branch = branch
		}

		if interactive, ok := labels["qoder.interactive"]; ok {
			job.Interactive = interactive == "true"
		}

		if initPrompt, ok := labels["qoder.init-prompt"]; ok {
			job.InitPrompt = initPrompt
		}

		if workingDir, ok := labels["qoder.working-dir"]; ok {
			job.WorkingDir = workingDir
		}

		jobs = append(jobs, job)
	}

	return jobs, nil
}

// getKubernetesJobs 获取当前工作目录的 kubernetes job 列表
func getKubernetesJobs(workingDir string, kubeconfig string, jobName string) ([]KubernetesJob, error) {

	// 创建 K8s 客户端
	k8sClient := utils.NewK8sClient(kubeconfig)

	// 使用标签选择器查询 qoder pods，查询所有 namespace
	labelSelector := "app=qoder,qoder.workspace=" + utils.Hash(workingDir)
	pods, err := k8sClient.ListPods("", labelSelector)
	if err != nil {
		return []KubernetesJob{}, nil // 如果 kubectl 命令失败，返回空列表
	}

	var jobs []KubernetesJob
	for _, pod := range pods {
		// 检查 annotations 中的工作目录是否匹配
		if podWorkingDir, ok := pod.Metadata.Annotations["qoder.working-dir"]; !ok || podWorkingDir != workingDir {
			continue
		}

		if jobName != "" && pod.Metadata.Name != jobName {
			continue
		}

		// 转换为 KubernetesJob
		job := KubernetesJob{
			Name:      pod.Metadata.Name,
			Namespace: pod.Metadata.Namespace,
			Status:    pod.Status.Phase,
			CreatedAt: formatTimeAgo(pod.Metadata.CreationTimestamp),
		}

		// 从 labels 和 annotations 中提取信息
		if id, ok := pod.Metadata.Annotations["qoder.id"]; ok {
			job.ID = id
		}

		if branch, ok := pod.Metadata.Annotations["qoder.branch"]; ok {
			job.Branch = branch
		}

		if workingDir, ok := pod.Metadata.Annotations["qoder.working-dir"]; ok {
			job.WorkingDir = workingDir
		}

		if interactive, ok := pod.Metadata.Annotations["qoder.interactive"]; ok {
			job.Interactive = interactive == "true"
		}

		if initPrompt, ok := pod.Metadata.Annotations["qoder.init-prompt"]; ok {
			job.InitPrompt = initPrompt
		}

		// 获取镜像信息
		if len(pod.Spec.Containers) > 0 {
			job.Image = pod.Spec.Containers[0].Image
		}

		jobs = append(jobs, job)
	}

	return jobs, nil
}

// getWorktreeJobs 获取当前工作目录的 worktree job 列表
func getWorktreeJobs(workingDir string, jobName string) ([]WorktreeJob, error) {
	// 列出所有 worktree
	output, err := utils.ListWorktrees(workingDir)
	if err != nil {
		return []WorktreeJob{}, nil // 如果不是 git 仓库或没有 worktree，返回空列表
	}

	var jobs []WorktreeJob
	lines := strings.Split(output, "\n")

	var currentWorktree, currentBranch string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		if strings.HasPrefix(line, "worktree ") {
			currentWorktree = strings.TrimPrefix(line, "worktree ")
		} else if strings.HasPrefix(line, "branch ") {
			currentBranch = strings.TrimPrefix(line, "branch ")

			// 检查是否是 qoder job 分支
			if strings.HasPrefix(currentBranch, "refs/heads/qoder-job-") {
				job, err := loadWorktreeJobMetadata(currentWorktree, currentBranch)
				if err == nil && (jobName == "" || job.Name == jobName) {
					jobs = append(jobs, job)
				}
			}
		}
	}

	return jobs, nil
}

// loadWorktreeJobMetadata 从 git config 加载 worktree job metadata
func loadWorktreeJobMetadata(worktreePath, branch string) (WorktreeJob, error) {
	// 提取实际的分支名（去掉 refs/heads/ 前缀）
	branchName := strings.TrimPrefix(branch, "refs/heads/")

	job := WorktreeJob{
		Name:         branchName,
		Branch:       branchName,
		WorktreePath: worktreePath,
		Status:       "exited",
	}

	// 从 git config 读取 metadata（一次查询获取所有信息）
	configKey := fmt.Sprintf("branch.%s.qoder", branchName)
	if metadata, err := utils.GetGitConfig(worktreePath, configKey); err == nil {
		var heartbeatTimeStr string
		pairs := strings.Split(metadata, ",")
		for _, pair := range pairs {
			kv := strings.SplitN(pair, "=", 2)
			if len(kv) == 2 {
				key := strings.TrimSpace(kv[0])
				value := strings.TrimSpace(kv[1])

				switch key {
				case "id":
					job.ID = value
				case "init-prompt":
					job.InitPrompt = value
				case "interactive":
					job.Interactive = value == "true"
				case "heartbeat":
					heartbeatTimeStr = value
				}
			}
		}

		// 检查心跳时间，判断进程是否在运行
		if heartbeatTimeStr != "" {
			if heartbeatTime, err := time.Parse(time.RFC3339, heartbeatTimeStr); err == nil {
				// 如果心跳时间在过去10秒钟内，认为进程在运行
				if time.Since(heartbeatTime) < 10*time.Second {
					job.Status = "running"
				}
			}
		}
	}

	// 获取worktree目录创建时间
	if stat, err := os.Stat(worktreePath); err == nil {
		job.CreatedAt = formatTimeAgo(stat.ModTime().Format("2006-01-02 15:04:05 -0700"))
	}
	return job, nil
}

// truncateString 根据显示宽度截断字符串，正确处理Unicode字符
func truncateString(s string, maxWidth int) string {
	if runewidth.StringWidth(s) <= maxWidth {
		return s
	}

	runes := []rune(s)
	width := 0
	for i, r := range runes {
		width += runewidth.RuneWidth(r)
		if width > maxWidth-3 { // 留3个字符给"..."
			return string(runes[:i]) + "..."
		}
	}
	return s
}

// padString 根据显示宽度填充字符串，正确处理Unicode字符
func padString(s string, width int) string {
	currentWidth := runewidth.StringWidth(s)
	if currentWidth >= width {
		return s
	}
	return s + strings.Repeat(" ", width-currentWidth)
}

// printJobs 格式化打印容器job列表
func printJobs(title string, headers []string, widths []int, jobs [][]string) {
	// 打印标题
	headerStyle := lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("12"))
	fmt.Println(headerStyle.Render(title + ":"))

	if len(jobs) == 0 {
		fmt.Printf("  No %s found.\n", strings.ToLower(title))
		return
	}

	// 打印表头
	subHeaderStyle := lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("14"))
	fmt.Print(" ")
	for i, header := range headers {
		fmt.Printf(" %-s", subHeaderStyle.Render(padString(header, widths[i])))
	}
	fmt.Println()

	// 打印行数据，使用正确的Unicode宽度对齐
	for _, job := range jobs {
		fmt.Print(" ")
		for i, field := range job {
			truncated := truncateString(field, widths[i])
			fmt.Printf(" %-s", padString(truncated, widths[i]))
		}
		fmt.Println()
	}
}

// printContainerJobs 格式化打印容器job列表
func printContainerJobs(jobs []ContainerJob) {
	headers := []string{"ID", "INIT PROMPT", "IMAGE", "BRANCH", "STATUS", "CREATED"}
	widths := []int{15, 30, 35, 15, 15, 20}

	// 打印每个job信息
	var data [][]string
	for _, job := range jobs {
		// 根据交互模式添加前缀
		prefix := "[N]"
		if job.Interactive {
			prefix = "[I]"
		}

		// 组合前缀和初始提示
		initPrompt := job.InitPrompt
		if len(initPrompt) == 0 {
			initPrompt = "-"
		}
		plainPrompt := fmt.Sprintf("%s %s", prefix, initPrompt)
		branch := job.Branch
		if branch == "" {
			branch = "-"
		}
		row := []string{job.ID, plainPrompt, job.Image, branch, job.Status, job.CreatedAt}
		data = append(data, row)
	}
	printJobs("Container Jobs", headers, widths, data)
}

// printKubernetesJobs 格式化打印kubernetes job列表
func printKubernetesJobs(jobs []KubernetesJob) {
	headers := []string{"ID", "INIT PROMPT", "IMAGE", "BRANCH", "NAMESPACE", "STATUS", "CREATED"}
	widths := []int{15, 30, 35, 15, 15, 15, 20}

	// 打印每个job信息
	var data [][]string
	for _, job := range jobs {
		// 根据交互模式添加前缀
		prefix := "[N]"
		if job.Interactive {
			prefix = "[I]"
		}

		// 组合前缀和初始提示
		initPrompt := job.InitPrompt
		if len(initPrompt) == 0 {
			initPrompt = "-"
		}
		plainPrompt := fmt.Sprintf("%s %s", prefix, initPrompt)

		// 简化镜像名称，只显示最后一部分
		image := job.Image
		if parts := strings.Split(image, "/"); len(parts) > 1 {
			image = parts[len(parts)-1]
		}
		if parts := strings.Split(image, ":"); len(parts) > 1 {
			image = parts[0] + ":" + parts[1]
		}

		branch := job.Branch
		if branch == "" {
			branch = "-"
		}
		row := []string{job.ID, plainPrompt, image, branch, job.Namespace, job.Status, job.CreatedAt}
		data = append(data, row)
	}
	printJobs("Kubernetes Jobs", headers, widths, data)
}

// printWorktreeJobs 格式化打印worktree job列表
func printWorktreeJobs(jobs []WorktreeJob) {
	headers := []string{"ID", "INIT PROMPT", "PATH", "STATUS", "CREATED"}
	widths := []int{15, 30, 35, 15, 20}

	// 打印每个job信息
	var data [][]string
	for _, job := range jobs {
		// 根据交互模式添加前缀
		prefix := "[N]"
		if job.Interactive {
			prefix = "[I]"
		}

		// 组合前缀和初始提示
		initPrompt := job.InitPrompt
		if len(initPrompt) == 0 {
			initPrompt = "-"
		}
		plainPrompt := fmt.Sprintf("%s %s", prefix, initPrompt)

		// 显示简化路径
		worktreePath := job.WorktreePath
		home := os.Getenv("HOME")
		if strings.HasPrefix(worktreePath, home) {
			worktreePath = "~" + worktreePath[len(home):]
		}
		row := []string{job.ID, plainPrompt, worktreePath, job.Status, job.CreatedAt}
		data = append(data, row)
	}
	printJobs("Worktree Jobs", headers, widths, data)
}

// showJobs 展示当前工作目录相关的qoder jobs（包括容器、worktree和kubernetes）
func showJobs(cfg *config.Service) error {
	fmt.Printf("Qoder jobs for workspace: %s\n", cfg.WorkingDir)

	// 获取并展示 worktree jobs
	worktreeJobs, err := getWorktreeJobs(cfg.WorkingDir, "")
	if err != nil {
		return err
	}
	fmt.Println() // 添加分隔符
	printWorktreeJobs(worktreeJobs)

	// 获取并展示 container jobs
	containerJobs, err := getContainerJobs(cfg.WorkingDir, "")
	if err != nil {
		return err
	}
	fmt.Println()
	printContainerJobs(containerJobs)

	// 获取并展示 kubernetes jobs
	kubernetesJobs, err := getKubernetesJobs(cfg.WorkingDir, cfg.KubernetesConfig, "")
	if err != nil {
		return err
	}
	fmt.Println()
	printKubernetesJobs(kubernetesJobs)

	// 打印总计信息
	fmt.Printf("\nTotal: %d worktree job(s), %d container job(s), %d kubernetes job(s)\n", len(worktreeJobs), len(containerJobs), len(kubernetesJobs))
	return nil
}

func checkJobExistence(jobId string, cfg *config.Service) (interface{}, error) {
	jobName := start.GetJobName(jobId)
	jobType := start.JobType(jobId[0:1])
	switch jobType {
	case start.WorktreeJobType:
		jobs, err := getWorktreeJobs(cfg.WorkingDir, jobName)
		if err != nil {
			return nil, fmt.Errorf("failed to verify worktree job: %w", err)
		} else if len(jobs) == 0 {
			return nil, fmt.Errorf("worktree job '%s' not found or does not belong to current workspace", jobName)
		}
		return jobs[0], nil
	case start.ContainerJobType:
		jobs, err := getContainerJobs(cfg.WorkingDir, jobName)
		if err != nil {
			return nil, fmt.Errorf("failed to verify container: %w", err)
		} else if len(jobs) == 0 {
			return nil, fmt.Errorf("container job '%s' not found or does not belong to current workspace", jobName)
		}
		return jobs[0], nil
	case start.KubernetesJobType:
		jobs, err := getKubernetesJobs(cfg.WorkingDir, cfg.KubernetesConfig, jobName)
		if err != nil {
			return nil, fmt.Errorf("failed to verify kubernetes pod: %w", err)
		} else if len(jobs) == 0 {
			return nil, fmt.Errorf("kubernetes pod '%s' not found or does not belong to current workspace", jobName)
		}
		return jobs[0], nil
	}
	return nil, fmt.Errorf("invalid job id: %s", jobId)
}

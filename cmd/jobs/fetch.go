package jobs

import (
	"fmt"
	"os"

	"github.com/qoder-ai/qodercli/cmd/start"
	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/spf13/cobra"
)

var fetchCmd = &cobra.Command{
	Use:   "fetch [id]",
	Short: "Fetch code changes from a container job",
	Long:  `Fetch code changes from a container job and save them to a new branch without affecting local modifications.`,
	Args:  cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		jobId := args[0]
		jobType := start.JobType(jobId[0:1])
		if jobType == start.WorktreeJobType {
			fmt.Println("Fetch operation is not supported for worktree jobs.")
			return nil
		}

		cfg, err := utils.ParseConfig(cmd)
		if err != nil {
			return err
		}

		if job, err := checkJobExistence(jobId, cfg); err != nil {
			fmt.Printf(err.<PERSON><PERSON>r() + "\n")
			return nil
		} else if j, ok := job.(ContainerJob); ok {
			if len(j.Branch) == 0 {
				fmt.Println("Cannot fetch from a job without branch (Feature WIP).")
				return nil
			}
		} else if j, ok := job.(KubernetesJob); ok {
			if len(j.Branch) == 0 {
				fmt.Println("Cannot fetch from a job without branch (Feature WIP).")
				return nil
			}
		} else {
			fmt.Printf("Job '%s' not found. Use 'qodercli jobs' to list available jobs", jobId)
			return nil
		}

		// 检测本地是否为Git管理
		if !utils.IsGitRepo(cfg.WorkingDir) {
			fmt.Println("Not a Git repository. Initialize Git first.")
			return nil
		}

		// 检查目标分支是否已经存在
		branchName := fmt.Sprintf("qoder-fetch-%s", jobId)
		if utils.IsBranchExists(cfg.WorkingDir, branchName) {
			fmt.Printf("Branch '%s' already exists, skipping fetch.\n", branchName)
			return nil
		}

		// 创建临时目录用于存放容器workspace
		localRepoPath, err := os.MkdirTemp("", "qoder-fetch-temp-")
		if err != nil {
			return fmt.Errorf("failed to create temp directory: %w", err)
		}
		defer os.RemoveAll(localRepoPath)

		// 从容器/Pod中获取workspace
		jobName := start.GetJobName(jobId)
		switch jobType {
		case start.ContainerJobType:
			fmt.Printf("Fetching code changes from container: %s\n", jobName)
			err = copyWorkspaceFromContainer(jobName, localRepoPath)
			if err != nil {
				fmt.Printf("Failed to copy workspace from container: %v\n", err)
				return nil
			}
		case start.KubernetesJobType:
			fmt.Printf("Fetching code changes from kubernetes: %s\n", jobName)
			namespace, kubeconfig := cfg.KubernetesNamespace, cfg.KubernetesConfig
			if err := copyWorkspaceFromPod(jobName, namespace, kubeconfig, localRepoPath); err != nil {
				fmt.Printf("Failed to attach kubernetes job: %v\n", err)
				return nil
			}
		default:
			fmt.Printf("Job '%s' not found. Use 'qodercli jobs' to list available jobs", jobId)
			return nil
		}

		// 在临时目录中执行git操作
		err = commitChangesInTempRepo(localRepoPath, branchName, jobId)
		if err != nil {
			return fmt.Errorf("failed to commit changes in temp directory: %w", err)
		}

		// 将临时目录作为remote repo，fetch回本地仓库
		err = fetchFromTempRepo(cfg.WorkingDir, jobId, localRepoPath, branchName)
		if err != nil {
			return fmt.Errorf("failed to fetch from temp repo: %w", err)
		}

		fmt.Printf("Successfully fetched changes to branch: %s\n", branchName)
		return nil
	},
}

// copyWorkspaceFromContainer 使用docker cp将整个workspace拷贝到临时目录
func copyWorkspaceFromContainer(containerName, tempDir string) error {
	fmt.Println("Copying workspace from container...")

	// 使用docker cp拷贝整个workspace目录
	_, err := utils.CopyFromContainer(containerName, "/workspace/.", tempDir)
	if err != nil {
		return fmt.Errorf("failed to copy workspace: %s", err)
	}

	fmt.Println("Copied workspace to local")
	return nil
}

// copyWorkspaceFromPod 使用kubectl cp将整个workspace拷贝到临时目录
func copyWorkspaceFromPod(podName, namespace, kubeconfig string, tempDir string) error {
	k8sClient := utils.NewK8sClient(kubeconfig)
	_, err := k8sClient.CopyFromPod(podName, namespace, "/workspace/.", tempDir)
	if err != nil {
		return fmt.Errorf("failed to copy workspace: %s", err)
	}
	fmt.Println("Copied workspace to local")
	return nil
}

// commitChangesInTempRepo 在临时目录中执行git操作
func commitChangesInTempRepo(localRepoPath, branchName, jobId string) error {
	fmt.Println("Commit changes in local temp directory...")

	// 提交修改（包含添加修改、检查状态、提交的完整流程）
	commitMsg := fmt.Sprintf("Container changes fetched from job %s", jobId)
	output, hasChanges, err := utils.CreateCommit(localRepoPath, commitMsg)
	if err != nil {
		return fmt.Errorf("failed to commit changes: %s", output)
	} else if !hasChanges {
		fmt.Println("No changes to commit")
		return nil
	}

	// 创建目标分支
	if output, err := utils.CreateBranch(localRepoPath, branchName, ""); err != nil {
		return fmt.Errorf("failed to create branch %s: %s", branchName, output)
	}

	// 切换到目标分支
	if output, err := utils.CheckoutBranch(localRepoPath, branchName); err != nil {
		return fmt.Errorf("failed to checkout branch %s: %s", branchName, output)
	}

	fmt.Printf("Changes commited and created branch: %s\n", branchName)
	return nil
}

// fetchFromTempRepo 将临时目录作为remote repo，fetch回本地仓库
func fetchFromTempRepo(workingDir, jobId, tempDir, branchName string) error {
	fmt.Println("Fetching branch from local temp repo...")

	// 1. 添加临时目录作为remote
	remoteName := fmt.Sprintf("fetch-remote-%s", jobId)
	if output, err := utils.AddRemote(workingDir, remoteName, tempDir); err != nil {
		return fmt.Errorf("failed to add remote: %s", output)
	}

	// 确保在函数结束时删除remote
	defer func() {
		if output, err := utils.RemoveRemote(workingDir, remoteName); err != nil {
			fmt.Printf("Warning: failed to remove remote %s: %s\n", remoteName, output)
		}
	}()

	// 2. 从remote fetch目标分支
	refSpec := branchName + ":" + branchName
	if output, err := utils.FetchFromRemote(workingDir, remoteName, refSpec); err != nil {
		return fmt.Errorf("failed to fetch branch: %s", output)
	}

	fmt.Printf("Fetched branch %s from local temp repo\n", branchName)
	return nil
}

func init() {
	fetchCmd.Flags().StringP("kubeconfig", "", "", "Kubeconfig of the kubernetes job")
	fetchCmd.Flags().StringP("namespace", "", "", "Namespace of the kubernetes job")
}

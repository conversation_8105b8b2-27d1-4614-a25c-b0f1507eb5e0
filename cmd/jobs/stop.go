package jobs

import (
	"fmt"

	"github.com/qoder-ai/qodercli/cmd/start"

	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/spf13/cobra"
)

var stopCmd = &cobra.Command{
	Use:   "stop [id]",
	Short: "Stop concurrent job(s)",
	Long:  `Stop concurrent job(s) for current workspace.`,
	Args:  cobra.MinimumNArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		jobId := args[0]
		jobType := start.JobType(jobId[0:1])
		if jobType == start.WorktreeJobType {
			fmt.Println("Stop operation is not supported for worktree jobs.")
			return nil
		}

		cfg, err := utils.ParseConfig(cmd)
		if err != nil {
			return err
		}

		if _, err := checkJobExistence(jobId, cfg); err != nil {
			fmt.Printf(err.Error() + "\n")
			return nil
		}

		return stopJob(jobId, cfg)
	},
}

// stopJob 统一的作业停止函数，自动检测作业类型并停止
func stopJob(jobId string, cfg *config.Service) error {
	jobName := start.GetJobName(jobId)
	jobType := start.JobType(jobId[0:1])
	switch jobType {
	case start.ContainerJobType:
		if err := stopContainerJob(jobName); err != nil {
			fmt.Printf("Failed to stop container job: %v\n", err)
			return err
		}
	case start.KubernetesJobType:
		if err := stopKubernetesJob(jobName, cfg.KubernetesNamespace, cfg); err != nil {
			fmt.Printf("Failed to stop kubernetes job: %v\n", err)
			return err
		}
	default:
		fmt.Printf("Job '%s' not found. Use 'qodercli jobs' to list available jobs", jobId)
		return nil
	}
	fmt.Printf("Job '%s' stopped successfully\n", jobId)
	return nil
}

// stopContainerJob 停止指定名称的容器
func stopContainerJob(containerName string) error {
	if output, err := utils.StopContainer(containerName); err != nil {
		return fmt.Errorf("failed to stop container: %s", output)
	}

	fmt.Printf("Container job '%s' stopped successfully\n", containerName)
	return nil
}

// stopKubernetesPod 停止指定的 Kubernetes pod
func stopKubernetesJob(podName, namespace string, cfg *config.Service) error {
	fmt.Printf("Stopping kubernetes pod: %s in namespace %s...\n", podName, namespace)
	k8sClient := utils.NewK8sClient(cfg.KubernetesConfig)
	if output, err := k8sClient.DeletePod(podName, namespace); err != nil {
		return fmt.Errorf("failed to stop kubernetes pod: %s", output)
	}

	fmt.Printf("Kubernetes pod '%s' stopped successfully\n", podName)
	return nil
}

func init() {
	stopCmd.Flags().StringP("kubeconfig", "", "", "Kubeconfig of the kubernetes job")
	stopCmd.Flags().StringP("namespace", "", "", "Namespace of the kubernetes job")
}

package jobs

import (
	"fmt"

	"github.com/qoder-ai/qodercli/cmd/start"
	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/spf13/cobra"
)

var attachCmd = &cobra.Command{
	Use:   "attach [id]",
	Short: "Attach to a container job",
	Long:  `Attach to a container job for current workspace.`,
	Args:  cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		jobId := args[0]
		jobType := start.JobType(jobId[0:1])
		if jobType == start.WorktreeJobType {
			fmt.Println("Attach operation is not supported for worktree jobs.")
			return nil
		}

		cfg, err := utils.ParseConfig(cmd)
		if err != nil {
			return err
		}

		if _, err := checkJobExistence(jobId, cfg); err != nil {
			fmt.Printf(err.<PERSON>rror() + "\n")
			return nil
		}

		jobName := start.GetJobName(jobId)
		switch jobType {
		case start.ContainerJobType:
			if err := start.AttachContainerJobSession(jobName); err != nil {
				fmt.Printf("Failed to attach container job: %v\n", err)
				return err
			}
		case start.KubernetesJobType:
			k8sClient := utils.NewK8sClient(cfg.KubernetesConfig)
			if err := start.AttachKubernetesJobSession(jobName, cfg.KubernetesNamespace, k8sClient); err != nil {
				fmt.Printf("Failed to attach kubernetes job: %v\n", err)
				return err
			}
		default:
			fmt.Printf("Job '%s' not found. Use 'qodercli jobs' to list available jobs", jobId)
			return nil
		}
		return nil
	},
}

func init() {
	attachCmd.Flags().StringP("kubeconfig", "", "", "Kubeconfig of the kubernetes job")
	attachCmd.Flags().StringP("namespace", "", "", "Namespace of the kubernetes job")
}

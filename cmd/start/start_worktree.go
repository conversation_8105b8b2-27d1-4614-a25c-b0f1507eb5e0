package start

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/qoder-ai/qodercli/core/config"
)

func RunInWorktree(ctx context.Context, cfg *config.Service) error {
	// 注册清理函数
	jobId := newJobId(WorktreeJobType)
	RegisterCleanup(WorktreeCleaner, func() {
		removeWorktree(jobId)
	})

	// 创建工作树
	if err := checkoutWorktree(jobId, cfg); err != nil {
		return err
	}

	if err := os.Chdir(cfg.WorktreePath); err != nil {
		return fmt.Errorf("failed to change directory: %v", err)
	}
	cfg.WorkingDir = cfg.WorktreePath

	// 启动心跳协程
	stopHeartbeat := startHeartbeat(ctx, jobId, cfg)
	defer stopHeartbeat()

	err := RunInLocal(ctx, cfg)
	heartbeatWithMetadata(jobId, cfg, "")
	return err
}

func checkoutWorktree(jobId string, cfg *config.Service) error {
	// 验证参数
	if len(cfg.WorktreePath) == 0 {
		return fmt.Errorf("worktree path cannot be empty")
	}

	// 默认生成路径中存在一个$ID占位路径，需要替换为实际ID
	cfg.WorktreePath = strings.ReplaceAll(cfg.WorktreePath, "$ID", jobId)

	// 检查 Workspace 是否在 git 仓库中
	if !utils.IsGitRepo(cfg.WorkingDir) {
		return fmt.Errorf("not in a git workspace")
	}

	// 如果分支名称为空，自动生成一个分支名称
	if len(cfg.Branch) == 0 {
		cfg.Branch = GetJobName(jobId)
	}

	// 检查分支是否存在，如果不存在则创建
	if err := ensureBranchExists(cfg.WorkingDir, cfg.Branch); err != nil {
		return fmt.Errorf("failed to ensure branch exists: %w", err)
	}

	// 创建 git worktree
	cleanPath := filepath.Clean(cfg.WorktreePath)
	if output, err := utils.AddWorktree(cfg.WorkingDir, cleanPath, cfg.Branch); err != nil {
		return fmt.Errorf("failed to create worktree: %s", output)
	}
	return nil
}

// ensureBranchExists 检查分支是否存在，如果不存在则创建
func ensureBranchExists(workingDir, branchName string) error {
	// 检查分支是否存在（本地分支）
	if utils.IsBranchExists(workingDir, branchName) {
		// 本地分支存在
		return nil
	}

	// 检查分支是否存在（远程分支）
	if utils.IsBranchExists(workingDir, "origin/"+branchName) {
		// 远程分支存在，创建本地跟踪分支
		output, err := utils.CreateBranch(workingDir, branchName, "origin/"+branchName)
		if err != nil {
			return fmt.Errorf("failed to create tracking branch: %s", strings.TrimSpace(string(output)))
		}
		return nil
	}

	// 分支不存在，创建新分支
	output, err := utils.CreateBranch(workingDir, branchName, "")
	if err != nil {
		return fmt.Errorf("failed to create new branch: %s", output)
	}
	return nil
}

// removeWorktree 删除 worktree 的示例函数（用户可根据需要启用）
func removeWorktree(worktreePath string) {
	// 回到原目录避免在要删除的目录中操作
	if homeDir, err := os.UserHomeDir(); err == nil {
		os.Chdir(homeDir)
	}

	// 删除 worktree (使用空的 workingDir，因为这里使用全局 git worktree remove)
	if output, err := utils.RemoveWorktree("", worktreePath, true); err != nil {
		fmt.Printf("Warning: failed to remove worktree %s: %s\n", worktreePath, output)
	} else {
		fmt.Printf("Worktree %s removed.\n", worktreePath)
	}
}

// startHeartbeat 启动心跳协程，定期更新 git config 中的时间戳
func startHeartbeat(ctx context.Context, jobId string, cfg *config.Service) func() {
	heartbeatTicker := time.NewTicker(10 * time.Second) // 每10秒更新一次心跳
	heartbeatCtx, cancel := context.WithCancel(ctx)
	go func() {
		defer heartbeatTicker.Stop()

		// 立即更新一次心跳，标记进程开始运行
		heartbeatTime := time.Now().Format(time.RFC3339)
		heartbeatWithMetadata(jobId, cfg, heartbeatTime)

		for {
			select {
			case <-heartbeatCtx.Done():
				heartbeatWithMetadata(jobId, cfg, "")
				return
			case <-heartbeatTicker.C:
				heartbeatTime := time.Now().Format(time.RFC3339)
				heartbeatWithMetadata(jobId, cfg, heartbeatTime)
			}
		}
	}()

	return cancel
}

// heartbeatWithMetadata 更新心跳时间戳+Metadata到 git config
func heartbeatWithMetadata(jobId string, cfg *config.Service, heartbeatTime string) string {
	var interactive bool
	var initPrompt string
	if len(cfg.Print) > 0 {
		interactive = false
		initPrompt = cfg.Print
	} else {
		interactive = true
		initPrompt = cfg.InitPromptWithTui
	}

	updatedMetadata := fmt.Sprintf("id=%s,interactive=%t,init-prompt=%s,heartbeat=%s",
		jobId, interactive, initPrompt, heartbeatTime)

	// 写回 git config
	key := fmt.Sprintf("branch.%s.qoder", cfg.Branch)
	utils.SetGitConfig(cfg.WorktreePath, key, updatedMetadata) // 忽略错误，避免心跳更新影响主流程

	return updatedMetadata
}

package start

import (
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"
)

type CleanerType string

const (
	ContainerCleaner  CleanerType = "container"
	WorktreeCleaner   CleanerType = "worktree"
	KubernetesCleaner CleanerType = "kubernetes"
)

// 清理函数类型
type CleanupFunc func()

// 全局清理函数列表
var cleanupFuncs map[CleanerType]CleanupFunc

var mux sync.Mutex

// 注册清理函数
func RegisterCleanup(t CleanerType, fn CleanupFunc) {
	mux.Lock()
	defer mux.Unlock()
	if cleanupFuncs == nil {
		cleanupFuncs = make(map[CleanerType]CleanupFunc)
	}
	cleanupFuncs[t] = fn
}

// 执行所有清理函数
func executeCleanup() {
	mux.Lock()
	defer mux.Unlock()
	for _, fn := range cleanupFuncs {
		if fn != nil {
			fn()
		}
	}
	cleanupFuncs = nil
}

// 设置信号处理
func SetupSignalHandling() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)

	// 在单独的 goroutine 中监听信号
	go func() {
		<-sigChan
		fmt.Printf("\nReceived interrupt signal. Cleaning up resources...\n")
		executeCleanup()
		os.Exit(1)
	}()
}

// 执行清理并返回（供 defer 使用）
func ExecuteCleanup(t CleanerType) {
	var cleanupFunc CleanupFunc
	mux.Lock()
	if fn, ok := cleanupFuncs[t]; ok && fn != nil {
		cleanupFunc = fn
		delete(cleanupFuncs, t)
	}
	defer mux.Unlock()
	if cleanupFunc != nil {
		cleanupFunc()
	}
}

package start

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"sync"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	zone "github.com/lrstanley/bubblezone"
	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/monitoring"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"github.com/qoder-ai/qodercli/core/runtime"
	"github.com/qoder-ai/qodercli/tui"
	tuiconfig "github.com/qoder-ai/qodercli/tui/config"
	themenew "github.com/qoder-ai/qodercli/tui/theme"
)

const containerStartCommand = `
	touch /tmp/exit
	echo "Job started, monitoring /tmp/exit for changes..."
	tail -f /tmp/exit | while read -r line; do
		echo "Exit file change detected, stopping job..."
		exit 0
	done
`

type JobType string

const (
	WorktreeJobType   JobType = "1"
	ContainerJobType  JobType = "2"
	KubernetesJobType JobType = "3"
)

func newJobId(t JobType) string {
	return string(t) + strconv.FormatInt(time.Now().UnixMilli(), 10)
}

func GetJobName(jobId string) string {
	return fmt.Sprintf("qoder-job-%s", jobId)
}

// getInitBranchName 获取容器中初始化的分支
func getInitBranchName(workingDir string, branch string) string {
	if len(branch) > 0 {
		return branch
	}
	currentBranch, err := utils.GetCurrentBranch(workingDir)
	if err != nil {
		return ""
	}
	return currentBranch
}

func startupArguments(cfg *config.Service) []string {
	args := []string{"--dangerously-skip-permissions"}
	if len(cfg.InitPromptWithTui) > 0 {
		args = append(args, cfg.InitPromptWithTui)
	}
	if cfg.Debug {
		args = append(args, "--debug")
	}
	if cfg.MaxTurns > 0 {
		args = append(args, "--max-turns", fmt.Sprintf("%d", cfg.MaxTurns))
	}
	if len(cfg.Print) > 0 {
		args = append(args, "--print", cfg.Print)
	}
	if len(cfg.OutputFormat) > 0 {
		args = append(args, "--output-format", cfg.OutputFormat)
	}
	if len(cfg.Branch) > 0 {
		args = append(args, "--branch", cfg.Branch)
	}
	if len(cfg.SystemPrompt) > 0 {
		args = append(args, "--system-prompt", cfg.SystemPrompt)
	}
	return args
}

func initEnvironmentScript() string {
	return `
		if command -v apt >/dev/null 2>&1; then
			PKG_MANAGER="apt"
			UPDATE_CMD="apt update"
			INSTALL_CMD="apt install -y"
		elif command -v yum >/dev/null 2>&1; then
			PKG_MANAGER="yum"
			UPDATE_CMD="yum update -y"
			INSTALL_CMD="yum install -y"
		elif command -v dnf >/dev/null 2>&1; then
			PKG_MANAGER="dnf"
			UPDATE_CMD="dnf update -y"
			INSTALL_CMD="dnf install -y"
		else
			echo "No supported package manager found (apt/yum/dnf)"
			exit 1
		fi &&
		if ! command -v tmux >/dev/null 2>&1; then
			$UPDATE_CMD && $INSTALL_CMD tmux
		fi &&
		if ! command -v curl >/dev/null 2>&1; then
			$UPDATE_CMD && $INSTALL_CMD curl
		fi &&
		if ! command -v unzip >/dev/null 2>&1; then
			$UPDATE_CMD && $INSTALL_CMD unzip
		fi &&
		if ! command -v git >/dev/null 2>&1; then
			$UPDATE_CMD && $INSTALL_CMD git
		fi &&
		if ! command -v qodercli >/dev/null 2>&1; then
			bash -c "$(curl -fsSL https://lingma-agents-public.oss-cn-hangzhou.aliyuncs.com/qoder-cli/install.sh)"
		fi &&
		echo 'unbind C-b' >> ~/.tmux.conf &&
		echo 'bind C-b detach-client' >> ~/.tmux.conf &&
		echo 'set -g status off' >> ~/.tmux.conf &&
		echo 'set -g default-terminal "xterm-256color"' >> ~/.tmux.conf &&
		echo 'set-option -ga terminal-overrides ",xterm-256color:Tc"' >> ~/.tmux.conf &&
		echo 'set-hook -g session-closed "run-shell \"echo exit > /tmp/exit\""' >> ~/.tmux.conf &&
        
		git config --global --add safe.directory /workspace || true &&
		git config --global user.name "qoder" &&
		git config --global user.email "<EMAIL>"`
}

func initWorkspaceScript(branch string) string {
	resetBranch := `
		if [ -d "/qoder" ]; then
		    mkdir -p ~/.config/Qoder/SharedClientCache
			ln -sf /qoder ~/.config/Qoder/SharedClientCache/cache
		fi &&
    `
	if len(branch) > 0 {
		format := "cd /workspace && git reset --hard HEAD && git clean -fd && git checkout -f %s"
		resetBranch += fmt.Sprintf(format, branch)
	} else {
		resetBranch += "cd /workspace && git init && git add -A && git commit -m 'init commit'"
	}
	return resetBranch
}

func getResumeSessionId(app *runtime.AppRuntime) (string, error) {
	cfg := app.GetConfig()
	if cfg.Continue {
		sessions, err := app.ListSessions(context.Background())
		if err != nil {
			logging.Error("Failed to list sessions: %v", err)
			return "", err
		}

		sessionId := ""
		lastUpdate := int64(0)
		for _, session := range sessions {
			if session.WorkingDir == app.GetWorkingDir() && session.ParentSessionId == "" {
				if session.UpdatedAt > lastUpdate {
					sessionId = session.Id
					lastUpdate = session.UpdatedAt
				}
			}
		}

		if sessionId == "" {
			logging.Error("No session found to continue")
			return "", err
		}
		return sessionId, nil
	}

	return cfg.Resume, nil
}

func runNonInteractiveMode(ctx context.Context, app *runtime.AppRuntime, sessionId string) error {
	cfg := app.GetConfig()
	ch, cancelSubs := SetupSubscriptions(app, ctx)

	var outputWg sync.WaitGroup
	outputWg.Add(1)
	go func() {
		defer outputWg.Done()
		outputNonInteractiveStream(ch, OutputFormat(cfg.OutputFormat))
	}()

	err := app.RunNonInteractive(ctx, cfg.Print, sessionId)

	cancelSubs()
	outputWg.Wait()

	return err
}

// runInteractiveMode handles the interactive TUI mode
func runInteractiveMode(ctx context.Context, app *runtime.AppRuntime, sessionId string) error {
	start := time.Now()
	monitoring.StartRun()
	zone.NewGlobal()

	var program *tea.Program

	cfg := app.GetConfig()
	tuiConfig := tuiconfig.NewTuiConfig(cfg, app.GetProjectService())
	themenew.InitTheme(cfg)
	program = tea.NewProgram(tui.New(app, tuiConfig, cfg.InitPromptWithTui, sessionId))
	r := tui.NewRenderer(os.Stdout, false, 60)
	program.SetRender(r)

	ch, cancelSubs := SetupSubscriptions(app, ctx)
	tuiCtx, tuiCancel := context.WithCancel(ctx)
	var tuiWg sync.WaitGroup
	tuiWg.Add(1)

	go func() {
		defer tuiWg.Done()
		defer logging.RecoverPanic("TUI-message-handler", func() {
			attemptTuiRecovery(program)
		})

		msgChan := make(chan pubsub.Event[message.Message])
		buffer := make(map[string]pubsub.Event[message.Message])
		msgLock := sync.Mutex{}
		bufferLock := sync.Mutex{}
		flushTicker := time.NewTicker(10 * time.Millisecond)

		// 更新消息缓存，避免update消息积压，以最新的update消息为准
		go func() {
			for msg := range msgChan {
				bufferLock.Lock()
				buffer[msg.Payload.Id] = msg
				bufferLock.Unlock()
			}
		}()

		// 将消息异步刷新给 TUI
		go func() {
			for range flushTicker.C {
				bufferLock.Lock()
				size := len(buffer)
				toSend := make([]pubsub.Event[message.Message], size)
				i := 0
				for k := range buffer {
					toSend[i] = buffer[k]
					i++
				}
				buffer = make(map[string]pubsub.Event[message.Message])
				bufferLock.Unlock()

				if i > 0 {
					msgLock.Lock()
					for _, msg := range toSend {
						program.Send(msg)
					}
					msgLock.Unlock()
				}
			}
		}()

		for {
			select {
			case <-tuiCtx.Done():
				logging.Info("TUI message handler shutting down")
				flushTicker.Stop()
				close(msgChan)
				return
			case msg, ok := <-ch:
				if !ok {
					logging.Info("TUI message channel closed")
					return
				}
				// 消息事件非常多，合并处理
				if msgPayload, ok := msg.(pubsub.Event[message.Message]); ok && msgPayload.Type == pubsub.UpdatedEvent {
					msgChan <- msgPayload
				} else {
					msgLock.Lock()
					// program 是阻塞队列，这个步骤可能会比较耗时间
					program.Send(msg)
					msgLock.Unlock()
				}
			}
		}
	}()

	cleanup := func() {
		cancelSubs()
		tuiCancel()
		tuiWg.Wait()

		logging.Info("All goroutines cleaned up")
	}

	result, err := program.Run()
	cleanup()

	// 打印运行级结束页
	printRunSummary(time.Since(start))

	if err != nil {
		logging.Error("TUI error: %v", err)
		return fmt.Errorf("TUI error: %v", err)
	}

	logging.Info("TUI exited with result: %v", result)
	return nil
}

func attemptTuiRecovery(program *tea.Program) {
	logging.Info("Attempting to recover TUI after panic")
	program.Quit()
}

// 打印会话结束页
func printRunSummary(wall time.Duration) {
	sum := monitoring.GetRunSummary()
	fmt.Println()
	// 暂时隐藏cost和usage信息，保留代码以备后用
	// fmt.Printf("Total cost:            $%.4f\n", sum.TotalCostUSD)
	fmt.Printf("Total duration (API):  %.1fs\n", float64(sum.TotalAPIDurationMs)/1000.0)
	fmt.Printf("Total duration (wall): %.1fs\n", wall.Seconds())
	fmt.Printf("Total code changes:    %d lines added, %d lines removed\n", sum.TotalLinesAdded, sum.TotalLinesRemoved)
	// if len(sum.ModelUsage) > 0 {
	// 	fmt.Println("Usage by model:")
	// 	// 稳定顺序
	// 	keys := make([]string, 0, len(sum.ModelUsage))
	// 	for k := range sum.ModelUsage {
	// 		keys = append(keys, k)
	// 	}
	// 	sort.Strings(keys)
	// 	for _, model := range keys {
	// 		usage := sum.ModelUsage[model]
	// 		fmt.Printf("    %s:  %d input, %d output, %d cache read, %d cache write\n",
	// 			model,
	// 			usage.InputTokens,
	// 			usage.OutputTokens,
	// 			usage.CacheReadTokens,
	// 			usage.CacheCreationTokens,
	// 		)
	// 	}
	// }
}

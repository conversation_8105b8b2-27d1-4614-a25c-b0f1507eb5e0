package start

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/config"
)

const kubernetesSessionName = "qoder"

// startKubernetesJob 启动Kubernetes Job
func startKubernetesJob(id, jobName, branch string, cfg *config.Service, k8sClient *utils.K8sClient) error {
	fmt.Printf("Starting Kubernetes Job with image: %s\n", cfg.KubernetesImage)

	// 准备运行选项
	namespace := cfg.KubernetesNamespace
	opts := utils.PodOptions{
		Name:      jobName,
		Namespace: namespace,
		Image:     cfg.KubernetesImage,
		Labels: map[string]string{
			"app":             "qoder",
			"qoder.id":        id,
			"qoder.workspace": utils.Hash(cfg.WorkingDir),
		},
		Annotations: map[string]string{
			"qoder.id":          id,
			"qoder.branch":      branch,
			"qoder.working-dir": cfg.WorkingDir,
		},
		EnvVars: map[string]string{
			"LANG": "en_US.UTF-8",
		},
		Volumes:       make(map[string]string),
		Command:       []string{"bash", "-c"},
		Args:          []string{containerStartCommand},
		RestartPolicy: "Never",
	}

	// 添加交互模式标签
	opts.Annotations["qoder.interactive"] = "true"
	if len(cfg.Print) > 0 {
		opts.Annotations["qoder.interactive"] = "false"
		opts.Annotations["qoder.init-prompt"] = cfg.Print
	}
	if len(cfg.InitPromptWithTui) > 0 {
		opts.Annotations["qoder.interactive"] = "true"
		opts.Annotations["qoder.init-prompt"] = cfg.InitPromptWithTui
	}

	// 添加环境变量
	if ideaLabKey := os.Getenv("QODER_IDEALAB_API_KEY"); ideaLabKey != "" {
		opts.EnvVars["QODER_IDEALAB_API_KEY"] = ideaLabKey
	}
	if dashScopeKey := os.Getenv("QODER_DASHSCOPE_API_KEY"); dashScopeKey != "" {
		opts.EnvVars["QODER_DASHSCOPE_API_KEY"] = dashScopeKey
	}

	// 创建Pod
	output, err := k8sClient.CreatePod(opts)
	if err != nil {
		return fmt.Errorf("failed to create Kubernetes job: %s", output)
	}

	// 等待pod就绪
	fmt.Printf("Kubernetes job %s created, waiting for pod to be ready...\n", jobName)
	return k8sClient.WaitForPodReady(jobName, namespace, "60s")
}

// copyWorkspaceToJob 拷贝当前工作目录到Job的Pod (使用tar压缩优化)
func copyWorkspaceToJob(podName, branch string, cfg *config.Service, k8sClient *utils.K8sClient) error {

	// 准备目录映射
	dirMappings := map[string]string{cfg.WorkingDir: "workspace"}
	if homeDir, err := os.UserHomeDir(); err == nil {
		cachePath := filepath.Join(homeDir, "Library", "Application Support", "Qoder", "SharedClientCache", "cache")
		if _, err := os.Stat(cachePath); err == nil {
			dirMappings[cachePath] = "qoder"
		}
	}
	if len(dirMappings) < 2 &&
		os.Getenv("QODER_IDEALAB_API_KEY") == "" &&
		os.Getenv("QODER_DASHSCOPE_API_KEY") == "" {
		return fmt.Errorf("failed to create job, please login %s IDE first", core.AppName)
	}

	// 创建临时zip文件
	fmt.Println("Creating compressed zip archive of workspace and cache...")
	archivePath := filepath.Join(os.TempDir(), fmt.Sprintf("qoder-workspace-%s.zip", podName))
	if err := utils.CreateCompressedArchive(archivePath, dirMappings); err != nil {
		return fmt.Errorf("failed to create archive: %v", err)
	}

	// 确保在函数结束时清理临时文件
	defer func() {
		if err := os.Remove(archivePath); err != nil {
			fmt.Printf("Warning: failed to remove temporary archive: %v\n", err)
		}
	}()

	// 拷贝压缩包到Pod
	namespace := cfg.KubernetesNamespace
	fmt.Printf("Copying workspace to pod %s:/tmp/...\n", podName)
	if output, err := k8sClient.CopyToPod(podName, namespace, archivePath, "/tmp/workspace.zip"); err != nil {
		return fmt.Errorf("failed to copy archive to pod: %s", output)
	}

	// 在Pod中解压缩文件
	fmt.Println("Initialize workspace settings in pod...")
	extractScript := "cd / && unzip -q /tmp/workspace.zip && rm -f /tmp/workspace.zip &&"
	extractCommand := []string{"bash", "-c", strings.Join([]string{extractScript, initWorkspaceScript(branch)}, "\n")}
	if output, err := k8sClient.ExecInPod(podName, namespace, false, false, extractCommand, "60s"); err != nil {
		return fmt.Errorf("failed to extract archive in pod: %s", output)
	}

	fmt.Println("Workspace and cache directories copied and extracted.")
	return nil
}

// initializeJobPod 初始化Job Pod环境
func initializeJobPod(podName string, cfg *config.Service, k8sClient *utils.K8sClient) error {
	fmt.Printf("Initializing pod %s, installing qoder and setting up tmux...\n", podName)
	command := []string{"bash", "-c", initEnvironmentScript()}
	namespace := cfg.KubernetesNamespace
	if output, err := k8sClient.ExecInPod(podName, namespace, false, false, command, "180s"); err != nil {
		return fmt.Errorf("failed to initialize pod: %s", output)
	}
	return nil
}

// createTmuxSessionInJob 在Job Pod中创建tmux session并启动程序
func createTmuxSessionInJob(podName string, cfg *config.Service, k8sClient *utils.K8sClient) error {
	args := startupArguments(cfg)
	command := []string{"tmux", "new-session", "-d", "-s", kubernetesSessionName, "-c", "/workspace", core.ReleaseName}
	command = append(command, args...)

	namespace := cfg.KubernetesNamespace
	if output, err := k8sClient.ExecInPod(podName, namespace, false, false, command, ""); err != nil {
		return fmt.Errorf("failed to start job session: %s", output)
	}
	return nil
}

// AttachKubernetesJobSession 连接到Kubernetes Job的tmux session
func AttachKubernetesJobSession(podName, namespace string, k8sClient *utils.K8sClient) error {
	fmt.Printf("Attaching to job session '%s' in pod '%s'...\n", kubernetesSessionName, podName)

	command := []string{"tmux", "attach-session", "-t", kubernetesSessionName}
	attachCmd := k8sClient.ExecInPodInteractive(podName, namespace, command)
	attachCmd.Stdin = os.Stdin
	attachCmd.Stdout = os.Stdout
	attachCmd.Stderr = os.Stderr
	return attachCmd.Run()
}

// cleanupKubernetesJob 清理Kubernetes Job
func cleanupKubernetesJob(jobName, namespace string, k8sClient *utils.K8sClient) {
	fmt.Printf("Cleaning up Kubernetes job: %s...\n", jobName)

	if output, err := k8sClient.DeletePod(jobName, namespace); err != nil {
		fmt.Printf("Warning: failed to delete job %s: %v, output: %s\n", jobName, err, output)
	} else {
		fmt.Printf("Kubernetes job %s deleted.\n", jobName)
	}
}

// RunInKubernetes 在Kubernetes中运行Job
func RunInKubernetes(ctx context.Context, cfg *config.Service) error {
	// 创建K8sClient实例，在整个流程中复用
	k8sClient := utils.NewK8sClient(cfg.KubernetesConfig)

	// 注册清理函数
	jobId := newJobId(KubernetesJobType)
	jobName := GetJobName(jobId)
	namespace := cfg.KubernetesNamespace

	RegisterCleanup(KubernetesCleaner, func() {
		cleanupKubernetesJob(jobName, namespace, k8sClient)
	})

	// 获取目标分支名称
	workingDir := cfg.WorkingDir
	branch := getInitBranchName(workingDir, cfg.Branch)

	// 启动Kubernetes Job
	err := startKubernetesJob(jobId, jobName, branch, cfg, k8sClient)
	if err != nil {
		return err
	}

	// 初始化Pod环境并安装CLI
	if err := initializeJobPod(jobName, cfg, k8sClient); err != nil {
		return err
	}

	// 拷贝工作目录到Pod
	if err := copyWorkspaceToJob(jobName, branch, cfg, k8sClient); err != nil {
		return err
	}

	// 创建tmux session
	err = createTmuxSessionInJob(jobName, cfg, k8sClient)
	if err != nil {
		return err
	}

	// 连接到tmux session
	if err := AttachKubernetesJobSession(jobName, namespace, k8sClient); err != nil {
		fmt.Printf("Attach failed, retry: qodercli attach %s\n", jobId)
		return nil
	}

	return nil
}

package start

import (
	"context"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/runtime"
)

func RunInLocal(ctx context.Context, cfg *config.Service) error {
	app, err := runtime.NewAppRuntime(ctx, cfg)
	if err != nil {
		logging.Error("Failed to create app: %v", err)
		return err
	}
	defer app.Shutdown()

	sessionId, err := getResumeSessionId(app)
	if err != nil {
		return err
	}

	if len(cfg.Print) > 0 {
		return runNonInteractiveMode(ctx, app, sessionId)
	}
	return runInteractiveMode(ctx, app, sessionId)
}

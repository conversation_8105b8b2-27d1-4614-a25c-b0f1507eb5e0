package start

import (
	"encoding/json"
	"fmt"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

type OutputFormat string

const (
	Text       OutputFormat = "text"
	JSON       OutputFormat = "json"
	StreamJSON OutputFormat = "stream-json"
)

func (f OutputFormat) String() string {
	return string(f)
}

var SupportedFormats = []string{
	string(Text),
	string(JSON),
	string(StreamJSON),
}

func outputNonInteractiveStream(ch <-chan tea.Msg, format OutputFormat) {
	var events []agent.Event
	var lastResult *agent.Event

	for {
		msg, ok := <-ch
		if !ok {
			// Channel closed, process final output
			processFinalOutput(events, lastResult, format)
			return
		}
		switch msg := msg.(type) {
		case pubsub.Event[message.Message]:
			evtType := agent.AgentEventTypeAssistant
			if msg.Payload.Role == message.User {
				continue
			} else if msg.Payload.Role == message.Tool {
				evtType = agent.AgentEventTypeUser
			}
			agentEvent := agent.Event{
				Config:    nil,
				Type:      evtType,
				Subtype:   "stream",
				Message:   &msg.Payload,
				SessionId: msg.Payload.SessionId,
			}
			events = append(events, agentEvent)

			// For stream-json format, output immediately
			if format == StreamJSON {
				outputEvent(agentEvent)
			}
		case pubsub.Event[core.Session]:
			//bytes, err := json.Marshal(msg.Payload)
			//if err != nil {
			//	logging.Error("Failed to marshal message: %v", err)
			//	continue
			//}
			//fmt.Println(string(bytes))
			//fmt.Println("Session event")
		case pubsub.Event[agent.Event]:
			events = append(events, msg.Payload)

			// Track the last result event
			if msg.Payload.Type == agent.AgentEventTypeResult {
				lastResult = &msg.Payload
			}

			// For stream-json format, output immediately
			if format == StreamJSON {
				outputEvent(msg.Payload)
			}
		default:
			// Ignore other event types
		}
	}

}

func outputEvent(event agent.Event) {
	bytes, err := json.Marshal(event)
	if err != nil {
		logging.Error("Failed to marshal event: %v", err)
		return
	}
	fmt.Println(string(bytes))
}

func processFinalOutput(events []agent.Event, lastResult *agent.Event, format OutputFormat) {
	switch format {
	case JSON:
		if lastResult != nil {
			outputEvent(*lastResult)
		} else if len(events) > 0 {
			outputEvent(events[len(events)-1])
		}
	case StreamJSON:
		return
	case Text:
		outputTextFormat(events, lastResult)
	default:
		outputTextFormat(events, lastResult)
	}
}

func outputTextFormat(events []agent.Event, lastResult *agent.Event) {
	if lastResult != nil && lastResult.Message != nil {
		content := lastResult.Message.Content()
		if content.Text != "" {
			text := strings.TrimSpace(content.Text)
			if text != "" {
				if !strings.HasSuffix(text, "\n") {
					text += "\n"
				}
				fmt.Print(text)
				return
			}
		}
	}

	for i := len(events) - 1; i >= 0; i-- {
		event := events[i]
		if event.Message != nil && event.Message.Role == message.Assistant {
			content := event.Message.Content()
			if content.Text != "" {
				text := strings.TrimSpace(content.Text)
				if text != "" {
					// Ensure text ends with newline
					if !strings.HasSuffix(text, "\n") {
						text += "\n"
					}
					fmt.Print(text)
					return
				}
			}
		}
	}

	if lastResult != nil {
		if lastResult.Error != nil {
			fmt.Println("Execution error")
		} else if lastResult.Subtype == "error_max_turns" {
			fmt.Println("Error: Reached maximum turns")
		} else {
			fmt.Println("No output generated")
		}
	} else {
		fmt.Println("No output generated")
	}
}

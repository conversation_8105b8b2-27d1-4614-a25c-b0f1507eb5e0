package start

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/config"
)

const sessionName = "qoder"

// startContainer 启动Docker容器
func startContainer(id, containerName, branch string, cfg *config.Service) (string, error) {
	fmt.Printf("Starting Docker container with image: %s\n", cfg.ContainerImage)

	// 准备运行选项
	opts := utils.DockerRunOptions{
		Name:     containerName,
		Image:    cfg.ContainerImage,
		Detached: true,
		Labels: map[string]string{
			"qoder.id":          id,
			"qoder.branch":      branch,
			"qoder.working-dir": cfg.WorkingDir,
		},
		EnvVars: map[string]string{
			"LANG": "en_US.UTF-8",
		},
		Volumes: make(map[string]string),
		Command: []string{"bash", "-c", containerStartCommand},
	}

	// 添加交互模式标签
	opts.Labels["qoder.interactive"] = "true"
	if len(cfg.Print) > 0 {
		opts.Labels["qoder.interactive"] = "false"
		opts.Labels["qoder.init-prompt"] = cfg.Print
	}
	if len(cfg.InitPromptWithTui) > 0 {
		opts.Labels["qoder.interactive"] = "true"
		opts.Labels["qoder.init-prompt"] = cfg.InitPromptWithTui
	}

	// 添加环境变量
	if ideaLabKey := os.Getenv("QODER_IDEALAB_API_KEY"); ideaLabKey != "" {
		opts.EnvVars["QODER_IDEALAB_API_KEY"] = ideaLabKey
	}
	if dashScopeKey := os.Getenv("QODER_DASHSCOPE_API_KEY"); dashScopeKey != "" {
		opts.EnvVars["QODER_DASHSCOPE_API_KEY"] = dashScopeKey
	}

	// 检查并挂载缓存目录
	if homeDir, err := os.UserHomeDir(); err == nil {
		cacheDir := filepath.Join(homeDir, "Library", "Application Support", "Qoder", "SharedClientCache", "cache")
		if _, err := os.Stat(cacheDir); err == nil {
			// 缓存目录存在，添加卷挂载到容器内的固定路径
			opts.Volumes[cacheDir] = "/qoder"
		}
	}

	containerID, err := utils.RunContainer(opts)
	if err != nil {
		return "", fmt.Errorf("failed to start container: %s", containerID)
	}

	fmt.Printf("Container started with ID: %s\n", containerID[:12])
	return containerID, nil
}

// copyWorkspace 拷贝当前工作目录到容器
func copyWorkspace(containerName, workingDir string) error {
	fmt.Println("Copying current directory to container /workspace...")
	if output, err := utils.CopyToContainer(containerName, workingDir, "/workspace/"); err != nil {
		return fmt.Errorf("failed to copy workspace to container: %s", output)
	}
	return nil
}

// initializeContainer 初始化容器环境
func initializeContainer(containerName string, branch string) error {
	fmt.Println("Initializing container, installing qoder and setting up tmux...")

	scripts := []string{initEnvironmentScript() + " &&", initWorkspaceScript(branch)}
	command := []string{"bash", "-c", strings.Join(scripts, "\n")}
	if output, err := utils.ExecInContainer(containerName, false, false, false, command); err != nil {
		return fmt.Errorf("failed to initialize container: %s", output)
	}
	return nil
}

// createTmuxSession 创建tmux session并启动程序
func createTmuxSession(containerName string, args []string) error {
	command := []string{"tmux", "new-session", "-d", "-s", sessionName, "-c", "/workspace", core.ReleaseName}
	command = append(command, args...)

	if output, err := utils.ExecInContainer(containerName, false, false, true, command); err != nil {
		return fmt.Errorf("failed to start job session: %s", output)
	}
	return nil
}

// AttachContainerJobSession 连接到tmux session
func AttachContainerJobSession(containerName string) error {
	fmt.Printf("Attaching to job session '%s' in container '%s'...\n", sessionName, containerName)

	command := []string{"tmux", "attach-session", "-t", sessionName}
	attachCmd := utils.ExecInteractiveCommand(containerName, command)
	attachCmd.Stdin = os.Stdin
	attachCmd.Stdout = os.Stdout
	attachCmd.Stderr = os.Stderr
	return attachCmd.Run()
}

// cleanupContainer 清理容器（停止并删除）
func cleanupContainer(containerName string) {
	fmt.Printf("Cleaning up container: %s...\n", containerName)

	// 使用封装的函数停止并删除容器
	if err := utils.StopAndRemoveContainer(containerName); err != nil {
		fmt.Printf("Warning: failed to remove container %s: %v\n", containerName, err)
	} else {
		fmt.Printf("Container %s removed.\n", containerName)
	}
}

func RunInContainer(ctx context.Context, cfg *config.Service) error {

	// 注册清理函数
	jobId := newJobId(ContainerJobType)
	containerName := GetJobName(jobId)
	RegisterCleanup(ContainerCleaner, func() {
		cleanupContainer(containerName)
	})

	// 获取目标分支名称
	workingDir := cfg.WorkingDir
	branch := getInitBranchName(workingDir, cfg.Branch)

	// 启动Docker容器
	_, err := startContainer(jobId, containerName, branch, cfg)
	if err != nil {
		return err
	}

	// 拷贝工作目录到容器
	if err := copyWorkspace(containerName, workingDir); err != nil {
		return err
	}

	// 初始化容器环境并安装CLI
	if err := initializeContainer(containerName, branch); err != nil {
		return err
	}

	// 创建tmux session
	args := startupArguments(cfg)
	err = createTmuxSession(containerName, args)
	if err != nil {
		return err
	}

	// 连接到tmux session
	if err := AttachContainerJobSession(containerName); err != nil {
		fmt.Printf("Attach failed, retry: qodercli attach %s\n", jobId)
		return nil
	}
	return nil
}

package start

import (
	"context"
	"fmt"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"github.com/qoder-ai/qodercli/core/runtime"
	"sync"
	"time"
)

func setupSubscriber[T any](
	ctx context.Context,
	wg *sync.WaitGroup,
	name string,
	subscriber func(context.Context) <-chan pubsub.Event[T],
	outputCh chan<- tea.Msg,
) {
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer logging.RecoverPanic(fmt.Sprintf("subscription-%s", name), nil)

		subCh := subscriber(ctx)

		for {
			select {
			case event, ok := <-subCh:
				if !ok {
					logging.Info("subscription channel closed", "name", name)
					return
				}

				var msg tea.Msg = event

				select {
				case outputCh <- msg:
				case <-time.After(2 * time.Second):
					logging.Warn("message dropped due to slow consumer", "name", name)
				case <-ctx.Done():
					logging.Info("subscription cancelled", "name", name)
					return
				}
			case <-ctx.Done():
				logging.Info("subscription cancelled", "name", name)
				return
			}
		}
	}()
}

func SetupSubscriptions(app *runtime.AppRuntime, parentCtx context.Context) (chan tea.Msg, func()) {
	ch := make(chan tea.Msg, 100)

	wg := sync.WaitGroup{}
	ctx, cancel := context.WithCancel(parentCtx)
	setupSubscriber(ctx, &wg, "logging", logging.Subscribe, ch)
	setupSubscriber(ctx, &wg, "sessions", app.SubscribeSession, ch)
	setupSubscriber(ctx, &wg, "messages", app.SubscribeMessage, ch)
	setupSubscriber(ctx, &wg, "permissions", app.SubscribePermission, ch)
	setupSubscriber(ctx, &wg, "coderAgent", app.SubscribeCoderAgent, ch)
	setupSubscriber(ctx, &wg, "commands", app.SubscribeCommand, ch)

	cleanupFunc := func() {
		logging.Info("Cancelling all subscriptions")
		cancel()

		waitCh := make(chan struct{})
		go func() {
			defer logging.RecoverPanic("subscription-cleanup", nil)
			wg.Wait()
			close(waitCh)
		}()

		select {
		case <-waitCh:
			logging.Info("All subscription goroutines completed successfully")
			close(ch)
		case <-time.After(5 * time.Second):
			logging.Warn("Timed out waiting for some subscription goroutines to complete")
			close(ch)
		}
	}
	return ch, cleanupFunc
}

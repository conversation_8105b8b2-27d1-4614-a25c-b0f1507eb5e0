package mcp

import (
	"github.com/qoder-ai/qodercli/core"
	config2 "github.com/qoder-ai/qodercli/core/config"
	"github.com/spf13/cobra"
	"strings"
)

var mcpGetCmd = &cobra.Command{
	Use:   "get [name]",
	Short: "Get details of an MCP server",
	Long:  `Get detailed information about a specific MCP server.`,
	Args:  cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		name := args[0]

		servers, err := cfg.ListMcpServers()
		if err != nil {
			return err
		}
		for _, server := range servers {
			if server.Name == name {
				cmd.Println(name + ":")
				cmd.Printf("  Scope: %s\n", server.Scope)
				cmd.Printf("  Type: %s\n", server.Type)
				switch server.Type {
				case config2.McpStdio:
					cmd.Printf("  Command: %s\n", server.Command)
					cmd.Printf("  Args: %s\n", strings.Join(server.Args, " "))
					cmd.Printf("  Environments:\n")
					for k, v := range server.Env {
						cmd.Printf("    %s: %s\n", strings.ToUpper(k), v)
					}
				case config2.McpSse, config2.McpHttp:
					cmd.Printf("  URL: %s\n", server.URL)
					cmd.Printf("  Headers:")
					for k, v := range server.Headers {
						cmd.Printf("    %s: %s\n", k, v)
					}
				}
				cmd.Printf("\nTo remove this server, run: %s mcp remove \"%s\" -s %s", core.ReleaseName, name, server.Scope)
				return nil
			}
		}
		return nil
	},
}

package mcp

import (
	"fmt"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/spf13/cobra"
	"strings"
)

var mcpAddCmd = &cobra.Command{
	Use:   "add [name] [endpoint]",
	Short: "Add a new MCP server",
	Long:  `Add a new MCP server with the specified name and endpoint.`,
	Args:  cobra.MinimumNArgs(2),
	RunE: func(cmd *cobra.Command, args []string) error {
		mcpServer, err := parseMcpServer(cmd, args)
		if err != nil {
			return err
		}

		svrName := args[0]
		scope, _ := cmd.Flags().GetString("scope")
		scopeType := config.ConfigScope(scope)
		if err := cfg.AddMcpServers(scopeType, svrName, mcpServer); err != nil {
			return err
		}

		if mcpServer.Type == config.McpStdio {
			command := strings.Join(args[1:], " ")
			cmd.Printf("Added %s MCP server %s with command: %s to %s cfg\n", mcpServer.Type, svrName, command, scope)
		} else if mcpServer.Type == config.McpSse || mcpServer.Type == config.McpHttp {
			cmd.Printf("Added %s MCP server %s with URL: %s to %s cfg\n", mcpServer.Type, svrName, mcpServer.URL, scope)
		}

		//fmt.Printf("File modified: %s\n", modifiedFile)
		return nil
	},
}

func parseMcpServer(cmd *cobra.Command, args []string) (config.McpServer, error) {
	transport, _ := cmd.Flags().GetString("transport")
	mcpType := config.McpType(transport)
	mcpServer := config.McpServer{Type: mcpType}
	switch mcpType {
	case config.McpStdio:
		mcpServer.Command = args[1]
		if len(args) > 2 {
			mcpServer.Args = args[2:]
		}
		mcpServer.Env = map[string]string{}
		envs, _ := cmd.Flags().GetStringArray("env")
		for i := 0; i < len(envs); i++ {
			parts := strings.SplitN(envs[i], "=", 2)
			if len(parts) != 2 {
				return mcpServer, fmt.Errorf("Invalid env format: '%s'. Expected format: 'KEY=value'", envs[i])
			}
			mcpServer.Env[parts[0]] = parts[1]
		}
	case config.McpSse, config.McpHttp:
		mcpServer.URL = args[1]
		headers, _ := cmd.Flags().GetStringArray("headers")
		mcpServer.Headers = map[string]string{}
		for i := 0; i < len(headers); i++ {
			parts := strings.SplitN(headers[i], ":", 2)
			if len(parts) != 2 {
				return mcpServer, fmt.Errorf("Invalid header format: '%s'. Expected format: 'Header-Name: value'", headers[i])
			}
			mcpServer.Headers[parts[0]] = parts[1]
		}
	default:
		return mcpServer, fmt.Errorf("invalid transport type: %s", transport)
	}
	return mcpServer, nil
}

func init() {
	mcpAddCmd.Flags().StringP("transport", "t", "stdio", "Transport type (stdio, sse, http) (default: \"stdio\")")
	mcpAddCmd.Flags().StringArrayP("env", "e", []string{}, "Set environment variables (e.g. -e KEY=value)")
	mcpAddCmd.Flags().StringArrayP("header", "H", []string{}, "Set HTTP headers for SSE and HTTP transports (e.g. -H \"X-Api-Key: abc123\" -H \"X-Custom: value\")")
	mcpAddCmd.Flags().StringP("scope", "s", "local", "Configuration scope (local, user, or project) (default: \"local\")")
}

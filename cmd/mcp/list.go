package mcp

import (
	"github.com/qoder-ai/qodercli/core"
	corecfg "github.com/qoder-ai/qodercli/core/config"
	"github.com/spf13/cobra"
	"strings"
)

var mcpListCmd = &cobra.Command{
	Use:   "list",
	Short: "List all MCP servers",
	Long:  `List all configured MCP servers and their status.`,
	RunE: func(cmd *cobra.Command, args []string) error {
		cmd.Print("Checking MCP server health...\n\n")
		servers, err := cfg.ListMcpServers()
		if err != nil {
			return err
		}

		statusMap := map[string]error{}
		for _, server := range servers {
			if cli, err := server.InitClient(cmd.Context()); err != nil {
				statusMap[server.Name] = err
			} else {
				_ = cli.Close()
			}
		}

		for _, server := range servers {
			status := "Connected"
			if _, ok := statusMap[server.Name]; ok {
				status = "Disconnected"
			}

			switch server.Type {
			case corecfg.McpSse:
				cmd.Printf("[SSE] %s: %s - %s\n", server.Name, server.URL, status)
			case corecfg.McpHttp:
				cmd.Printf("[HTTP] %s: %s - %s\n", server.Name, server.URL, status)
			case corecfg.McpStdio:
				args := strings.Join(server.Args, " ")
				cmd.Printf("[STDIO] %s: %s %s - %s\n", server.Name, server.Command, args, status)
			}
		}
		if len(servers) == 0 {
			cmd.Printf("No MCP servers configured. Use `%s mcp add` to add a server.", core.ReleaseName)
		}
		return nil
	},
}

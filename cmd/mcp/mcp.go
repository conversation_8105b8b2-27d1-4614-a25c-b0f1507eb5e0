package mcp

import (
	"github.com/qoder-ai/qodercli/cmd/utils"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/spf13/cobra"
)

var cfg *config.Service

var mcpCmd = &cobra.Command{
	Use:   "mcp",
	Short: "Manage MCP (Model Context Protocol) servers",
	Long:  `Manage MCP (Model Context Protocol) servers for AI model interactions.`,
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		var err error
		cfg, err = utils.ParseConfig(cmd)
		return err
	},
}

func AddSubCommand(rootCmd *cobra.Command) {
	rootCmd.AddCommand(mcpCmd)
}

func init() {
	mcpCmd.AddCommand(mcpListCmd)
	mcpCmd.AddCommand(mcpAddCmd)
	mcpCmd.AddCommand(mcpRemoveCmd)
	mcpCmd.AddCommand(mcpGetCmd)
}

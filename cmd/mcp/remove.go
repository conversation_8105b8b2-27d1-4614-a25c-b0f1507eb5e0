package mcp

import (
	corecfg "github.com/qoder-ai/qodercli/core/config"
	"github.com/spf13/cobra"
)

var mcpRemoveCmd = &cobra.Command{
	Use:   "remove [name]",
	Short: "Remove an MCP Server",
	Long:  `Remove an MCP Server by its name.`,
	Args:  cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		name := args[0]
		scope, _ := cmd.Flags().GetString("scope")
		cfgScope := corecfg.ConfigScope(scope)

		err := removeServerInScope(cmd, corecfg.ProjectScope, cfgScope, name)
		if cfgScope != "" && err != nil {
			return err
		}

		err = removeServerInScope(cmd, corecfg.LocalScope, cfgScope, name)
		if cfgScope != "" && err != nil {
			return err
		}

		err = removeServerInScope(cmd, corecfg.UserScope, cfgScope, name)
		if cfgScope != "" && err != nil {
			return err
		}
		return nil
	},
}

func removeServerInScope(cmd *cobra.Command, inScope corecfg.ConfigScope, scope corecfg.ConfigScope, name string) error {
	if scope == "" || scope == inScope {
		deleted, err := cfg.RemoveMcpServer(name, inScope)
		if scope == inScope {
			if err != nil {
				return err
			} else if deleted {
				cmd.Printf("MCP server [%s] in %s removed successfully.\n", name, inScope)
			} else {
				cmd.Printf("MCP server [%s] in %s not found.\n", name, inScope)
			}
			return nil
		}
	}
	return nil
}

func init() {
	mcpRemoveCmd.Flags().StringP("scope", "s", "", "Configuration scope (local, user, or project) - if not specified, removes from whichever scope it exists in")
}

package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qodercli/cmd/start"
	"net/http"

	"github.com/qoder-ai/qodercli/cmd/update"
	"github.com/qoder-ai/qodercli/cmd/utils"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"github.com/qoder-ai/qodercli/core/runtime"

	"github.com/spf13/cobra"
)

var application *runtime.AppRuntime

var serverCmd = &cobra.Command{
	Use:   "server",
	Short: fmt.Sprintf("Start %s server", core.AppName),
	Long:  fmt.Sprintf(`Start %s as a server to provide AI assistance services.`, core.AppName),
	RunE: func(cmd *cobra.Command, args []string) error {
		port, _ := cmd.Flags().GetString("port")
		cfg, err := utils.ParseConfig(cmd)
		if err != nil {
			logging.Error("Failed to parse config: %v", err)
			return err
		}

		// 在后台调度自动更新（静默，无输出）
		update.ScheduleAutoUpdateIfEnabled(cfg)

		cfg.SkipAllPermissions = true
		app, err := runtime.NewAppRuntime(cmd.Context(), cfg)
		if err != nil {
			logging.Error("Failed to create app: %v", err)
			return err
		}
		startWebServer(cmd.Context(), app, port)
		application = app
		return nil
	},
	PostRunE: func(cmd *cobra.Command, args []string) error {
		if application != nil {
			application.Shutdown()
		}
		return nil
	},
}

// corsMiddleware adds CORS headers to all responses
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "*")
		w.Header().Set("Access-Control-Allow-Headers", "*")
		w.Header().Set("Access-Control-Allow-Credentials", "true")
		w.Header().Set("Access-Control-Max-Age", "86400") // 24 hours

		// Handle preflight requests
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func startWebServer(ctx context.Context, app *runtime.AppRuntime, port string) {
	mux := http.NewServeMux()
	mux.HandleFunc("/events", func(w http.ResponseWriter, r *http.Request) {
		prompt := r.URL.Query().Get("prompt")
		sessionID := r.URL.Query().Get("resume")

		// 设置SSE头
		w.Header().Set("Content-Type", "text/event-stream")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("Connection", "keep-alive")
		flusher, ok := w.(http.Flusher)
		if !ok {
			http.Error(w, "Streaming unsupported", http.StatusInternalServerError)
			return
		}

		// Run non-interactive flow using the App method
		ch, cancelSubs := start.SetupSubscriptions(app, ctx)
		defer cancelSubs()
		go outputSseStream(ch, w, flusher)
		_ = app.RunNonInteractive(ctx, prompt, sessionID)
	})

	// Apply CORS middleware to the mux
	handler := corsMiddleware(mux)

	addr := ":" + port
	logging.Info("SSE server listening on", "addr", addr)
	if err := http.ListenAndServe(addr, handler); err != nil {
		logging.Error("SSE server error: %v", err)
	}
}

func outputSseStream(ch <-chan tea.Msg, w http.ResponseWriter, flusher http.Flusher) {
	defer func() {
		if r := recover(); r != nil {
			logging.Error("Recovered from panic in outputSseStream: %v", r)
		}
	}()

	for {
		msg, ok := <-ch
		if !ok {
			return
		}
		switch msg := msg.(type) {
		case pubsub.Event[message.Message]:
			evtType := agent.AgentEventTypeAssistant
			if msg.Payload.Role == message.User {
				continue
			} else if msg.Payload.Role == message.Tool {
				evtType = agent.AgentEventTypeUser
			}
			agentEvent := agent.Event{
				Config:    nil,
				Type:      evtType,
				Subtype:   "stream",
				Message:   &msg.Payload,
				SessionId: msg.Payload.SessionId,
			}
			bytes, err := json.Marshal(agentEvent)
			if err != nil {
				logging.Error("Failed to marshal message: %v", err)
				continue
			}
			_, _ = fmt.Fprintf(w, "data: %s\n\n", string(bytes))
			flusher.Flush()
		case pubsub.Event[core.Session]:

		case pubsub.Event[agent.Event]:
			bytes, err := json.Marshal(msg.Payload)
			if err != nil {
				logging.Error("Failed to marshal message: %v", err)
				continue
			}
			_, _ = fmt.Fprintf(w, "data: %s\n\n", string(bytes))
			flusher.Flush()
		default:
		}
	}

}

func init() {
	// Add port flag for server command
	serverCmd.Flags().StringP("port", "p", "8128", "Port for the server to listen on")
}

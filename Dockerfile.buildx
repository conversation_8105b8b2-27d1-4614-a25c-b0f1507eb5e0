# Qoder CLI 多平台原生构建 - 使用 Docker Buildx 分平台构建
# 支持 linux/amd64, linux/arm64, darwin/amd64, darwin/arm64
FROM golang:alpine AS builder

# 安装构建依赖和 CGO 所需工具
RUN apk add --no-cache git zip make bash ca-certificates \
    gcc musl-dev && \
    update-ca-certificates

# 设置构建参数
ARG TARGETOS
ARG TARGETARCH  
ARG VERSION
ARG UPDATE_BASE_URL

# 代理环境变量支持
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY

# 设置标准工作目录
WORKDIR /build

# 使用在线模式，支持模块下载
ENV GOPROXY=https://proxy.golang.org,direct
ENV GOSUMDB=sum.golang.org
ENV CGO_ENABLED=1

# 设置代理环境变量（如果提供的话）
ENV HTTP_PROXY=${HTTP_PROXY}
ENV HTTPS_PROXY=${HTTPS_PROXY}
ENV NO_PROXY=${NO_PROXY}

# 复制依赖文件用于缓存优化
COPY go.mod go.sum ./

# 复制源代码
COPY . .

# 所有平台都禁用 CGO（Linux 不需要 CGO，使用外部命令）
RUN echo "构建 ${TARGETOS}/${TARGETARCH}，禁用 CGO（Linux 使用 xclip/xsel 外部命令）" && \
    CGO_ENABLED=0 GOOS=${TARGETOS} GOARCH=${TARGETARCH} go build -buildvcs=false \
      -ldflags "-s -w -X github.com/qoder-ai/qodercli/core/version.Version=${VERSION} -X github.com/qoder-ai/qodercli/core/version.UpdateBaseURL=${UPDATE_BASE_URL}" \
      -o qodercli$(if [ "${TARGETOS}" = "windows" ]; then echo ".exe"; fi) \
      .

# 创建压缩包
RUN zip -j qodercli_${VERSION}_${TARGETOS}_${TARGETARCH}.zip qodercli$(if [ "${TARGETOS}" = "windows" ]; then echo ".exe"; fi)

# 导出构建产物
FROM scratch AS export
COPY --from=builder /build/qodercli_*.zip /
COPY --from=builder /build/qodercli* /
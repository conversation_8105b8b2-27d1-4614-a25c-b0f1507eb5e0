# Qoder CLI

[![Go Version](https://img.shields.io/github/go-mod/go-version/qoder-ai/qoder-cli)](https://golang.org/)
[![GitHub release (latest SemVer)](https://img.shields.io/github/v/release/qoder-ai/qoder-cli)](https://github.com/qoder-ai/qodercli/releases)
[![License](https://img.shields.io/github/license/qoder-ai/qoder-cli)](LICENSE)

Qoder CLI is a powerful terminal-based AI assistant that helps with software development tasks. It provides an interactive chat interface with AI capabilities and code analysis integration to assist developers in writing, debugging, and understanding code directly from the terminal.

## Features

- 🤖 **AI-Powered Assistance**: Get intelligent help with coding tasks, explanations, and suggestions
- 💬 **Interactive Chat Interface**: Engage in natural conversations with the AI assistant
- 📁 **Code Analysis**: Analyze codebases and get insights about your project
- 🔧 **Tool Integration**: Built-in tools for file operations, web searches, and more
- 🎨 **Beautiful TUI**: Modern terminal user interface with themes and customization
- 🌐 **Multiple AI Providers**: Support for various AI models including <PERSON><PERSON>, <PERSON>, and more
- 📦 **MCP Support**: Model Context Protocol integration for extended capabilities

## Installation

### Using Go

```bash
go install github.com/qoder-ai/qodercli@latest
```

### From Source

```bash
git clone https://github.com/qoder-ai/qodercli.git
cd qoder-cli
go build -o qodercli .
```

### Docker Build (Production)

Production builds use Docker to hide local paths in error messages:

```bash
# Production build (Docker required)
QODERCLI_RELEASE_MODE=prod \
QODERCLI_OSS_BUCKET=my-bucket \
QODERCLI_OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com \
QODERCLI_PUBLIC_BASE_URL=https://cdn.example.com/myproject \
OSS_ACCESS_KEY_ID=xxx \
OSS_ACCESS_KEY_SECRET=yyy \
bash scripts/release.sh

# Development with Docker  
bash scripts/release.sh --use-docker

# Development local build
bash scripts/release.sh --no-docker
```

**Config:** `OSS_BUCKET` is bucket name, `PUBLIC_BASE_URL` is full access URL, path prefix extracted automatically.

## Quick Start

1. Set up your API key as an environment variable:
   ```bash
   export DASHSCOPE_API_KEY=your_api_key_here  # For Qwen models
   # or
   export IDEALAB_API_KEY=your_api_key_here    # For Idealab models
   ```

2. Run Qoder CLI in interactive mode:
   ```bash
   qodercli
   ```

3. Or run a single command:
   ```bash
   qodercli -p "Explain the use of context in Go"
   ```

## Usage

### Interactive Mode

Run Qoder CLI in interactive mode for a full chat experience:

```bash
qodercli
```

### Non-Interactive Mode

Run a single prompt and get the response:

```bash
qodercli -p "How do I implement a binary search in Python?"
```

### Options

- `-d, --debug`: Enable debug logging
- `-c, --cwd`: Set current working directory
- `-r, --resume`: Resume a previous conversation
- `-p, --print`: Print response and exit (useful for pipes)
- `-v, --version`: Show version information

## Configuration

Qoder CLI can be configured through a JSON file located at `~/.qodercli.json`. Example configuration:

```json
{
  "contextPaths": [
    ".github/copilot-instructions.md",
    ".cursorrules",
    "CLAUDE.md"
  ],
  "tui": {
    "theme": "tron"
  },
  "providers": {
    "openai": {
      "apiKey": "your-api-key"
    }
  }
}
```

## Supported AI Models

- Qwen series (Qwen3CoderPlus, etc.)
- Claude models (Sonnet 4, etc.)
- Custom models through Model Context Protocol (MCP)

## Commands

- `qodercli` - Start interactive mode
- `qodercli mcp` - Manage MCP servers
- `qodercli -p "prompt"` - Run a single prompt
- `qodercli --help` - Show help information

## Examples

Add a new MCP server
- `qodercli mcp add playwrite -- npx -y @playwright/mcp@latest`

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the Qoder License - see the [LICENSE](LICENSE) file for details.
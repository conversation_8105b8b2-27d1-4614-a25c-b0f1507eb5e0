#!/usr/bin/env bash

set -euo pipefail

# Qoder CLI release script
# - 从当前仓库构建多平台 zip 包
# - 生成 checksums.txt 与 manifest.json  
# - 上传到 OSS（需安装并可用 ossutil）
# - 支持 Docker 构建（隐藏本地路径，生产环境强制）
#
# Docker 构建用法：
# 生产：QODERCLI_RELEASE_MODE=prod bash scripts/release.sh
# 开发：bash scripts/release.sh --use-docker / --no-docker
#
# 依赖：go、zip、shasum/sha256sum、sed、docker（可选）

########################################
# 配置与环境变量
########################################

# 发布模式，默认开发环境(dev)。生产(prod) 下将强制要求外部提供配置
# 最终版专用变量（不再兼容旧名）：
# QODERCLI_RELEASE_MODE, QODERCLI_RELEASE_VERSION,
# QODERCLI_OSS_ENDPOINT, QODERCLI_OSS_BUCKET, QODERCLI_PUBLIC_BASE_URL,
# QODERCLI_BUILD_WINDOWS, QODERCLI_KEEP_LOCAL, QODERCLI_OSSUTIL_CMD,
# QODERCLI_USE_DOCKER, QODERCLI_DOCKER_IMAGE

MODE=${QODERCLI_RELEASE_MODE:-dev}
KEEP_LOCAL=${QODERCLI_KEEP_LOCAL:-0}
YES=${QODERCLI_YES:-0}
OSS_ENDPOINT=${QODERCLI_OSS_ENDPOINT:-}
OSS_BUCKET=${QODERCLI_OSS_BUCKET:-}
PUBLIC_BASE_URL=${QODERCLI_PUBLIC_BASE_URL:-}
BUILD_WINDOWS=${QODERCLI_BUILD_WINDOWS:-0}
VERSION=${QODERCLI_RELEASE_VERSION:-${VERSION:-}}
RELEASE_TYPE=${QODERCLI_RELEASE_TYPE:-artifacts}
USE_BUILDX=${QODERCLI_USE_BUILDX:-1}  # 默认使用 Buildx
BUILDX_BUILDER_NAME=${QODERCLI_BUILDX_BUILDER:-qodercli-builder}

# 开发环境默认配置（如果环境变量未设置才使用默认值）
if [[ "$MODE" == "dev" ]]; then
  : "${OSS_ENDPOINT:=oss-cn-hangzhou.aliyuncs.com}"
  # OSS_BUCKET 只是存储桶名称
  : "${OSS_BUCKET:=qs-cli-dev}"
  # PUBLIC_BASE_URL 是完整的公共访问基址，包含项目路径
  : "${PUBLIC_BASE_URL:=https://qs-cli-dev.oss-cn-hangzhou.aliyuncs.com/qodercli}"
fi

# 生产环境必须显式设置
if [[ "$MODE" == "prod" ]]; then
  : "${OSS_BUCKET:?必须设置 OSS_BUCKET，例如: my-bucket}"
  : "${OSS_ENDPOINT:?必须设置 OSS_ENDPOINT，例如: oss-cn-hangzhou.aliyuncs.com}"
  : "${PUBLIC_BASE_URL:?必须设置 PUBLIC_BASE_URL，例如: https://cdn.example.com/myproject}"
fi

# 检查 Docker 和 Buildx 可用性（Buildx 构建必需）
if [[ "$USE_BUILDX" == "1" ]]; then
  if ! command -v docker >/dev/null 2>&1; then
    echo "错误：Buildx 构建需要 docker 命令" >&2
    exit 1
  fi
  if ! docker info >/dev/null 2>&1; then
    echo "错误：Docker 守护进程不可用" >&2
    exit 1
  fi
  if ! docker buildx version >/dev/null 2>&1; then
    echo "错误：Docker Buildx 不可用，请安装或启用 Buildx" >&2
    exit 1
  fi
fi

# 从 PUBLIC_BASE_URL 提取 OSS 对象路径前缀
# 支持多种格式:
# https://domain.com/path -> path
# http://domain.com/path -> path  
# domain.com/path -> path
# domain.com/path/subpath -> path/subpath
if [[ -n "$PUBLIC_BASE_URL" ]]; then
  # 移除可能的协议前缀
  temp_url=$(echo "$PUBLIC_BASE_URL" | sed -E 's|^https?://||')
  
  # 检查是否包含路径部分（域名后有 /）
  if [[ "$temp_url" =~ ^[^/]+/(.+)$ ]]; then
    # 提取域名后的路径部分，去掉尾部的 /
    OSS_PATH_PREFIX=$(echo "$temp_url" | sed -E 's|^[^/]+/||' | sed 's|/$||')
  else
    # 没有路径部分，使用默认前缀
    OSS_PATH_PREFIX="qodercli"
  fi
  
  # 确保提取到了有效路径
  if [[ -z "$OSS_PATH_PREFIX" ]]; then
    OSS_PATH_PREFIX="qodercli"
  fi
else
  OSS_PATH_PREFIX="qodercli"
fi

# 可选：指定 ossutil 的命令名或路径（默认: 自动在 PATH 中查找 ossutil）
OSSUTIL_CMD=${QODERCLI_OSSUTIL_CMD:-ossutil}
USE_OSSUTIL=0
if command -v "$OSSUTIL_CMD" >/dev/null 2>&1; then
  USE_OSSUTIL=1
fi

# 版本号：优先使用环境变量 VERSION；否则使用 git tag（要求 exact-match）
if [[ -z "${VERSION:-}" ]]; then
  if VERSION=$(git describe --tags --exact-match 2>/dev/null); then
    :
  else
    echo "错误：未设置 VERSION，且当前提交没有 exact-match 的 git tag。请先打 tag 或导出 VERSION。" >&2
    exit 1
  fi
fi

VERSION_STRIPPED=${VERSION#v}
PUBLISHED_AT=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
# Git 信息（用于确认与可选元数据写入）
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
GIT_COMMIT_SHORT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
if [[ -n "$(git status --porcelain 2>/dev/null)" ]]; then
  GIT_COMMIT_DISPLAY="${GIT_COMMIT_SHORT}-dirty"
else
  GIT_COMMIT_DISPLAY="${GIT_COMMIT_SHORT}"
fi

REPO_ROOT=$(cd "$(dirname "$0")/.." && pwd)
BUILD_DIR="$REPO_ROOT/.build"
DIST_DIR="$REPO_ROOT/.dist/$VERSION"
INSTALL_TEMPLATE="$REPO_ROOT/scripts/install.sh"
INSTALL_TMP="$BUILD_DIR/install.sh"

mkdir -p "$BUILD_DIR" "$DIST_DIR"

########################################
# 统一清理
########################################

cleanup() {
  # 清理 Buildx 资源
  cleanup_buildx
  
  # 保留本地构建产物时不清理
  if [[ ${KEEP_LOCAL:-0} -ne 0 ]]; then
    return
  fi
  # 尽量安静清理
  rm -rf "$BUILD_DIR" "$DIST_DIR" 2>/dev/null || true
}

# 默认在退出时清理（无论成功或失败）
trap cleanup EXIT

########################################
# 参数与帮助
########################################
print_help() {
  cat <<EOF
Qoder CLI 发布脚本

用法: bash scripts/release.sh [选项]

选项:
  --keep-local               发布结束后保留本地构建产物（默认不保留）
  --mode <dev|prod>          覆盖 MODE 环境变量，默认 dev
  --version <vX.Y.Z>         覆盖 VERSION 环境变量（否则读取当前提交的 git tag）
  --release-type <type>      发布类型，可选值：
                               artifacts  - 只发布版本产物（默认）
                               install    - 只发布安装脚本
                               all        - 发布所有内容（版本产物 + 安装脚本）
  --use-buildx               启用 Docker Buildx 多平台构建（默认启用）
  --no-buildx               禁用 Buildx，使用本地构建
  --yes                      非交互确认，跳过人工确认
  -h, --help                 显示本帮助

环境变量:
  QODERCLI_RELEASE_MODE      dev(默认) 或 prod；prod 模式下需显式配置下列变量
  QODERCLI_RELEASE_VERSION   发布版本（如 v1.2.3）；若未提供且当前提交有 tag，则取 exact-match tag
  QODERCLI_RELEASE_TYPE      发布类型：artifacts(默认)|install|all
  QODERCLI_OSS_ENDPOINT      OSS 访问域（dev 默认 oss-cn-hangzhou.aliyuncs.com）
  QODERCLI_OSS_BUCKET        OSS Bucket 名称（dev 默认 qs-cli-dev）
  QODERCLI_PUBLIC_BASE_URL   对外访问基址（包含项目路径，dev 默认 https://qs-cli-dev.oss-cn-hangzhou.aliyuncs.com/qodercli）
  OSS_ACCESS_KEY_ID          上传凭证（必需）
  OSS_ACCESS_KEY_SECRET      上传凭证（必需）
  QODERCLI_OSSUTIL_CMD       指定 ossutil 命令路径（存在则优先使用）
  QODERCLI_BUILD_WINDOWS     设为 1 时构建 Windows 包（默认 0）
  QODERCLI_KEEP_LOCAL        设为 1 时保留本地构建产物
  QODERCLI_YES               设为 1 时跳过人工确认
  QODERCLI_USE_BUILDX        设为 1 启用 Buildx 构建，0 禁用 Buildx（默认 1）
  QODERCLI_BUILDX_BUILDER    Buildx 构建器名称（默认 qodercli-builder）
  QODERCLI_KEEP_BUILDX       设为 1 时在开发环境保留 Buildx 构建器实例

示例:
  # 默认：只发布版本产物，使用当前提交 tag 作为版本
  OSS_ACCESS_KEY_ID=xxx OSS_ACCESS_KEY_SECRET=yyy bash scripts/release.sh

  # 只发布安装脚本
  bash scripts/release.sh --release-type install

  # 发布所有内容（版本产物 + 安装脚本）
  bash scripts/release.sh --release-type all

  # 指定版本并保留本地产物
  QODERCLI_RELEASE_VERSION=v1.0.0 QODERCLI_KEEP_LOCAL=1 bash scripts/release.sh

  # 使用 Docker Buildx 多平台构建（默认方式）
  OSS_ACCESS_KEY_ID=xxx OSS_ACCESS_KEY_SECRET=yyy bash scripts/release.sh

  # 生产环境发布所有内容
  QODERCLI_RELEASE_MODE=prod QODERCLI_OSS_ENDPOINT=oss-cn-xxx.aliyuncs.com QODERCLI_OSS_BUCKET=my-bucket \
  QODERCLI_PUBLIC_BASE_URL=https://cdn.example.com/myproject \
  OSS_ACCESS_KEY_ID=xxx OSS_ACCESS_KEY_SECRET=yyy bash scripts/release.sh --release-type all
EOF
}

ARGS=()
while [[ $# -gt 0 ]]; do
  case "$1" in
    --keep-local)
      KEEP_LOCAL=1; shift ;;
    --mode)
      MODE="$2"; shift 2 ;;
    --version)
      VERSION="$2"; shift 2 ;;
    --release-type)
      RELEASE_TYPE="$2"; shift 2 ;;
    --use-buildx)
      USE_BUILDX=1; shift ;;
    --no-buildx)
      USE_BUILDX=0; shift ;;
    --yes)
      YES=1; shift ;;
    -h|--help)
      print_help; exit 0 ;;
    *)
      ARGS+=("$1"); shift ;;
  esac
done
set -- "${ARGS[@]:-}"

# 验证 RELEASE_TYPE 参数
case "$RELEASE_TYPE" in
  artifacts|install|all)
    ;;
  *)
    echo "错误：无效的发布类型 '$RELEASE_TYPE'。允许的值：artifacts, install, all" >&2
    exit 1
    ;;
esac

########################################
# 执行前打印配置
########################################
echo "运行配置:"
echo "  MODE=${MODE}  VERSION=${VERSION}  RELEASE_TYPE=${RELEASE_TYPE}"
echo "  KEEP_LOCAL=${KEEP_LOCAL}  BUILD_WINDOWS=${BUILD_WINDOWS:-0}  USE_BUILDX=${USE_BUILDX:-0}"
echo "  GIT_BRANCH=${GIT_BRANCH}  GIT_COMMIT=${GIT_COMMIT_DISPLAY}"
echo "  OSS_ENDPOINT=${OSS_ENDPOINT:-}  OSS_BUCKET=${OSS_BUCKET:-}"
echo "  PUBLIC_BASE_URL=${PUBLIC_BASE_URL:-}"
echo "  OSS_PATH_PREFIX=${OSS_PATH_PREFIX}"
if [[ $USE_BUILDX -eq 1 ]]; then
  echo "  构建方式=Docker Buildx ($BUILDX_BUILDER_NAME)"
else
  echo "  构建方式=本地构建"
fi
echo "  上传工具=$(command -v \"$OSSUTIL_CMD\" >/dev/null 2>&1 && echo ossutil || echo internal-uploader)"

########################################
# 预拉取远端 latest 与 manifest，并二次确认
########################################
REMOTE_LATEST=""
REMOTE_MANIFEST_URL="${PUBLIC_BASE_URL}/channels/manifest.json"
REMOTE_LATEST_URL="${PUBLIC_BASE_URL}/channels/latest.txt"

if command -v curl >/dev/null 2>&1; then
  REMOTE_LATEST=$(curl -fsS "$REMOTE_LATEST_URL" || true)
fi

if [[ -n "$REMOTE_LATEST" ]]; then
  echo "远端 latest: $REMOTE_LATEST"
else
  echo "远端 latest: (无法获取或不存在)"
fi

if [[ ${YES} -ne 1 ]]; then
  echo "即将发布: VERSION=${VERSION} BRANCH=${GIT_BRANCH} COMMIT=${GIT_COMMIT_DISPLAY}" \
       " -> 目标: ${PUBLIC_BASE_URL} ENDPOINT=${OSS_ENDPOINT} BUCKET=${OSS_BUCKET}" \
       " (远端 latest: ${REMOTE_LATEST:-N/A})"
  read -r -p "确认继续发布? [y/N] " ans
  case "$ans" in
    y|Y|yes|YES) ;;
    *) echo "已取消"; exit 1 ;;
  esac
fi

########################################
# 检查依赖
########################################

require_cmd() {
  if ! command -v "$1" >/dev/null 2>&1; then
    echo "缺少依赖：$1" >&2
    exit 1
  fi
}

require_cmd go
require_cmd zip

if command -v shasum >/dev/null 2>&1; then
  SHASUM_CMD="shasum -a 256"
elif command -v sha256sum >/dev/null 2>&1; then
  SHASUM_CMD="sha256sum"
else
  echo "缺少依赖：shasum 或 sha256sum" >&2
  exit 1
fi

# 若未安装 ossutil，将回退使用 scripts/ossput.go 进行上传
if [[ $USE_OSSUTIL -eq 0 ]]; then
  echo "信息：未检测到 ${OSSUTIL_CMD}，发布将回退使用内部上传器 scripts/ossput.go。"
fi

########################################
# Buildx 构建器初始化
########################################

setup_buildx() {
  if [[ $USE_BUILDX -ne 1 ]]; then
    return 0
  fi

  echo "==> 设置 Docker Buildx 构建器 ..."
  
  # 在 macOS 环境下，优先使用系统默认的构建器（如 OrbStack）
  if [[ "$(uname)" == "Darwin" ]]; then
    # 检查是否有可用的 docker 驱动构建器
    local available_builder=""
    if docker buildx inspect orbstack >/dev/null 2>&1; then
      available_builder="orbstack"
      echo "    检测到 OrbStack 构建器，使用现有的 orbstack 构建器"
    elif docker buildx inspect default >/dev/null 2>&1; then
      available_builder="default"
      echo "    使用默认构建器: default"
    fi
    
    if [[ -n "$available_builder" ]]; then
      # 使用现有的构建器
      if ! docker buildx use "$available_builder"; then
        echo "警告：切换到 $available_builder 构建器失败" >&2
      else
        echo "    ✅ 使用 $available_builder 构建器（继承系统网络配置）"
        return 0
      fi
    fi
  fi
  
  # 检查是否已存在自定义构建器
  if docker buildx inspect "$BUILDX_BUILDER_NAME" >/dev/null 2>&1; then
    echo "    构建器 $BUILDX_BUILDER_NAME 已存在，使用现有构建器"
  else
    echo "    创建新的构建器 $BUILDX_BUILDER_NAME ..."
    # 创建构建器时继承网络配置
    BUILDX_CREATE_OPTS=(
      --name "$BUILDX_BUILDER_NAME"
      --driver docker-container
    )
    
    # Linux 环境：使用主机网络
    if [[ "$(uname)" != "Darwin" ]]; then
      BUILDX_CREATE_OPTS+=(--driver-opt network=host)
    fi
    
    # 传递代理环境变量到构建器
    if [[ -n "${HTTP_PROXY:-}" ]]; then
      BUILDX_CREATE_OPTS+=(--driver-opt env.HTTP_PROXY="$HTTP_PROXY")
      echo "    传递 HTTP_PROXY: $HTTP_PROXY"
    fi
    if [[ -n "${HTTPS_PROXY:-}" ]]; then
      BUILDX_CREATE_OPTS+=(--driver-opt env.HTTPS_PROXY="$HTTPS_PROXY")
      echo "    传递 HTTPS_PROXY: $HTTPS_PROXY"
    fi
    if [[ -n "${NO_PROXY:-}" ]]; then
      BUILDX_CREATE_OPTS+=(--driver-opt env.NO_PROXY="$NO_PROXY")
      echo "    传递 NO_PROXY: $NO_PROXY"
    fi
    
    # 添加 --use 选项
    BUILDX_CREATE_OPTS+=(--use)
    
    if ! docker buildx create "${BUILDX_CREATE_OPTS[@]}"; then
      echo "警告：创建 Buildx 构建器失败，回退到传统 Docker 构建" >&2
      USE_BUILDX=0
      return 1
    fi
  fi
  
  # 切换到目标构建器
  if ! docker buildx use "$BUILDX_BUILDER_NAME"; then
    echo "警告：切换到 Buildx 构建器失败，回退到传统 Docker 构建" >&2
    USE_BUILDX=0
    return 1
  fi
  
  # 启动构建器实例
  echo "    启动构建器实例 ..."
  if ! docker buildx inspect --bootstrap >/dev/null 2>&1; then
    echo "警告：启动 Buildx 构建器失败，回退到传统 Docker 构建" >&2
    USE_BUILDX=0
    return 1
  fi
  
  echo "    ✅ Buildx 构建器准备就绪"
  return 0
}

cleanup_buildx() {
  if [[ $USE_BUILDX -eq 1 ]] && command -v docker >/dev/null 2>&1; then
    # 清理 Buildx 构建器（如果是临时创建的）
    # 不清理系统默认构建器（如 orbstack、default）
    if [[ "$BUILDX_BUILDER_NAME" != "orbstack" && "$BUILDX_BUILDER_NAME" != "default" ]]; then
      if docker buildx inspect "$BUILDX_BUILDER_NAME" >/dev/null 2>&1; then
        # 仅在开发环境清理，生产环境保留构建器以便重用
        if [[ "$MODE" == "dev" && "${QODERCLI_KEEP_BUILDX:-0}" != "1" ]]; then
          docker buildx rm "$BUILDX_BUILDER_NAME" >/dev/null 2>&1 || true
        fi
      fi
    fi
  fi
}

########################################
# 构建矩阵
########################################

if [[ "$RELEASE_TYPE" == "artifacts" || "$RELEASE_TYPE" == "all" ]]; then
  echo "==> 开始构建版本产物 ..."
  
  OS_ARCHES=(
    "darwin arm64"
    "darwin amd64"
    "linux arm64"
    "linux amd64"
  )
  # 如需构建 Windows，请设置 BUILD_WINDOWS=1
  if [[ ${BUILD_WINDOWS:-0} -eq 1 ]]; then
    OS_ARCHES+=("windows amd64")
  fi

  CHECKSUMS_FILE="$DIST_DIR/checksums.txt"
  > "$CHECKSUMS_FILE"

  ARTIFACTS_JSON_ENTRIES=()

  # 本机构建 macOS 目标
  build_native_macos() {
    local goos="$1"
    local goarch="$2"
    
    echo "    构建 $goos/$goarch (本机原生构建) ..."
    
    local output_dir="$BUILD_DIR/$goos-$goarch"
    mkdir -p "$output_dir"
    
    # 本机构建，启用 CGO，使用 -trimpath
    local binary_name="qodercli"
    CGO_ENABLED=1 GOOS="$goos" GOARCH="$goarch" go build \
      -buildvcs=false \
      -trimpath \
      -ldflags "-s -w -X github.com/qoder-ai/qodercli/core/version.Version=${VERSION} -X github.com/qoder-ai/qodercli/core/version.UpdateBaseURL=${PUBLIC_BASE_URL}" \
      -o "$output_dir/$binary_name" \
      .
    
    if [ $? -ne 0 ]; then
      echo "    ❌ 本机构建 $goos/$goarch 失败"
      return 1
    fi
    
    # 创建压缩包
    local zip_name="qodercli_${VERSION}_${goos}_${goarch}.zip"
    cd "$output_dir"
    zip -j "$zip_name" "$binary_name"
    cd - >/dev/null
    
    # 移动到分发目录并计算校验和
    local final_zip_path="$DIST_DIR/$zip_name"
    mv "$output_dir/$zip_name" "$final_zip_path"
    
    local sha256
    sha256=$($SHASUM_CMD "$final_zip_path" | awk '{print $1}')
    local size
    size=$(wc -c < "$final_zip_path" | tr -d ' ')
    echo "$sha256  $zip_name" >> "$CHECKSUMS_FILE"

    local url="$PUBLIC_BASE_URL/releases/$VERSION/$zip_name"
    ARTIFACTS_JSON_ENTRIES+=(
      "{\"os\":\"$goos\",\"arch\":\"$goarch\",\"url\":\"$url\",\"sha256\":\"$sha256\",\"size\":$size}"
    )
    
    echo "    ✅ $goos/$goarch 本机构建完成"
    return 0
  }

  # 使用混合构建策略：macOS 本机构建 + Linux Docker 构建
  build_with_buildx() {
    echo "==> 使用混合构建策略 (macOS 本机 + Linux Docker) ..."
    
    local current_os=$(uname -s | tr '[:upper:]' '[:lower:]')
    
    for item in "${OS_ARCHES[@]}"; do
      local goos=$(echo "$item" | awk '{print $1}')
      local goarch=$(echo "$item" | awk '{print $2}')
      
      # macOS 平台且当前系统是 macOS：使用本机构建
      if [[ "$goos" == "darwin" && "$current_os" == "darwin" ]]; then
        if ! build_native_macos "$goos" "$goarch"; then
          return 1
        fi
        continue
      fi
      
      # Windows 暂时跳过
      if [[ "$goos" == "windows" ]]; then
        echo "    ⏭️  跳过 $goos/$goarch (暂不支持)"
        continue
      fi
      
      # Linux 平台：使用 Docker 构建
      if [[ "$goos" == "linux" ]]; then
        # 设置 Buildx 构建器（仅在需要时）
        if ! command -v docker >/dev/null 2>&1 || ! docker buildx version >/dev/null 2>&1; then
          echo "Docker Buildx 不可用，跳过 $goos/$goarch" >&2
          continue
        fi
        
        if ! setup_buildx; then
          echo "Buildx 设置失败，跳过 $goos/$goarch" >&2
          continue
        fi
        
        local build_platform
        case "$goarch" in
          "arm64") build_platform="linux/arm64" ;;
          "amd64") build_platform="linux/amd64" ;;
          *) build_platform="linux/amd64" ;;
        esac
        
        echo "    构建 $goos/$goarch (Docker 在 $build_platform 平台上) ..."
        
        # 使用 buildx 构建并导出
        local output_dir="$BUILD_DIR/$goos-$goarch"
        mkdir -p "$output_dir"
        
        # 构建时传递代理环境变量和目标平台信息
        BUILDX_BUILD_OPTS=(
          --file "$REPO_ROOT/Dockerfile.buildx"
          --platform "$build_platform"
          --build-arg TARGETOS="$goos"
          --build-arg TARGETARCH="$goarch"
          --build-arg VERSION="$VERSION"
          --build-arg UPDATE_BASE_URL="$PUBLIC_BASE_URL"
          --output type=local,dest="$output_dir"
        )
        
        # 传递代理环境变量到构建过程
        if [[ -n "${HTTP_PROXY:-}" ]]; then
          BUILDX_BUILD_OPTS+=(--build-arg HTTP_PROXY="$HTTP_PROXY")
        fi
        if [[ -n "${HTTPS_PROXY:-}" ]]; then
          BUILDX_BUILD_OPTS+=(--build-arg HTTPS_PROXY="$HTTPS_PROXY")
        fi
        if [[ -n "${NO_PROXY:-}" ]]; then
          BUILDX_BUILD_OPTS+=(--build-arg NO_PROXY="$NO_PROXY")
        fi
        
        if ! docker buildx build "${BUILDX_BUILD_OPTS[@]}" "$REPO_ROOT"; then
          echo "    ❌ Buildx 构建 $goos/$goarch 失败"
          return 1
        fi
        
        # 验证构建产物
        local zip_name="qodercli_${VERSION}_${goos}_${goarch}.zip"
        local zip_path="$output_dir/$zip_name"
        
        if [[ ! -f "$zip_path" ]]; then
          echo "    ❌ 构建产物不存在: $zip_path"
          ls -la "$output_dir" || true
          return 1
        fi
        
        # 移动到分发目录
        local final_zip_path="$DIST_DIR/$zip_name"
        mv "$zip_path" "$final_zip_path"
        
        # 计算校验和
        local sha256
        sha256=$($SHASUM_CMD "$final_zip_path" | awk '{print $1}')
        local size
        size=$(wc -c < "$final_zip_path" | tr -d ' ')
        echo "$sha256  $zip_name" >> "$CHECKSUMS_FILE"

        local url="$PUBLIC_BASE_URL/releases/$VERSION/$zip_name"
        ARTIFACTS_JSON_ENTRIES+=(
          "{\"os\":\"$goos\",\"arch\":\"$goarch\",\"url\":\"$url\",\"sha256\":\"$sha256\",\"size\":$size}"
        )
        
        echo "    ✅ $goos/$goarch 构建完成"
      fi
    done
    
    return 0
  }



  # 使用 Docker Buildx 构建
  if [[ $USE_BUILDX -eq 1 ]]; then
    if ! build_with_buildx; then
      echo "❌ Buildx 构建失败" >&2
      exit 1
    fi
  else
    echo "❌ 错误：当前版本仅支持 Buildx 构建，请启用 USE_BUILDX=1" >&2
    exit 1
  fi
else
  echo "==> 跳过版本产物构建(RELEASE_TYPE=$RELEASE_TYPE)"
  ARTIFACTS_JSON_ENTRIES=()
fi

########################################
# 生成 manifest.json 与 latest.txt
########################################

if [[ "$RELEASE_TYPE" == "artifacts" || "$RELEASE_TYPE" == "all" ]]; then
  echo "==> 生成 manifest.json 和 latest.txt ..."
  
  MANIFEST_FILE="$DIST_DIR/manifest.json"
  {
    echo "{";
    echo "  \"latest\": \"$VERSION\",";
    echo "  \"published_at\": \"$PUBLISHED_AT\",";
    echo "  \"files\": [";
    first=1
    for entry in "${ARTIFACTS_JSON_ENTRIES[@]}"; do
      if [[ $first -eq 1 ]]; then
        echo "    $entry";
        first=0
      else
        echo "    ,$entry";
      fi
    done
    echo "  ]";
    echo "}";
  } > "$MANIFEST_FILE"

  LATEST_FILE="$DIST_DIR/latest.txt"
  echo "$VERSION" > "$LATEST_FILE"
else
  echo "==> 跳过 manifest.json 和 latest.txt 生成(RELEASE_TYPE=$RELEASE_TYPE)"
fi

########################################
# 预处理 install.sh（注入 BASE_URL）
########################################

if [[ "$RELEASE_TYPE" == "install" || "$RELEASE_TYPE" == "all" ]]; then
  echo "==> 处理安装脚本 ..."
  
  if [[ -f "$INSTALL_TEMPLATE" ]]; then
    # 替换占位符 __BASE_URL__ 为 PUBLIC_BASE_URL
    # 避免 BSD sed -i 兼容性问题，这里使用临时文件重定向，避免产生备份文件（如 install.sh-e）
    esc_base=$(printf '%s' "$PUBLIC_BASE_URL" | sed 's/[\/[\&]/\\&/g')
    tmp_install="$BUILD_DIR/install.sh.tmp"
    sed "s|__BASE_URL__|$esc_base|g" "$INSTALL_TEMPLATE" > "$tmp_install"
    mv -f "$tmp_install" "$INSTALL_TMP"
  else
    echo "警告：找不到安装脚本模板 $INSTALL_TEMPLATE，跳过处理 install.sh" >&2
  fi
else
  echo "==> 跳过安装脚本处理(RELEASE_TYPE=$RELEASE_TYPE)"
fi

########################################
# 配置 ossutil
########################################

if [[ $USE_OSSUTIL -eq 1 ]]; then
  "$OSSUTIL_CMD" config -e "$OSS_ENDPOINT" -i "${OSS_ACCESS_KEY_ID:-}" -k "${OSS_ACCESS_KEY_SECRET:-}" >/dev/null
fi

########################################
# 上传制品与元数据
########################################

# 定义上传函数（如果使用内部上传器）
if [[ $USE_OSSUTIL -eq 0 ]]; then
  upload() {
    local src="$1" dst_key="$2"
    go run "$REPO_ROOT/scripts/ossput.go" \
      -endpoint "$OSS_ENDPOINT" -bucket "$OSS_BUCKET" \
      -object "$dst_key" -file "$src" >/dev/null
  }
fi

# 上传版本产物
if [[ "$RELEASE_TYPE" == "artifacts" || "$RELEASE_TYPE" == "all" ]]; then
  echo "==> 上传版本产物到 oss://$OSS_BUCKET/$OSS_PATH_PREFIX/releases/$VERSION/"
  if [[ $USE_OSSUTIL -eq 1 ]]; then
    "$OSSUTIL_CMD" mkdir "oss://$OSS_BUCKET/$OSS_PATH_PREFIX/releases/$VERSION/" >/dev/null || true
    "$OSSUTIL_CMD" cp -f "$DIST_DIR/"*.zip "oss://$OSS_BUCKET/$OSS_PATH_PREFIX/releases/$VERSION/" >/dev/null
    "$OSSUTIL_CMD" cp -f "$CHECKSUMS_FILE" "oss://$OSS_BUCKET/$OSS_PATH_PREFIX/releases/$VERSION/checksums.txt" >/dev/null
    # 版本级 manifest（便于回滚/指定版本安装/更新）
    "$OSSUTIL_CMD" cp -f "$MANIFEST_FILE" "oss://$OSS_BUCKET/$OSS_PATH_PREFIX/releases/$VERSION/manifest.json" >/dev/null
  else
    upload "$CHECKSUMS_FILE" "$OSS_PATH_PREFIX/releases/$VERSION/checksums.txt"
    for f in "$DIST_DIR"/*.zip; do
      fname=$(basename "$f")
      upload "$f" "$OSS_PATH_PREFIX/releases/$VERSION/$fname"
    done
    # 版本级 manifest
    upload "$MANIFEST_FILE" "$OSS_PATH_PREFIX/releases/$VERSION/manifest.json"
  fi
  
  echo "==> 上传全局 manifest 与 latest"
  if [[ $USE_OSSUTIL -eq 1 ]]; then
    "$OSSUTIL_CMD" mkdir "oss://$OSS_BUCKET/$OSS_PATH_PREFIX/channels/" >/dev/null || true
    "$OSSUTIL_CMD" cp -f "$MANIFEST_FILE" "oss://$OSS_BUCKET/$OSS_PATH_PREFIX/channels/manifest.json" >/dev/null
    "$OSSUTIL_CMD" cp -f "$LATEST_FILE" "oss://$OSS_BUCKET/$OSS_PATH_PREFIX/channels/latest.txt" >/dev/null
  else
    upload "$MANIFEST_FILE" "$OSS_PATH_PREFIX/channels/manifest.json"
    upload "$LATEST_FILE" "$OSS_PATH_PREFIX/channels/latest.txt"
  fi
else
  echo "==> 跳过版本产物上传(RELEASE_TYPE=$RELEASE_TYPE)"
fi

# 上传安装脚本
if [[ "$RELEASE_TYPE" == "install" || "$RELEASE_TYPE" == "all" ]]; then
  echo "==> 上传安装脚本"
  if [[ -f "$INSTALL_TMP" ]]; then
    if [[ $USE_OSSUTIL -eq 1 ]]; then
      "$OSSUTIL_CMD" cp -f "$INSTALL_TMP" "oss://$OSS_BUCKET/$OSS_PATH_PREFIX/install.sh" >/dev/null
    else
      upload "$INSTALL_TMP" "$OSS_PATH_PREFIX/install.sh"
    fi
  else
    echo "警告：安装脚本不存在，跳过上传" >&2
  fi
else
  echo "==> 跳过安装脚本上传(RELEASE_TYPE=$RELEASE_TYPE)"
fi

echo "发布完成：$VERSION (MODE=$MODE, RELEASE_TYPE=$RELEASE_TYPE)"
echo "下载基址：$PUBLIC_BASE_URL"

case "$RELEASE_TYPE" in
  artifacts)
    echo "已发布版本产物："
    echo "- 最新版本：$PUBLIC_BASE_URL/channels/latest.txt"
    echo "- Manifest：$PUBLIC_BASE_URL/channels/manifest.json"
    echo "- 版本目录：$PUBLIC_BASE_URL/releases/$VERSION/"
    ;;
  install)
    echo "已发布安装脚本："
    echo "- 安装脚本：$PUBLIC_BASE_URL/install.sh"
    ;;
  all)
    echo "已发布所有内容："
    echo "- 最新版本：$PUBLIC_BASE_URL/channels/latest.txt"
    echo "- Manifest：$PUBLIC_BASE_URL/channels/manifest.json"
    echo "- 版本目录：$PUBLIC_BASE_URL/releases/$VERSION/"
    echo "- 安装脚本：$PUBLIC_BASE_URL/install.sh"
    ;;
esac


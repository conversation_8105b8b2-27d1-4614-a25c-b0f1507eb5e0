#!/usr/bin/env bash

set -euo pipefail

# Qoder CLI Installation Script
# 
# This script automatically installs the latest version of Qoder CLI by:
# - Detecting the operating system and architecture
# - Downloading the appropriate binary package from the release manifest
# - Verifying the download integrity using SHA-256 checksums
# - Installing to ~/.qoder with proper executable permissions
# - Creating a symbolic link in /usr/local/bin for system-wide access

BASE_URL="__BASE_URL__"
FORCE=0
INSTALL_DIR="$HOME/.qoder"
BIN_LINK="/usr/local/bin/qodercli"
NO_LINK=0

usage() {
  if [[ "${1:-}" == "--detailed" ]]; then
    cat <<EOF >&2
Qoder CLI Installation Script

USAGE:
  curl -fsSL ${BASE_URL}/install.sh | bash
  curl -fsSL ${BASE_URL}/install.sh | bash -s -- [OPTIONS]

OPTIONS:
  --force                Force overwrite existing installation
  --install-dir DIR      Custom installation directory (default: ~/.qoder)
  --bin-link PATH        Custom symlink path (default: /usr/local/bin/qodercli)
  --no-link             Skip symlink creation, install binary only
  -h, --help            Show this help message

DESCRIPTION:
  This script installs Qoder CLI to ~/.qoder and creates a system-wide
  symlink at /usr/local/bin/qodercli. If administrative privileges are 
  required for /usr/local/bin, the script will prompt for sudo access.

EXAMPLES:
  # Standard installation
  curl -fsSL ${BASE_URL}/install.sh | bash
  
  # Force reinstall
  curl -fsSL ${BASE_URL}/install.sh | bash -s -- --force
  
  # Custom installation directory
  curl -fsSL ${BASE_URL}/install.sh | bash -s -- --install-dir ~/my-tools
EOF
  else
    cat <<EOF >&2
Qoder CLI Installation Script

USAGE:
  curl -fsSL ${BASE_URL}/install.sh | bash

Use --help for advanced options.
EOF
  fi
  exit 1
}

while [[ $# -gt 0 ]]; do
  case "$1" in
    --force)
      FORCE=1
      shift
      ;;
    --install-dir)
      INSTALL_DIR="$2"
      shift 2
      ;;
    --bin-link)
      BIN_LINK="$2"
      shift 2
      ;;
    --no-link)
      NO_LINK=1
      shift
      ;;
    -h|--help)
      usage --detailed
      ;;
    *)
      echo "Error: Unknown option '$1'" >&2
      usage
      ;;
  esac
done

detect_os_arch() {
  local os arch
  case "$(uname -s)" in
    Linux) os="linux" ;;
    Darwin) os="darwin" ;;
    *) echo "Error: Unsupported operating system: $(uname -s)" >&2; exit 1 ;;
  esac
  case "$(uname -m)" in
    x86_64|amd64) arch="amd64" ;;
    arm64|aarch64) arch="arm64" ;;
    *) echo "Error: Unsupported architecture: $(uname -m)" >&2; exit 1 ;;
  esac
  echo "$os" "$arch"
}

download() {
  local url="$1" out="$2"
  if command -v curl >/dev/null 2>&1; then
    curl -fL "$url" -o "$out"
  elif command -v wget >/dev/null 2>&1; then
    wget -q "$url" -O "$out"
  else
    echo "Error: curl or wget is required for downloading" >&2
    exit 1
  fi
}

require_cmd() {
  if ! command -v "$1" >/dev/null 2>&1; then
    echo "Error: Required dependency '$1' not found" >&2
    exit 1
  fi
}

create_symlink() {
  local link_dir="$(dirname "$BIN_LINK")"
  
  echo "==> Creating symlink at $BIN_LINK"
  
  # Check if link directory exists and is writable
  if [[ ! -d "$link_dir" ]]; then
    echo "Link directory does not exist: $link_dir"
    if [[ "$link_dir" == "/usr/local/bin" ]] && command -v sudo >/dev/null 2>&1; then
      echo "Attempting to create directory (requires administrative privileges)..."
      if sudo mkdir -p "$link_dir" 2>/dev/null; then
        echo "Directory created successfully"
      else
        echo "Warning: Cannot create directory $link_dir, skipping symlink creation" >&2
        return 1
      fi
    else
      echo "Warning: Cannot create directory $link_dir, skipping symlink creation" >&2
      return 1
    fi
  fi
  
  # Remove existing link or file
  if [[ -e "$BIN_LINK" || -L "$BIN_LINK" ]]; then
    if [[ -w "$link_dir" ]]; then
      rm -f "$BIN_LINK"
    elif command -v sudo >/dev/null 2>&1; then
      echo "Removing existing file (requires administrative privileges)..."
      sudo rm -f "$BIN_LINK"
    else
      echo "Warning: Cannot remove existing file $BIN_LINK, skipping symlink creation" >&2
      return 1
    fi
  fi
  
  # Create symlink
  if [[ -w "$link_dir" ]]; then
    ln -s "$target_bin" "$BIN_LINK"
  elif command -v sudo >/dev/null 2>&1; then
    echo "Creating symlink (requires administrative privileges)..."
    sudo ln -s "$target_bin" "$BIN_LINK"
  else
    echo "Warning: Cannot create symlink to $BIN_LINK, please manually add $INSTALL_DIR to PATH" >&2
    return 1
  fi
  
  if [[ -L "$BIN_LINK" ]]; then
    echo "Symlink created successfully: $BIN_LINK -> $target_bin"
    return 0
  else
    echo "Warning: Symlink creation failed" >&2
    return 1
  fi
}

main() {
  require_cmd unzip
  local os arch
  read -r os arch < <(detect_os_arch)

  echo "==> Fetching release manifest..."
  local manifest_url="${BASE_URL}/channels/manifest.json"
  local manifest_json
  manifest_json=$(curl -fsSL "$manifest_url")

  # Parse manifest using awk for minimal dependencies (no jq required)
  # This depends on stable manifest JSON structure
  local entry
  entry=$(printf '%s' "$manifest_json" | awk -v os="$os" -v arch="$arch" '
    BEGIN{RS="{";FS=","}
    /"files"/ {next}
    /"os"/ && /"arch"/ && $0 ~ "\"os\":\"" os "\"" && $0 ~ "\"arch\":\"" arch "\"" {print "{"$0}
  ' | head -n1)

  if [[ -z "$entry" ]]; then
    echo "Error: No matching release found in manifest for ${os}/${arch}" >&2
    exit 1
  fi

  local url sha256
  url=$(printf '%s' "$entry" | sed -n 's/.*"url"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/p')
  sha256=$(printf '%s' "$entry" | sed -n 's/.*"sha256"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/p')

  if [[ -z "$url" ]]; then
    echo "Error: Missing download URL in manifest" >&2
    exit 1
  fi

  # Extract version from manifest
  local version
  version=$(printf '%s' "$manifest_json" | sed -n 's/.*"latest"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/p')

  local zip_name="$(basename "$url")"
  local bin_name="qodercli"
  [[ "$os" == "windows" ]] && bin_name="qodercli.exe"

  # Expand user directory path
  INSTALL_DIR="${INSTALL_DIR/#~/$HOME}"
  local target_bin="$INSTALL_DIR/$bin_name"

  if [[ -e "$target_bin" && "$FORCE" != "1" ]]; then
    echo "Error: Target file $target_bin already exists. Use --force to overwrite." >&2
    exit 1
  fi

  echo "==> Downloading Qoder CLI ${version} for ${os}/${arch}..."
  download "$url" "$zip_name"

  if [[ -n "$sha256" ]]; then
    echo "==> Verifying download integrity..."
    if command -v shasum >/dev/null 2>&1; then
      calc=$(shasum -a 256 "$zip_name" | awk '{print $1}')
    else
      calc=$(sha256sum "$zip_name" | awk '{print $1}')
    fi
    if [[ "$calc" != "$sha256" ]]; then
      echo "Error: SHA-256 verification failed" >&2
      echo "  Expected: $sha256" >&2
      echo "  Actual:   $calc" >&2
      exit 1
    fi
    echo "Checksum verification passed"
  fi

  echo "==> Extracting archive..."
  tmpdir=$(mktemp -d)
  unzip -q "$zip_name" -d "$tmpdir"
  if [[ ! -f "$tmpdir/$bin_name" ]]; then
    # Handle cross-platform binary naming
    if [[ -f "$tmpdir/qodercli" ]]; then
      bin_name="qodercli"
    elif [[ -f "$tmpdir/qodercli.exe" ]]; then
      bin_name="qodercli.exe"
    else
      echo "Error: Executable not found in archive" >&2
      exit 1
    fi
  fi

  echo "==> Installing to $INSTALL_DIR"
  mkdir -p "$INSTALL_DIR"
  mv -f "$tmpdir/$bin_name" "$target_bin"
  chmod +x "$target_bin"
  rm -rf "$tmpdir"
  rm -f "$zip_name"

  echo "Binary installed: $target_bin"

  # Create symlink
  if [[ $NO_LINK -eq 0 ]]; then
    create_symlink
  fi

  echo ""
  echo "🎉 Qoder CLI ${version} installed successfully!"
  echo ""
  if [[ $NO_LINK -eq 0 ]] && [[ -L "$BIN_LINK" ]]; then
    echo "Run 'qodercli -h' to get started."
  else
    echo "Run '$target_bin -h' to get started."
    echo "Consider adding $INSTALL_DIR to your PATH for easier access."
  fi
}

main "$@"


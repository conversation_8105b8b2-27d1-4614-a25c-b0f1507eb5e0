package main

// 一个最小可用的 OSS 上传工具（单次 PUT），避免在 CI 或本地依赖外部二进制。
// 使用阿里云 OSS 签名 V1（Header 签名），不依赖第三方 SDK。

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"flag"
	"fmt"
	"io"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

func main() {
	var (
		endpoint   = flag.String("endpoint", "", "OSS endpoint，例如 oss-cn-hangzhou.aliyuncs.com")
		bucket     = flag.String("bucket", "", "OSS Bucket 名称")
		keyID      = flag.String("access-key-id", os.Getenv("OSS_ACCESS_KEY_ID"), "AccessKeyId")
		keySec     = flag.String("access-key-secret", os.Getenv("OSS_ACCESS_KEY_SECRET"), "AccessKeySecret")
		object     = flag.String("object", "", "对象键（例如 qodercli/releases/v1.2.3/file.zip）")
		file       = flag.String("file", "", "本地文件路径")
		publicRead = flag.Bool("public-read", true, "是否设置 x-oss-object-acl: public-read")
	)
	flag.Parse()

	if *endpoint == "" || *bucket == "" || *object == "" || *file == "" || *keyID == "" || *keySec == "" {
		fmt.Fprintln(os.Stderr, "缺少必要参数：--endpoint --bucket --object --file 以及 AK/SK")
		os.Exit(2)
	}

	f, err := os.Open(*file)
	if err != nil {
		panic(err)
	}
	defer f.Close()

	st, err := f.Stat()
	if err != nil {
		panic(err)
	}

	// Content-MD5（Base64）
	h := md5.New()
	if _, err := io.Copy(h, f); err != nil {
		panic(err)
	}
	md5sum := h.Sum(nil)
	contentMD5 := base64.StdEncoding.EncodeToString(md5sum)
	// reset reader
	if _, err := f.Seek(0, 0); err != nil {
		panic(err)
	}

	contentType := mime.TypeByExtension(filepath.Ext(*file))
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	date := time.Now().UTC().Format(http.TimeFormat)

	// CanonicalizedOSSHeaders
	var ossHeaders []string
	if *publicRead {
		ossHeaders = append(ossHeaders, "x-oss-object-acl:public-read")
	}
	canonicalOSS := ""
	if len(ossHeaders) > 0 {
		canonicalOSS = strings.Join(ossHeaders, "\n") + "\n"
	}

	// CanonicalizedResource
	canonicalResource := fmt.Sprintf("/%s/%s", *bucket, *object)

	// StringToSign
	stringToSign := strings.Join([]string{
		"PUT",
		contentMD5,
		contentType,
		date,
		canonicalOSS + canonicalResource,
	}, "\n")

	mac := hmac.New(sha1.New, []byte(*keySec))
	mac.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	auth := fmt.Sprintf("OSS %s:%s", *keyID, signature)

	url := fmt.Sprintf("https://%s.%s/%s", *bucket, *endpoint, *object)
	req, err := http.NewRequest("PUT", url, f)
	if err != nil {
		panic(err)
	}
	req.Header.Set("Content-MD5", contentMD5)
	req.Header.Set("Content-Type", contentType)
	req.Header.Set("Date", date)
	if *publicRead {
		req.Header.Set("x-oss-object-acl", "public-read")
	}
	req.Header.Set("Authorization", auth)

	req.ContentLength = st.Size()

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode/100 != 2 {
		b, _ := io.ReadAll(resp.Body)
		fmt.Fprintf(os.Stderr, "上传失败：%s\n%s\n", resp.Status, string(b))
		os.Exit(1)
	}
}

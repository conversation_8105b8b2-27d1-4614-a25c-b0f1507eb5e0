# Qoder CLI 构建镜像 - 隐藏本地路径，提供干净的构建环境
# 使用经过验证的生产级基础镜像
FROM golang:alpine AS builder

# 安装构建依赖
RUN apk add --no-cache git zip make bash ca-certificates && \
    update-ca-certificates

# 设置标准工作目录（构建错误显示 /build 而非本地路径）
WORKDIR /build

# 使用 vendor 模式，避免网络依赖
ENV GOPROXY=off
ENV GOSUMDB=off

# 复制依赖文件（可选，主要用于缓存）
COPY go.mod go.sum ./

# 复制源代码
COPY . .

# 导出构建产物
FROM scratch AS export
COPY --from=builder /build/qodercli* /
package qoder

import (
	"bytes"

	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"strings"
	"time"
)

const IllegalReplaceChar = rune(65533)

// ChatMessagePartType is the type of the part in a chat message.
type ChatMessagePartType string

const (
	// ChatMessagePartTypeText means the part is a text.
	ChatMessagePartTypeText ChatMessagePartType = "text"
	// ChatMessagePartTypeImageURL means the part is an image url.
	ChatMessagePartTypeImageURL ChatMessagePartType = "image_url"

	appCode = "cosy"
	appSalt = "d2FyLCB3YXIgbmV2ZXIgY2hhbmdlcw=="
)

// ChatMessagePart is the part in a chat message.
type ChatMessagePart struct {
	// Type is the type of the part, eg. "text", "image_url", "audio_url", "video_url", "file_url".
	Type ChatMessagePartType `json:"type,omitempty"`

	// Text is the text of the part, it's used when Type is "text".
	Text string `json:"text,omitempty"`

	// ImageURL is the image url of the part, it's used when Type is "image_url".
	ImageURL *ChatMessageImageURL `json:"image_url,omitempty"`
}

// ImageURLDetail is the detail of the image url.
type ImageURLDetail string

const (
	// ImageURLDetailHigh means the high quality image url.
	ImageURLDetailHigh ImageURLDetail = "high"
	// ImageURLDetailLow means the low quality image url.
	ImageURLDetailLow ImageURLDetail = "low"
	// ImageURLDetailAuto means the auto quality image url.
	ImageURLDetailAuto ImageURLDetail = "auto"
)

// ChatMessageImageURL is used to represent an image part in a chat message.
// Choose either URL or URI.
// If your model implementation supports it, URL could be used to embed inline image data as defined in RFC-2397.
type ChatMessageImageURL struct {
	// URL can either be a traditional URL or a special URL conforming to RFC-2397 (https://www.rfc-editor.org/rfc/rfc2397).
	// double check with model implementations for detailed instructions on how to use this.
	URL string `json:"url,omitempty"`
	URI string `json:"uri,omitempty"`
	// Detail is the quality of the image url.
	Detail ImageURLDetail `json:"detail,omitempty"`

	// MIMEType is the mime type of the image, eg. "image/png".
	MIMEType string `json:"mime_type,omitempty"`
	// Extra is used to store extra information for the image url.
	Extra map[string]any `json:"extra,omitempty"`
}

type ModelConfig struct {
	Key            string `json:"key"`              // 唯一标识，如 deepseek-v3
	DisplayName    string `json:"display_name"`     // 可读性名称，插件侧展示
	Model          string `json:"model"`            // 制定模型
	Format         string `json:"format"`           // 输出格式，如 openai, dashscope
	IsVl           bool   `json:"is_vl"`            // 是否支持多模态，默认为 false
	IsReasoning    bool   `json:"is_reasoning"`     // 是否支持推理，默认为 false
	ApiKey         string `json:"api_key"`          // 可选，API Key
	Url            string `json:"url"`              // 可选，连接的模型 URL
	Source         string `json:"source"`           // 类型，用户自定义还是系统级别，system/user
	MaxInputTokens int    `json:"max_input_tokens"` // 模型的最大token限制
}

type BigModelSvcRequest struct {
	ServiceName string      `json:"service_name"` // 服务名称
	FetchKey    string      `json:"fetch_key"`    // 获取键
	Async       bool        `json:"async"`        // 是否异步
	RequestBody interface{} `json:"request_body"` // 请求体
	RequestID   string      `json:"request_id"`   // 请求ID
	AgentID     string      `json:"agent_id"`     // 代理ID
}

type RoleType string

const (
	RoleTypeAssistant RoleType = "assistant"
	RoleTypeUser      RoleType = "user"
	RoleTypeSystem    RoleType = "system"
	RoleTypeTool      RoleType = "tool"
)

// FunctionCall 模型返回的function调用(tool_call.function)
type FunctionCall struct {
	Name      string `json:"name,omitempty"`      // function名
	Arguments string `json:"arguments,omitempty"` // function参数，JSON格式
}

// ToolCall 模型返回的工具调用(tool_call)
type ToolCall struct {
	Index    int            `json:"index,omitempty"` // 标识多工具调用的顺序
	Id       string         `json:"id"`
	Type     string         `json:"type"` // 工具调用类型，默认function
	Function FunctionCall   `json:"function"`
	Extra    map[string]any `json:"extra,omitempty"`
}

// ResponseMeta 模型response的元数据
type ResponseMeta struct {
	Id           string    `json:"id"`                      //	RequestId
	FinishReason string    `json:"finish_reason,omitempty"` //	response结束原因，stop/tool_call
	Usage        UsageMeta `json:"usage,omitempty"`
}

// UsageMeta token用量
type UsageMeta struct {
	PromptTokens            int `json:"prompt_tokens"`     // prompt token.
	CompletionTokens        int `json:"completion_tokens"` // 回答token
	TotalTokens             int `json:"total_tokens"`      // 总token，prompt+completion
	CompletionTokensDetails struct {
		ReasoningTokens int `json:"reasoning_tokens"`
	} `json:"completion_tokens_details"`
	PromptTokensDetails struct {
		CachedTokens int `json:"cached_tokens"`
	} `json:"prompt_tokens_details"` // prompt缓存token详情
}

type Message struct {
	Role    RoleType `json:"role"`    // 角色
	Content string   `json:"content"` // 文本内容
	// if MultiContent is not empty, use this instead of Content
	// if MultiContent is empty, use Content
	MultiContent              []ChatMessagePart `json:"contents,omitempty"`
	ToolCalls                 []ToolCall        `json:"tool_calls,omitempty"` // only for AssistantMessage
	Name                      string            `json:"name,omitempty"`       // only for ToolMessage
	ToolCallId                string            `json:"tool_call_id,omitempty"`
	ResponseMeta              ResponseMeta      `json:"response_meta,omitempty"`
	Extra                     map[string]any    `json:"extra,omitempty"`
	ReasoningContent          string            `json:"reasoning_content,omitempty"` // 推理内容
	ReasoningContentSignature string            `json:"reasoning_content_signature"` // 推理内容的signature
}

// Tool is a tool that can be used by the model.
type Tool struct {
	// Type is the type of the tool.
	Type string `json:"type"`
	// Function is the function to call.
	Function *FunctionDefinition `json:"function,omitempty"`
}

// FunctionDefinition is a definition of a function that can be called by the model.
type FunctionDefinition struct {
	// Name is the name of the function.
	Name string `json:"name"`
	// Description is a description of the function.
	Description string `json:"description"`
	// Parameters is a list of parameters for the function.
	Parameters any `json:"parameters,omitempty"`
	// Strict is a flag to indicate if the function should be called strictly. Only used for openai llm structured output.
	Strict bool `json:"strict,omitempty"`
}

type RemoteChatAsk struct {
	RequestId               string                 `json:"request_id"`     //请求id，历史原因，卡片id等同于requestId相同
	RequestSetId            string                 `json:"request_set_id"` //问答集合id
	ChatRecordId            string                 `json:"chat_record_id"` //问答卡片id
	Stream                  bool                   `json:"stream"`
	ChatTask                string                 `json:"chat_task,omitempty"`
	ChatContext             interface{}            `json:"chat_context,omitempty"`
	ImageUrls               []string               `json:"image_urls"` //多模态场景下，图片链接
	IsReply                 bool                   `json:"is_reply"`
	IsRetry                 bool                   `json:"is_retry"`
	SessionId               string                 `json:"session_id"`
	CodeLanguage            string                 `json:"code_language"`
	Source                  int                    `json:"source"`
	Version                 string                 `json:"version,omitempty"`                    //会话本地化后为2
	ChatPrompt              string                 `json:"chat_prompt"`                          //agent模式下使用，直接使用端侧的prompt
	CustomSystemRoleContent string                 `json:"custom_system_role_content,omitempty"` //废弃了！老版约定的chat ask接口的系统提示词，服务端要求
	SystemRoleContent       string                 `json:"system_role_content,omitempty"`        //新版ai agent下覆盖系统提示词
	Parameters              map[string]interface{} `json:"parameters"`
	UserType                string                 `json:"aliyun_user_type"`
	TaskDefinitionType      string                 `json:"task_definition_type,omitempty"`
	SessionType             string                 `json:"session_type,omitempty"` //agent类型
	AgentId                 string                 `json:"agent_id,omitempty"`     //约定的agent标识，用于区分模型路由
	TaskId                  string                 `json:"task_id,omitempty"`      //约定的agent下面具体的task标识，用于区分模型路由
	ModelConfig             ModelConfig            `json:"model_config"`
	Messages                []*Message             `json:"messages,omitempty"` //新版openAI标准格式
	Tools                   []Tool                 `json:"tools"`              //新版openAI工具调用
	Mode                    string                 `json:"mode,omitempty"`     //新版common agent下，模式
	Passkey                 string                 `json:"passkey,omitempty"`
	LineUpType              string                 `json:"line_up_type,omitempty"` // 资源限制，排队类型
	LineUpId                string                 `json:"line_up_id,omitempty"`   // 资源限制，排队ID
}

func encodeRequestBody(requestBody interface{}) ([]byte, error) {
	if requestBody == nil {
		return nil, nil
	}

	var bodyBytes []byte
	var err error

	// 处理字节流类型（文件上传）
	if data, ok := requestBody.([]byte); ok {
		bodyBytes = data
	} else {
		// JSON编码
		bodyBytes, err = json.Marshal(requestBody)
		if err != nil {
			slog.Error("Failed to marshal request body: " + err.Error())
			return nil, errors.New("marshal request failed")
		}
	}

	// 如果需要加密
	bodyBytes = CustomEncryptV1(bodyBytes)

	return bodyBytes, nil
}

func createHTTPRequest(method, urlString string, bodyBytes []byte) (*http.Request, error) {
	req, err := http.NewRequest(method, urlString, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, errors.New("create request failed")
	}
	return req, nil
}

func addBasicHeaders(req *http.Request) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Accept-Encoding", "identity")

	req.Header.Set("Cosy-Version", "dev")
	req.Header.Set("Cosy-ClientIp", GetMachineIp())
	req.Header.Set("Cosy-MachineId", GetMachineId())
	req.Header.Set("Cosy-ClientType", "0")

	//升级协议
	req.Header.Set("Login-Version", "v2")
}

// 去除algo前缀，和服务端ingress部署有关
// /algo/api/v2/service/pro/sse/chat_ask  No
// /api/v2/service/pro/sse/chat_ask       Yes
func trimQueryPath(url string, endpoint string) string {
	trimPath := url
	if index := strings.Index(trimPath, "?"); index >= 0 {
		trimPath = trimPath[0:index]
	}

	if endpoint != "" {
		if strings.HasPrefix(trimPath, endpoint) {
			trimPath = strings.TrimPrefix(trimPath, endpoint)
		}
	}
	if strings.HasPrefix(trimPath, "/algo") {
		trimPath = strings.TrimPrefix(trimPath, "/algo")
	}
	return trimPath
}

func addBigModelSignatureHeaders(req *http.Request) {
	req.Header.Set("Date", fmt.Sprint(time.Now().UTC().Format(http.TimeFormat)))
	req.Header.Set("Signature", Md5Encode(appCode, appSalt, req.Header.Get("Date")))
	req.Header.Set("Appcode", appCode)
}

func addBigModelAuthorizationHeaders(req *http.Request, urlPath, requestBody string) error {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	var authToken, userInfoKey, uid string
	// 普通版本
	authToken, userInfoKey = AuthToken(urlPath, requestBody, timestamp)
	if userInfoKey == "" {
		return errors.New("you must login to use remote model")
	}
	_, uid, _ = GetUserIdAndName()
	req.Header.Set("Cosy-User", uid)
	req.Header.Set("Cosy-Key", userInfoKey)
	req.Header.Set("Cosy-Date", timestamp)
	req.Header.Set("Authorization", authToken)
	return nil
}

func BuildRequest(ask *RemoteChatAsk, modelConfig *ModelConfig) (*http.Request, error) {
	cfg := GetConfig()
	var endpoint string
	if cfg == nil || cfg.RegionConfig.PreferredInferenceNode.Endpoint == "" {
		// NOTE: 如果修改 Qoder 默认 Endpoint，这里与 /status 的展示需要同步更新：
		// core/internal/llm/command/status.go -> buildStatusText()
		endpoint = "https://api2.qoder.sh"
	} else {
		endpoint = cfg.RegionConfig.PreferredInferenceNode.Endpoint
	}

	urlPath := fmt.Sprintf("/algo/api/v2/service/pro/sse/%s?FetchKeys=%s&AgentId=%s&Encode=1", "agent_chat_generation", "llm_model_result", "agent_common")
	urlString := endpoint + urlPath

	bodyBytes, err := encodeRequestBody(ask)
	if err != nil {
		return nil, err
	}

	req, err := createHTTPRequest("POST", urlString, bodyBytes)
	if err != nil {
		return nil, err
	}

	// 设置基本headers
	addBasicHeaders(req)
	urlPath = trimQueryPath(urlString, endpoint)
	err = addBigModelAuthorizationHeaders(req, urlPath, string(bodyBytes))

	if err != nil {
		return nil, err
	}

	return req, nil
}

// BuildRequest 构建 HTTP 请求，复制 qoder 包的编码和认证逻辑
func BuildSignatureRequest(endpoint string, data any) (*http.Request, error) {
	// 构建 URL
	url := endpoint + "/algo/api/v1/tracking?Encode=1"

	bodyBytes, err := encodeRequestBody(data)
	if err != nil {
		return nil, err
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	addBasicHeaders(req)
	addBigModelSignatureHeaders(req)

	return req, nil
}

type ChatResponse struct {
	Headers         map[string][]string `json:"headers"`
	Body            string              `json:"body"`
	StatusCodeValue int                 `json:"statusCodeValue"`
	StatusCode      string              `json:"statusCode"`
}

type LlmUsage struct {
	TotalTokens    int     `json:"total_tokens"`
	OutputTokens   int     `json:"output_tokens"`
	InputTokens    int     `json:"input_tokens"`
	FirstTokenTime float64 `json:"first_token_time"`
	TotalTime      float64 `json:"total_time"`
}

type ChatBody struct {
	Output        interface{} `json:"output"`
	Usage         LlmUsage    `json:"usage"`
	RequestId     string      `json:"request_id"`
	decodedOutput interface{}
}

// NewChatBody 解析 JSON 格式的聊天消息体并返回 ChatBody 结构。
// 参数：
//   - body: 包含聊天消息的 JSON 字符串。
//
// 返回值：
//   - ChatBody: 解析后的聊天消息体。
//   - error: 如果解析过程中发生错误，则返回非空错误。
//
// 该函数首先尝试将输入字符串反序列化为 ChatBody 结构。然后，它会进一步尝试将其中的输出部分分别按照 OpenAI 输出格式和旧版输出格式进行解析。
// 如果 OpenAI 输出格式有效，则使用该格式；否则，使用旧版输出格式。
func NewChatBody(body string) (ChatBody, error) {
	chatBody := ChatBody{}
	if err := json.Unmarshal([]byte(body), &chatBody); err != nil {
		return ChatBody{}, fmt.Errorf("unmarshal to Chat body: %v", err)
	}
	if chatBody.Output == nil {
		return ChatBody{}, fmt.Errorf("no valid chat output. body text: %s", body)
	}

	var output []byte
	output, _ = json.Marshal(chatBody.Output)

	// 尝试按 OpenAI 的输出格式解析
	openAIOutput, valid, err := parseOpenAIOutput(output)
	if err != nil {
		return ChatBody{}, err
	}
	if valid {
		chatBody.decodedOutput = openAIOutput
		return chatBody, nil
	}
	// 尝试按旧版输出格式解析
	obsoleteOutput := &ObsoleteOutput{}
	if err := json.Unmarshal(output, &obsoleteOutput); err != nil {
		return ChatBody{}, fmt.Errorf("unmarshal to Obsolete Output: %v", err)
	}
	chatBody.decodedOutput = obsoleteOutput
	return chatBody, nil
}

func parseOpenAIOutput(output []byte) (Output, bool, error) {
	// 尝试按 OpenAI 的输出格式解析
	openAIOutput := &OpenAIOutput{}
	if err := json.Unmarshal(output, &openAIOutput); err != nil {
		return nil, false, fmt.Errorf("unmarshal to OpenAI Output: %v", err)
	}
	if openAIOutput.Choices != nil {
		for i := range openAIOutput.Choices {
			choice := &openAIOutput.Choices[i]
			if _, ok := choice.Message.Content.(string); ok {
				//ignore
			} else {
				var structContent []OpenAIOutputMessageContent
				contentStr, _ := json.Marshal(choice.Message.Content)
				if err := json.Unmarshal(contentStr, &structContent); err != nil {
					return nil, false, fmt.Errorf("unmarshal to OpenAI Output Message Content: %v", err)
				}
				choice.Message.Content = structContent
			}
		}
	}
	return openAIOutput, openAIOutput.isValid(), nil
}

func (c *ChatBody) GetOutputText() string {
	// 如果 decodedOutput 为空，返回空字符串
	if c.decodedOutput == nil {
		return ""
	}
	outputText := ""
	// 尝试将 decodedOutput 断言为 *OpenAIOutput 类型
	if output, ok := c.decodedOutput.(*OpenAIOutput); ok {
		// 如果 Choices 非空且第一个 Choice 的 Message.Content 非空，
		// 返回第一个 Content 的 Text 字段，否则返回空字符串
		if len(output.Choices) > 0 {
			outputContent := output.Choices[0].Message.Content
			if content, ok := outputContent.(string); ok {
				outputText = content
			} else if contentArray, ok := outputContent.([]OpenAIOutputMessageContent); ok {
				outputText = contentArray[0].Text
			}
		}
	} else if obsoleteOutput, ok := c.decodedOutput.(*ObsoleteOutput); ok {
		// 尝试将 decodedOutput 断言为 *ObsoleteOutput 类型
		// 返回 ObsoleteOutput 的 Text 字段
		outputText = obsoleteOutput.Text
	}
	if outputText != "" {
		outputText = trimLastIllegalReplaceChar(outputText, 1)
	}
	return outputText
}

func (c *ChatBody) GetReasoningText() string {
	// 如果 decodedOutput 为空，返回空字符串
	if c.decodedOutput == nil {
		return ""
	}
	reasoningText := ""
	// 尝试将 decodedOutput 断言为 *OpenAIOutput 类型
	if output, ok := c.decodedOutput.(*OpenAIOutput); ok {
		// 如果 Choices 非空且第一个 Choice 的 Message.Content 非空，
		// 返回第一个 Content 的 Text 字段，否则返回空字符串
		if len(output.Choices) > 0 {
			reasoningText = output.Choices[0].Message.ReasoningContent
		}
	} else if obsoleteOutput, ok := c.decodedOutput.(*ObsoleteOutput); ok {
		// 尝试将 decodedOutput 断言为 *ObsoleteOutput 类型
		// 返回 ObsoleteOutput 的 Text 字段
		reasoningText = obsoleteOutput.ReasoningText
	}
	if reasoningText != "" {
		reasoningText = trimLastIllegalReplaceChar(reasoningText, 1)
	}
	return reasoningText
}

type Output interface {
	isValid() bool
}

// OpenAIOutput 表示来自 OpenAI API 的输出响应。
// 包含一个 Choices 列表，每个元素代表一个可能的回复选项。
type OpenAIOutput struct {
	Choices []OpenAIOutputChoice `json:"choices"`
}

// isValid 检查 OpenAIOutput 是否有效。
// 有效的条件是 Choices 字段非空且至少包含一个元素。
func (o *OpenAIOutput) isValid() bool {
	if o.Choices == nil || len(o.Choices) == 0 {
		return false
	}
	choice0 := o.Choices[0]
	if choice0.Message.Content == nil {
		return false
	}
	if contentArray, ok := choice0.Message.Content.([]OpenAIOutputMessageContent); ok {
		if len(contentArray) <= 0 {
			return false
		}
	}
	return true
}

// OpenAIOutputChoice 表示 OpenAI API 输出中的单个选择项。
// 每个选择项包含一条消息和完成原因。
type OpenAIOutputChoice struct {
	Message      OpenAIOutputMessage `json:"message"`       // 回复的消息内容
	FinishReason string              `json:"finish_reason"` // 完成的原因
}

// OpenAIOutputMessage 表示 OpenAI API 输出的消息内容。
// 消息包含多个文本块和发送者的角色。
type OpenAIOutputMessage struct {
	Content          any    `json:"content"`           // 消息内容，多模态情况下是数组，其他场景为字符串
	ReasoningContent string `json:"reasoning_content"` //推理过程，可选
	Role             string `json:"role"`              // 发送者角色
}

type OpenAIOutputMessageContent struct {
	Text string `json:"text"` // 文本内容
}

// ObsoleteOutput 表示已废弃的输出格式。
// 该结构体用于向后兼容旧版本的 API 响应。
type ObsoleteOutput struct {
	FinishReason  string `json:"finish_reason"`  // 完成的原因
	Text          string `json:"text"`           // 文本内容
	ReasoningText string `json:"reasoning_text"` // 推理文本内容
}

// isValid 检查 ObsoleteOutput 是否有效。
// 有效的条件是 FinishReason 和 Text 字段均不为空。
func (o *ObsoleteOutput) isValid() bool {
	return o.FinishReason != "" && len(o.Text) > 0
}

// 移除最后的非法字符，最多移除 $checkCount 个
func trimLastIllegalReplaceChar(s string, checkCount int) string {
	if len(s) == 0 {
		return s
	}
	runes := []rune(s)
	validText := s
	c := 0 //最多校验3个rune
	for i := len(runes) - 1; i >= 0; i-- {
		c += 1
		if c > checkCount {
			break
		}
		if IllegalReplaceChar == runes[i] {
			validText = string(runes[:i])
			continue
		} else {
			validText = string(runes[:i+1])
			break
		}

	}
	return validText
}

type CommonAgentResponse struct {
	Text      string
	RequestId string
	Usage     LlmUsage `json:"usage"`
	Err       error
	Tools     []ToolCall
}

func (c *CommonAgentResponse) IsSuccess() bool {
	return c.Err == nil
}

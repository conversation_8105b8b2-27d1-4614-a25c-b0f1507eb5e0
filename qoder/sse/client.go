/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

package sse

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"sync"
	"sync/atomic"
	"time"
)

var (
	headerID    = []byte("id:")
	headerData  = []byte("data:")
	headerEvent = []byte("event:")
	headerRetry = []byte("retry:")
)

const (
	SsePeriodTimeout = 570 * time.Second

	//强制结束时的sse body
	ForceFinishReason = "forceFinish"
)

type SseTimeoutErr struct {
}

func (s SseTimeoutErr) Error() string {
	return "timeout"
}

// ConnCallback defines a function to be called on a particular connection event
type ConnCallback func(c *Client)

// ResponseValidator validates a response
type ResponseValidator func(c *Client, resp *http.Response) error

// Client handles an incoming server stream
type Client struct {
	Retry             time.Time
	disconnectcb      ConnCallback
	connectedcb       ConnCallback
	subscribed        map[chan *Event]chan struct{}
	Headers           map[string]string
	ResponseValidator ResponseValidator
	Connection        *http.Client
	LastEventID       atomic.Value // []byte
	maxBufferSize     int
	mu                sync.Mutex
	EncodingBase64    bool
	Connected         bool
	httpMethod        string
}

// NewClient creates a new client
func NewClient(httpMethod string, headers map[string]string, opts ...func(c *Client)) *Client {
	c := &Client{
		Connection:    http.DefaultClient,
		Headers:       headers,
		subscribed:    make(map[chan *Event]chan struct{}),
		maxBufferSize: 1 << 17,
		httpMethod:    httpMethod,
	}
	for _, opt := range opts {
		opt(c)
	}
	return c
}

// Subscribe to a data stream
func (c *Client) Subscribe(req *http.Request, timeout time.Duration, handler func(msg *Event), timeoutHandler func()) error {
	return c.SubscribeWithContext(context.Background(), timeout, req, func(req *http.Request, rsp *http.Response, msg *Event, closeChan chan error) {
		handler(msg)
	}, func(req *http.Request, rsp *http.Response) {
		timeoutHandler()
	})
}

func (c *Client) SubscribeWithContext(ctx context.Context, timeout time.Duration, req *http.Request, handler func(req *http.Request, rsp *http.Response, msg *Event, closeChan chan error), timeoutHandler func(req *http.Request, rsp *http.Response)) error {
	timestamp := time.Now()
	resp, err := c.Request(ctx, req)
	elapsedTime := time.Since(timestamp).Seconds()
	slog.Debug(fmt.Sprintf("subscribe sse time: %fs", elapsedTime))
	if err != nil {
		return err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	if validator := c.ResponseValidator; validator != nil {
		err = validator(c, resp)
		if err != nil {
			return err
		}
	} else if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		slog.Error(fmt.Sprintf("subscribe sse error. status code: %d, body: %s", resp.StatusCode, string(body)))

		return fmt.Errorf("could not connect to stream: %s", http.StatusText(resp.StatusCode))
	}

	reader := NewEventStreamReader(resp.Body, c.maxBufferSize)
	eventChan, errorChan := c.startReadLoop(reader)

	// 这里的超时时间需要稍比client超时时间晚，否则触发不到
	var timeoutChan <-chan time.Time
	if timeout > 0 {
		timeoutChan = time.After(timeout)
	}

	//关闭事件回调
	closeChan := make(chan error, 1)

	//TODO 临时设置成57s，线上改成30s
	ssePeriodTimer := time.NewTimer(SsePeriodTimeout)

	for {
		// 首先检查closeChan是否有错误，优先处理
		select {
		case err = <-closeChan:
			ssePeriodTimer.Stop()

			return err
		default:
			// 如果closeChan没有错误，继续正常的select逻辑
		}

		select {
		case err = <-errorChan:
			ssePeriodTimer.Stop()
			return err
		case msg := <-eventChan:
			ssePeriodTimer.Reset(SsePeriodTimeout)

			// 执行处理函数
			handler(req, resp, msg, closeChan)

			// 处理完后立即检查closeChan是否有错误
			select {
			case err = <-closeChan:
				ssePeriodTimer.Stop()

				return err
			default:
				// 继续处理下一个消息
			}
		case <-timeoutChan:
			ssePeriodTimer.Stop()

			timeoutHandler(req, resp)
			return SseTimeoutErr{}
		case <-ssePeriodTimer.C:
			ssePeriodTimer.Stop()

			finishEvent := newFinishSseEvent()
			handler(req, resp, finishEvent, closeChan)
		}
	}
}

func (c *Client) startReadLoop(reader *EventStreamReader) (chan *Event, chan error) {
	outCh := make(chan *Event)
	erChan := make(chan error)
	go c.readLoop(reader, outCh, erChan)
	return outCh, erChan
}

func (c *Client) readLoop(reader *EventStreamReader, outCh chan *Event, erChan chan error) {
	for {
		// Read each new line and process the type of event
		event, err := reader.ReadEvent()
		if err != nil {
			if err == io.EOF {
				erChan <- nil
				return
			}
			// run user specified disconnect function
			if c.disconnectcb != nil {
				c.Connected = false
				c.disconnectcb(c)
			}
			erChan <- err
			return
		}

		if !c.Connected && c.connectedcb != nil {
			c.Connected = true
			c.connectedcb(c)
		}

		// If we get an error, ignore it.
		var msg *Event
		if msg, err = ProcessEvent(event); err == nil {
			if len(msg.ID) > 0 {
				c.LastEventID.Store(msg.ID)
			} else {
				msg.ID, _ = c.LastEventID.Load().([]byte)
			}

			// Send downstream if the event has something useful
			if msg.hasContent() {
				outCh <- msg
			}
		}
	}
}

// Unsubscribe unsubscribes a channel
func (c *Client) Unsubscribe(ch chan *Event) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.subscribed[ch] != nil {
		c.subscribed[ch] <- struct{}{}
	}
}

// OnDisconnect specifies the function to run when the connection disconnects
func (c *Client) OnDisconnect(fn ConnCallback) {
	c.disconnectcb = fn
}

// OnConnect specifies the function to run when the connection is successful
func (c *Client) OnConnect(fn ConnCallback) {
	c.connectedcb = fn
}

//func (c *Client) Request(ctx context.Context, stream string) (*http.Response, error) {
//    req, err := http.NewRequest(c.httpMethod, c.URL, nil)
//    if err != nil {
//        return nil, err
//    }
//    req = req.WithContext(ctx)
//
//    // Setup Request, specify stream to connect to
//    if stream != "" {
//        query := req.URL.Query()
//        query.Add("stream", stream)
//        req.URL.RawQuery = query.Encode()
//    }
//    return c.requestRaw(req)
//}

func (c *Client) Request(ctx context.Context, req *http.Request) (*http.Response, error) {
	lastID, exists := c.LastEventID.Load().([]byte)
	if exists && lastID != nil {
		req.Header.Set("Last-Event-ID", string(lastID))
	}

	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Accept", "text/event-stream")
	req.Header.Set("Connection", "keep-alive")

	// Add user specified headers
	for k, v := range c.Headers {
		req.Header.Set(k, v)
	}

	return c.Connection.Do(req)
}

func ProcessEvent(msg []byte) (event *Event, err error) {
	var e Event

	if len(msg) < 1 {
		return nil, errors.New("event message was empty")
	}

	// Normalize the crlf to lf to make it easier to split the lines.
	// Split the line by "\n" or "\r", per the spec.
	for _, line := range bytes.FieldsFunc(msg, func(r rune) bool { return r == '\n' || r == '\r' }) {
		switch {
		case bytes.HasPrefix(line, headerID):
			e.ID = append([]byte(nil), trimHeader(len(headerID), line)...)
		case bytes.HasPrefix(line, headerData):
			// The spec allows for multiple data fields per event, concatenated them with "\n".
			e.Data = append(e.Data[:], append(trimHeader(len(headerData), line), byte('\n'))...)
		// The spec says that a line that simply contains the string "data" should be treated as a data field with an empty body.
		case bytes.Equal(line, bytes.TrimSuffix(headerData, []byte(":"))):
			e.Data = append(e.Data, byte('\n'))
		case bytes.HasPrefix(line, headerEvent):
			e.Event = append([]byte(nil), trimHeader(len(headerEvent), line)...)
		case bytes.HasPrefix(line, headerRetry):
			e.Retry = append([]byte(nil), trimHeader(len(headerRetry), line)...)
		default:
			// Ignore any garbage that doesn't match what we're looking for.
		}
	}

	// Trim the last "\n" per the spec.
	e.Data = bytes.TrimSuffix(e.Data, []byte("\n"))

	return &e, err
}

func (c *Client) cleanup(ch chan *Event) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.subscribed[ch] != nil {
		close(c.subscribed[ch])
		delete(c.subscribed, ch)
	}
}

func trimHeader(size int, data []byte) []byte {
	if data == nil || len(data) < size {
		return data
	}

	data = data[size:]
	// Remove optional leading whitespace
	if len(data) > 0 && data[0] == 32 {
		data = data[1:]
	}
	// Remove trailing new line
	if len(data) > 0 && data[len(data)-1] == 10 {
		data = data[:len(data)-1]
	}
	return data
}

func newFinishSseEvent() *Event {
	return &Event{
		Data:  []byte(ForceFinishReason),
		Event: []byte("finish"),
	}
}

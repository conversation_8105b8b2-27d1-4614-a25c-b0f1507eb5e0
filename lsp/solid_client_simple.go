package lsp

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qodercli/core/logging"
	"golang.org/x/net/websocket"
	"io"
	"sync"
	"sync/atomic"
	"time"
)

type SimpleSolidLspClient struct {
	// Websocket connection
	wsConn *websocket.Conn

	// Request Id counter
	nextID atomic.Int32

	// Response handlers
	handlers   map[int32]chan *Message
	handlersMu sync.RWMutex

	// Server request handlers
	serverRequestHandlers map[string]ServerRequestHandler
	serverHandlersMu      sync.RWMutex

	// Notification handlers
	notificationHandlers map[string]NotificationHandler
	notificationMu       sync.RWMutex

	// Server state
	serverState atomic.Value

	// LSP configuration
	config *Config

	// Connection state
	connected bool
}

func NewSimpleSolidLspClient(ctx context.Context, lspConfig *Config) (*SimpleSolidLspClient, error) {
	// Connect to the solid LSP server via websocket
	wsURL := "ws://localhost:8008"
	origin := "http://localhost/"

	wsConn, err := websocket.Dial(wsURL, "", origin)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to solid LSP server: %w", err)
	}

	client := &SimpleSolidLspClient{
		wsConn:                wsConn,
		handlers:              make(map[int32]chan *Message),
		notificationHandlers:  make(map[string]NotificationHandler),
		serverRequestHandlers: make(map[string]ServerRequestHandler),
		config:                lspConfig,
		connected:             true,
	}

	// Initialize server state
	client.serverState.Store(StateStarting)

	// Start message handling loop
	go func() {
		defer logging.RecoverPanic("SimpleSolidLSP-message-handler", func() {
			logging.ErrorPersist("Simple Solid LSP message handler crashed, LSP functionality may be impaired")
		})
		client.handleMessages()
	}()

	return client, nil
}

func (c *SimpleSolidLspClient) RegisterNotificationHandler(method string, handler NotificationHandler) {
	c.notificationMu.Lock()
	defer c.notificationMu.Unlock()
	c.notificationHandlers[method] = handler
}

func (c *SimpleSolidLspClient) RegisterServerRequestHandler(method string, handler ServerRequestHandler) {
	c.serverHandlersMu.Lock()
	defer c.serverHandlersMu.Unlock()
	c.serverRequestHandlers[method] = handler
}

// Close closes the websocket connection
func (c *SimpleSolidLspClient) Close() error {
	c.connected = false

	if c.wsConn != nil {
		return c.wsConn.Close()
	}

	return nil
}

// GetServerState returns the current state of the LSP server
func (c *SimpleSolidLspClient) GetServerState() ServerState {
	if val := c.serverState.Load(); val != nil {
		return val.(ServerState)
	}
	return StateStarting
}

// SetServerState sets the current state of the LSP server
func (c *SimpleSolidLspClient) SetServerState(state ServerState) {
	c.serverState.Store(state)
}

// WaitForServerReady waits for the server to be ready
func (c *SimpleSolidLspClient) WaitForServerReady(ctx context.Context) error {
	// Set initial state
	c.SetServerState(StateStarting)

	// Create a context with timeout
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Try to ping the server with a simple request
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	if c.config.DebugLsp {
		logging.Debug("Waiting for Simple Solid LSP server to be ready...")
	}

	for {
		select {
		case <-ctx.Done():
			c.SetServerState(StateError)
			return fmt.Errorf("timeout waiting for LSP server to be ready")
		case <-ticker.C:
			// Try a simple echo request to test connection
			var result json.RawMessage
			err := c.Call(ctx, "textDocument/hover", struct{}{}, &result)

			if err == nil {
				// Server responded successfully
				c.SetServerState(StateReady)
				if c.config.DebugLsp {
					logging.Debug("Simple Solid LSP server is ready")
				}
				return nil
			} else {
				logging.Debug("Simple Solid LSP server not ready yet", "error", err)
			}

			if c.config.DebugLsp {
				logging.Debug("Simple Solid LSP server not ready yet", "error", err)
			}
		}
	}
}

// handleMessages reads and dispatches messages in a loop
func (c *SimpleSolidLspClient) handleMessages() {
	for c.connected {
		// Read message from websocket
		var msg Message
		err := websocket.JSON.Receive(c.wsConn, &msg)
		if err != nil {
			if err == io.EOF || !c.connected {
				if c.config.DebugLsp {
					logging.Debug("Websocket connection closed")
				}
				return
			}

			if c.config.DebugLsp {
				logging.Error("Error reading message from websocket", "error", err)
			}
			continue
		}

		// Handle server->client request (has both Method and Id)
		if msg.Method != "" && msg.ID != 0 {
			if c.config.DebugLsp {
				logging.Debug("Received request from server", "method", msg.Method, "id", msg.ID)
			}

			response := &Message{
				JSONRPC: "2.0",
				ID:      msg.ID,
			}

			// Look up handler for this method
			c.serverHandlersMu.RLock()
			handler, ok := c.serverRequestHandlers[msg.Method]
			c.serverHandlersMu.RUnlock()

			if ok {
				result, err := handler(msg.Params)
				if err != nil {
					response.Error = &ResponseError{
						Code:    -32603,
						Message: err.Error(),
					}
				} else {
					rawJSON, err := json.Marshal(result)
					if err != nil {
						response.Error = &ResponseError{
							Code:    -32603,
							Message: fmt.Sprintf("failed to marshal response: %v", err),
						}
					} else {
						response.Result = rawJSON
					}
				}
			} else {
				response.Error = &ResponseError{
					Code:    -32601,
					Message: fmt.Sprintf("method not found: %s", msg.Method),
				}
			}

			// Send response back to server
			if err := websocket.JSON.Send(c.wsConn, response); err != nil {
				logging.Error("Error sending response to server", "error", err)
			}

			continue
		}

		// Handle notification (has Method but no Id)
		if msg.Method != "" && msg.ID == 0 {
			c.notificationMu.RLock()
			handler, ok := c.notificationHandlers[msg.Method]
			c.notificationMu.RUnlock()

			if ok {
				if c.config.DebugLsp {
					logging.Debug("Handling notification", "method", msg.Method)
				}
				go handler(msg.Params)
			} else if c.config.DebugLsp {
				logging.Debug("No handler for notification", "method", msg.Method)
			}
			continue
		}

		// Handle response to our request (has Id but no Method)
		if msg.ID != 0 && msg.Method == "" {
			c.handlersMu.RLock()
			ch, ok := c.handlers[msg.ID]
			c.handlersMu.RUnlock()

			if ok {
				if c.config.DebugLsp {
					logging.Debug("Received response for request", "id", msg.ID)
				}
				ch <- &msg
				close(ch)
			} else if c.config.DebugLsp {
				logging.Debug("No handler for response", "id", msg.ID)
			}
		}
	}
}

// Call makes a request and waits for the response
func (c *SimpleSolidLspClient) Call(ctx context.Context, method string, params any, result any) error {
	id := c.nextID.Add(1)

	if c.config.DebugLsp {
		logging.Debug("Making call", "method", method, "id", id)
	}

	msg, err := NewRequest(id, method, params)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Create response channel
	ch := make(chan *Message, 1)
	c.handlersMu.Lock()
	c.handlers[id] = ch
	c.handlersMu.Unlock()

	defer func() {
		c.handlersMu.Lock()
		delete(c.handlers, id)
		c.handlersMu.Unlock()
	}()

	// Send request via websocket
	if err := websocket.JSON.Send(c.wsConn, msg); err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}

	if c.config.DebugLsp {
		logging.Debug("Request sent", "method", method, "id", id)
	}

	// Wait for response
	resp := <-ch

	if c.config.DebugLsp {
		logging.Debug("Received response", "id", id)
	}

	if resp.Error != nil {
		return fmt.Errorf("request failed: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	if result != nil {
		// If result is a json.RawMessage, just copy the raw bytes
		if rawMsg, ok := result.(*json.RawMessage); ok {
			*rawMsg = resp.Result
			return nil
		}
		// Otherwise unmarshal into the provided type
		if err := json.Unmarshal(resp.Result, result); err != nil {
			return fmt.Errorf("failed to unmarshal result: %w", err)
		}
	}

	return nil
}

// Notify sends a notification (a request without an Id that doesn't expect a response)
func (c *SimpleSolidLspClient) Notify(ctx context.Context, method string, params any) error {
	if c.config.DebugLsp {
		logging.Debug("Sending notification", "method", method)
	}

	msg, err := NewNotification(method, params)
	if err != nil {
		return fmt.Errorf("failed to create notification: %w", err)
	}

	if err := websocket.JSON.Send(c.wsConn, msg); err != nil {
		return fmt.Errorf("failed to send notification: %w", err)
	}

	return nil
}

// Shutdown sends a shutdown request to the server
func (c *SimpleSolidLspClient) Shutdown(ctx context.Context) error {
	return c.Call(ctx, "shutdown", nil, nil)
}

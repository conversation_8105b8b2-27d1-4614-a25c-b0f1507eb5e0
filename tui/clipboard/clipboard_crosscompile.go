//go:build (darwin || windows) && !cgo

package clipboard

import (
	"github.com/atotto/clipboard"
)

// getClipboardFiles 交叉编译时的 fallback 实现
// 文件拖拽功能不可用，但不会报错
func getClipboardFiles() ([]string, error) {
	return nil, nil // 返回空，表示无文件可粘贴
}

// GetClipboardText 使用纯 Go 实现（保留文本剪贴板功能）
func GetClipboardText() (string, error) {
	return clipboard.ReadAll()
}

// SetClipboardText 使用纯 Go 实现（保留文本剪贴板功能）
func SetClipboardText(text string) error {
	return clipboard.WriteAll(text)
}

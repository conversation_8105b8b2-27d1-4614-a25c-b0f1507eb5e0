//go:build linux

package clipboard

import (
	"os/exec"
	"strings"
)

func getClipboardFiles() ([]string, error) {
	// 尝试使用xclip获取剪贴板内容
	cmd := exec.Command("xclip", "-o", "-selection", "clipboard", "-t", "text/uri-list")
	output, err := cmd.Output()
	if err != nil {
		// 尝试使用xsel作为备选
		cmd = exec.Command("xsel", "--clipboard", "--output")
		output, err = cmd.Output()
		if err != nil {
			return nil, err
		}
	}

	content := strings.TrimSpace(string(output))
	if content == "" {
		return nil, nil
	}

	lines := strings.Split(content, "\n")
	var files []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 处理file://格式的URI
		if strings.HasPrefix(line, "file://") {
			// 去掉file://前缀
			filePath := strings.TrimPrefix(line, "file://")
			// URL解码（简单处理常见字符）
			filePath = strings.ReplaceAll(filePath, "%20", " ")
			filePath = strings.ReplaceAll(filePath, "%25", "%")
			files = append(files, filePath)
		} else if strings.HasPrefix(line, "/") {
			// 直接的文件路径
			files = append(files, line)
		}
	}

	return files, nil
}

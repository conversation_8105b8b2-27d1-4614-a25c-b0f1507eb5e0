//go:build windows && cgo

package clipboard

import (
	"syscall"
	"unsafe"
)

var (
	user32           = syscall.NewLazyDLL("user32.dll")
	kernel32         = syscall.NewLazyDLL("kernel32.dll")
	shell32          = syscall.NewLazyDLL("shell32.dll")
	openClipboard    = user32.NewProc("OpenClipboard")
	closeClipboard   = user32.NewProc("CloseClipboard")
	getClipboardData = user32.NewProc("GetClipboardData")
	globalLock       = kernel32.NewProc("GlobalLock")
	globalUnlock     = kernel32.NewProc("GlobalUnlock")
	dragQueryFileW   = shell32.NewProc("DragQueryFileW")
)

const (
	CF_HDROP = 15
)

func getClipboardFiles() ([]string, error) {
	ret, _, _ := openClipboard.Call(0)
	if ret == 0 {
		return nil, nil
	}
	defer closeClipboard.Call()

	handle, _, _ := getClipboardData.Call(CF_HDROP)
	if handle == 0 {
		return nil, nil
	}

	dropFiles, _, _ := globalLock.Call(handle)
	if dropFiles == 0 {
		return nil, nil
	}
	defer globalUnlock.Call(handle)

	// 获取文件数量
	fileCount, _, _ := dragQueryFileW.Call(dropFiles, 0xFFFFFFFF, 0, 0)
	if fileCount == 0 {
		return nil, nil
	}

	var files []string
	for i := uintptr(0); i < fileCount; i++ {
		// 获取文件名长度
		length, _, _ := dragQueryFileW.Call(dropFiles, i, 0, 0)
		if length == 0 {
			continue
		}

		// 获取文件名
		buffer := make([]uint16, length+1)
		dragQueryFileW.Call(dropFiles, i, uintptr(unsafe.Pointer(&buffer[0])), length+1)
		fileName := syscall.UTF16ToString(buffer)
		if fileName != "" {
			files = append(files, fileName)
		}
	}

	return files, nil
}

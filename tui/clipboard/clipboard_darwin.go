//go:build darwin && cgo

package clipboard

/*
#cgo CFLAGS: -x objective-c
#cgo LDFLAGS: -framework Cocoa
#import <Cocoa/Cocoa.h>

char* getClipboardFilePaths() {
    NSPasteboard *pasteboard = [NSPasteboard generalPasteboard];
    NSArray *items = [pasteboard pasteboardItems];

    if (items && [items count] > 0) {
        NSMutableString *result = [[NSMutableString alloc] init];
        for (NSPasteboardItem *item in items) {
            NSString *urlString = [item stringForType:NSPasteboardTypeFileURL];
            if (urlString) {
                NSURL *url = [NSURL URLWithString:urlString];
                if (url && [url isFileURL]) {
                    [result appendString:[url path]];
                    [result appendString:@"\n"];
                }
            }
        }
        if ([result length] > 0) {
            return strdup([result UTF8String]);
        }
    }

    return NULL;
}
*/
import "C"

import (
	"strings"
	"unsafe"
)

func getClipboardFiles() ([]string, error) {
	cResult := C.getClipboardFilePaths()
	if cResult == nil {
		return nil, nil
	}

	goResult := C.GoString(cResult)
	C.free(unsafe.Pointer(cResult))

	if goResult == "" {
		return nil, nil
	}

	lines := strings.Split(strings.TrimSpace(goResult), "\n")
	var files []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			files = append(files, line)
		}
	}

	return files, nil
}

package clipboard

import (
	"bytes"
	"fmt"
	"github.com/google/uuid"
	"github.com/qoder-ai/qodercli/core/message"
	iclipboard "github.com/skanehira/clipboard-image/v2"
	"image/jpeg"
	"image/png"
	"io"
	"net/http"
	"os"
	"path/filepath"
)

const (
	maxAttachmentSizeBytes = int64(32 * 1024 * 1024) // 5MB
	imageSizeToCompress    = int64(256 * 1024)       // 256KB
)

func GetAttachmentsFromClipboard() ([]message.Attachment, error) {
	// 优先返回文件剪贴版
	attachments, err := ProcessClipboardFiles()
	if err != nil {
		return nil, err
	}

	if len(attachments) > 0 {
		return attachments, nil
	}

	// 截图
	screenShot, err := ProcessClipboardScreenShot()
	if err != nil {
		return nil, err
	}

	if screenShot != nil {
		return []message.Attachment{*screenShot}, nil
	}

	return nil, nil
}

func ProcessClipboardFiles() ([]message.Attachment, error) {
	files, err := getClipboardFiles()
	if err != nil {
		return nil, err
	}

	var attachments []message.Attachment
	for _, file := range files {
		var content []byte
		mimeType := ""
		switch filepath.Ext(file) {
		case ".jpg", ".jpeg", ".png":
			content, err = os.ReadFile(file)
			if err != nil {
				return nil, err
			}

			content, mimeType, err = processImage(content)
			if err != nil {
				return nil, err
			}
		case ".pdf":
			content, err = os.ReadFile(file)
			if err != nil {
				return nil, err
			}

			mimeType = "application/pdf"
		default:
			return nil, fmt.Errorf("unsupported file type: %s, all supported format are .png .jpg .jpeg .pdf", filepath.Ext(file))
		}

		attachments = append(attachments, message.Attachment{
			FilePath: filepath.Base(file),
			FileName: filepath.Base(file),
			MimeType: mimeType,
			Content:  content,
		})
	}

	return attachments, nil
}

func processImage(imageData []byte) ([]byte, string, error) {
	if int64(len(imageData)) > maxAttachmentSizeBytes {
		return nil, "", fmt.Errorf("screenshot too large, max 32MB")
	}

	mimeBufferSize := min(512, len(imageData))
	mimeType := http.DetectContentType(imageData[:mimeBufferSize])

	// 如果图片太大了，要压缩下防止上下文超长
	if mimeType == "image/png" && int64(len(imageData)) > imageSizeToCompress {
		img, err := png.Decode(bytes.NewReader(imageData))
		if err == nil {
			quality := 100 * int(imageSizeToCompress) / len(imageData)
			output := bytes.NewBuffer([]byte{})
			// 用jpeg压缩
			err = jpeg.Encode(output, img, &jpeg.Options{
				Quality: quality,
			})
			if err == nil {
				imageData = output.Bytes()
				mimeType = "image/jpeg"
			}
		}
	}

	return imageData, mimeType, nil
}

func ProcessClipboardScreenShot() (*message.Attachment, error) {
	r, err := iclipboard.Read()
	// 如果读取不到图片内容，要吞掉这个错误
	if err != nil {
		return nil, nil
	}

	imageData, err := io.ReadAll(r)
	if err != nil {
		return nil, err
	}

	if int64(len(imageData)) > maxAttachmentSizeBytes {
		return nil, fmt.Errorf("screenshot too large, max 32MB")
	}

	imageData, mimeType, err := processImage(imageData)

	if err != nil {
		return nil, err
	}

	attachment := message.Attachment{
		FilePath: uuid.NewString(),
		FileName: "screenshot",
		MimeType: mimeType,
		Content:  imageData,
	}

	return &attachment, nil
}

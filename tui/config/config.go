package config

import (
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/models"
)

var storedConfig *TuiConfig
var workingDir string

func WorkingDirectory() string {
	if storedConfig == nil {
		panic("config not loaded")
	}
	return workingDir
}

// 应用程序模式枚举
type AppMode string

const (
	NormalMode           AppMode = "normal"
	InteractionMode      AppMode = "interaction"
	FilePickerMode       AppMode = "filepicker"
	GitHubAppInstallMode AppMode = "github_app_install"
	ExpandedMessagesMode AppMode = "expanded_messages"
)

// 布局配置常量
const (
	// 基础布局尺寸
	EditorHeight = 6 // 编辑器高度
	StatusHeight = 1 // 状态栏高度

	// 最小窗口宽度
	MinWindowWidth = 40 // 最小窗口宽度
)

// UI文本配置
type UITexts struct {
	// 错误消息
	WindowTooSmallMessage string
}

// 默认UI文本配置
var DefaultUITexts = UITexts{
	WindowTooSmallMessage: "Terminal window too small. Required: %dx%d, Current: %dx%d",
}

// TUI配置
type TuiConfig struct {
	Theme               string
	CurrentModel        models.ModelId
	AgentCoder          config.AgentName
	AutoCompact         bool
	Agents              map[config.AgentName]config.AgentConfig
	ProjectInitialized  bool
	AvailableModels     map[models.ModelId]models.Model
	Providers           map[models.ModelProvider]config.ProviderConfig
	UpdateModelCallback func(string, models.ModelId) error
	ThemeCallback       func(string) error
	InitCallback        func() error
	ShouldShowInit      func() (bool, error)
}

type projectInitializer interface {
	Initialized() bool
	Initialize() error
}

func NewTuiConfig(service *config.Service, initializer projectInitializer) *TuiConfig {

	availableModels := make(map[models.ModelId]models.Model)
	agents := service.GetAgents()
	for _, agentCfg := range agents {
		if model, exists := models.SupportedModels[agentCfg.Model]; exists {
			availableModels[agentCfg.Model] = model
		}
	}

	preferences := service.GetPreferences()
	themeCallback := func(theme string) error {
		preferences.Theme = theme
		return service.UpdatePreferences(preferences)
	}

	tuiConfig := &TuiConfig{
		Theme:           preferences.Theme,
		CurrentModel:    agents[config.AgentCoder].Model,
		AgentCoder:      config.AgentCoder,
		AutoCompact:     *preferences.AutoCompactEnabled,
		Agents:          service.GetAgents(),
		AvailableModels: availableModels,
		Providers:       service.GetProviders(),
		ThemeCallback:   themeCallback,
		InitCallback:    initializer.Initialize,
		ShouldShowInit: func() (bool, error) {
			return !initializer.Initialized(), nil
		},
	}

	storedConfig = tuiConfig
	workingDir = service.WorkingDir
	return tuiConfig
}

func GetCurrent() *TuiConfig {
	return storedConfig
}

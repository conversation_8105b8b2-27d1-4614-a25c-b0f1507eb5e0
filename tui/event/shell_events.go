package event

import "time"

// ShellEvent Shell 执行事件
type ShellEvent struct {
	Type        ShellEventType `json:"type"`
	ExecutionId string         `json:"execution_id"`
	Content     string         `json:"content"`
	Error       error          `json:"error,omitempty"`
	ExitCode    int            `json:"exit_code"`
	Timestamp   time.Time      `json:"timestamp"`
}

// ShellEventType Shell 事件类型
type ShellEventType int

const (
	ShellEventStarted  ShellEventType = iota // 命令开始执行
	ShellEventOutput                         // 标准输出
	ShellEventError                          // 标准错误输出
	ShellEventFinished                       // 命令执行完成
	ShellEventCanceled                       // 命令被取消
)

// String 返回事件类型的字符串表示
func (t ShellEventType) String() string {
	switch t {
	case ShellEventStarted:
		return "started"
	case ShellEventOutput:
		return "output"
	case ShellEventError:
		return "error"
	case ShellEventFinished:
		return "finished"
	case ShellEventCanceled:
		return "canceled"
	default:
		return "unknown"
	}
}

// NewShellEvent 创建新的 Shell 事件
func NewShellEvent(eventType ShellEventType, executionId string) ShellEvent {
	return ShellEvent{
		Type:        eventType,
		ExecutionId: executionId,
		Timestamp:   time.Now(),
	}
}

// WithContent 设置事件内容
func (e ShellEvent) WithContent(content string) ShellEvent {
	e.Content = content
	return e
}

// WithError 设置事件错误
func (e ShellEvent) WithError(err error) ShellEvent {
	e.Error = err
	return e
}

// WithExitCode 设置退出码
func (e ShellEvent) WithExitCode(code int) ShellEvent {
	e.ExitCode = code
	return e
}

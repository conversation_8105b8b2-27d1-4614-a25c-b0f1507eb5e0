package runtime

import (
	"context"
	coreShell "github.com/qoder-ai/qodercli/core/llm/shell"

	"github.com/qoder-ai/qodercli/tui/event"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/config"
	coreAgent "github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/core/llm/memory"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
)

type AppRuntime interface {
	ListSessions(ctx context.Context) ([]core.Session, error)
	GetSession(ctx context.Context, id string) (core.Session, error)
	CreateSession(ctx context.Context, title string) (core.Session, error)
	GrantPersistent(permission core.PermissionRequest)
	Grant(permission core.PermissionRequest)
	Deny(permission core.PermissionRequest)
	AutoApproveSession(sessionId string)
	ListMessages(ctx context.Context, sessionId string) ([]message.Message, error)
	CreateMessage(ctx context.Context, sessionId string, params message.CreateMessageParams) (message.Message, error)
	// 文件历史与订阅（用于变更条组件）
	ListLatestSessionFiles(ctx context.Context, sessionId string) ([]core.File, error)
	ListBySession(ctx context.Context, sessionId string) ([]core.File, error)
	SubscribeHistory(ctx context.Context) <-chan pubsub.Event[core.File]
	GetCoderAgent() coreAgent.Agent
	GetAgentService() coreAgent.SubAgentService
	LoadAllCommands() ([]command.Command, error)
	RunAgent(ctx context.Context, sessionId string, content string, attachments ...message.Attachment) (<-chan coreAgent.Event, error)
	GetWorkingDir() string

	// memory
	AppendMemory(location memory.MemoryLocation, content string) error
	GetUserMemoryFilePath() string
	GetProjectMemoryFilePath() string
	GetProjectLocalMemoryFilePath() string

	// permissions
	ToggleAutoApproveAll() bool
	IsAutoApproveAll() bool

	// history
	AppendHistory(entry config.HistoryEntry) error
	GetHistoryList() []config.HistoryEntry

	// github
	GetGitHubService() core.GitHubService

	// shell execution
	ExecuteShell(ctx context.Context, command string, workingDir string, executionId string) (<-chan event.ShellEvent, error)
	CancelShell(executionId string) error

	// llm-shells
	ListShells() []coreShell.Shell
	SubscribeShell(ctx context.Context) <-chan pubsub.Event[coreShell.Event]
}

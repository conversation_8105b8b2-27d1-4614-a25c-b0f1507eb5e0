package tui

import (
	"context"
	"fmt"
	hinttypes "github.com/qoder-ai/qodercli/tui/components/types/hint"
	"os"
	"strings"
	"time"

	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"

	"github.com/qoder-ai/qodercli/core/llm/command"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core"
	coreconfig "github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/memory"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"github.com/qoder-ai/qodercli/qoder"
	"github.com/qoder-ai/qodercli/tui/components/chat"

	messageshandler "github.com/qoder-ai/qodercli/tui/components/handler/messages"
	messagestypes "github.com/qoder-ai/qodercli/tui/components/types/messages"
	statustypes "github.com/qoder-ai/qodercli/tui/components/types/status"
	"github.com/qoder-ai/qodercli/tui/components/utils"
	"github.com/qoder-ai/qodercli/tui/config"
	tuiconfig "github.com/qoder-ai/qodercli/tui/config"
	"github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/util"
)

// appModel 表示主应用程序状态
type appModel struct {
	app             runtime.AppRuntime
	selectedSession core.Session
	tuiConfig       *tuiconfig.TuiConfig

	// UI 组件
	messages       *chat.MessagesComponent
	editor         *chat.EditorComponent
	interaction    *chat.InteractionComponent
	status         *chat.StatusComponent
	hint           *chat.HintComponent
	progress       *chat.ProgressIndicatorComponent
	filepicker     *utils.FilePickerComponent
	atSelector     *utils.AtSelector
	cmdSelector    *utils.CommandSelector
	memorySelector *utils.MemorySelector

	// 状态管理
	currentMode      config.AppMode
	previousMode     config.AppMode // 进入展开模式前的模式，用于恢复
	width            int
	height           int
	memoryInputChan  chan any // 用于与memory命令通信
	memoryInputMsgID *string  // 记忆操作的InputMsgID

	// GitHub App安装组件
	gitHubAppInstaller *utils.GitHubAppInstaller

	// 退出计数
	quitKeyCount    int       // Ctrl+C 键按下次数，用于双击退出
	quitKeyLastTime time.Time // 上次Ctrl+C按键的时间，用于超时重置

	isBashRunning bool
}

// Init 初始化应用程序和所有组件
func (a *appModel) Init() tea.Cmd {
	var cmds []tea.Cmd

	cmd := a.status.Init()
	cmds = append(cmds, cmd)
	cmd = a.messages.Init()
	cmds = append(cmds, cmd)
	cmd = a.editor.Init()
	cmds = append(cmds, cmd)
	cmd = a.interaction.Init()
	cmds = append(cmds, cmd)
	cmd = a.hint.Init()
	cmds = append(cmds, cmd)
	cmd = a.progress.Init()
	cmds = append(cmds, cmd)
	cmd = a.filepicker.Init()
	cmds = append(cmds, cmd)

	// 初始化GitHub App安装组件
	cmd = a.gitHubAppInstaller.Init()
	cmds = append(cmds, cmd)

	// 初始化会话
	if a.selectedSession.Id != "" {
		cmd = func() tea.Msg {
			return messagestypes.SessionChangedMsg{Session: a.selectedSession}
		}
		cmds = append(cmds, cmd)
	}

	return tea.Batch(cmds...)
}

func (a *appModel) UpdateAllComponents(msg tea.Msg) []tea.Cmd {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	default:
		m, cmd := a.messages.Update(msg)
		a.messages = m
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		e, cmd := a.editor.Update(msg)
		a.editor = e.(*chat.EditorComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		i, cmd := a.interaction.Update(msg)
		a.interaction = i.(*chat.InteractionComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		s, cmd := a.status.Update(msg)
		a.status = s.(*chat.StatusComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		h, cmd := a.hint.Update(msg)
		a.hint = h.(*chat.HintComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		p, cmd := a.progress.Update(msg)
		a.progress = p.(*chat.ProgressIndicatorComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		f, cmd := a.filepicker.Update(msg)
		a.filepicker = f.(*utils.FilePickerComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		as, cmd := a.atSelector.Update(msg)
		a.atSelector = as.(*utils.AtSelector)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		cs, cmd := a.cmdSelector.Update(msg)
		a.cmdSelector = cs.(*utils.CommandSelector)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		ms, cmd := a.memorySelector.Update(msg)
		a.memorySelector = ms.(*utils.MemorySelector)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
	}

	return cmds
}

// isNavigationKey 判断按键是否为导航键，这些键应该路由到选择器
func (a *appModel) isNavigationKey(msg tea.KeyMsg) bool {
	switch msg.String() {
	case "up", "down", // 上下导航键
		"enter", "esc", // 选择和取消键
		"tab",         // 选择不执行
		"home", "end", // 首尾导航
		"pgup", "pgdown": // 翻页键
		return true
	default:
		return false
	}
}

// Update 处理应用程序状态更新
func (a *appModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case messagestypes.EnterExpandedModeMsg:
		// 进入展开模式，保存当前模式以便稍后恢复
		a.previousMode = a.currentMode
		a.currentMode = config.ExpandedMessagesMode

		// 传递消息给hint组件更新状态
		hint, cmd := a.hint.Update(msg)
		a.hint = hint.(*chat.HintComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		return a, tea.Batch(cmds...)

	case messagestypes.ExitExpandedModeMsg:
		// 退出展开模式，恢复之前的模式
		if a.previousMode != "" {
			a.currentMode = a.previousMode
		} else {
			a.currentMode = config.NormalMode
		}

		// 传递消息给hint组件更新状态
		hint, cmd := a.hint.Update(msg)
		a.hint = hint.(*chat.HintComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		return a, tea.Batch(cmds...)

	case pubsub.Event[command.Event]:
		// 处理不同类型的命令事件
		switch msg.Payload.CommandName {
		case "install-github-app":
			// 触发 GitHub App 安装组件
			a.currentMode = config.GitHubAppInstallMode
			startCmd := a.gitHubAppInstaller.Start()
			if startCmd != nil {
				cmds = append(cmds, startCmd)
			}
			return a, tea.Batch(cmds...)
		case "login":
			// /login命令执行后启动登录状态监听
			startMonitor := func() tea.Msg {
				return statustypes.StartLoginMonitorMsg{}
			}
			cmds = append(cmds, startMonitor)
		case "delay":
			// fixme 仅演示用
			inputChan := msg.Payload.InputChan
			if inputChan != nil {
				inputChan <- "abc"
			}
			return a, a.sendMessage(msg.Payload.Payload.(string), nil)
		case "vim", "memory", "clear", "resume":
			return a, a.handleCommandEvent(msg)
		}
	case tea.WindowSizeMsg:
		a.width, a.height = msg.Width, msg.Height
		// 将窗口尺寸变化传递给所有组件
		cmds = append(cmds, a.UpdateAllComponents(msg)...)
	case tea.KeyMsg:
		// 处理ctrl+r切换展开模式
		if msg.String() == "ctrl+r" {
			cmd := a.messages.ToggleExpansion()
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return a, tea.Batch(cmds...)
		}

		// 处理ctrl+e切换完全展开模式（仅在展开模式下有效）
		if msg.String() == "ctrl+e" && a.currentMode == config.ExpandedMessagesMode {
			cmd := a.messages.ToggleFullExpansion()
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return a, tea.Batch(cmds...)
		}

		// 如果有任一选择器激活，则优先路由导航键
		if a.memorySelector.IsActive() && a.isNavigationKey(msg) {
			ms, cmd := a.memorySelector.Update(msg)
			a.memorySelector = ms.(*utils.MemorySelector)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return a, tea.Batch(cmds...)
		}
		if a.cmdSelector.IsActive() && a.isNavigationKey(msg) {
			cs, cmd := a.cmdSelector.Update(msg)
			a.cmdSelector = cs.(*utils.CommandSelector)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return a, tea.Batch(cmds...)
		}
		if a.atSelector.IsActive() && a.isNavigationKey(msg) {
			as, cmd := a.atSelector.Update(msg)
			a.atSelector = as.(*utils.AtSelector)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return a, tea.Batch(cmds...)
		}
		// 根据当前模式路由键盘输入
		switch a.currentMode {
		case config.GitHubAppInstallMode:
			// GitHub App 安装模式：优先路由给 GitHub App 安装组件
			gh, cmd := a.gitHubAppInstaller.Update(msg)
			a.gitHubAppInstaller = gh.(*utils.GitHubAppInstaller)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return a, tea.Batch(cmds...)
		case config.FilePickerMode:
			// FilePicker 模式：优先路由给 filepicker
			f, cmd := a.filepicker.Update(msg)
			a.filepicker = f.(*utils.FilePickerComponent)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return a, tea.Batch(cmds...)
		case config.InteractionMode:
			if a.interaction.IsActive() {
				m, cmd := a.interaction.Update(msg)
				a.interaction = m.(*chat.InteractionComponent)
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
				return a, tea.Batch(cmds...)
			}
			// 如果交互不活跃，切换回普通模式
			a.currentMode = config.NormalMode
			fallthrough
		default:
			// 处理全局键和基于焦点的路由
			switch msg.String() {
			case "esc":
				// 如果当前为消息展开模式，esc触发退出该模式
				if a.currentMode == config.ExpandedMessagesMode {
					cmd := a.messages.ToggleExpansion()
					if cmd != nil {
						cmds = append(cmds, cmd)
					}
					a.currentMode = a.previousMode
					return a, tea.Batch(cmds...)
				}
				if a.isBashRunning {
					m, cmd := a.messages.Update(msg)
					a.messages = m
					if cmd != nil {
						cmds = append(cmds, cmd)
					}
					return a, tea.Batch(cmds...)
				}
				m, cmd := a.editor.Update(msg)
				a.editor = m.(*chat.EditorComponent)
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
				return a, tea.Batch(cmds...)
			case "ctrl+c":
				// 如果当前为消息展开模式，ctrl+c触发退出该模式
				if a.currentMode == config.ExpandedMessagesMode {
					cmd := a.messages.ToggleExpansion()
					if cmd != nil {
						cmds = append(cmds, cmd)
					}
					a.currentMode = a.previousMode
					return a, tea.Batch(cmds...)
				}
				currentTime := time.Now()
				if !a.quitKeyLastTime.IsZero() && currentTime.Sub(a.quitKeyLastTime) > time.Second {
					a.quitKeyCount = 0
				}
				a.quitKeyCount++
				a.quitKeyLastTime = currentTime
				if a.quitKeyCount >= 2 {
					a.quitKeyCount = 0
					a.quitKeyLastTime = time.Time{} // 重置时间
					return a, tea.Quit
				}
				// 第一次按下，触发 Hint 的 quit 提示，并在 0.5s 后自动恢复
				currentHintState := a.hint.GetState()
				return a, tea.Sequence(
					func() tea.Msg { return hinttypes.SwitchStateMsg{State: hinttypes.HintQuitTip} },
					tea.Tick(500*time.Millisecond, func(t time.Time) tea.Msg { return hinttypes.SwitchStateMsg{State: currentHintState} }),
				)
			case "ctrl+f":
				// 检查模型是否支持附件
				cfg := tuiconfig.GetCurrent()
				agentCfg := cfg.Agents[coreconfig.AgentCoder]
				modelInfo := models.SupportedModels[agentCfg.Model]
				if !modelInfo.SupportsAttachments {
					return a, util.ReportWarn("Current model does not support attachments")
				}
				a.currentMode = config.FilePickerMode
				return a, nil
			default:
				// 展开模式下不接受键盘输入到编辑框
				if a.currentMode == config.ExpandedMessagesMode {
					return a, nil
				}

				// 正常情况：路由到editor组件
				m, cmd := a.editor.Update(msg)
				a.editor = m.(*chat.EditorComponent)
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
				return a, tea.Batch(cmds...)
			}
		}
	case messagestypes.BashOperationStartMsg:
		// 确保有活跃会话
		session, sessionCreated, err := a.ensureSession()
		if err != nil {
			return a, func() tea.Msg {
				return util.InfoMsg{
					Type: util.InfoTypeError,
					Msg:  "Failed to create session for bash command: " + err.Error(),
					TTL:  5 * time.Second,
				}
			}
		}

		a.isBashRunning = true

		// 如果创建了新会话，需要先通知messages组件更新会话，然后再处理bash命令
		if sessionCreated {
			// 先更新messages组件的session
			m, _ := a.messages.Update(messagestypes.SessionChangedMsg{Session: *session})
			a.messages = m

			// 然后处理bash命令
			m, cmd := a.messages.Update(msg)
			a.messages = m
			if cmd != nil {
				cmds = append(cmds, cmd)
			}

			// 通知其他组件会话变更
			sessionCmd := a.notifySessionChanged(*session)
			cmds = append(cmds, sessionCmd)
		} else {
			// 没有创建新会话，直接处理bash命令
			m, cmd := a.messages.Update(msg)
			a.messages = m
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
		}

		return a, tea.Batch(cmds...)
	case messagestypes.BashFinishedMsg:
		a.isBashRunning = false
		m, cmd := a.messages.Update(msg)
		a.messages = m
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		return a, tea.Batch(cmds...)

	case messagestypes.MemoryOperationStartMsg:
		// 确保有活跃会话
		session, sessionCreated, err := a.ensureSession()
		if err != nil {
			return a, func() tea.Msg {
				return util.InfoMsg{
					Type: util.InfoTypeError,
					Msg:  "Failed to create session for memory operation: " + err.Error(),
					TTL:  5 * time.Second,
				}
			}
		}

		// 如果创建了新会话，需要先通知messages组件更新会话，然后再处理记忆操作
		if sessionCreated {
			// 先更新messages组件的session
			m, _ := a.messages.Update(messagestypes.SessionChangedMsg{Session: *session})
			a.messages = m

			// 然后处理记忆操作
			m, cmd := a.messages.Update(msg)
			a.messages = m
			if cmd != nil {
				cmds = append(cmds, cmd)
			}

			// 通知其他组件会话变更
			sessionCmd := a.notifySessionChanged(*session)
			cmds = append(cmds, sessionCmd)
		} else {
			// 没有创建新会话，直接处理记忆操作
			m, cmd := a.messages.Update(msg)
			a.messages = m
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
		}

		return a, tea.Batch(cmds...)

	case messagestypes.MemoryOperationCompleteMsg:
		// 处理记忆操作完成
		m, cmd := a.messages.Update(msg)
		a.messages = m
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		return a, tea.Batch(cmds...)

	case messageshandler.MemorySelectorTriggerMsg:
		// 处理记忆选择器触发消息，存储InputMsgID用于后续使用
		a.memoryInputMsgID = msg.InputMsgID
		ms, cmd := a.memorySelector.Update(utils.MemorySelectorOpenMsg{Content: msg.Content})
		a.memorySelector = ms.(*utils.MemorySelector)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		return a, tea.Batch(cmds...)
	case utils.CommandSelectedMsg:
		// 只填入，不发送
		insert := msg.Command.Name
		e, cmd := a.editor.Update(utils.InputCommandToEditorMsg{Command: msg.Command})
		a.editor = e.(*chat.EditorComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		_ = insert
		return a, tea.Batch(cmds...)
	case utils.CommandExecuteMsg:
		// 填入并发送（顺序）
		fill := func() tea.Msg { return utils.InputCommandToEditorMsg{Command: msg.Command} }
		send := func() tea.Msg { return editortypes.TriggerEditorSendMsg{} }
		return a, tea.Sequence(fill, send)
	case utils.FilePickerExitMsg:
		// 文件选择组件请求退出
		a.currentMode = config.NormalMode
		return a, nil
	case utils.MemoryTargetSelectedMsg:
		// 检查是否是memory命令触发的选择（通过memoryInputChan判断）
		if a.memoryInputChan != nil {
			// 这是memory命令触发的，需要打开文件编辑
			cmd := a.handleMemoryFileEdit(msg)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
		} else {
			// 这是普通的保存memory操作，传递InputMsgID
			msgWithInputMsgID := utils.MemoryTargetSelectedMsg{
				Location:   msg.Location,
				Content:    msg.Content,
				InputMsgID: a.memoryInputMsgID,
			}
			e, cmd := a.editor.Update(msgWithInputMsgID)
			a.editor = e.(*chat.EditorComponent)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			// 清理InputMsgID
			a.memoryInputMsgID = nil
		}
		// 清理状态并关闭选择器
		ms, closeCmd := a.memorySelector.Update(utils.MemorySelectorCanceledMsg{})
		a.memorySelector = ms.(*utils.MemorySelector)
		if closeCmd != nil {
			cmds = append(cmds, closeCmd)
		}
		return a, tea.Batch(cmds...)
	case utils.MemorySelectorCanceledMsg:
		// 检查是否是memory命令触发的选择
		if a.memoryInputChan != nil {
			// 发送取消结果给命令
			a.memoryInputChan <- map[string]interface{}{
				"success":   false,
				"cancelled": true,
			}
			a.memoryInputChan = nil
		} else if a.memoryInputMsgID != nil {
			// 这是#记忆模式取消，发送取消消息给MemoryHandler
			cmds = append(cmds, util.CmdHandler(messagestypes.MemoryOperationCompleteMsg{
				Content:    "",          // 将由MemoryHandler从Input消息中提取
				Location:   "CANCELLED", // 特殊标记表示取消
				Success:    false,
				InputMsgID: a.memoryInputMsgID,
			}))
			// 清理InputMsgID
			a.memoryInputMsgID = nil
		}
		// 取消选择，不清空编辑器也不退出记忆模式
		return a, tea.Batch(cmds...)

	case pubsub.Event[core.PermissionRequest]:
		if a.currentMode == config.ExpandedMessagesMode {
			a.previousMode = config.InteractionMode
		} else {
			a.currentMode = config.InteractionMode
		}
		a.interaction.SetPermissionRequest(msg.Payload)
		return a, nil

	case chat.InteractionCompleteMsg:
		a.currentMode = config.NormalMode
		a.interaction.Clear()

		// 处理权限响应
		if msg.Request != nil {
			switch msg.Action {
			case chat.PermissionAllow:
				a.app.Grant(*msg.Request)
			case chat.PermissionAllowForSession:
				a.app.GrantPersistent(*msg.Request)
			case chat.PermissionDeny:
				a.app.Deny(*msg.Request)
			}
		}
		return a, nil

	case pubsub.Event[core.Session]:
		switch msg.Type {
		case pubsub.UpdatedEvent:
			if a.selectedSession.Id == msg.Payload.Id {
				a.selectedSession = msg.Payload
			}
		}
		return a, tea.Batch(cmds...)

	case pubsub.Event[message.Message]:
		// 处理消息事件 - 同时发送给messages组件和progress组件
		m, cmd := a.messages.Update(msg)
		a.messages = m
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		// 同时发送给进度指示器
		p, cmd := a.progress.Update(msg)
		a.progress = p.(*chat.ProgressIndicatorComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

	case pubsub.Event[agent.Event]:
		payload := msg.Payload

		// 处理错误
		if payload.Error != nil {
			m, cmd := a.messages.Update(util.InfoMsg{
				Type: util.InfoTypeError,
				Msg:  payload.Error.Error(),
				TTL:  5 * time.Second,
			})
			a.messages = m
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return a, tea.Batch(cmds...)
		}

		// 将 agent 事件继续转发给所有组件（例如 ProgressIndicator 处理 summarize 进度）
		cmds = append(cmds, a.UpdateAllComponents(msg)...)
		return a, tea.Batch(cmds...)

	case editortypes.SendMsg:
		// 处理消息发送
		return a, a.sendMessage(msg.Text, msg.Attachments)

	case util.InfoMsg:
		m, cmd := a.messages.Update(msg)
		a.messages = m
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		return a, tea.Batch(cmds...)

	case pubsub.Event[logging.LogMessage]:
		if msg.Payload.Persist {
			switch msg.Payload.Level {
			case "error":
				m, cmd := a.messages.Update(util.InfoMsg{
					Type: util.InfoTypeError,
					Msg:  msg.Payload.Message,
					TTL:  msg.Payload.PersistTime,
				})
				a.messages = m
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
			case "info":
				m, cmd := a.messages.Update(util.InfoMsg{
					Type: util.InfoTypeInfo,
					Msg:  msg.Payload.Message,
					TTL:  msg.Payload.PersistTime,
				})
				a.messages = m
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
			case "warn":
				m, cmd := a.messages.Update(util.InfoMsg{
					Type: util.InfoTypeWarn,
					Msg:  msg.Payload.Message,
					TTL:  msg.Payload.PersistTime,
				})
				a.messages = m
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
			default:
				m, cmd := a.messages.Update(util.InfoMsg{
					Type: util.InfoTypeInfo,
					Msg:  msg.Payload.Message,
					TTL:  msg.Payload.PersistTime,
				})
				a.messages = m
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
			}
		}
		return a, tea.Batch(cmds...)

	case utils.GitHubAppInstallResponseMsg:
		// 处理GitHub App安装响应消息
		response := msg
		// 更新组件状态
		gh, cmd := a.gitHubAppInstaller.Update(msg)
		a.gitHubAppInstaller = gh.(*utils.GitHubAppInstaller)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		// 只有在以下情况才关闭组件：
		// 1. 组件已被设置为不活跃状态（用户在完成页面按了Enter或Esc）
		// 2. 用户主动取消时（Success=false 且 Message包含"cancelled"）
		if !a.gitHubAppInstaller.IsActive() {
			a.currentMode = config.NormalMode
			// 组件已关闭，退出安装模式
		} else if !response.Success && strings.Contains(response.Message, "cancelled") {
			a.currentMode = config.NormalMode
			// 用户主动取消，退出安装模式
		}
		// 其他情况（包括完成状态）继续保持在安装模式，直到用户显式确认
		return a, tea.Batch(cmds...)

	default:
		return a, tea.Batch(append(cmds, a.UpdateAllComponents(msg)...)...)
	}

	return a, tea.Batch(cmds...)
}

// ensureSession 确保有活跃的会话，如果没有则创建
func (a *appModel) ensureSession() (*core.Session, bool, error) {
	if a.selectedSession.Id != "" {
		return &a.selectedSession, false, nil
	}

	newSession, err := a.app.CreateSession(context.Background(), "New Session")
	if err != nil {
		return nil, false, err
	}

	a.selectedSession = newSession
	return &newSession, true, nil
}

// notifySessionChanged 通知组件会话已变更
func (a *appModel) notifySessionChanged(session core.Session) tea.Cmd {
	// 更新progress组件
	p, cmd := a.progress.Update(messagestypes.SessionChangedMsg{Session: session})
	a.progress = p.(*chat.ProgressIndicatorComponent)

	sessionCmd := func() tea.Msg {
		return messagestypes.SessionChangedMsg{Session: session}
	}

	if cmd != nil {
		return tea.Batch(sessionCmd, cmd)
	}
	return sessionCmd
}

// sendMessage 发送消息给AI Agent
func (a *appModel) sendMessage(text string, attachments []message.Attachment) tea.Cmd {
	// 检查登录状态，如果未登录则自动触发登录
	// 但如果发送的是/help命令，则跳过登录检查，直接执行
	if !qoder.IsUserLoggedIn() && !strings.HasPrefix(strings.TrimSpace(text), "/login") && !strings.HasPrefix(strings.TrimSpace(text), "/help") {
		return a.executeLoginCommand()
	}

	// 确保有活跃的session
	session, sessionCreated, err := a.ensureSession()
	if err != nil {
		return func() tea.Msg {
			return util.InfoMsg{
				Type: util.InfoTypeError,
				Msg:  "Failed to create session: " + err.Error(),
				TTL:  5 * time.Second,
			}
		}
	}

	// 发送消息给AI Agent
	sendCmd := func() tea.Msg {
		_, err := a.app.RunAgent(context.Background(), session.Id, text, attachments...)
		if err != nil {
			return util.InfoMsg{
				Type: util.InfoTypeError,
				Msg:  "发送消息失败: " + err.Error(),
				TTL:  5 * time.Second,
			}
		}
		return nil
	}

	// 如果创建了新会话，需要通知组件
	if sessionCreated {
		sessionCmd := a.notifySessionChanged(*session)
		return tea.Sequence(sessionCmd, sendCmd)
	}

	return sendCmd
}

// executeLoginCommand 自动触发登录命令
func (a *appModel) executeLoginCommand() tea.Cmd {
	// 创建login命令对象
	loginCommand := utils.Command{
		ID:          "/login",
		Name:        "login",
		Description: "Sign in with your Qoder account",
	}

	// 使用和其他slash命令相同的机制：填充并发送
	fill := func() tea.Msg { return utils.InputCommandToEditorMsg{Command: loginCommand} }
	send := func() tea.Msg { return editortypes.TriggerEditorSendMsg{} }

	return tea.Sequence(fill, send)
}

// View 渲染应用程序界面
func (a *appModel) View() string {
	// 计算最小窗口高度：状态栏 + 编辑器 + 提示框
	minRequiredHeight := config.StatusHeight + config.EditorHeight + 1 // +1 for hint minimum

	// 检查最小窗口尺寸
	if a.height < minRequiredHeight || a.width < config.MinWindowWidth {
		return fmt.Sprintf(config.DefaultUITexts.WindowTooSmallMessage,
			config.MinWindowWidth, minRequiredHeight, a.width, a.height)
	}

	// 根据当前模式渲染UI组件
	switch a.currentMode {
	case config.ExpandedMessagesMode:
		// 展开模式：显示消息组件（展开版本）+ hint组件
		var components []string
		components = append(components, a.messages.ViewExpanded())
		components = append(components, a.hint.View())
		return strings.Join(components, "\n") + "\033[0J"
	case config.GitHubAppInstallMode:
		// GitHub App 安装模式：消息 + 安装组件
		var components []string
		components = append(components, a.messages.View())
		components = append(components, a.gitHubAppInstaller.View())
		return strings.Join(components, "\n") + "\033[0J"
	case config.InteractionMode:
		// 交互模式：交互框 + 状态栏
		var components []string
		components = append(components, a.messages.View())
		components = append(components, a.interaction.View())
		return strings.Join(components, "\n") + "\033[0J"
	case config.FilePickerMode:
		// 文件选择器模式：仅消息 + 文件选择器
		var components []string
		components = append(components, a.messages.View())
		components = append(components, a.filepicker.View())
		return strings.Join(components, "\n") + "\033[0J"
	default:
		var components []string

		components = append(components, a.messages.View(), "\n")
		components = append(components, a.progress.View())
		components = append(components, a.editor.View())
		// 选择器区域：优先 memory，其次 slash，再次 @，否则 hint
		a.atSelector.SetSize(a.width)
		a.cmdSelector.SetSize(a.width, 5)
		a.memorySelector.SetSize(a.width)
		if a.memorySelector.IsActive() {
			components = append(components, a.memorySelector.View())
		} else if a.cmdSelector.IsActive() {
			components = append(components, a.cmdSelector.View())
		} else if a.atSelector.IsActive() {
			components = append(components, a.atSelector.View())
		} else {
			components = append(components, a.hint.View())
		}
		components = append(components, a.status.View())

		return strings.Join(components, "\n") + "\033[0J"
	}
}

func (a *appModel) GetCurrentSession() core.Session {
	return a.selectedSession
}

// New 创建新的应用程序实例
func New(app runtime.AppRuntime, tuiConfig *tuiconfig.TuiConfig, initPrompt string, sessionId string) tea.Model {
	hint := chat.NewHintComponent(app)

	selectedSession := core.Session{}

	if sessionId != "" {
		sess, err := app.GetSession(context.Background(), sessionId)
		if err != nil || sess.Id == "" {
			os.Exit(1)
		}

		selectedSession = sess
	}
	a := &appModel{
		app:             app,
		tuiConfig:       tuiConfig,
		selectedSession: selectedSession,

		// 初始化UI组件
		messages:       chat.NewMessagesComponent(app, messageshandler.NewBashHandler(app, 80), messageshandler.NewCommandHandler(), messageshandler.NewMemoryHandler(app, 80)),
		editor:         chat.NewEditorComponent(app, hint, initPrompt, selectedSession),
		interaction:    chat.NewInteractionComponent(),
		hint:           chat.NewHintComponent(app),
		progress:       chat.NewProgressIndicatorComponent(app),
		filepicker:     utils.NewFilePickerComponent(tuiConfig),
		atSelector:     utils.NewAtSelector(),
		cmdSelector:    utils.NewCommandSelector(app),
		memorySelector: utils.NewMemorySelector(),

		// 设置默认模式
		currentMode: config.NormalMode,

		// GitHub App安装组件
		gitHubAppInstaller: utils.NewGitHubAppInstaller(app),
	}

	status := chat.NewStatusCmp(app, a)
	a.status = status

	return a
}

// handleCommandEvent 处理命令事件，特别是增强的 compact 命令
func (a *appModel) handleCommandEvent(msg pubsub.Event[command.Event]) tea.Cmd {
	payload := msg.Payload

	switch payload.CommandName {
	case "vim":
		if payload.SessionId != a.selectedSession.Id {
			return nil
		}
		return a.handleVimEvent(payload)
	case "memory":
		if payload.SessionId != a.selectedSession.Id {
			return nil
		}
		return a.handleMemoryEvent(payload)
	case "clear":
		return a.handleClearEvent(payload)
	case "resume":
		return a.handleResumeEvent(payload)
	case "delay":
		if payload.SessionId != a.selectedSession.Id {
			return nil
		}
		// 处理 delay 命令的示例交互
		if payload.InputChan != nil {
			payload.InputChan <- "received from UI"
		}
		// 将 payload 作为信息消息显示
		if payloadStr, ok := payload.Payload.(string); ok {
			return util.ReportInfo("延迟命令: " + payloadStr)
		}
	}

	return nil
}

// handleVimEvent 处理 vim 命令的不同阶段事件
func (a *appModel) handleVimEvent(payload command.Event) tea.Cmd {
	payloadMap, ok := payload.Payload.(map[string]interface{})
	if !ok {
		return nil
	}

	eventType, _ := payloadMap["type"].(string)

	switch eventType {
	case "open_editor":
		// 打开外部编辑器
		return a.editor.OpenExternalEditor(payload)

	case "complete":
		// 显示完成信息并发送内容
		if content, ok := payloadMap["content"].(string); ok {
			// 发送编辑的内容作为消息
			return a.sendMessage(content, nil)
		}
	}

	return nil
}

// handleMemoryEvent 处理 memory 命令事件
func (a *appModel) handleMemoryEvent(payload command.Event) tea.Cmd {
	payloadMap, ok := payload.Payload.(map[string]interface{})
	if !ok {
		return nil
	}

	eventType, _ := payloadMap["type"].(string)

	switch eventType {
	case "open_selector":
		// 打开记忆选择器
		return a.openMemorySelector(payload)
	}

	return nil
}

// openMemorySelector 打开记忆选择器
func (a *appModel) openMemorySelector(payload command.Event) tea.Cmd {
	// 打开记忆选择器
	ms, cmd := a.memorySelector.Update(utils.MemorySelectorOpenMsg{Content: ""})
	a.memorySelector = ms.(*utils.MemorySelector)

	// 存储payload的InputChan用于后续通信
	a.memoryInputChan = payload.InputChan

	return cmd
}

// handleMemoryFileEdit 处理记忆文件编辑
func (a *appModel) handleMemoryFileEdit(msg utils.MemoryTargetSelectedMsg) tea.Cmd {
	var filePath string

	// 根据选择的位置获取文件路径
	switch msg.Location {
	case memory.UserMemoryLocation:
		filePath = a.app.GetUserMemoryFilePath()
	case memory.ProjectMemoryLocation:
		filePath = a.app.GetProjectMemoryFilePath()
	case memory.ProjectLocalMemoryLocation:
		filePath = a.app.GetProjectLocalMemoryFilePath()
	default:
		// 发送错误结果给命令
		if a.memoryInputChan != nil {
			a.memoryInputChan <- map[string]interface{}{
				"success": false,
				"error":   "Unsupported memory location",
			}
			a.memoryInputChan = nil
		}
		return nil
	}

	// 使用vim编辑文件
	cmd := a.editor.OpenExternalEditorWithFile(filePath, a.memoryInputChan)
	a.memoryInputChan = nil // 清理状态
	return cmd
}

// handleClearEvent 处理 clear 命令事件
func (a *appModel) handleClearEvent(payload command.Event) tea.Cmd {
	payloadMap, ok := payload.Payload.(map[string]interface{})
	if !ok {
		return nil
	}

	eventType, ok := payloadMap["type"].(string)
	if !ok {
		return nil
	}

	switch eventType {
	case "session_switch":
		// 处理会话切换
		newSessionId, ok := payloadMap["new_session_id"].(string)
		if !ok {
			// 发送错误结果给服务端
			if payload.InputChan != nil {
				payload.InputChan <- map[string]interface{}{
					"success": false,
					"error":   "Invalid session ID",
				}
			}
			return nil
		}

		// 执行会话切换
		return a.switchToSessionWithResult(newSessionId, payload.InputChan)
	}

	return nil
}

// handleResumeEvent 处理 resume 命令事件
func (a *appModel) handleResumeEvent(payload command.Event) tea.Cmd {
	payloadMap, ok := payload.Payload.(map[string]interface{})
	if !ok {
		return nil
	}

	eventType, ok := payloadMap["type"].(string)
	if !ok {
		return nil
	}

	switch eventType {
	case "session_switch":
		// 处理会话切换
		newSessionId, ok := payloadMap["new_session_id"].(string)
		if !ok {
			// 发送错误结果给服务端
			if payload.InputChan != nil {
				payload.InputChan <- map[string]interface{}{
					"success": false,
					"error":   "Invalid session ID",
				}
			}
			return nil
		}

		// 执行会话切换
		return a.switchToSessionWithResult(newSessionId, payload.InputChan)
	}

	return nil
}

// switchToSessionWithResult 切换到指定会话并回传结果
func (a *appModel) switchToSessionWithResult(sessionId string, inputChan chan interface{}) tea.Cmd {
	// 获取会话详情
	session, err := a.app.GetSession(context.Background(), sessionId)
	if err != nil {
		// 发送错误结果给服务端
		if inputChan != nil {
			inputChan <- map[string]interface{}{
				"success": false,
				"error":   "Failed to get session: " + err.Error(),
			}
		}
		return nil
	}

	// 更新当前会话
	a.selectedSession = session

	// 通知各子组件加载新会话
	sessionChangedMsg := messagestypes.SessionChangedMsg{Session: session}

	var cmds []tea.Cmd

	// 更新所有组件
	cmds = append(cmds, a.UpdateAllComponents(sessionChangedMsg)...)

	// 发送成功结果给服务端
	if inputChan != nil {
		go func() {
			inputChan <- map[string]interface{}{
				"success": true,
			}
		}()
	}

	// 返回批量命令
	return tea.Batch(cmds...)
}

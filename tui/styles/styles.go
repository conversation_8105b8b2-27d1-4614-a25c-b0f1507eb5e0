// Package styles 为TUI组件提供一致的样式函数
package styles

import (
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qodercli/tui/theme"
)

// 使用当前主题的样式生成函数

// BaseStyle 返回基础样式
func BaseStyle() lipgloss.Style {
	t := theme.CurrentTheme()
	return lipgloss.NewStyle().Foreground(t.Text())
}

// Regular 返回基本的无样式 lipgloss.Style
func Regular() lipgloss.Style {
	return lipgloss.NewStyle()
}

// Bold 返回粗体样式
func Bold() lipgloss.Style {
	return lipgloss.NewStyle().Bold(true)
}

// Padded 返回带有水平内边距的样式
func Padded() lipgloss.Style {
	return lipgloss.NewStyle().Padding(0, 1)
}

// Border 返回带有普通边框的样式
func Border() lipgloss.Style {
	t := theme.CurrentTheme()
	return lipgloss.NewStyle().
		Border(lipgloss.NormalBorder()).
		BorderForeground(t.BorderNormal())
}

// ThickBorder 返回带有粗边框的样式
func ThickBorder() lipgloss.Style {
	t := theme.CurrentTheme()
	return lipgloss.NewStyle().
		Border(lipgloss.ThickBorder()).
		BorderForeground(t.BorderNormal())
}

// DoubleBorder 返回带有双线边框的样式
func DoubleBorder() lipgloss.Style {
	t := theme.CurrentTheme()
	return lipgloss.NewStyle().
		Border(lipgloss.DoubleBorder()).
		BorderForeground(t.BorderNormal())
}

// FocusedBorder 返回使用焦点边框色的边框样式
func FocusedBorder() lipgloss.Style {
	t := theme.CurrentTheme()
	return lipgloss.NewStyle().
		Border(lipgloss.NormalBorder()).
		BorderForeground(t.BorderFocused())
}

// DimBorder 返回使用暗淡边框色的边框样式
func DimBorder() lipgloss.Style {
	t := theme.CurrentTheme()
	return lipgloss.NewStyle().
		Border(lipgloss.NormalBorder()).
		BorderForeground(t.BorderDim())
}

// PrimaryColor 返回当前主题的主色调
func PrimaryColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().Primary()
}

// SecondaryColor 返回当前主题的辅助色
func SecondaryColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().Secondary()
}

// AccentColor 返回当前主题的强调色
func AccentColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().Accent()
}

// ErrorColor 返回当前主题的错误色
func ErrorColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().Error()
}

// WarningColor 返回当前主题的警告色
func WarningColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().Warning()
}

// SuccessColor 返回当前主题的成功色
func SuccessColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().Success()
}

// InfoColor 返回当前主题的信息色
func InfoColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().Info()
}

// TextColor 返回当前主题的文本色
func TextColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().Text()
}

// TextMutedColor 返回当前主题的暗淡文本色
func TextMutedColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().TextMuted()
}

// TextEmphasizedColor 返回当前主题的强调文本色
func TextEmphasizedColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().TextEmphasized()
}

// BackgroundColor 返回当前主题的背景色
func BackgroundColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().Background()
}

// BackgroundSecondaryColor 返回当前主题的辅助背景色
func BackgroundSecondaryColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().BackgroundSecondary()
}

// BackgroundDarkerColor 返回当前主题的深色背景色
func BackgroundDarkerColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().BackgroundDarker()
}

// BorderNormalColor 返回当前主题的普通边框色
func BorderNormalColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().BorderNormal()
}

// BorderFocusedColor 返回当前主题的焦点边框色
func BorderFocusedColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().BorderFocused()
}

// BorderDimColor 返回当前主题的暗淡边框色
func BorderDimColor() lipgloss.AdaptiveColor {
	return theme.CurrentTheme().BorderDim()
}

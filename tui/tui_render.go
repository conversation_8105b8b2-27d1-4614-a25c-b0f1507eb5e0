package tui

import (
	"bytes"
	"io"
	"strings"
	"sync"
	"time"

	"github.com/charmbracelet/x/ansi"
	"github.com/muesli/ansi/compressor"

	tea "github.com/charmbracelet/bubbletea"
	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"
)

const (
	defaultFPS = 60
	maxFPS     = 120
)

type QoderRender struct {
	mtx *sync.Mutex
	out io.Writer

	buf                 bytes.Buffer
	queuedMessageLines  []string
	framerate           time.Duration
	ticker              *time.Ticker
	done                chan struct{}
	lastWrittenLines    []string
	queuedLinesRendered int
	altLinesRendered    int
	useANSICompressor   bool
	once                sync.Once

	// cursor visibility state
	cursorHidden bool

	// essentially whether or not we're using the full size of the terminal
	altScreenActive bool

	// whether or not we're currently using bracketed paste
	bpActive bool

	// reportingFocus whether reporting focus events is enabled
	reportingFocus bool

	// renderer dimensions; usually the size of the window
	width  int
	height int

	lastWidth  int
	lastHeight int

	flushing bool

	// lines explicitly set not to render
	ignoreLines map[int]struct{}

	lastToWrite string
	lastWritten string
}

// NewRenderer creates a new renderer. Normally you'll want to initialize it
// with os.Stdout as the first argument.
func NewRenderer(out io.Writer, useANSICompressor bool, fps int) *QoderRender {
	if fps < 1 {
		fps = defaultFPS
	} else if fps > maxFPS {
		fps = maxFPS
	}
	r := &QoderRender{
		out:                out,
		mtx:                &sync.Mutex{},
		done:               make(chan struct{}),
		framerate:          time.Second / time.Duration(fps),
		useANSICompressor:  useANSICompressor,
		queuedMessageLines: []string{},
	}
	if r.useANSICompressor {
		r.out = &compressor.Writer{Forward: out}
	}
	return r
}

// start starts the renderer.
func (r *QoderRender) Start() {
	r.execute(ansi.SaveCurrentCursorPosition)
	if r.ticker == nil {
		r.ticker = time.NewTicker(r.framerate)
	} else {
		// If the ticker already exists, it has been stopped and we need to
		// reset it.
		r.ticker.Reset(r.framerate)
	}

	// Since the renderer can be restarted after a stop, we need to reset
	// the done channel and its corresponding sync.Once.
	r.once = sync.Once{}

	go r.listen()
}

// stop permanently halts the renderer, rendering the final frame.
func (r *QoderRender) Stop() {
	// Stop the renderer before acquiring the mutex to avoid a deadlock.
	r.once.Do(func() {
		r.done <- struct{}{}
	})

	// flush locks the mutex
	r.flush()

	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.EraseEntireLine)
	// Move the cursor back to the beginning of the line
	r.execute("\r")

	if r.useANSICompressor {
		if w, ok := r.out.(io.WriteCloser); ok {
			_ = w.Close()
		}
	}
}

// execute writes a sequence to the terminal.
func (r *QoderRender) execute(seq string) {
	_, _ = io.WriteString(r.out, seq)
}

// kill halts the renderer. The final frame will not be rendered.
func (r *QoderRender) Kill() {
	// Stop the renderer before acquiring the mutex to avoid a deadlock.
	r.once.Do(func() {
		r.done <- struct{}{}
	})

	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.EraseEntireLine)
	// Move the cursor back to the beginning of the line
	r.execute("\r")
}

// listen waits for ticks on the ticker, or a signal to stop the renderer.
func (r *QoderRender) listen() {
	for {
		select {
		case <-r.done:
			r.ticker.Stop()
			return

		case <-r.ticker.C:
			r.flush()
		}
	}

}

func (r *QoderRender) renderFromStart() {
	buf := &bytes.Buffer{}
	buf.WriteString(ansi.EraseEntireDisplay)
	buf.WriteString(ansi.CursorHomePosition)

	linesWritten := strings.Split(r.lastToWrite, "\n")

	var linesToRender = make([]string, len(linesWritten)+len(r.queuedMessageLines))
	i := 0
	for _, line := range r.queuedMessageLines {
		linesToRender[i] = line
		i++
	}

	for _, line := range linesWritten {
		linesToRender[i] = line
		i++
	}

	for _, line := range linesToRender {
		if ansi.StringWidth(line) < r.width {
			line = line + ansi.EraseLineRight
		}

		if r.width > 0 {
			line = ansi.Truncate(line, r.width, "")
		}

		_, _ = buf.WriteString(line)
		_, _ = buf.WriteString("\r\n")
	}

	buf.WriteString(ansi.CursorBackward(r.width))
	_, _ = r.out.Write(buf.Bytes())
	r.lastWrittenLines = linesWritten
	r.lastWritten = r.lastToWrite
	r.queuedLinesRendered = len(r.queuedMessageLines)

	return
}

func (r *QoderRender) sizeChanged() bool {
	if r.width == 0 && r.height == 0 {
		return false
	}

	if r.lastWidth == 0 && r.lastHeight == 0 {
		return false
	}

	return r.width != r.lastWidth
}

func (r *QoderRender) flush() {
	r.mtx.Lock()
	defer r.mtx.Unlock()
	if r.flushing {
		return
	}

	r.flushing = true
	defer func() {
		r.flushing = false
	}()

	sizeChanged := r.sizeChanged()
	r.lastWidth, r.lastHeight = r.width, r.height

	// 动态内容和静态内容都一样，则不需要处理
	if r.lastToWrite == r.lastWritten && len(r.queuedMessageLines) == r.queuedLinesRendered {
		return
	}

	linesWritten := strings.Split(r.lastToWrite, "\n")

	// 整理所有需要渲染的内容
	// 未渲染的静态内容，静态内容只会向后追加，渲染过的行号和当前行号一致则不需要处理
	var linesToRender = make([]string, len(linesWritten)+len(r.queuedMessageLines)-r.queuedLinesRendered)
	i := 0
	for _, line := range r.queuedMessageLines[r.queuedLinesRendered:] {
		linesToRender[i] = line
		i++
	}

	for _, line := range linesWritten {
		linesToRender[i] = line
		i++
	}

	// 计算变更的行号
	changedFrom := 0
	for i := 0; i < min(len(linesToRender), len(r.lastWrittenLines)); i++ {
		if linesToRender[i] != r.lastWrittenLines[i] {
			break
		}
		changedFrom++
	}

	// 开始变更的位置超过了窗口的高度，需要渲染到窗口看不见的位置，只能通过清理整个屏幕来实现
	// 窗口大小变化也要清理屏幕
	if len(r.lastWrittenLines)-changedFrom > r.height || sizeChanged {
		r.renderFromStart()
		return
	}

	// 否则从开始变化的位置渲染，避免大范围闪屏
	upLines := len(r.lastWrittenLines) - changedFrom
	r.execute(ansi.CursorUp(upLines))
	r.execute(ansi.CursorBackward(r.width))

	buf := &bytes.Buffer{}
	for i := changedFrom; i < len(linesToRender); i++ {
		line := linesToRender[i]

		// 如果内容一样，则不清理原来渲染的内容
		if i < len(r.lastWrittenLines) && line == r.lastWrittenLines[i] {
			_, _ = buf.WriteString("\n")
			continue
		}

		if ansi.StringWidth(line) < r.width {
			line = line + ansi.EraseLineRight
		}

		if r.width > 0 {
			line = ansi.Truncate(line, r.width, "")
		}

		_, _ = buf.WriteString(line)
		_, _ = buf.WriteString("\r\n")
	}

	buf.WriteString(ansi.CursorBackward(r.width))
	_, _ = r.out.Write(buf.Bytes())
	r.execute(ansi.EraseScreenBelow)
	r.lastWrittenLines = linesWritten
	r.lastWritten = r.lastToWrite
	r.queuedLinesRendered = len(r.queuedMessageLines)
}

// lastLinesRendered returns the number of lines rendered lastly.
func (r *QoderRender) lastLinesRendered() int {
	if r.altScreenActive {
		return r.altLinesRendered
	}
	return r.queuedLinesRendered
}

func (r *QoderRender) Write(s string) {
	r.mtx.Lock()
	defer r.mtx.Unlock()
	if s == "" {
		s = " "
	}

	r.lastToWrite = s
}

func (r *QoderRender) Repaint() {
	r.lastWrittenLines = nil
}

func (r *QoderRender) ClearScreen() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.EraseEntireScreen)
	r.execute(ansi.CursorHomePosition)

	r.Repaint()
}

func (r *QoderRender) AltScreen() bool {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	return r.altScreenActive
}

func (r *QoderRender) EnterAltScreen() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	if r.altScreenActive {
		return
	}

	r.altScreenActive = true
	r.execute(ansi.SetAltScreenSaveCursorMode)

	// Ensure that the terminal is cleared, even when it doesn't support
	// alt screen (or alt screen support is disabled, like GNU screen by
	// default).
	//
	// Note: we can't use r.clearScreen() here because the mutex is already
	// locked.
	r.execute(ansi.EraseEntireScreen)
	r.execute(ansi.CursorHomePosition)

	// cmd.exe and other terminals keep separate cursor states for the AltScreen
	// and the main buffer. We have to explicitly reset the cursor visibility
	// whenever we enter AltScreen.
	if r.cursorHidden {
		r.execute(ansi.HideCursor)
	} else {
		r.execute(ansi.ShowCursor)
	}

	// Entering the alt screen resets the lines rendered count.
	r.altLinesRendered = 0

	r.Repaint()
}

func (r *QoderRender) ExitAltScreen() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	if !r.altScreenActive {
		return
	}

	r.altScreenActive = false
	r.execute(ansi.ResetAltScreenSaveCursorMode)

	// cmd.exe and other terminals keep separate cursor states for the AltScreen
	// and the main buffer. We have to explicitly reset the cursor visibility
	// whenever we exit AltScreen.
	if r.cursorHidden {
		r.execute(ansi.HideCursor)
	} else {
		r.execute(ansi.ShowCursor)
	}

	r.Repaint()
}

func (r *QoderRender) ShowCursor() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.cursorHidden = false
	r.execute(ansi.ShowCursor)
}

func (r *QoderRender) HideCursor() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.cursorHidden = true
	r.execute(ansi.HideCursor)
}

func (r *QoderRender) EnableMouseCellMotion() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.SetButtonEventMouseMode)
}

func (r *QoderRender) DisableMouseCellMotion() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.ResetButtonEventMouseMode)
}

func (r *QoderRender) EnableMouseAllMotion() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.SetAnyEventMouseMode)
}

func (r *QoderRender) DisableMouseAllMotion() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.ResetAnyEventMouseMode)
}

func (r *QoderRender) EnableMouseSGRMode() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.SetSgrExtMouseMode)
}

func (r *QoderRender) DisableMouseSGRMode() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.ResetSgrExtMouseMode)
}

func (r *QoderRender) EnableBracketedPaste() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.SetBracketedPasteMode)
	r.bpActive = true
}

func (r *QoderRender) DisableBracketedPaste() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.ResetBracketedPasteMode)
	r.bpActive = false
}

func (r *QoderRender) BracketedPasteActive() bool {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	return r.bpActive
}

func (r *QoderRender) EnableReportFocus() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.SetFocusEventMode)
	r.reportingFocus = true
}

func (r *QoderRender) DisableReportFocus() {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	r.execute(ansi.ResetFocusEventMode)
	r.reportingFocus = false
}

func (r *QoderRender) ReportFocus() bool {
	r.mtx.Lock()
	defer r.mtx.Unlock()

	return r.reportingFocus
}

// setWindowTitle sets the terminal window title.
func (r *QoderRender) SetWindowTitle(title string) {
	r.execute(ansi.SetWindowTitle(title))
}

func (r *QoderRender) ResetLinesRendered() {
	r.queuedLinesRendered = 0
}

func (r *QoderRender) HandleMessages(msg tea.Msg) {
	switch msg := msg.(type) {
	case tea.RepaintMsg:
		// Force a repaint by clearing the render cache as we slide into a
		// render.
		r.mtx.Lock()
		r.Repaint()
		r.mtx.Unlock()

	case editortypes.ForceRenderFromStartMsg:
		// 强制从头渲染，彻底清理包括回滚缓冲区的所有内容
		r.mtx.Lock()
		r.renderFromStart()
		r.mtx.Unlock()

	case tea.WindowSizeMsg:
		r.mtx.Lock()
		r.width = msg.Width
		r.height = msg.Height
		//r.Repaint()
		r.mtx.Unlock()

	case tea.PrintLineMessage:
		if !r.altScreenActive {
			lines := strings.Split(msg.MessageBody, "\n")
			r.mtx.Lock()
			r.queuedMessageLines = append(r.queuedMessageLines, lines...)
			//r.Repaint()
			r.mtx.Unlock()
		}
	}
}

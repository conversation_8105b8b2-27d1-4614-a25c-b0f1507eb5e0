package theme

import (
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/logging"
)

func InitTheme(service *config.Service) {
	preferences := service.GetPreferences()
	theme := preferences.Theme
	if theme == "" {
		return // Use default theme
	}

	// Set up the theme callback for TUI
	SetUpdateCallback(func(themeName string) error {
		preferences.Theme = themeName
		return service.UpdatePreferences(preferences)
	})

	// Try to set the theme from config
	err := SetTheme(theme)
	if err != nil {
		logging.Warn("Failed to set theme from config, using default theme", "theme", theme, "error", err)
	} else {
		logging.Debug("Set theme from config", "theme", theme)
	}
}

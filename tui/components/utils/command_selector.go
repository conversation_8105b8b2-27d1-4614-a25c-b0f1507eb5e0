package utils

import (
	"sort"
	"strings"

	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/tui/runtime"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
)

// GitHubAppInstallStartMsg GitHub App 安装开始消息
type GitHubAppInstallStartMsg struct{}

// Command 表示一个可执行命令
type Command struct {
	CustomCommand *command.Command
	ID            string // 命令ID
	Name          string // 命令名称
	Description   string // 命令描述
	Shortcut      string // 快捷键（可选）
}

// CommandSelector 命令选择器组件
type CommandSelector struct {
	active        bool
	width         int
	slashCommands []Command
	plusCommands  []Command
	filteredItems []Command
	cursor        int
	maxRows       int
}

// filterCommands 根据查询过滤命令（包含匹配 + 简单匹配度排序，大小写不敏感，最多 50 项）
func (cs *CommandSelector) filterCommands(query string) []Command {
	query = strings.TrimSpace(query)
	if len(query) == 0 {
		return nil
	}
	if len(query) == 1 {
		if query == "/" {
			return cs.slashCommands
		}
		if query == "+" {
			return cs.plusCommands
		}
		return nil
	}

	candidates := cs.slashCommands
	if strings.HasPrefix(query, "+") {
		candidates = cs.plusCommands
	} else if strings.HasPrefix(query, "/") {
		candidates = cs.slashCommands
	} else {
		return nil
	}

	rawQuery := strings.ToLower(query[1:])
	q := strings.SplitN(rawQuery, " ", 2)[0]
	type scored struct {
		cmd   Command
		score int
	}
	var arr []scored
	for _, cmd := range candidates {
		name := strings.ToLower(cmd.Name)
		desc := strings.ToLower(cmd.Description)
		idxName := strings.Index(name, q)
		idxDesc := strings.Index(desc, q)
		if idxName < 0 && idxDesc < 0 {
			continue
		}

		// 打分：name 命中权重高于 desc；首次出现越靠前分越高；开头命中额外加分；较短名称略微加分
		s := 0
		if idxName >= 0 {
			s += 2000 - idxName // 位置越靠前越大
			if idxName == 0 {
				s += 200
			}
			// 更短的名称更优（小幅度）
			s += 100 - len(name)
		}
		if idxDesc >= 0 {
			s += 1000 - idxDesc
			if idxDesc == 0 {
				s += 50
			}
		}
		arr = append(arr, scored{cmd: cmd, score: s})
	}

	sort.Slice(arr, func(i, j int) bool {
		if arr[i].score != arr[j].score {
			return arr[i].score > arr[j].score
		}
		// 次级排序：按命令名字母序，确保稳定
		return arr[i].cmd.Name < arr[j].cmd.Name
	})

	// 最多 50 项
	limit := 50
	if len(arr) < limit {
		limit = len(arr)
	}
	out := make([]Command, 0, limit)
	for i := 0; i < limit; i++ {
		out = append(out, arr[i].cmd)
	}
	return out
}

// CommandSelectedMsg 命令选择消息
type CommandSelectedMsg struct {
	Command Command
}

type InputCommandToEditorMsg struct {
	Command Command
}

// CommandExecuteMsg 选中并执行命令
type CommandExecuteMsg struct {
	Command Command
}

// CommandQueryChangedMsg 编辑器发送的 触发/变更消息
// Active=false 表示退出选择器
// Query 为输入的完整内容（不含空格）
type CommandQueryChangedMsg struct {
	Active bool
	Query  string
}

// NewCommandSelector 创建命令选择器
func NewCommandSelector(app runtime.AppRuntime) *CommandSelector {
	// 获取默认命令列表
	defaultCommands := GetDefaultCommands()
	var slashCommands []Command
	var plusCommands []Command
	commands := loadCustomCommands(app)
	for i := range commands {
		cmd := &commands[i]
		if strings.HasPrefix(cmd.ID, "/") {
			slashCommands = append(slashCommands, *cmd)
		} else if strings.HasPrefix(cmd.ID, "+") {
			plusCommands = append(plusCommands, *cmd)
		}
	}
	slashCommands = append(slashCommands, defaultCommands...)

	// 根据命令ID的字母排序整个列表
	sort.Slice(slashCommands, func(i, j int) bool {
		return slashCommands[i].ID < slashCommands[j].ID
	})
	sort.Slice(plusCommands, func(i, j int) bool {
		return plusCommands[i].ID < plusCommands[j].ID
	})

	return &CommandSelector{
		slashCommands: slashCommands,
		plusCommands:  plusCommands,
		filteredItems: nil,
		active:        false,
		maxRows:       5, // 与原来的PerPage保持一致
	}
}

// Init 初始化
func (cs *CommandSelector) Init() tea.Cmd {
	return nil
}

// Update 更新
func (cs *CommandSelector) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch m := msg.(type) {
	case tea.WindowSizeMsg:
		cs.width = m.Width
		return cs, nil

	case CommandQueryChangedMsg:
		if !m.Active {
			cs.active = false
			cs.filteredItems = nil
			cs.cursor = 0
			return cs, nil
		}
		cs.active = true
		cs.filteredItems = cs.filterCommands(m.Query)
		cs.cursor = 0
		return cs, nil
	case tea.KeyMsg:
		if !cs.active {
			return cs, nil
		}
		switch m.String() {
		case "up", "k":
			if cs.cursor > 0 {
				cs.cursor--
			}
			return cs, nil
		case "down", "j":
			if cs.cursor < len(cs.filteredItems)-1 {
				cs.cursor++
			}
			return cs, nil
		case "tab":
			if len(cs.filteredItems) > 0 {
				cmd := cs.filteredItems[cs.cursor]
				cs.active = false
				return cs, func() tea.Msg { return CommandSelectedMsg{Command: cmd} }
			}
			return cs, nil
		case "enter":
			if len(cs.filteredItems) > 0 {
				cmd := cs.filteredItems[cs.cursor]
				cs.active = false
				return cs, func() tea.Msg { return CommandExecuteMsg{Command: cmd} }
			}
			return cs, nil
		case "esc":
			cs.active = false
			return cs, nil
		}
	}
	return cs, nil
}

// View 渲染
func (cs *CommandSelector) View() string {
	if !cs.active || len(cs.filteredItems) == 0 || cs.width == 0 {
		return ""
	}

	t := theme.CurrentTheme()
	maxWidth := cs.width - 2 // 左右各预留 1 空间

	// 计算滚动窗口，保证当前选中项可见
	start := 0
	if len(cs.filteredItems) > cs.maxRows {
		half := cs.maxRows / 2
		if cs.cursor >= half && cs.cursor < len(cs.filteredItems)-half {
			start = cs.cursor - half
		} else if cs.cursor >= len(cs.filteredItems)-half {
			start = len(cs.filteredItems) - cs.maxRows
		}
	}
	end := start + cs.maxRows
	if end > len(cs.filteredItems) {
		end = len(cs.filteredItems)
	}

	rows := cs.filteredItems[start:end]
	var rendered []string

	for i, cmd := range rows {
		// 构建显示文本：名称 - 描述
		nameLength := len(cmd.Name)
		spaceCount := 30 - nameLength
		if spaceCount < 1 {
			spaceCount = 1
		}
		spaceStr := strings.Repeat(" ", spaceCount)
		displayText := cmd.ID + spaceStr + cmd.Description

		// 截断过长的文本
		if len(displayText) > maxWidth-4 {
			displayText = displayText[:maxWidth-7] + "..."
		}

		globalIndex := start + i
		if globalIndex == cs.cursor {
			// 选中项样式：箭头 + 高亮颜色
			selectedStyle := styles.BaseStyle().Foreground(t.Primary()).Bold(true)
			rendered = append(rendered, selectedStyle.Render("→ "+displayText))
		} else {
			// 普通项样式：减少左边距，与选中项对齐
			normalStyle := styles.BaseStyle().Foreground(t.Text()).PaddingLeft(1)
			rendered = append(rendered, normalStyle.Render(" "+displayText))
		}
	}

	return strings.Join(rendered, "\n")
}

// SetSize 设置尺寸
func (cs *CommandSelector) SetSize(width, height int) {
	cs.width = width
}

// IsActive 是否激活
func (cs *CommandSelector) IsActive() bool {
	return cs.active
}

// GetDefaultCommands 获取默认命令列表
func GetDefaultCommands() []Command { return []Command{} }

func loadCustomCommands(app runtime.AppRuntime) []Command {
	var commands []Command
	customCommands, _ := app.LoadAllCommands()
	for i := range customCommands {
		cmd := customCommands[i]
		commands = append(commands, Command{
			CustomCommand: &cmd,
			ID:            cmd.ID,
			Name:          cmd.Name,
			Description:   cmd.Description,
			Shortcut:      "",
		})
	}
	return commands
}

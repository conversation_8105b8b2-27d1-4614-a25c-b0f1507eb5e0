package utils

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/key"
	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	tuiconfig "github.com/qoder-ai/qodercli/tui/config"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
	"github.com/qoder-ai/qodercli/tui/util"
)

const maxAttachmentSize = int64(5 * 1024 * 1024)

// AttachmentAddedMsg 通知编辑器新增附件
type AttachmentAddedMsg struct {
	Attachment message.Attachment
}

// FilePickerExitMsg 请求退出文件选择模式
type FilePickerExitMsg struct{}

// 注意：maxAttachmentSize 常量已在同包 editor.go 中定义并复用

type filePickerKeyMap struct {
	Enter     key.Binding
	Down      key.Binding
	Up        key.Binding
	Forward   key.Binding
	Backward  key.Binding
	Esc       key.Binding
	InsertCWD key.Binding
}

var fpKeys = filePickerKeyMap{
	Enter: key.NewBinding(
		key.WithKeys("enter"),
		key.WithHelp("enter", "select/enter"),
	),
	Down: key.NewBinding(
		key.WithKeys("j", "down"),
		key.WithHelp("↓/j", "down"),
	),
	Up: key.NewBinding(
		key.WithKeys("k", "up"),
		key.WithHelp("↑/k", "up"),
	),
	Forward: key.NewBinding(
		key.WithKeys("l"),
		key.WithHelp("l", "enter dir"),
	),
	Backward: key.NewBinding(
		key.WithKeys("h", "backspace"),
		key.WithHelp("h/⌫", "go back"),
	),
	Esc: key.NewBinding(
		key.WithKeys("esc"),
		key.WithHelp("esc", "exit"),
	),
	InsertCWD: key.NewBinding(
		key.WithKeys("i"),
		key.WithHelp("i", "type path"),
	),
}

type dirNode struct {
	parent    *dirNode
	child     *dirNode
	directory string
}

type indexStack []int

func (s indexStack) Push(v int) indexStack { return append(s, v) }
func (s indexStack) Pop() (indexStack, int) {
	l := len(s)
	if l == 0 {
		return s, 0
	}
	return s[:l-1], s[l-1]
}

// FilePickerComponent 内联文件选择组件
// 仅在 FilePicker 模式下被渲染与接收按键
// 单文件选择：选择后发送 AttachmentAddedMsg，但仍停留在文件选择模式以便连续选择
// 退出由 esc 触发，若处于路径输入状态则先退出输入
// 注意：不做弹窗，不管理模式切换，由上层 app 控制 currentMode

type FilePickerComponent struct {
	cfg *tuiconfig.TuiConfig

	width  int
	height int

	cwdInput   textinput.Model
	root       *dirNode
	cursorPath *dirNode
	cursor     int
	cursorHist indexStack
	dirs       []os.DirEntry

	err error
}

func NewFilePickerComponent(cfg *tuiconfig.TuiConfig) *FilePickerComponent {
	home, err := os.UserHomeDir()
	if err != nil {
		logging.Error("error loading user home", "err", err)
		home = "."
	}

	base := &dirNode{directory: home}
	dirs := readDir(home, false)
	input := textinput.New()
	input.CharLimit = 200
	input.Width = 60
	input.Cursor.Blink = true
	input.SetValue(base.directory)

	return &FilePickerComponent{
		cfg:        cfg,
		cwdInput:   input,
		root:       base,
		cursorPath: base,
		cursor:     0,
		dirs:       dirs,
		cursorHist: make(indexStack, 0),
	}
}

func (f *FilePickerComponent) Init() tea.Cmd { return nil }

func (f *FilePickerComponent) SetSize(width, height int) tea.Cmd {
	f.width = width
	f.height = height
	return nil
}

func (f *FilePickerComponent) GetSize() (int, int) { return f.width, f.height }

func (f *FilePickerComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch m := msg.(type) {
	case tea.WindowSizeMsg:
		return f, f.SetSize(m.Width, m.Height)
	case tea.MouseMsg:
		// 仅处理滚轮上下
		if m.Action == tea.MouseActionPress {
			if m.Button == tea.MouseButtonWheelUp {
				if !f.cwdInput.Focused() && f.cursor > 0 {
					f.cursor--
				}
			} else if m.Button == tea.MouseButtonWheelDown {
				if !f.cwdInput.Focused() && f.cursor < len(f.dirs)-1 {
					f.cursor++
				}
			}
		}
	case tea.KeyMsg:
		if f.cwdInput.Focused() {
			var cmd tea.Cmd
			f.cwdInput, cmd = f.cwdInput.Update(m)
			// enter 在输入状态：跳转目录或选择文件
			if key.Matches(m, fpKeys.Enter) {
				path := strings.TrimSpace(f.cwdInput.Value())
				if path == "" {
					return f, nil
				}
				fi, err := os.Stat(path)
				if err != nil {
					return f, util.ReportWarn("Invalid path")
				}
				if fi.IsDir() {
					f.navigateTo(path)
					return f, nil
				}
				return f, f.addAttachment(path)
			}
			if key.Matches(m, fpKeys.Esc) {
				f.cwdInput.Blur()
				return f, nil
			}
			return f, cmd
		}

		// 非输入状态的按键
		switch {
		case key.Matches(m, fpKeys.InsertCWD):
			f.cwdInput.Focus()
			return f, nil
		case key.Matches(m, fpKeys.Esc):
			return f, func() tea.Msg { return FilePickerExitMsg{} }
		case key.Matches(m, fpKeys.Down):
			if f.cursor < len(f.dirs)-1 {
				f.cursor++
			}
			return f, nil
		case key.Matches(m, fpKeys.Up):
			if f.cursor > 0 {
				f.cursor--
			}
			return f, nil
		case key.Matches(m, fpKeys.Forward), key.Matches(m, fpKeys.Enter):
			if len(f.dirs) == 0 {
				return f, nil
			}
			entry := f.dirs[f.cursor]
			path := filepath.Join(f.cursorPath.directory, entry.Name())
			if entry.IsDir() {
				f.cursorHist = f.cursorHist.Push(f.cursor)
				f.navigateTo(path)
				return f, nil
			}
			return f, f.addAttachment(path)
		case key.Matches(m, fpKeys.Backward):
			if f.cursorPath.parent != nil {
				var prev int
				f.cursorHist, prev = f.cursorHist.Pop()
				f.cursorPath = f.cursorPath.parent
				f.cursorPath.child = nil
				f.dirs = readDir(f.cursorPath.directory, false)
				f.cursor = intMin(prev, intMax(0, len(f.dirs)-1))
				f.cwdInput.SetValue(f.cursorPath.directory)
			}
			return f, nil
		}
	}
	return f, nil
}

func (f *FilePickerComponent) View() string {
	t := theme.CurrentTheme()

	// 目标总宽度：窗口宽度的 1/3（至少 40 列）
	desiredWidth := intMax(40, f.width/3)
	// 内容宽度：扣除左右边框(2)与水平内边距(4)
	leftMaxWidth := intMax(20, desiredWidth-6)

	// 计算滑动窗口范围
	const maxVisible = 20
	start := 0
	if len(f.dirs) > maxVisible {
		half := maxVisible / 2
		if f.cursor >= half && f.cursor < len(f.dirs)-half {
			start = f.cursor - half
		} else if f.cursor >= len(f.dirs)-half {
			start = len(f.dirs) - maxVisible
		}
	}
	end := intMin(start+maxVisible, len(f.dirs))

	// 保证输入框宽度与内容宽度一致
	f.cwdInput.Width = leftMaxWidth

	// 构造文件列表
	var lines []string
	for i := start; i < end; i++ {
		it := f.dirs[i]
		name := it.Name()
		if len(name) > leftMaxWidth-4 {
			name = name[:leftMaxWidth-7] + "..."
		}
		if it.IsDir() {
			name += "/"
		}
		style := styles.BaseStyle().Width(leftMaxWidth)
		if i == f.cursor {
			style = style.Background(t.Primary()).Foreground(t.Background()).Bold(true)
		}
		lines = append(lines, style.Padding(0, 1).Render(name))
	}
	for len(lines) < maxVisible {
		lines = append(lines, styles.BaseStyle().Width(leftMaxWidth).Render(""))
	}

	// 路径输入
	pathLine := styles.BaseStyle().Height(1).Width(leftMaxWidth).Render(f.cwdInput.View())

	// 快捷键信息
	shortcut := f.renderShortcuts(leftMaxWidth)

	left := lipgloss.JoinVertical(lipgloss.Left,
		pathLine,
		styles.BaseStyle().Width(leftMaxWidth).Render(""),
		styles.BaseStyle().Width(leftMaxWidth).Render(lipgloss.JoinVertical(lipgloss.Left, lines...)),
		styles.BaseStyle().Width(leftMaxWidth).Render(""),
		shortcut,
	)

	// 外层盒子固定为窗口的 1/3 宽
	leftBox := styles.BaseStyle().
		Padding(1, 2).
		Border(lipgloss.RoundedBorder()).
		BorderForeground(t.TextMuted()).
		Width(desiredWidth).
		Render(left)

	return leftBox
}

func (f *FilePickerComponent) renderShortcuts(width int) string {
	t := theme.CurrentTheme()
	base := styles.BaseStyle().Width(width)
	line1 := base.Foreground(t.TextMuted()).Render("i: type path           esc: exit")
	line2 := base.Foreground(t.TextMuted()).Render("j/k or ↑/↓: move       l/enter: enter or select")
	line3 := base.Foreground(t.TextMuted()).Render("h/backspace: back")
	return lipgloss.JoinVertical(lipgloss.Left, line1, line2, line3)
}

// 辅助：导航到目录
func (f *FilePickerComponent) navigateTo(path string) {
	n := &dirNode{parent: f.cursorPath, directory: path}
	f.cursorPath.child = n
	f.cursorPath = n
	f.dirs = readDir(f.cursorPath.directory, false)
	f.cursor = 0
	f.cwdInput.SetValue(f.cursorPath.directory)
}

// 添加附件
func (f *FilePickerComponent) addAttachment(path string) tea.Cmd {
	modeInfo := models.SupportedModels[f.cfg.CurrentModel]
	if !modeInfo.SupportsAttachments {
		return util.ReportWarn(fmt.Sprintf("Model %s doesn't support attachments", modeInfo.Name))
	}
	if !isExtSupported(path) {
		return util.ReportWarn("Unsupported file")
	}
	content, err := os.ReadFile(path)
	if err != nil {
		return util.ReportWarn("Unable to read file")
	}
	if int64(len(content)) > maxAttachmentSize {
		return util.ReportWarn("file too large, max 5MB")
	}
	mimeProbe := intMin(512, len(content))
	mime := http.DetectContentType(content[:mimeProbe])
	att := message.Attachment{FilePath: path, FileName: filepath.Base(path), MimeType: mime, Content: content}
	return tea.Batch(
		util.CmdHandler(AttachmentAddedMsg{Attachment: att}),
		func() tea.Msg { return FilePickerExitMsg{} },
	)
}

// 目录读取（按目录优先、隐藏文件默认不显示、仅保留支持的文件后缀）
func readDir(path string, showHidden bool) []os.DirEntry {
	entriesChan := make(chan []os.DirEntry, 1)
	errChan := make(chan error, 1)
	go func() {
		files, err := os.ReadDir(path)
		if err != nil {
			errChan <- err
			return
		}
		entriesChan <- files
	}()
	select {
	case entries := <-entriesChan:
		sort.Slice(entries, func(i, j int) bool {
			if entries[i].IsDir() == entries[j].IsDir() {
				return entries[i].Name() < entries[j].Name()
			}
			return entries[i].IsDir()
		})
		if showHidden {
			return entries
		}
		var filtered []os.DirEntry
		for _, e := range entries {
			if hidden, _ := isHidden(e.Name()); hidden {
				continue
			}
			if e.IsDir() || isExtSupported(e.Name()) {
				filtered = append(filtered, e)
			}
		}
		return filtered
	case err := <-errChan:
		logging.ErrorPersist(fmt.Sprintf("Error reading directory %s", path), err)
		return []os.DirEntry{}
	case <-time.After(5 * time.Second):
		logging.ErrorPersist(fmt.Sprintf("Timeout reading directory %s", path), nil)
		return []os.DirEntry{}
	}
}

func isHidden(name string) (bool, error) { return strings.HasPrefix(name, "."), nil }

func isExtSupported(path string) bool {
	ext := strings.ToLower(filepath.Ext(path))
	return ext == ".jpg" || ext == ".jpeg" || ext == ".webp" || ext == ".png"
}

// small helpers
func intMin(a, b int) int {
	if a < b {
		return a
	}
	return b
}
func intMax(a, b int) int {
	if a > b {
		return a
	}
	return b
}

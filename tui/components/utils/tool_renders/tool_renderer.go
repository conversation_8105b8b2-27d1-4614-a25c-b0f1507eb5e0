package tool_renders

import (
	"encoding/json"
	"fmt"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
)

// ==== 核心接口定义 ====

// ToolRenderer 工具渲染器基础接口
type ToolRenderer interface {
	Render(toolCall message.ToolCall, result *message.ToolResult, width int) string
	CanHandle(toolName string) bool
}

// ExpandableRenderer 可展开渲染器接口
type ExpandableRenderer interface {
	SetExpandedMode(expanded bool)
}

// ==== 枚举定义 ====

// ToolState 工具执行状态枚举
type ToolState int

const (
	StatePending   ToolState = iota // 执行中
	StateSuccess                    // 成功
	StateError                      // 错误
	StateCancelled                  // 取消
)

// ContentType 内容渲染类型枚举
type ContentType int

const (
	ContentPlain    ContentType = iota // 普通文本
	ContentCode                        // 代码块
	ContentDiff                        // 差异显示
	ContentMarkdown                    // Markdown内容
	ContentJSON                        // JSON格式
	ContentError                       // 错误数据
)

// ==== 基础渲染器组件 ====

// BaseRenderer 基础渲染器组件
type BaseRenderer struct {
	expandedMode bool // 展开模式标志
}

// SetExpandedMode 设置展开模式
func (br *BaseRenderer) SetExpandedMode(expanded bool) {
	br.expandedMode = expanded
}

// GetToolState 根据工具调用和结果确定执行状态
func (br *BaseRenderer) GetToolState(toolCall message.ToolCall, result *message.ToolResult) ToolState {
	if !toolCall.Finished {
		return StatePending
	}
	if result == nil {
		return StateCancelled
	}
	if result.IsError {
		return StateError
	}
	return StateSuccess
}

// getStateColor 获取状态对应的主题颜色
func (br *BaseRenderer) getStateColor(state ToolState) lipgloss.AdaptiveColor {
	t := theme.CurrentTheme()
	switch state {
	case StateSuccess:
		return t.Success()
	case StateError:
		return t.Error()
	case StatePending:
		return t.Warning()
	case StateCancelled:
		return t.TextMuted()
	default:
		return t.Text()
	}
}

// GetStatusIndicator 获取状态指示器符号
func (br *BaseRenderer) GetStatusIndicator(state ToolState) string {
	switch state {
	case StateSuccess:
		return "✔"
	case StateError:
		return "✗"
	case StatePending:
		return "○"
	case StateCancelled:
		return "–"
	default:
		return "?"
	}
}

// RenderHeader 渲染工具头部信息
func (br *BaseRenderer) RenderHeader(toolName, params string, state ToolState, width int, backgroundColor string) string {
	t := theme.CurrentTheme()

	// 状态指示器
	statusIndicator := br.GetStatusIndicator(state)
	indicatorStyle := lipgloss.NewStyle().Foreground(br.getStateColor(state))

	// 工具名称样式
	toolNameStyle := lipgloss.NewStyle().Bold(true)
	if backgroundColor != "" {
		toolNameStyle = br.getNameStyleWithBackground(backgroundColor, t)
	} else {
		toolNameStyle = toolNameStyle.Foreground(t.Primary())
	}

	// 组装头部内容
	var parts []string
	parts = append(parts, indicatorStyle.Render(statusIndicator))
	parts = append(parts, toolNameStyle.Render(toolName))
	if params != "" {
		paramStyle := lipgloss.NewStyle().Foreground(t.Text())
		parts = append(parts, paramStyle.Render(params))
	}

	// 应用头部样式
	headerContent := strings.Join(parts, " ")
	headerStyle := styles.BaseStyle().Width(width).PaddingLeft(4)
	return headerStyle.Render(headerContent)
}

// getNameStyleWithBackground 根据背景色名称返回对应的工具名称样式
func (br *BaseRenderer) getNameStyleWithBackground(bgColor string, t theme.Theme) lipgloss.Style {
	style := lipgloss.NewStyle().Bold(true)

	colorMap := map[string]struct {
		bg, fg lipgloss.AdaptiveColor
	}{
		"red":    {t.BaseRed(), t.BaseWhite()},
		"blue":   {t.BaseBlue(), t.BaseWhite()},
		"yellow": {t.BaseYellow(), t.BaseWhite()},
		"green":  {t.BaseGreen(), t.BaseWhite()},
		"cyan":   {t.BaseCyan(), t.BaseWhite()},
		"purple": {t.BasePurple(), t.BaseWhite()},
		"orange": {t.BaseOrange(), t.BaseWhite()},
		"gray":   {t.BaseGray(), t.BaseWhite()},
		"white":  {t.BaseWhite(), t.BaseBlack()},
		"black":  {t.BaseBlack(), t.BaseWhite()},
	}

	if colors, exists := colorMap[strings.ToLower(bgColor)]; exists {
		return style.Background(colors.bg).Foreground(colors.fg)
	}
	return style.Background(t.BaseBlue()).Foreground(t.BaseWhite())
}

// JoinHeaderContent 连接头部和内容
func (br *BaseRenderer) JoinHeaderContent(header, content string) string {
	if content == "" {
		return header
	}
	return lipgloss.JoinVertical(lipgloss.Left, header, content)
}

// TruncateContent 截断内容到指定行数
// 在展开模式下不进行截断，否则截断并添加省略提示
func (br *BaseRenderer) TruncateContent(content string, maxLines int) string {
	if content == "" || br.expandedMode {
		return content
	}

	lines := strings.Split(content, "\n")
	if len(lines) <= maxLines {
		return content
	}

	// 截断并添加省略提示
	truncatedLines := lines[:maxLines]
	omittedCount := len(lines) - maxLines

	t := theme.CurrentTheme()
	ellipsisStyle := lipgloss.NewStyle().Foreground(t.TextMuted())
	ellipsisLine := fmt.Sprintf("%d more lines omitted  (ctrl+r to expand)", omittedCount)

	return strings.Join(truncatedLines, "\n") + "\n" + ellipsisStyle.Render(ellipsisLine)
}

// RenderContent 渲染内容，根据类型应用不同的样式和语法高亮
func (br *BaseRenderer) RenderContent(content, filePath string, contentType ContentType, width int) string {
	if content == "" {
		return ""
	}

	style := br.getContentStyle(contentType, width)

	switch contentType {
	case ContentJSON:
		return br.renderJSONContent(content, style)
	case ContentCode:
		return br.renderCodeContent(content, style)
	case ContentDiff:
		return br.renderDiffContent(content, style)
	case ContentMarkdown:
		return br.renderMarkdownContent(content, style, width)
	case ContentError:
		return style.Foreground(theme.CurrentTheme().Error()).Render(content)
	default:
		return style.Render(content)
	}
}

// getContentStyle 获取内容样式配置
func (br *BaseRenderer) getContentStyle(contentType ContentType, width int) lipgloss.Style {
	return styles.BaseStyle().
		Width(width - 2).
		PaddingLeft(6)
}

// renderJSONContent 渲染JSON内容，包含语法高亮
func (br *BaseRenderer) renderJSONContent(content string, style lipgloss.Style) string {
	// 尝试格式化JSON
	var jsonObj interface{}
	if err := json.Unmarshal([]byte(content), &jsonObj); err == nil {
		if formatted, err := json.MarshalIndent(jsonObj, "", "  "); err == nil {
			content = string(formatted)
		}
	}

	// 简单的JSON语法高亮
	t := theme.CurrentTheme()
	keyPattern := regexp.MustCompile(`"([^"]+)":`)
	content = keyPattern.ReplaceAllStringFunc(content, func(match string) string {
		return lipgloss.NewStyle().Foreground(t.Primary()).Render(match)
	})

	return style.Render(content)
}

// renderCodeContent 渲染代码内容，包含基础语法高亮
func (br *BaseRenderer) renderCodeContent(content string, style lipgloss.Style) string {
	t := theme.CurrentTheme()

	// 高亮注释
	commentPattern := regexp.MustCompile(`//.*$`)
	content = commentPattern.ReplaceAllStringFunc(content, func(match string) string {
		return lipgloss.NewStyle().Foreground(t.TextMuted()).Render(match)
	})

	// 高亮字符串
	stringPattern := regexp.MustCompile(`"([^"]*)"`)
	content = stringPattern.ReplaceAllStringFunc(content, func(match string) string {
		return lipgloss.NewStyle().Foreground(t.Success()).Render(match)
	})

	return style.Render(content)
}

// renderDiffContent 渲染差异内容，包含颜色标记和文件信息
func (br *BaseRenderer) renderDiffContent(content string, style lipgloss.Style) string {
	t := theme.CurrentTheme()

	// 解析并生成差异摘要
	diffInfo := br.parseDiffInfo(content)
	summary := br.generateDiffSummary(diffInfo, t)

	// 渲染差异内容
	contentLines := br.renderDiffLines(content, diffInfo, t)

	result := summary
	if len(contentLines) > 0 {
		result += "\n" + strings.Join(contentLines, "\n")
	}

	return style.Render(result)
}

// DiffInfo 差异解析信息
type DiffInfo struct {
	fileName     string
	addedLines   int
	deletedLines int
	oldStart     int
	newStart     int
}

// parseDiffInfo 解析差异获取关键信息
func (br *BaseRenderer) parseDiffInfo(content string) *DiffInfo {
	info := &DiffInfo{}
	lines := strings.Split(content, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 解析文件名
		if strings.HasPrefix(line, "+++") {
			if parts := strings.Fields(line); len(parts) >= 2 {
				filePath := strings.TrimPrefix(parts[1], "b/")
				info.fileName = filepath.Base(filePath)
			}
		}

		// 解析行号信息
		if strings.HasPrefix(line, "@@") {
			re := regexp.MustCompile(`@@\s*-(\d+)(?:,\d+)?\s*\+(\d+)(?:,\d+)?\s*@@`)
			if matches := re.FindStringSubmatch(line); len(matches) >= 3 {
				if oldStart, err := strconv.Atoi(matches[1]); err == nil {
					info.oldStart = oldStart
				}
				if newStart, err := strconv.Atoi(matches[2]); err == nil {
					info.newStart = newStart
				}
			}
		}

		// 统计增删行数
		if strings.HasPrefix(line, "+") && !strings.HasPrefix(line, "+++") {
			info.addedLines++
		} else if strings.HasPrefix(line, "-") && !strings.HasPrefix(line, "---") {
			info.deletedLines++
		}
	}

	return info
}

// generateDiffSummary 生成差异摘要信息
func (br *BaseRenderer) generateDiffSummary(info *DiffInfo, t theme.Theme) string {
	fileName := info.fileName
	if fileName == "" {
		fileName = "file"
	}

	// 文件名
	fileNameStyle := lipgloss.NewStyle().Bold(true).Foreground(t.Primary())
	summary := "Updated " + fileNameStyle.Render(fileName)

	// 统计信息
	var stats []string
	if info.addedLines > 0 {
		addStyle := lipgloss.NewStyle().Foreground(t.Success())
		stats = append(stats, addStyle.Render(fmt.Sprintf("+%d", info.addedLines)))
	}
	if info.deletedLines > 0 {
		delStyle := lipgloss.NewStyle().Foreground(t.Error())
		stats = append(stats, delStyle.Render(fmt.Sprintf("-%d", info.deletedLines)))
	}

	if len(stats) > 0 {
		summary += " (" + strings.Join(stats, ", ") + ")"
	}

	return summary
}

// renderDiffLines 渲染差异内容行
func (br *BaseRenderer) renderDiffLines(content string, info *DiffInfo, t theme.Theme) []string {
	lines := strings.Split(content, "\n")
	var result []string

	oldLineNum := info.oldStart
	newLineNum := info.newStart

	for _, line := range lines {
		line = strings.TrimRight(line, "\r\n")

		// 跳过技术头部
		if strings.HasPrefix(line, "diff ") || strings.HasPrefix(line, "index ") ||
			strings.HasPrefix(line, "---") || strings.HasPrefix(line, "+++") ||
			strings.HasPrefix(line, "@@") || line == "" {
			continue
		}

		// 渲染内容行
		if strings.HasPrefix(line, "+") {
			result = append(result, br.renderDiffLine(line, newLineNum, "add", t))
			newLineNum++
		} else if strings.HasPrefix(line, "-") {
			result = append(result, br.renderDiffLine(line, oldLineNum, "delete", t))
			oldLineNum++
		} else if strings.HasPrefix(line, " ") {
			result = append(result, br.renderDiffLine(line, oldLineNum, "context", t))
			oldLineNum++
			newLineNum++
		}
	}

	return result
}

// renderDiffLine 渲染单行差异
func (br *BaseRenderer) renderDiffLine(line string, lineNum int, lineType string, t theme.Theme) string {
	// 提取符号和内容
	symbol := " "
	content := line
	if len(line) > 0 {
		symbol = string(line[0])
		if len(line) > 1 {
			content = line[1:]
		} else {
			content = ""
		}
	}

	// 行号部分
	lineNumStr := fmt.Sprintf("%4d", lineNum)
	lineNumPart := lipgloss.NewStyle().
		Foreground(t.TextMuted()).
		Width(5).
		Align(lipgloss.Right).
		Render(lineNumStr)

	// 符号和内容样式
	var symbolColor, fgColor lipgloss.AdaptiveColor
	var bgColor lipgloss.AdaptiveColor

	switch lineType {
	case "add":
		bgColor = lipgloss.AdaptiveColor{Light: "#e8f5e8", Dark: "#0f2e0f"}
		symbolColor = t.Success()
		fgColor = t.Success()
	case "delete":
		bgColor = lipgloss.AdaptiveColor{Light: "#ffeaea", Dark: "#2e0f0f"}
		symbolColor = t.Error()
		fgColor = t.Error()
	default:
		symbolColor = t.TextMuted()
		fgColor = t.Text()
	}

	symbolPart := lipgloss.NewStyle().
		Background(bgColor).
		Foreground(symbolColor).
		Bold(true).
		Render(" " + symbol + " ")

	contentPart := lipgloss.NewStyle().
		Background(bgColor).
		Foreground(fgColor).
		Render(content)

	return lipgloss.JoinHorizontal(lipgloss.Top, lineNumPart, symbolPart, contentPart)
}

// renderMarkdownContent 渲染Markdown内容
func (br *BaseRenderer) renderMarkdownContent(content string, style lipgloss.Style, width int) string {
	// 计算可用宽度
	innerWidth := width - 8
	if innerWidth < 20 {
		innerWidth = 20
	}

	r := styles.GetMarkdownRenderer(innerWidth)
	rendered, _ := r.Render(content)
	return style.Render(rendered)
}

// ==== 内容提取和渲染策略 ====

// GetContentExtractor 根据工具名获取内容提取器
func GetContentExtractor(toolName string) ContentExtractor {
	switch toolName {
	case tools.ReadToolName:
		return &ReadContentExtractor{}
	case tools.WriteToolName:
		return &WriteContentExtractor{}
	case tools.EditToolName:
		return &EditContentExtractor{}
	case tools.LSToolName:
		return &LSContentExtractor{}
	case tools.GrepToolName:
		return &GrepContentExtractor{}
	case tools.TodoWriteToolName:
		return &TodoContentExtractor{}
	case tools.GlobToolName:
		return &GlobContentExtractor{}
	default:
		return &PlainContentExtractor{}
	}
}

// GetRenderStrategy 根据工具名获取渲染策略
func GetRenderStrategy(toolName string) RenderStrategy {
	switch toolName {
	case tools.ReadToolName, tools.LSToolName, tools.BashToolName:
		return &CodeRenderStrategy{}
	case tools.EditToolName, tools.WriteToolName:
		return &DiffRenderStrategy{}
	default:
		return &PlainRenderStrategy{}
	}
}

// ContentExtractor 内容提取器接口
type ContentExtractor interface {
	ExtractSummary(toolCall message.ToolCall, result *message.ToolResult) string
	ExtractFullContent(toolCall message.ToolCall, result *message.ToolResult) string
	ShouldAddExpandHint(result *message.ToolResult, isExpanded bool) bool
}

// RenderStrategy 渲染策略接口
type RenderStrategy interface {
	GetSummaryContentType(toolName string) ContentType
	GetFullContentType(toolName string) ContentType
}

// ==== 内容提取器实现 ====

// PlainContentExtractor 通用内容提取器
type PlainContentExtractor struct{}

func (p *PlainContentExtractor) ExtractSummary(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}
	return result.Content
}

func (p *PlainContentExtractor) ExtractFullContent(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}
	return result.Content
}

func (p *PlainContentExtractor) ShouldAddExpandHint(result *message.ToolResult, isExpanded bool) bool {
	// 通用提取器默认不需要展开提示
	return false
}

// ReadContentExtractor Read工具内容提取器
type ReadContentExtractor struct{}

func (r *ReadContentExtractor) ExtractSummary(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}

	// 解析参数获取文件信息
	var params specs.ReadParams
	filePath := "unknown file"
	if err := json.Unmarshal([]byte(toolCall.Input), &params); err == nil {
		filePath = params.FilePath
	}

	// 计算读取的行数
	lines := strings.Split(r.ExtractFullContent(toolCall, result), "\n")
	actualLines := len(lines)

	return r.generateReadSummary(params, filepath.Base(filePath), actualLines)
}

func (r *ReadContentExtractor) ExtractFullContent(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Metadata == "" {
		return result.Content
	}

	// 从metadata中提取完整文件内容
	var metadata specs.ReadResponseMetadata
	if err := json.Unmarshal([]byte(result.Metadata), &metadata); err != nil {
		return result.Content
	}

	return metadata.Content
}

func (r *ReadContentExtractor) ShouldAddExpandHint(result *message.ToolResult, isExpanded bool) bool {
	// Read工具总是需要展开提示，因为总结简洁但有完整文件内容可查看
	if result == nil || result.IsError || isExpanded {
		return false
	}
	return true
}

// generateReadSummary 生成Read工具的总结信息
func (r *ReadContentExtractor) generateReadSummary(params specs.ReadParams, fileName string, actualLines int) string {
	if params.Offset != nil && params.Limit != nil {
		startLine := *params.Offset + 1
		endLine := startLine + *params.Limit - 1
		actualEndLine := startLine + actualLines - 1
		if actualEndLine < endLine {
			endLine = actualEndLine
		}
		return fmt.Sprintf("Read lines %d-%d from %s (%d lines)",
			startLine, endLine, fileName, actualLines)
	}

	if params.Offset != nil {
		startLine := *params.Offset + 1
		endLine := startLine + actualLines - 1
		return fmt.Sprintf("Read lines %d-%d from %s (%d lines)",
			startLine, endLine, fileName, actualLines)
	}

	if params.Limit != nil {
		endLine := min(actualLines, *params.Limit)
		return fmt.Sprintf("Read lines 1-%d from %s (%d lines)",
			endLine, fileName, actualLines)
	}

	return fmt.Sprintf("Read complete file %s (%d lines)", fileName, actualLines)
}

// WriteContentExtractor Write工具内容提取器
type WriteContentExtractor struct{}

func (w *WriteContentExtractor) ExtractSummary(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}

	lines := strings.Split(result.Content, "\n")
	lineCount := len(lines)

	// 尝试从metadata中提取文件路径
	if result.Metadata != "" {
		var metadata map[string]interface{}
		if err := json.Unmarshal([]byte(result.Metadata), &metadata); err == nil {
			if filePath, ok := metadata["file_path"].(string); ok {
				return fmt.Sprintf("Wrote %d lines to %s", lineCount, filepath.Base(filePath))
			}
		}
	}

	return fmt.Sprintf("Wrote %d lines", lineCount)
}

func (w *WriteContentExtractor) ExtractFullContent(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil {
		return ""
	}

	// 组合content和diff
	fullContent := result.Content

	// 从metadata中提取diff
	if result.Metadata != "" {
		var metadata specs.WriteResponseMetadata
		if err := json.Unmarshal([]byte(result.Metadata), &metadata); err == nil && metadata.Diff != "" {
			fullContent += "\n\n" + metadata.Diff
		}
	}

	return fullContent
}

func (w *WriteContentExtractor) ShouldAddExpandHint(result *message.ToolResult, isExpanded bool) bool {
	// Write工具需要展开提示，因为显示简洁总结但有完整content+diff可查看
	if result == nil || result.IsError || isExpanded {
		return false
	}
	return true
}

// EditContentExtractor Edit工具内容提取器
type EditContentExtractor struct{}

func (e *EditContentExtractor) ExtractSummary(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}

	if strings.Contains(result.Content, "success") || strings.Contains(result.Content, "updated") {
		return "File updated successfully"
	}
	return "Edit completed"
}

func (e *EditContentExtractor) ExtractFullContent(toolCall message.ToolCall, result *message.ToolResult) string {
	return e.extractDiffFromMetadata(result)
}

func (e *EditContentExtractor) extractDiffFromMetadata(result *message.ToolResult) string {
	if result == nil {
		return ""
	}

	if result.Metadata != "" {
		var metadata specs.EditResponseMetadata
		if err := json.Unmarshal([]byte(result.Metadata), &metadata); err == nil && metadata.Diff != "" {
			return metadata.Diff
		}
	}

	return result.Content
}

func (e *EditContentExtractor) ShouldAddExpandHint(result *message.ToolResult, isExpanded bool) bool {
	// Edit工具需要展开提示，因为显示简洁总结但有详细diff可查看
	if result == nil || result.IsError || isExpanded {
		return false
	}
	return true
}

// LSContentExtractor LS工具内容提取器
type LSContentExtractor struct{}

func (l *LSContentExtractor) ExtractSummary(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil {
		return "Listed successfully"
	}

	if result.Metadata != "" {
		var metadata specs.LSResponseMetadata
		if err := json.Unmarshal([]byte(result.Metadata), &metadata); err == nil {
			return fmt.Sprintf("Listed %d items", metadata.NumberOfFiles)
		}
	}

	return "Listed successfully"
}

func (l *LSContentExtractor) ExtractFullContent(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil {
		return ""
	}

	// 尝试从metadata获取详细内容
	if result.Metadata != "" {
		var metadata specs.LSResponseMetadata
		if err := json.Unmarshal([]byte(result.Metadata), &metadata); err == nil {
			return metadata.Content
		}
	}

	return result.Content
}

func (l *LSContentExtractor) ShouldAddExpandHint(result *message.ToolResult, isExpanded bool) bool {
	// LS工具如果有metadata且内容不同，则需要展开提示
	if result == nil || result.IsError || isExpanded {
		return false
	}

	// 检查是否有更详细的metadata内容
	if result.Metadata != "" {
		var metadata specs.LSResponseMetadata
		if err := json.Unmarshal([]byte(result.Metadata), &metadata); err == nil {
			// 如果metadata内容和主内容不同，则需要展开提示
			return metadata.Content != result.Content
		}
	}

	return false
}

// GrepContentExtractor Grep工具内容提取器
type GrepContentExtractor struct{}

func (g *GrepContentExtractor) ExtractSummary(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}

	// 生成Grep总结
	lines := strings.Split(result.Content, "\n")
	matchCount := 0
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			matchCount++
		}
	}

	if matchCount == 1 {
		return "Found 1 match"
	}
	return fmt.Sprintf("Found %d matches", matchCount)
}

func (g *GrepContentExtractor) ExtractFullContent(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}
	return result.Content
}

func (g *GrepContentExtractor) ShouldAddExpandHint(result *message.ToolResult, isExpanded bool) bool {
	// Grep工具通常不需要展开提示，因为总结和完整内容差不多
	return false
}

// TodoContentExtractor Todo工具内容提取器
type TodoContentExtractor struct{}

func (t *TodoContentExtractor) ExtractSummary(toolCall message.ToolCall, result *message.ToolResult) string {
	return "Todo list updated"
}

func (t *TodoContentExtractor) ExtractFullContent(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}
	return result.Content
}

func (t *TodoContentExtractor) ShouldAddExpandHint(result *message.ToolResult, isExpanded bool) bool {
	// Todo工具不需要展开提示，因为总结就足够了
	return false
}

// GlobContentExtractor Glob工具内容提取器
type GlobContentExtractor struct{}

func (g *GlobContentExtractor) ExtractSummary(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil {
		return "Globbed successfully"
	}

	if result.Metadata != "" {
		var metadata specs.GlobResponseMetadata
		if err := json.Unmarshal([]byte(result.Metadata), &metadata); err == nil {
			return fmt.Sprintf("Globbed %d items", metadata.NumberOfFiles)
		}
	}

	return "Globbed successfully"
}

func (g *GlobContentExtractor) ExtractFullContent(toolCall message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}
	return result.Content
}

func (g *GlobContentExtractor) ShouldAddExpandHint(result *message.ToolResult, isExpanded bool) bool {
	// Glob工具如果有metadata且内容不同，则需要展开提示
	if result == nil || result.IsError || isExpanded {
		return false
	}

	// 检查是否有更详细的metadata内容
	if result.Metadata != "" {
		var metadata specs.GlobResponseMetadata
		if err := json.Unmarshal([]byte(result.Metadata), &metadata); err == nil {
			// 如果有metadata信息，则可能有更详细的内容
			return metadata.NumberOfFiles > 0
		}
	}

	return false
}

// ==== 渲染策略实现 ====

// CodeRenderStrategy 代码类内容渲染策略
type CodeRenderStrategy struct{}

func (c *CodeRenderStrategy) GetSummaryContentType(toolName string) ContentType {
	return ContentPlain
}

func (c *CodeRenderStrategy) GetFullContentType(toolName string) ContentType {
	return ContentCode
}

// DiffRenderStrategy 差异内容渲染策略
type DiffRenderStrategy struct{}

func (d *DiffRenderStrategy) GetSummaryContentType(toolName string) ContentType {
	return ContentPlain
}

func (d *DiffRenderStrategy) GetFullContentType(toolName string) ContentType {
	return ContentDiff
}

// PlainRenderStrategy 普通文本渲染策略
type PlainRenderStrategy struct{}

func (p *PlainRenderStrategy) GetSummaryContentType(toolName string) ContentType {
	return ContentPlain
}

func (p *PlainRenderStrategy) GetFullContentType(toolName string) ContentType {
	return ContentPlain
}

// ==== 统一渲染器 ====

// UnifiedToolRenderer 统一工具渲染器
type UnifiedToolRenderer struct {
	BaseRenderer
	toolName string
}

// NewUnifiedToolRenderer 创建统一工具渲染器
func NewUnifiedToolRenderer(toolName string) *UnifiedToolRenderer {
	return &UnifiedToolRenderer{
		toolName: toolName,
	}
}

// CanHandle 检查是否能处理指定工具
func (u *UnifiedToolRenderer) CanHandle(toolName string) bool {
	return toolName == u.toolName
}

// Render 渲染工具调用结果
func (u *UnifiedToolRenderer) Render(toolCall message.ToolCall, result *message.ToolResult, width int) string {
	if toolCall.Input == "" {
		return ""
	}

	// 获取工具执行状态
	state := u.GetToolState(toolCall, result)

	// 提取参数信息并渲染头部
	params := u.extractDisplayParams(toolCall)
	header := u.RenderHeader(u.getDisplayName(), params, state, width, "")

	// 处理错误情况
	if result != nil && result.IsError {
		errorContent := u.TruncateContent(result.Content, 10)
		content := u.RenderContent(errorContent, "", ContentError, width)
		return u.JoinHeaderContent(header, content)
	}

	// 处理正常结果
	if result != nil && result.Content != "" {
		renderedContent := u.renderNormalContent(toolCall, result, width)
		if renderedContent != "" {
			return u.JoinHeaderContent(header, renderedContent)
		}
	}

	return header
}

// renderNormalContent 渲染正常的工具结果内容
func (u *UnifiedToolRenderer) renderNormalContent(toolCall message.ToolCall, result *message.ToolResult, width int) string {
	// 使用工厂函数获取组件
	extractor := GetContentExtractor(u.toolName)
	strategy := GetRenderStrategy(u.toolName)

	var content string
	var contentType ContentType

	// 根据展开模式选择内容和类型
	if u.expandedMode {
		content = extractor.ExtractFullContent(toolCall, result)
		contentType = strategy.GetFullContentType(u.toolName)
	} else {
		content = extractor.ExtractSummary(toolCall, result)
		contentType = strategy.GetSummaryContentType(u.toolName)
	}

	if content == "" {
		return ""
	}

	// 应用截断处理
	truncatedContent := u.TruncateContent(content, 10)

	// 添加展开提示（如果需要）
	if extractor.ShouldAddExpandHint(result, u.expandedMode) {
		truncatedContent = u.addExpandHint(truncatedContent)
	}

	// 渲染最终内容
	return u.RenderContent(truncatedContent, "", contentType, width)
}

// addExpandHint 添加展开提示
func (u *UnifiedToolRenderer) addExpandHint(content string) string {
	t := theme.CurrentTheme()
	ellipsisStyle := lipgloss.NewStyle().Foreground(t.TextMuted())
	return content + ellipsisStyle.Render(" (ctrl+r to expand)")
}

// extractDisplayParams 提取用于头部显示的参数信息
func (u *UnifiedToolRenderer) extractDisplayParams(toolCall message.ToolCall) string {
	switch u.toolName {
	case tools.BashToolName:
		return u.extractBashParams(toolCall.Input)
	case tools.ReadToolName:
		return u.extractReadParams(toolCall.Input)
	case tools.WriteToolName:
		return u.extractWriteParams(toolCall.Input)
	case tools.EditToolName:
		return u.extractEditParams(toolCall.Input)
	case tools.LSToolName:
		return u.extractLSParams(toolCall.Input)
	case tools.GrepToolName:
		return u.extractGrepParams(toolCall.Input)
	case tools.TodoWriteToolName:
		return u.extractTodoParams(toolCall.Input)
	default:
		return u.extractGenericParams(toolCall.Input)
	}
}

// extractBashParams 提取Bash命令参数
func (u *UnifiedToolRenderer) extractBashParams(input string) string {
	var params specs.BashParams
	if err := json.Unmarshal([]byte(input), &params); err != nil {
		return input
	}

	// 清理命令中的换行符和制表符
	command := strings.ReplaceAll(params.Command, "\n", " ")
	command = strings.ReplaceAll(command, "\t", " ")
	return strings.TrimSpace(command)
}

// extractReadParams 提取Read工具参数
func (u *UnifiedToolRenderer) extractReadParams(input string) string {
	var params specs.ReadParams
	if err := json.Unmarshal([]byte(input), &params); err != nil {
		return "unknown file"
	}

	// 构建范围信息
	var rangeInfo string
	if params.Offset != nil && params.Limit != nil {
		rangeInfo = fmt.Sprintf("offset %d, limit %d", *params.Offset, *params.Limit)
	} else if params.Offset != nil {
		rangeInfo = fmt.Sprintf("from offset %d", *params.Offset)
	} else if params.Limit != nil {
		rangeInfo = fmt.Sprintf("limit %d", *params.Limit)
	}

	if rangeInfo != "" {
		return fmt.Sprintf("%s (%s)", params.FilePath, rangeInfo)
	}
	return params.FilePath
}

// extractWriteParams 提取Write工具参数
func (u *UnifiedToolRenderer) extractWriteParams(input string) string {
	var params specs.WriteParams
	if err := json.Unmarshal([]byte(input), &params); err != nil {
		return "unknown file"
	}
	return params.FilePath
}

// extractEditParams 提取Edit工具参数
func (u *UnifiedToolRenderer) extractEditParams(input string) string {
	var params specs.EditParams
	if err := json.Unmarshal([]byte(input), &params); err != nil {
		return "unknown file"
	}
	return params.FilePath
}

// extractLSParams 提取LS工具参数
func (u *UnifiedToolRenderer) extractLSParams(input string) string {
	var params specs.LSParams
	if err := json.Unmarshal([]byte(input), &params); err != nil {
		return "invalid params"
	}
	return params.Path
}

// extractGrepParams 提取Grep工具参数
func (u *UnifiedToolRenderer) extractGrepParams(input string) string {
	var params map[string]interface{}
	if err := json.Unmarshal([]byte(input), &params); err != nil {
		return "search"
	}

	pattern, _ := params["pattern"].(string)
	path, _ := params["path"].(string)

	if pattern != "" && path != "" {
		return fmt.Sprintf(`"%s" in %s`, pattern, path)
	} else if pattern != "" {
		return fmt.Sprintf(`"%s"`, pattern)
	}
	return "search"
}

// extractTodoParams 提取Todo工具参数
func (u *UnifiedToolRenderer) extractTodoParams(input string) string {
	var params struct {
		Todos []Todo `json:"todos"`
	}
	if err := json.Unmarshal([]byte(input), &params); err == nil {
		count := len(params.Todos)
		switch count {
		case 0:
			return "task list"
		case 1:
			return "1 task"
		default:
			return fmt.Sprintf("%d tasks", count)
		}
	}
	return "task list"
}

// extractGenericParams 提取通用工具参数
func (u *UnifiedToolRenderer) extractGenericParams(input string) string {
	if input == "" {
		return ""
	}

	// 尝试解析JSON
	var params map[string]interface{}
	if err := json.Unmarshal([]byte(input), &params); err != nil {
		return input
	}

	// 优先提取常见的重要参数
	priorityKeys := []string{"file_path", "path", "command", "query", "pattern", "description", "message"}
	for _, key := range priorityKeys {
		if value, exists := params[key]; exists {
			if strValue, ok := value.(string); ok && strValue != "" {
				return strValue
			}
		}
	}

	// 如果没有找到优先参数，取第一个字符串值
	for _, value := range params {
		if strValue, ok := value.(string); ok && strValue != "" {
			return strValue
		}
	}

	return fmt.Sprintf("(%d parameters)", len(params))
}

// getDisplayName 获取工具的友好显示名称
func (u *UnifiedToolRenderer) getDisplayName() string {
	displayNames := map[string]string{
		tools.BashToolName:      "Bash",
		tools.EditToolName:      "Edit",
		tools.ReadToolName:      "Read",
		tools.WriteToolName:     "Write",
		tools.LSToolName:        "LS",
		tools.GrepToolName:      "Grep",
		tools.TodoWriteToolName: "Todos",
	}

	if name, exists := displayNames[u.toolName]; exists {
		return name
	}

	// 如果没有预定义的显示名称，将首字母大写
	if len(u.toolName) > 0 {
		return strings.ToUpper(u.toolName[:1]) + u.toolName[1:]
	}
	return u.toolName
}

// SetExpandedMode 设置展开模式
func (u *UnifiedToolRenderer) SetExpandedMode(expanded bool) {
	u.BaseRenderer.SetExpandedMode(expanded)
}

// Todo 任务结构定义（与specs.Todo保持一致）
type Todo struct {
	Content  string `json:"content"`
	Status   string `json:"status"`
	Priority string `json:"priority"`
	ID       string `json:"id"`
}

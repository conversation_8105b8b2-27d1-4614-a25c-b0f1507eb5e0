package tool_renders

import (
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/tui/runtime"
)

// ToolRendererManager 工具渲染器管理器
// 负责管理所有工具渲染器，分发渲染任务，并统一管理展开模式状态
type ToolRendererManager struct {
	renderers    []ToolRenderer // 渲染器列表
	fallback     ToolRenderer   // 回退渲染器（通用渲染器）
	expandedMode bool           // 全局展开模式标志
}

// NewToolRendererManager 创建工具渲染器管理器
// 初始化所有支持的工具渲染器，按优先级排序
func NewToolRendererManager(app runtime.AppRuntime) *ToolRendererManager {
	return &ToolRendererManager{
		renderers: []ToolRenderer{
			// 特殊渲染器
			NewTaskRenderer(app), // Task工具需要特殊的树状渲染
			NewTodosRenderer(),   // Todos工具需要特殊的列表渲染

			// 基于UnifiedToolRenderer的标准渲染器
			NewBashRenderer(),  // Bash命令渲染器
			NewEditRenderer(),  // 文件编辑渲染器
			NewReadRenderer(),  // 文件读取渲染器
			NewWriteRenderer(), // 文件写入渲染器
			NewGrepRenderer(),  // 搜索渲染器
			NewLSRenderer(),    // 目录列表渲染器
		},
		fallback:     NewGenericRenderer(), // 通用渲染器作为回退
		expandedMode: false,                // 默认非展开模式
	}
}

// Render 渲染工具调用结果
// 自动选择合适的渲染器进行渲染，如果没有找到匹配的渲染器则使用回退渲染器
func (trm *ToolRendererManager) Render(toolCall message.ToolCall, result *message.ToolResult, width int) string {
	// 查找能处理该工具的专用渲染器
	for _, renderer := range trm.renderers {
		if renderer.CanHandle(toolCall.Name) {
			// 如果渲染器支持展开模式，同步状态
			if expandable, ok := renderer.(ExpandableRenderer); ok {
				expandable.SetExpandedMode(trm.expandedMode)
			}
			return renderer.Render(toolCall, result, width)
		}
	}

	// 没有找到专用渲染器，使用回退渲染器
	if expandable, ok := trm.fallback.(ExpandableRenderer); ok {
		expandable.SetExpandedMode(trm.expandedMode)
	}
	return trm.fallback.Render(toolCall, result, width)
}

// SetExpandedMode 设置展开模式
// 将展开状态同步到所有支持展开功能的渲染器
func (trm *ToolRendererManager) SetExpandedMode(expanded bool) {
	trm.expandedMode = expanded

	// 同步状态到所有渲染器
	for _, renderer := range trm.renderers {
		if expandable, ok := renderer.(ExpandableRenderer); ok {
			expandable.SetExpandedMode(expanded)
		}
	}

	// 同步状态到回退渲染器
	if expandable, ok := trm.fallback.(ExpandableRenderer); ok {
		expandable.SetExpandedMode(expanded)
	}
}

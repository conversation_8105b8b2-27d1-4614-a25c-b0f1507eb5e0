package tool_renders

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
	coreAgent "github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/llm/tools"
	"github.com/qoder-ai/qodercli/core/llm/tools/specs"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
)

// GenericRenderer 通用工具渲染器
// 作为回退渲染器，处理所有未被特定渲染器处理的工具
type GenericRenderer struct {
	*UnifiedToolRenderer
}

func NewGenericRenderer() *GenericRenderer {
	return &GenericRenderer{
		UnifiedToolRenderer: NewUnifiedToolRenderer(""), // Generic处理所有工具
	}
}

func (gr *GenericRenderer) CanHandle(toolName string) bool {
	return true // 通用渲染器处理所有工具
}

func (gr *GenericRenderer) Render(toolCall message.ToolCall, result *message.ToolResult, width int) string {
	// 为Generic渲染器动态设置当前工具名
	gr.toolName = toolCall.Name
	return gr.UnifiedToolRenderer.Render(toolCall, result, width)
}

// BashRenderer Bash命令渲染器
type BashRenderer struct {
	*UnifiedToolRenderer
}

func NewBashRenderer() *BashRenderer {
	return &BashRenderer{
		UnifiedToolRenderer: NewUnifiedToolRenderer(tools.BashToolName),
	}
}

// EditRenderer 文件编辑渲染器
type EditRenderer struct {
	*UnifiedToolRenderer
}

func NewEditRenderer() *EditRenderer {
	return &EditRenderer{
		UnifiedToolRenderer: NewUnifiedToolRenderer(tools.EditToolName),
	}
}

// ReadRenderer 文件读取渲染器
type ReadRenderer struct {
	*UnifiedToolRenderer
}

func NewReadRenderer() *ReadRenderer {
	return &ReadRenderer{
		UnifiedToolRenderer: NewUnifiedToolRenderer(tools.ReadToolName),
	}
}

// WriteRenderer 文件写入渲染器
type WriteRenderer struct {
	*UnifiedToolRenderer
}

func NewWriteRenderer() *WriteRenderer {
	return &WriteRenderer{
		UnifiedToolRenderer: NewUnifiedToolRenderer(tools.WriteToolName),
	}
}

// GrepRenderer Grep搜索渲染器
type GrepRenderer struct {
	*UnifiedToolRenderer
}

func NewGrepRenderer() *GrepRenderer {
	return &GrepRenderer{
		UnifiedToolRenderer: NewUnifiedToolRenderer(tools.GrepToolName),
	}
}

// LSRenderer LS命令渲染器
type LSRenderer struct {
	*UnifiedToolRenderer
}

func NewLSRenderer() *LSRenderer {
	return &LSRenderer{
		UnifiedToolRenderer: NewUnifiedToolRenderer(tools.LSToolName),
	}
}

// TaskRenderer 任务渲染器
// 专门处理Task工具的嵌套结构渲染，支持树状显示和递归调用
type TaskRenderer struct {
	BaseRenderer
	messageService MessageService
}

// TaskMetrics 任务指标
type TaskMetrics struct {
	TotalDurationMs   int64
	TotalTokens       int
	TotalToolUseCount int
}

// MessageService 消息服务接口
// 用于获取嵌套工具调用的消息数据
type MessageService interface {
	ListMessages(ctx context.Context, sessionId string) ([]message.Message, error)
	GetAgentService() coreAgent.SubAgentService
}

func NewTaskRenderer(messageService MessageService) *TaskRenderer {
	return &TaskRenderer{
		BaseRenderer:   BaseRenderer{},
		messageService: messageService,
	}
}

func (tr *TaskRenderer) CanHandle(toolName string) bool {
	return toolName == tools.TaskToolName
}

func (tr *TaskRenderer) Render(toolCall message.ToolCall, result *message.ToolResult, width int) string {
	if toolCall.Input == "" {
		return ""
	}

	// 解析Task参数
	var params specs.TaskParams
	if err := json.Unmarshal([]byte(toolCall.Input), &params); err != nil {
		return "Task params unmarshal error"
	}

	// 获取子代理信息
	subAgentInfo, err := tr.messageService.GetAgentService().Get(params.SubagentType)
	if err != nil {
		return "SubAgent not found"
	}

	// 渲染头部
	state := tr.GetToolState(toolCall, result)
	description := params.Description
	if tr.expandedMode {
		description = "\n" +
			"Task: " + params.Description + " (using " + params.SubagentType + " agent)" + "\n" +
			"Prompt: " + params.Prompt + "\n"
	}
	header := tr.RenderHeader(params.SubagentType, description, state, width, subAgentInfo.Color)

	// 渲染嵌套工具树
	if nestedContent := tr.renderNestedTree(toolCall.Id, width, result); nestedContent != "" {
		header = tr.JoinHeaderContent(header, nestedContent)
	}

	return header
}

// renderNestedTree 渲染嵌套工具的树状结构
func (tr *TaskRenderer) renderNestedTree(sessionId string, width int, taskResult *message.ToolResult) string {
	if tr.messageService == nil {
		return ""
	}

	var lines []string
	tr.walkNestedTools(sessionId, "", &lines, width, taskResult)

	if len(lines) == 0 {
		return ""
	}

	// 应用统一样式
	style := styles.BaseStyle().Width(width - 2).PaddingLeft(6)
	return style.Render(strings.Join(lines, "\n"))
}

// walkNestedTools 递归遍历嵌套工具
func (tr *TaskRenderer) walkNestedTools(sessionId, prefix string, lines *[]string, width int, taskResult *message.ToolResult) {
	nestedMessages, err := tr.messageService.ListMessages(context.Background(), sessionId)
	if err != nil {
		return
	}

	// 构建结果映射
	resultsByCall := make(map[string]message.ToolResult)
	for _, msg := range nestedMessages {
		for _, res := range msg.ToolResults() {
			resultsByCall[res.ToolCallId] = res
		}
	}

	// 收集工具调用
	var calls []message.ToolCall
	for _, msg := range nestedMessages {
		calls = append(calls, msg.ToolCalls()...)
	}

	// 解析Task Result内容
	var agentResponse string
	var metrics TaskMetrics
	var hasTaskResult bool
	if taskResult != nil {
		agentResponse, metrics, hasTaskResult = tr.parseTaskResult(taskResult)
	}

	// 如果任务完成且为非展开模式，只显示Done Metrics
	if hasTaskResult && !tr.expandedMode {
		tr.renderDoneMetrics(metrics, prefix, lines, width)
		return
	}

	// 显示工具调用过程
	calls, omittedCount := tr.applyTruncation(calls)

	// 渲染工具调用
	for i, call := range calls {
		// 判断是否为最后一个节点
		isLast := i == len(calls)-1 && !hasTaskResult
		tr.renderToolCall(call, resultsByCall, prefix, isLast, lines, width, omittedCount)

		// 递归处理嵌套Task
		if call.Name == tools.TaskToolName {
			childPrefix := tr.getChildPrefix(prefix)
			tr.walkNestedTools(call.Id, childPrefix, lines, width, nil) // 嵌套Task不传递result
		}
	}

	// 展开模式下，渲染完整Task结果
	if hasTaskResult && tr.expandedMode {
		if agentResponse != "" {
			// 渲染Agent Response
			tr.renderAgentResponse(agentResponse, prefix, lines, width)
		}
		// 渲染Done Metrics
		tr.renderDoneMetrics(metrics, prefix, lines, width)
	}
}

// applyTruncation 应用简单截断逻辑
func (tr *TaskRenderer) applyTruncation(calls []message.ToolCall) ([]message.ToolCall, int) {
	const maxToolCalls = 1

	if !tr.expandedMode && len(calls) > maxToolCalls {
		omittedCount := len(calls) - maxToolCalls
		return calls[len(calls)-maxToolCalls:], omittedCount
	}

	return calls, 0
}

// parseTaskResult 解析Task Result内容
func (tr *TaskRenderer) parseTaskResult(result *message.ToolResult) (agentResponse string, metrics TaskMetrics, ok bool) {
	if result == nil || result.Content == "" {
		return "", TaskMetrics{}, false
	}

	// 尝试解析JSON
	var parsed struct {
		Content []struct {
			Type string `json:"type"`
			Text string `json:"text"`
		} `json:"content"`
		TotalDurationMs   int64 `json:"totalDurationMs"`
		TotalTokens       int   `json:"totalTokens"`
		TotalToolUseCount int   `json:"totalToolUseCount"`
	}

	if err := json.Unmarshal([]byte(result.Content), &parsed); err != nil {
		// JSON解析失败，graceful fallback
		return "", TaskMetrics{}, false
	}

	// 提取Agent Response
	var responseText string
	for _, content := range parsed.Content {
		if content.Type == "text" && content.Text != "" {
			responseText = content.Text
			break
		}
	}

	// 构建指标
	metrics = TaskMetrics{
		TotalDurationMs:   parsed.TotalDurationMs,
		TotalTokens:       parsed.TotalTokens,
		TotalToolUseCount: parsed.TotalToolUseCount,
	}

	return responseText, metrics, true
}

// renderAgentResponse 渲染Agent Response节点
func (tr *TaskRenderer) renderAgentResponse(content string, prefix string, lines *[]string, width int) {
	t := theme.CurrentTheme()

	// Agent Response始终使用├─连接符（不是最后一个节点）
	connector := "├─"
	childPrefix := prefix + "   "

	// 渲染标题行
	titleStyle := lipgloss.NewStyle().Bold(true).Foreground(t.Success())
	titleLine := prefix + connector + " " + titleStyle.Render("Agent Response:")
	*lines = append(*lines, titleLine)

	// 展开模式下显示完整内容
	contentStyle := lipgloss.NewStyle().Foreground(t.Text())
	contentLines := strings.Split(content, "\n")

	for i, line := range contentLines {
		var linePrefix string
		if i == 0 {
			linePrefix = childPrefix
		} else {
			linePrefix = strings.Repeat(" ", len(childPrefix))
		}

		renderedLine := linePrefix + contentStyle.Render(line)
		*lines = append(*lines, renderedLine)
	}
}

// renderDoneMetrics 渲染Done指标节点
func (tr *TaskRenderer) renderDoneMetrics(metrics TaskMetrics, prefix string, lines *[]string, width int) {
	t := theme.CurrentTheme()

	// 构建连接符（Done通常是最后一个节点）
	connector := "└─"

	// 格式化指标
	duration := tr.formatDuration(metrics.TotalDurationMs)
	tokens := tr.formatTokens(metrics.TotalTokens)
	toolUses := fmt.Sprintf("%d tool uses", metrics.TotalToolUseCount)

	// 构建Done行
	doneStyle := lipgloss.NewStyle().Bold(true).Foreground(t.Text())
	metricsStyle := lipgloss.NewStyle().Foreground(t.TextMuted())

	metricsText := fmt.Sprintf("(%s · %s · %s)", toolUses, tokens, duration)
	doneLine := prefix + connector + " " + doneStyle.Render("Done") + " " + metricsStyle.Render(metricsText)

	*lines = append(*lines, doneLine)
}

// formatTokens 格式化token数量
func (tr *TaskRenderer) formatTokens(tokens int) string {
	if tokens < 1000 {
		return fmt.Sprintf("%d tokens", tokens)
	}
	return fmt.Sprintf("%.1fk tokens", float64(tokens)/1000.0)
}

// renderToolCall 渲染单个工具调用
func (tr *TaskRenderer) renderToolCall(call message.ToolCall, resultsByCall map[string]message.ToolResult, prefix string, isLast bool, lines *[]string, width int, omittedCount int) {
	// 构建连接符和子前缀
	connector := "├─"
	childPrefix := prefix + "   "
	if isLast {
		connector = "└─"
		childPrefix = prefix + "   "
	}

	// 获取结果
	var resPtr *message.ToolResult
	if r, ok := resultsByCall[call.Id]; ok {
		resPtr = &r
	}

	// 渲染工具调用行
	toolLine := tr.buildToolLine(call, resPtr, prefix, connector, width)
	*lines = append(*lines, toolLine)

	// 渲染工具结果
	if resPtr != nil && strings.TrimSpace(resPtr.Content) != "" {
		resultLines := tr.renderToolResult(call, resPtr, childPrefix, width, omittedCount)
		*lines = append(*lines, resultLines...)
	} else if omittedCount > 0 && isLast {
		// 添加截断提示
		tr.addTruncationHint(childPrefix, omittedCount, lines)
	}
}

// buildToolLine 构建工具调用行
func (tr *TaskRenderer) buildToolLine(call message.ToolCall, result *message.ToolResult, prefix, connector string, width int) string {
	t := theme.CurrentTheme()

	// 状态和指示器
	state := tr.GetToolState(call, result)
	indicator := lipgloss.NewStyle().Foreground(tr.getStateColor(state)).Render(tr.GetStatusIndicator(state))

	// 工具名称
	name := tr.getSimpleToolName(call.Name)
	namePart := lipgloss.NewStyle().Bold(true).Foreground(t.Primary()).Render(name)

	// 参数或状态信息
	var paramsPart string
	if !call.Finished {
		paramsPart = tr.getToolAction(call.Name)
	} else {
		paramsPart = tr.extractToolParams(call, width-12) // 预留空间给前缀和图标
	}

	if paramsPart != "" {
		paramsPart = lipgloss.NewStyle().Foreground(t.Text()).Render(paramsPart)
	}

	// 组装完整行
	line := prefix + connector + " [" + indicator + "] " + namePart
	if strings.TrimSpace(paramsPart) != "" {
		line += " " + paramsPart
	}

	return line
}

// renderToolResult 渲染工具执行结果
func (tr *TaskRenderer) renderToolResult(call message.ToolCall, result *message.ToolResult, prefix string, width int, omittedCount int) []string {
	t := theme.CurrentTheme()

	if tr.expandedMode {
		// 展开模式：显示完整内容
		return tr.renderExpandedResult(call, result, prefix, width)
	} else {
		// 普通模式：显示总结
		return tr.renderSummaryResult(call, result, prefix, width, omittedCount, t)
	}
}

// renderExpandedResult 渲染展开模式的结果
func (tr *TaskRenderer) renderExpandedResult(call message.ToolCall, result *message.ToolResult, prefix string, width int) []string {
	// 处理错误情况
	if result.IsError {
		renderedContent := tr.RenderContent(result.Content, "", ContentError, tr.calculateContentWidth(prefix, width))
		return tr.addPrefixToLines(renderedContent, prefix)
	}

	// 使用对应工具的渲染策略
	extractor := GetContentExtractor(call.Name)
	strategy := GetRenderStrategy(call.Name)

	// 获取完整内容和对应的内容类型
	fullContent := extractor.ExtractFullContent(call, result)
	if fullContent == "" {
		fullContent = strings.TrimSpace(result.Content)
	}

	contentType := strategy.GetFullContentType(call.Name)

	// 使用正确的渲染策略渲染内容
	renderedContent := tr.RenderContent(fullContent, "", contentType, tr.calculateContentWidth(prefix, width))
	return tr.addPrefixToLines(renderedContent, prefix)
}

// calculateContentWidth 计算内容可用宽度
func (tr *TaskRenderer) calculateContentWidth(prefix string, totalWidth int) int {
	// 可用宽度减去前缀长度
	return totalWidth - len(prefix)
}

// addPrefixToLines 给每行添加前缀
func (tr *TaskRenderer) addPrefixToLines(content string, prefix string) []string {
	contentLines := strings.Split(content, "\n")
	var lines []string
	for i, line := range contentLines {
		var linePrefix string
		if i == 0 {
			linePrefix = prefix
		} else {
			linePrefix = strings.Repeat(" ", len(prefix))
		}
		lines = append(lines, linePrefix+line)
	}
	return lines
}

// renderSummaryResult 渲染总结模式的结果
func (tr *TaskRenderer) renderSummaryResult(call message.ToolCall, result *message.ToolResult, prefix string, width int, omittedCount int, t theme.Theme) []string {
	// 生成总结内容
	summary := tr.generateToolSummary(call, result)
	if summary == "" {
		if omittedCount > 0 {
			tr.addTruncationHint(prefix, omittedCount, &[]string{})
		}
		return nil
	}

	// 计算可用宽度
	maxWidth := width - len(prefix) - 22 // 为"(ctrl+r to expand)"预留空间

	// 应用样式
	var contentStyle lipgloss.Style
	if result.IsError {
		contentStyle = lipgloss.NewStyle().Foreground(t.Error()).MaxWidth(maxWidth)
	} else {
		contentStyle = lipgloss.NewStyle().Foreground(t.Text()).MaxWidth(maxWidth)
	}

	// 构建结果行
	resultLine := prefix + contentStyle.Render(summary)
	if !result.IsError {
		ellipsisStyle := lipgloss.NewStyle().Foreground(t.TextMuted())
		resultLine += ellipsisStyle.Render(" (ctrl+r to expand)")
	}

	lines := []string{resultLine}

	// 添加截断提示
	if omittedCount > 0 {
		tr.addTruncationHint(prefix, omittedCount, &lines)
	}

	return lines
}

// generateToolSummary 生成工具执行结果的总结
func (tr *TaskRenderer) generateToolSummary(call message.ToolCall, result *message.ToolResult) string {
	if result == nil || result.Content == "" {
		return ""
	}

	if result.IsError {
		return tr.generateErrorSummary(result.Content, call.Name)
	}

	extractor := GetContentExtractor(call.Name)
	return extractor.ExtractSummary(call, result)
}

// generateErrorSummary 生成错误总结
func (tr *TaskRenderer) generateErrorSummary(content, toolName string) string {
	lines := strings.Split(content, "\n")
	var errorMsg string

	// 查找最有用的错误信息
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		if strings.HasPrefix(line, "Error:") {
			errorMsg = strings.TrimSpace(strings.TrimPrefix(line, "Error:"))
			break
		} else if strings.Contains(strings.ToLower(line), "error") ||
			strings.Contains(strings.ToLower(line), "failed") {
			errorMsg = line
			break
		} else if errorMsg == "" {
			errorMsg = line
		}
	}

	if errorMsg == "" {
		errorMsg = "Unknown error"
	}

	// 限制长度
	if len(errorMsg) > 80 {
		errorMsg = errorMsg[:77] + "..."
	}

	// 根据工具类型添加前缀
	toolErrorPrefixes := map[string]string{
		tools.ReadToolName:  "Read failed",
		tools.WriteToolName: "Write failed",
		tools.EditToolName:  "Edit failed",
		tools.BashToolName:  "Command failed",
		tools.GrepToolName:  "Search failed",
		tools.LSToolName:    "List failed",
		tools.GlobToolName:  "Glob failed",
	}

	if prefix, exists := toolErrorPrefixes[toolName]; exists {
		return fmt.Sprintf("%s: %s", prefix, errorMsg)
	}

	return fmt.Sprintf("Error: %s", errorMsg)
}

// addTruncationHint 添加截断提示
func (tr *TaskRenderer) addTruncationHint(prefix string, omittedCount int, lines *[]string) {
	t := theme.CurrentTheme()
	ellipsisStyle := lipgloss.NewStyle().Foreground(t.TextMuted())
	truncationLine := prefix + ellipsisStyle.Render(fmt.Sprintf("+%d more tool uses", omittedCount))
	*lines = append(*lines, truncationLine)
}

// getChildPrefix 获取子节点前缀
func (tr *TaskRenderer) getChildPrefix(prefix string) string {
	return prefix + "   "
}

// getSimpleToolName 获取简化的工具名称
func (tr *TaskRenderer) getSimpleToolName(toolName string) string {
	nameMap := map[string]string{
		tools.BashToolName:  "Bash",
		tools.EditToolName:  "Edit",
		tools.ReadToolName:  "Read",
		tools.WriteToolName: "Write",
		tools.TaskToolName:  "Task",
	}

	if name, exists := nameMap[toolName]; exists {
		return name
	}

	if len(toolName) > 0 {
		return strings.ToUpper(toolName[:1]) + toolName[1:]
	}
	return toolName
}

// getToolAction 获取工具执行时的动作描述
func (tr *TaskRenderer) getToolAction(name string) string {
	actionMap := map[string]string{
		tools.TaskToolName:     "Preparing prompt...",
		tools.BashToolName:     "Building command...",
		tools.EditToolName:     "Preparing edit...",
		tools.WebFetchToolName: "Writing fetch...",
		tools.GlobToolName:     "Finding files...",
		tools.GrepToolName:     "Searching content...",
		tools.LSToolName:       "Listing directory...",
		tools.ReadToolName:     "Reading file...",
		tools.WriteToolName:    "Preparing write...",
		tools.PatchToolName:    "Preparing patch...",
	}

	if action, exists := actionMap[name]; exists {
		return action
	}
	return "Working..."
}

// extractToolParams 提取工具参数
func (tr *TaskRenderer) extractToolParams(call message.ToolCall, paramWidth int) string {
	var main string

	switch call.Name {
	case tools.TaskToolName:
		var params specs.TaskParams
		if err := json.Unmarshal([]byte(call.Input), &params); err == nil {
			main = strings.ReplaceAll(params.Prompt, "\n", " ")
		}
	case tools.BashToolName:
		var params specs.BashParams
		if err := json.Unmarshal([]byte(call.Input), &params); err == nil {
			main = strings.ReplaceAll(params.Command, "\n", " ")
		}
	case tools.EditToolName, tools.ReadToolName, tools.WriteToolName:
		// 对于文件操作工具，直接提取文件路径
		var fileParams struct {
			FilePath string `json:"file_path"`
		}
		if err := json.Unmarshal([]byte(call.Input), &fileParams); err == nil {
			main = fileParams.FilePath
		}
	case tools.LSToolName:
		var params specs.LSParams
		if err := json.Unmarshal([]byte(call.Input), &params); err == nil {
			if params.Path == "" {
				main = "."
			} else {
				main = params.Path
			}
		}
	case tools.GrepToolName:
		var params map[string]interface{}
		if err := json.Unmarshal([]byte(call.Input), &params); err == nil {
			if pattern, ok := params["pattern"].(string); ok {
				main = pattern
			}
		}
	default:
		main = strings.ReplaceAll(call.Input, "\n", " ")
	}

	if main == "" {
		return ""
	}

	// 智能截断
	return tr.smartTruncate(main, paramWidth)
}

// smartTruncate 智能截断文本
func (tr *TaskRenderer) smartTruncate(text string, maxWidth int) string {
	if lipgloss.Width(text) <= maxWidth {
		return text
	}

	if maxWidth <= 3 {
		return "..."
	}

	// 简单截断
	if maxWidth-3 < len(text) {
		text = text[:maxWidth-3]
	}
	return text + "..."
}

// formatDuration 格式化持续时间
func (tr *TaskRenderer) formatDuration(ms int64) string {
	if ms < 1000 {
		return fmt.Sprintf("%dms", ms)
	}

	sec := float64(ms) / 1000.0
	if sec < 60 {
		return fmt.Sprintf("%.1fs", sec)
	}

	min := sec / 60.0
	return fmt.Sprintf("%.1fm", min)
}

// TodosRenderer Todos任务列表渲染器
// 专门处理Todo工具的列表显示和状态管理
type TodosRenderer struct {
	BaseRenderer
}

func NewTodosRenderer() *TodosRenderer {
	return &TodosRenderer{
		BaseRenderer: BaseRenderer{},
	}
}

func (tr *TodosRenderer) CanHandle(toolName string) bool {
	return toolName == tools.TodoWriteToolName
}

func (tr *TodosRenderer) Render(toolCall message.ToolCall, result *message.ToolResult, width int) string {
	if toolCall.Input == "" {
		return ""
	}

	state := tr.GetToolState(toolCall, result)

	// 提取todos数据
	todos := tr.extractTodos(toolCall.Input, result)
	params := tr.formatTodosHeader(len(todos))

	header := tr.RenderHeader("Todos", params, state, width, "")

	// 处理错误情况
	if result != nil && result.IsError {
		truncateContent := tr.TruncateContent(result.Content, 10)
		content := tr.RenderContent(truncateContent, "", ContentError, width)
		return tr.JoinHeaderContent(header, content)
	}

	// 渲染todos列表
	if len(todos) > 0 {
		content := tr.renderTodosList(todos, width)
		return tr.JoinHeaderContent(header, content)
	}

	return header
}

// extractTodos 从工具输入或结果中提取todos数据
func (tr *TodosRenderer) extractTodos(input string, result *message.ToolResult) []Todo {
	var todos []Todo

	// 优先从result的metadata中获取
	if result != nil && result.Metadata != "" {
		if resultTodos := tr.extractTodosFromMetadata(result.Metadata); len(resultTodos) > 0 {
			todos = resultTodos
		}
	}

	// 如果result中没有，从input中解析
	if len(todos) == 0 {
		var params struct {
			Todos []Todo `json:"todos"`
		}
		if err := json.Unmarshal([]byte(input), &params); err == nil {
			todos = params.Todos
		}
	}

	return todos
}

// extractTodosFromMetadata 从metadata中提取todos
func (tr *TodosRenderer) extractTodosFromMetadata(metadata string) []Todo {
	var metadataMap map[string]interface{}
	if err := json.Unmarshal([]byte(metadata), &metadataMap); err != nil {
		return nil
	}

	if resultData, ok := metadataMap["result"]; ok {
		if resultMap, ok := resultData.(map[string]interface{}); ok {
			if newTodos, ok := resultMap["newTodos"]; ok {
				return tr.parseTodosFromInterface(newTodos)
			}
		}
	}

	return nil
}

// parseTodosFromInterface 从interface{}解析todos
func (tr *TodosRenderer) parseTodosFromInterface(data interface{}) []Todo {
	var todos []Todo

	if todosArray, ok := data.([]interface{}); ok {
		for _, item := range todosArray {
			if todoMap, ok := item.(map[string]interface{}); ok {
				todo := Todo{}
				if content, ok := todoMap["content"].(string); ok {
					todo.Content = content
				}
				if status, ok := todoMap["status"].(string); ok {
					todo.Status = status
				}
				if priority, ok := todoMap["priority"].(string); ok {
					todo.Priority = priority
				}
				if id, ok := todoMap["id"].(string); ok {
					todo.ID = id
				}
				todos = append(todos, todo)
			}
		}
	}

	return todos
}

// formatTodosHeader 格式化todos头部信息
func (tr *TodosRenderer) formatTodosHeader(count int) string {
	switch count {
	case 0:
		return "task list"
	case 1:
		return "1 task"
	default:
		return fmt.Sprintf("%d tasks", count)
	}
}

// renderTodosList 渲染todos列表
func (tr *TodosRenderer) renderTodosList(todos []Todo, width int) string {
	t := theme.CurrentTheme()
	var parts []string

	for _, todo := range todos {
		// 获取状态样式
		statusIcon, statusColor := tr.getTodoStatusStyle(todo.Status)

		// 构建todo行
		iconStyle := lipgloss.NewStyle().Width(3).Foreground(statusColor)
		contentStyle := lipgloss.NewStyle().Foreground(t.Text())

		// 完成状态使用删除线
		if todo.Status == "completed" {
			contentStyle = contentStyle.Strikethrough(true).Foreground(t.TextMuted())
		}

		line := iconStyle.Render(statusIcon) + " " + contentStyle.Render(todo.Content)
		parts = append(parts, line)
	}

	content := strings.Join(parts, "\n")
	return tr.RenderContent(content, "", ContentPlain, width)
}

// getTodoStatusStyle 获取todo状态的图标和颜色
func (tr *TodosRenderer) getTodoStatusStyle(status string) (string, lipgloss.AdaptiveColor) {
	t := theme.CurrentTheme()

	statusStyles := map[string]struct {
		icon  string
		color lipgloss.AdaptiveColor
	}{
		"completed":   {"✔", t.Success()},
		"in_progress": {"◐", t.Warning()},
		"pending":     {"○", t.TextMuted()},
	}

	if style, exists := statusStyles[status]; exists {
		return style.icon, style.color
	}

	return "?", t.Text() // 未知状态
}

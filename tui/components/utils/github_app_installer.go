package utils

import (
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/key"
	"github.com/charmbracelet/bubbles/spinner"
	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
	"github.com/qoder-ai/qodercli/tui/util"
)

// GitHubAppInstallResponseMsg GitHub App安装响应消息
type GitHubAppInstallResponseMsg struct {
	Success bool
	Message string
}

// GitHubAppKeyMap 键绑定
type GitHubAppKeyMap struct {
	Enter key.Binding
	Tab   key.Binding
	Space key.Binding
	Up    key.Binding
	Down  key.Binding
	Esc   key.Binding
	CtrlC key.Binding
}

var gitHubAppKeys = GitHubAppKeyMap{
	Enter: key.NewBinding(key.WithKeys("enter"), key.WithHelp("enter", "continue")),
	Tab:   key.NewBinding(key.WithKeys("tab"), key.WithHelp("tab", "use current repo")),
	Space: key.NewBinding(key.WithKeys(" "), key.WithHelp("space", "toggle")),
	Up:    key.NewBinding(key.WithKeys("up", "k"), key.WithHelp("↑/k", "up")),
	Down:  key.NewBinding(key.WithKeys("down", "j"), key.WithHelp("↓/j", "down")),
	Esc:   key.NewBinding(key.WithKeys("esc"), key.WithHelp("esc", "cancel")),
	CtrlC: key.NewBinding(key.WithKeys("ctrl+c"), key.WithHelp("ctrl+c", "quit")),
}

// GitHubAppInstaller GitHub App安装组件
type GitHubAppInstaller struct {
	app    runtime.AppRuntime
	width  int
	height int
	active bool

	// UI组件
	repoInput textinput.Model
	spinner   spinner.Model

	// UI状态
	selectedOption int
	loading        bool
	message        string
}

// NewGitHubAppInstaller 创建新的GitHub App安装组件
func NewGitHubAppInstaller(app runtime.AppRuntime) *GitHubAppInstaller {
	repoInput := textinput.New()
	repoInput.Placeholder = "username/repository"
	repoInput.CharLimit = 100
	repoInput.Width = 50

	// 初始化spinner
	s := spinner.New()
	s.Spinner = spinner.Dot
	s.Style = styles.BaseStyle().Foreground(theme.CurrentTheme().Primary())

	g := &GitHubAppInstaller{
		app:       app,
		repoInput: repoInput,
		spinner:   s,
	}

	// Initialize state using the common reset method
	g.resetState()

	return g
}

// Init 初始化组件
func (g *GitHubAppInstaller) Init() tea.Cmd {
	return tea.Batch(textinput.Blink, g.spinner.Tick)
}

// Start 启动安装流程
func (g *GitHubAppInstaller) Start() tea.Cmd {
	g.resetState()
	g.active = true
	g.repoInput.Focus()
	return nil
}

// IsActive 返回是否处于活跃状态
func (g *GitHubAppInstaller) IsActive() bool {
	return g.active
}

// GetCurrentStep 获取当前步骤
func (g *GitHubAppInstaller) GetCurrentStep() core.GitHubInstallStep {
	githubService := g.app.GetGitHubService()
	return githubService.GetInstallState().CurrentStep
}

// Update 更新组件状态
func (g *GitHubAppInstaller) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	// 更新spinner
	if g.loading {
		spinnerModel, spinnerCmd := g.spinner.Update(msg)
		g.spinner = spinnerModel
		if spinnerCmd != nil {
			cmds = append(cmds, spinnerCmd)
		}
	}

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		g.width = msg.Width
		g.height = msg.Height
		return g, tea.Batch(cmds...)

	case tea.KeyMsg:
		// Handle global keys
		switch {
		case key.Matches(msg, gitHubAppKeys.Esc):
			g.active = false
			githubService := g.app.GetGitHubService()
			currentStep := githubService.GetInstallState().CurrentStep
			// If installation is already complete, don't show error
			if currentStep == core.GitHubStepComplete {
				return g, util.CmdHandler(GitHubAppInstallResponseMsg{
					Success: true,
					Message: "GitHub App installation completed!",
				})
			} else {
				return g, util.CmdHandler(GitHubAppInstallResponseMsg{
					Success: false,
					Message: "User cancelled installation",
				})
			}
		case key.Matches(msg, gitHubAppKeys.CtrlC):
			// 关闭组件，返回主界面，与Esc键处理逻辑保持一致
			g.active = false
			githubService := g.app.GetGitHubService()
			currentStep := githubService.GetInstallState().CurrentStep
			// 如果安装已经完成，不显示错误信息
			if currentStep == core.GitHubStepComplete {
				return g, util.CmdHandler(GitHubAppInstallResponseMsg{
					Success: true,
					Message: "GitHub App installation completed!",
				})
			} else {
				return g, util.CmdHandler(GitHubAppInstallResponseMsg{
					Success: false,
					Message: "User cancelled installation",
				})
			}
		}

		// Handle input based on current step
		return g.handleStepInput(msg)

	case GitHubAppInstallResponseMsg:
		// Handle installation response messages
		g.loading = false
		if msg.Success {
			g.message = msg.Message
		} else {
			g.message = msg.Message
		}

		// Add automatic flow progression logic
		var autoCmd tea.Cmd
		if msg.Success {
			githubService := g.app.GetGitHubService()
			currentStep := githubService.GetInstallState().CurrentStep
			switch currentStep {
			case core.GitHubStepTaskList:
				// Auto-trigger next task after 1.5 seconds
				autoCmd = tea.Tick(time.Millisecond*1500, func(t time.Time) tea.Msg {
					return tea.KeyMsg{Type: tea.KeyEnter}
				})
			}
		}

		if autoCmd != nil {
			return g, autoCmd
		}
		return g, nil
	}

	return g, tea.Batch(cmds...)
}

// handleStepInput 处理每个步骤的输入
func (g *GitHubAppInstaller) handleStepInput(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	githubService := g.app.GetGitHubService()
	currentStep := githubService.GetInstallState().CurrentStep

	switch currentStep {
	case core.GitHubStepRepositoryInput:
		return g.handleRepositoryInput(msg)
	case core.GitHubStepAppInstallInfo:
		return g.handleAppInstallInfo(msg)
	case core.GitHubStepWorkflowSelection:
		return g.handleWorkflowSelection(msg)
	case core.GitHubStepTaskList:
		return g.handleTaskList(msg)
	case core.GitHubStepComplete:
		return g.handleComplete(msg)
	default:
		// Other steps wait for processing
		if key.Matches(msg, gitHubAppKeys.Enter) {
			return g.processCurrentStep()
		}
		return g, nil
	}
}

// handleRepositoryInput 处理仓库输入步骤
func (g *GitHubAppInstaller) handleRepositoryInput(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	switch {
	case key.Matches(msg, gitHubAppKeys.Enter):
		repository := g.repoInput.Value()
		if repository == "" {
			g.message = "Please enter a valid repository name"
			return g, nil
		}
		// Start repository validation and directly open installation page
		g.loading = true
		g.message = ""
		return g, g.validateAndOpenInstallation(repository)

	case key.Matches(msg, gitHubAppKeys.Tab):
		// Fill current Git repository if available
		githubService := g.app.GetGitHubService()
		if defaultRepo := githubService.GetCurrentRepository(); defaultRepo != "" {
			g.repoInput.SetValue(defaultRepo)
		}
		return g, nil
	}

	// Update input field
	g.repoInput, cmd = g.repoInput.Update(msg)
	return g, cmd
}

// handleAppInstallInfo 处理GitHub App安装信息步骤
func (g *GitHubAppInstaller) handleAppInstallInfo(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	// In a container environment, Enter key might not be captured correctly.
	// We accept any key press to continue from this step.
	githubService := g.app.GetGitHubService()
	githubService.StartWorkflowConfiguration()
	g.message = ""
	return g, nil
}

// handleWorkflowSelection 处理工作流选择步骤
func (g *GitHubAppInstaller) handleWorkflowSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	githubService := g.app.GetGitHubService()
	state := githubService.GetInstallState()

	switch {
	case key.Matches(msg, gitHubAppKeys.Enter):
		// Check if at least one workflow is selected
		if !state.WorkflowOptions.CodeReview && !state.WorkflowOptions.MentionSupport {
			g.message = "Please select at least one workflow feature"
			return g, nil
		}
		// Clear message and start task execution
		g.message = ""
		return g, g.startTaskExecution()

	case key.Matches(msg, gitHubAppKeys.Space):
		// Toggle the currently focused workflow option
		newOptions := state.WorkflowOptions
		switch g.selectedOption {
		case 0:
			newOptions.CodeReview = !newOptions.CodeReview
		case 1:
			newOptions.MentionSupport = !newOptions.MentionSupport
		}
		githubService.SetWorkflowOptions(newOptions)
		g.message = "" // Clear any error messages when user makes changes
		return g, nil

	case key.Matches(msg, gitHubAppKeys.Down):
		// Move focus down
		g.selectedOption = (g.selectedOption + 1) % 2
		return g, nil

	case key.Matches(msg, gitHubAppKeys.Up):
		// Move focus up
		g.selectedOption = (g.selectedOption - 1 + 2) % 2
		return g, nil
	}

	return g, nil
}

// handleTaskList 处理任务列表步骤
func (g *GitHubAppInstaller) handleTaskList(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	// Task list is mostly automated, just allow manual trigger
	if key.Matches(msg, gitHubAppKeys.Enter) {
		return g.processCurrentStep()
	}
	return g, nil
}

// handleComplete 处理完成步骤
func (g *GitHubAppInstaller) handleComplete(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	// Both Enter and Esc should close the dialog successfully
	if key.Matches(msg, gitHubAppKeys.Enter) {
		// Close installer with success message
		g.active = false
		return g, util.CmdHandler(GitHubAppInstallResponseMsg{
			Success: true,
			Message: "GitHub App installation completed!",
		})
	}
	return g, nil
}

// processCurrentStep 处理当前步骤
func (g *GitHubAppInstaller) processCurrentStep() (tea.Model, tea.Cmd) {
	githubService := g.app.GetGitHubService()
	currentStep := githubService.GetInstallState().CurrentStep

	switch currentStep {
	case core.GitHubStepAppInstallInfo:
		// Open GitHub App installation page
		return g, g.openGitHubAppInstallation()
	case core.GitHubStepTaskList:
		// Start task execution
		g.loading = true
		return g, g.executeNextTask()
	case core.GitHubStepComplete:
		// Complete installation
		return g, util.CmdHandler(GitHubAppInstallResponseMsg{
			Success: true,
			Message: "GitHub App installation completed!",
		})
	default:
		return g, nil
	}
}

// resetState 重置UI状态到初始值
func (g *GitHubAppInstaller) resetState() {
	githubService := g.app.GetGitHubService()

	// 初始化业务状态
	githubService.InitializeInstallation()

	// 重置UI状态
	g.selectedOption = 0
	g.loading = false
	g.message = ""

	// 重置输入框
	defaultRepo := githubService.GetCurrentRepository()
	g.repoInput.SetValue(defaultRepo)
}

// validateAndOpenInstallation 验证仓库并打开安装页面
func (g *GitHubAppInstaller) validateAndOpenInstallation(repository string) tea.Cmd {
	return func() tea.Msg {
		githubService := g.app.GetGitHubService()
		success, message, err := githubService.StartRepositoryValidation(repository)

		if err != nil {
			return GitHubAppInstallResponseMsg{
				Success: false,
				Message: err.Error(),
			}
		}

		return GitHubAppInstallResponseMsg{
			Success: success,
			Message: message,
		}
	}
}

// openGitHubAppInstallation 打开GitHub App安装页面 (简化版，主要用于兼容性)
func (g *GitHubAppInstaller) openGitHubAppInstallation() tea.Cmd {
	return func() tea.Msg {
		githubService := g.app.GetGitHubService()
		githubService.StartWorkflowConfiguration()
		return GitHubAppInstallResponseMsg{
			Success: true,
			Message: "Please complete qoder-assist installation in the browser, then select workflows below.",
		}
	}
}

// startTaskExecution 开始任务执行
func (g *GitHubAppInstaller) startTaskExecution() tea.Cmd {
	return func() tea.Msg {
		githubService := g.app.GetGitHubService()
		success, message, err := githubService.StartTaskExecution()

		if err != nil {
			return GitHubAppInstallResponseMsg{
				Success: false,
				Message: err.Error(),
			}
		}

		return GitHubAppInstallResponseMsg{
			Success: success,
			Message: message,
		}
	}
}

// executeNextTask 执行下一个任务
func (g *GitHubAppInstaller) executeNextTask() tea.Cmd {
	return func() tea.Msg {
		githubService := g.app.GetGitHubService()
		success, message, err := githubService.ExecuteNextTask()

		if err != nil {
			return GitHubAppInstallResponseMsg{
				Success: false,
				Message: err.Error(),
			}
		}

		return GitHubAppInstallResponseMsg{
			Success: success,
			Message: message,
		}
	}
}

// View 渲染组件
func (g *GitHubAppInstaller) View() string {
	if !g.active {
		return ""
	}

	t := theme.CurrentTheme()
	titleStyle := lipgloss.NewStyle().
		Foreground(t.Primary()).
		Bold(true).
		Align(lipgloss.Center)

	// 标题行：在标题右侧显示spinner
	title := "Install GitHub App"
	if g.loading {
		title = "Install GitHub App " + g.spinner.View()
	}
	content := titleStyle.Render(title)
	content += "\n\n"

	// Add current step content
	content += g.renderCurrentStep()

	// Add error or message
	githubService := g.app.GetGitHubService()
	state := githubService.GetInstallState()
	if state.Error != "" {
		errorStyle := lipgloss.NewStyle().Foreground(t.Error())
		content += "\n\n" + errorStyle.Render(state.Error)
	} else if g.message != "" {
		messageStyle := lipgloss.NewStyle().Foreground(t.Success())
		content += "\n\n" + messageStyle.Render(g.message)
	}

	// 移除了会导致界面跳动的loading文本显示

	// Add buttons/hints
	content += "\n\n" + g.renderButtons()

	// Use fixed dialog size for task list to prevent border movement
	borderStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(t.BorderFocused()).
		Padding(1, 2).
		Margin(1, 2)

	// For task list step, set fixed width to prevent border movement
	githubService = g.app.GetGitHubService()
	currentStep := githubService.GetInstallState().CurrentStep
	if currentStep == core.GitHubStepTaskList {
		borderStyle = borderStyle.Width(70).Height(12)
	}

	return borderStyle.Render(content)
}

// renderCurrentStep 渲染当前步骤内容
func (g *GitHubAppInstaller) renderCurrentStep() string {
	githubService := g.app.GetGitHubService()
	currentStep := githubService.GetInstallState().CurrentStep

	switch currentStep {
	case core.GitHubStepRepositoryInput:
		return g.renderRepositoryInput()
	case core.GitHubStepAppInstallInfo:
		return g.renderAppInstallInfo()
	case core.GitHubStepWorkflowSelection:
		return g.renderWorkflowSelection()
	case core.GitHubStepTaskList:
		return g.renderTaskList()
	case core.GitHubStepComplete:
		return g.renderComplete()
	default:
		return "Unknown step"
	}
}

// renderRepositoryInput 渲染仓库输入
func (g *GitHubAppInstaller) renderRepositoryInput() string {
	t := theme.CurrentTheme()
	textStyle := lipgloss.NewStyle().Foreground(t.Text())
	hintStyle := lipgloss.NewStyle().Foreground(t.TextMuted())

	content := textStyle.Render("Repository")
	content += "\n\n"
	content += g.repoInput.View()
	content += "\n\n"
	content += hintStyle.Render("Enter the GitHub repository where you want to install qoder-assist")

	return content
}

// renderAppInstallInfo 渲染GitHub App安装信息
func (g *GitHubAppInstaller) renderAppInstallInfo() string {
	t := theme.CurrentTheme()
	textStyle := lipgloss.NewStyle().Foreground(t.Text())
	successStyle := lipgloss.NewStyle().Foreground(t.Success())
	hintStyle := lipgloss.NewStyle().Foreground(t.TextMuted())

	githubService := g.app.GetGitHubService()
	repository := githubService.GetInstallState().Repository

	content := textStyle.Render("qoder-assist GitHub App Installation")
	content += "\n\n"
	content += successStyle.Render("✓ Repository: " + repository)
	content += "\n\n"
	content += textStyle.Render("The qoder-assist GitHub App provides AI-powered code review and mention support.")
	content += "\n"
	content += textStyle.Render("The installation page has been opened in your browser.")
	content += "\n\n"
	content += hintStyle.Render("After completing the installation, workflow selection will appear below.")

	return content
}

// renderWorkflowSelection 渲染工作流选择界面
func (g *GitHubAppInstaller) renderWorkflowSelection() string {
	t := theme.CurrentTheme()
	textStyle := lipgloss.NewStyle().Foreground(t.Text())
	hintStyle := lipgloss.NewStyle().Foreground(t.TextMuted())
	focusedStyle := lipgloss.NewStyle().Foreground(t.Primary()).Bold(true)
	unfocusedStyle := lipgloss.NewStyle().Foreground(t.Text())
	dimStyle := lipgloss.NewStyle().Foreground(t.TextMuted())

	githubService := g.app.GetGitHubService()
	workflowOptions := githubService.GetInstallState().WorkflowOptions

	content := textStyle.Render("Select Workflow Features")
	content += "\n\n"
	content += hintStyle.Render("Choose the features you want to enable (at least one required):")
	content += "\n\n"

	// Code Review option
	var codeReviewText string
	if workflowOptions.CodeReview {
		codeReviewText = "[✓] Code Review Workflow"
	} else {
		codeReviewText = "[ ] Code Review Workflow"
	}

	if g.selectedOption == 0 {
		content += focusedStyle.Render("> " + codeReviewText)
	} else {
		content += unfocusedStyle.Render("  " + codeReviewText)
	}
	content += "\n"
	content += dimStyle.Render("    Automatic code review on pull requests")
	content += "\n\n"

	// Mention Support option
	var mentionText string
	if workflowOptions.MentionSupport {
		mentionText = "[✓] Mention Support Workflow"
	} else {
		mentionText = "[ ] Mention Support Workflow"
	}

	if g.selectedOption == 1 {
		content += focusedStyle.Render("> " + mentionText)
	} else {
		content += unfocusedStyle.Render("  " + mentionText)
	}
	content += "\n"
	content += dimStyle.Render("    AI assistance when mentioned in issues/PRs")

	return content
}

// renderTaskList 渲染任务执行界面
func (g *GitHubAppInstaller) renderTaskList() string {
	t := theme.CurrentTheme()
	textStyle := lipgloss.NewStyle().Foreground(t.Text())
	successStyle := lipgloss.NewStyle().Foreground(t.Success())
	processingStyle := lipgloss.NewStyle().Foreground(t.Primary())
	dimStyle := lipgloss.NewStyle().Foreground(t.TextMuted())

	githubService := g.app.GetGitHubService()
	currentTaskStep := githubService.GetInstallState().TaskProgress.CurrentStep

	// Fixed layout with consistent width (80 characters)
	content := textStyle.Render("Setting up qoder-assist...")
	content += "\n\n"

	// Get all task steps dynamically
	allSteps := core.GetAllTaskSteps()

	// Use fixed-width layout to prevent border movement
	for _, step := range allSteps {
		// Pad each line to exactly 40 characters for consistent width
		var statusIcon, line string
		stepDescription := step.GetDescription()

		if step < currentTaskStep {
			// Completed step
			statusIcon = "✓"
			line = fmt.Sprintf("%s %s", statusIcon, stepDescription)
			content += successStyle.Render(line)
		} else if step == currentTaskStep {
			// Current step
			statusIcon = "•"
			line = fmt.Sprintf("%s %s...", statusIcon, stepDescription)
			content += processingStyle.Render(line)
		} else {
			// Pending step
			statusIcon = " "
			line = fmt.Sprintf("%s %s", statusIcon, stepDescription)
			content += dimStyle.Render(line)
		}

		// Pad to fixed width (40 chars) to maintain consistent layout
		padding := 40 - len(line)
		if padding > 0 {
			content += strings.Repeat(" ", padding)
		}
		content += "\n"
	}

	// Add consistent bottom padding
	content += "\n\n\n"

	return content
}

// renderComplete 渲染完成界面
func (g *GitHubAppInstaller) renderComplete() string {
	t := theme.CurrentTheme()
	textStyle := lipgloss.NewStyle().Foreground(t.Text())
	successStyle := lipgloss.NewStyle().Foreground(t.Success())
	errorStyle := lipgloss.NewStyle().Foreground(t.Error())
	hintStyle := lipgloss.NewStyle().Foreground(t.TextMuted())
	linkStyle := lipgloss.NewStyle().Foreground(t.Primary()).Underline(true)

	githubService := g.app.GetGitHubService()
	state := githubService.GetInstallState()

	// Check if there was an error
	if state.Error != "" {
		// Error case - show configuration guidance
		content := errorStyle.Render("Installation Failed")
		content += "\n\n"
		content += errorStyle.Render("Error: ") + textStyle.Render(state.Error)
		content += "\n\n"

		// Provide Git configuration guidance
		if strings.Contains(state.Error, "Git credentials") {
			content += textStyle.Render("Please configure Git credentials using one of the following methods:")
			content += "\n\n"
			content += hintStyle.Render("Method 1: Environment Variable")
			content += "\n"
			content += textStyle.Render("export GITHUB_TOKEN=your_github_token_here")
			content += "\n\n"
			content += hintStyle.Render("Method 2: Git Config")
			content += "\n"
			content += textStyle.Render("git config --global github.token your_github_token_here")
			content += "\n"
			content += textStyle.Render("git config --global github.user your_username")
			content += "\n\n"
			content += hintStyle.Render("Method 3: Git Credential Helper (Recommended)")
			content += "\n"
			content += textStyle.Render("git config --global credential.helper store")
			content += "\n"
			content += textStyle.Render("Then push to any GitHub repo to store your token")
			content += "\n\n"
			content += hintStyle.Render("After configuring credentials, please run '/install-github-app' again.")
		} else {
			content += hintStyle.Render("Please check the error message above and try again.")
		}

		return content
	}

	// Success case - show completion info
	content := successStyle.Render("Installation Complete!")
	content += "\n\n"

	if state.PRURL != "" {
		content += textStyle.Render("Pull Request created successfully:")
		content += "\n"
		content += linkStyle.Render(state.PRURL)
		content += "\n\n"
	}

	content += textStyle.Render("Next steps:")
	content += "\n"
	content += hintStyle.Render("1. Review and merge the Pull Request")
	content += "\n"
	content += hintStyle.Render("2. Ensure qoder-assist GitHub App is properly installed")
	content += "\n"
	content += hintStyle.Render("3. Test the workflow features in your repository")
	content += "\n\n"
	content += textStyle.Render("The qoder-assist is now ready to help with your development workflow!")

	return content
}

// renderButtons 渲染按钮提示
func (g *GitHubAppInstaller) renderButtons() string {
	t := theme.CurrentTheme()
	hintStyle := lipgloss.NewStyle().Foreground(t.TextMuted())

	githubService := g.app.GetGitHubService()
	currentStep := githubService.GetInstallState().CurrentStep

	switch currentStep {
	case core.GitHubStepRepositoryInput:
		return hintStyle.Render("Enter: continue • Tab: use current repo • Esc: cancel")
	case core.GitHubStepAppInstallInfo:
		return hintStyle.Render("Enter: continue to workflow selection • Esc: cancel")
	case core.GitHubStepWorkflowSelection:
		return hintStyle.Render("↑↓: navigate • Space: toggle checkbox • Enter: continue • Esc: cancel")
	case core.GitHubStepTaskList:
		return hintStyle.Render("Setting up... • Esc: cancel")
	case core.GitHubStepComplete:
		return hintStyle.Render("Enter: close • Esc: close")
	default:
		return hintStyle.Render("Enter: continue • Esc: cancel")
	}
}

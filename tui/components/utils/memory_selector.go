package utils

import (
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qodercli/core/llm/memory"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
)

// MemorySelectorOpenMsg 打开记忆选择器
type MemorySelectorOpenMsg struct{ Content string }

// MemoryTargetSelectedMsg 选择了目标位置
type MemoryTargetSelectedMsg struct {
	Location   memory.MemoryLocation
	Content    string
	InputMsgID *string // 记忆操作的InputMsgID
}

// MemorySelectorCanceledMsg 取消选择
type MemorySelectorCanceledMsg struct{}

type memoryOption struct {
	Title    string
	Location memory.MemoryLocation
}

type MemorySelector struct {
	active         bool
	width          int
	options        []memoryOption
	selectedIndex  int
	pendingContent string
}

func NewMemorySelector() *MemorySelector {
	return &MemorySelector{
		options: []memoryOption{
			{Title: "Project memory          Checked in at ./QODER.md", Location: memory.ProjectMemoryLocation},
			{Title: "Project memory (local)  Gitignored in ./QODER.local.md", Location: memory.ProjectLocalMemoryLocation},
			{Title: "User memory             Saved in ~/.qoder/QODER.md", Location: memory.UserMemoryLocation},
		},
	}
}

func (m *MemorySelector) Init() tea.Cmd { return nil }

func (m *MemorySelector) IsActive() bool { return m.active }

func (m *MemorySelector) SetSize(width int) { m.width = width }

func (m *MemorySelector) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case MemorySelectorOpenMsg:
		m.active = true
		m.pendingContent = msg.Content
		m.selectedIndex = 0
		return m, nil
	case tea.KeyMsg:
		if !m.active {
			return m, nil
		}
		switch msg.String() {
		case "up":
			if m.selectedIndex > 0 {
				m.selectedIndex--
			}
			return m, nil
		case "down":
			if m.selectedIndex < len(m.options)-1 {
				m.selectedIndex++
			}
			return m, nil
		case "enter":
			m.active = false
			opt := m.options[m.selectedIndex]
			content := m.pendingContent
			m.pendingContent = ""
			return m, func() tea.Msg {
				return MemoryTargetSelectedMsg{Location: opt.Location, Content: content, InputMsgID: nil}
			}
		case "esc":
			m.active = false
			m.pendingContent = ""
			return m, func() tea.Msg { return MemorySelectorCanceledMsg{} }
		}
	}
	return m, nil
}

func (m *MemorySelector) View() string {
	if !m.active {
		return ""
	}
	t := theme.CurrentTheme()
	var rows []string
	for i, opt := range m.options {
		prefix := "  "
		style := styles.BaseStyle().Foreground(t.Text())
		if i == m.selectedIndex {
			prefix = "→ "
			style = styles.BaseStyle().Foreground(t.Text()).Bold(true)
		}
		row := prefix + opt.Title
		rows = append(rows, style.Render(row))
	}
	content := lipgloss.JoinVertical(lipgloss.Left, rows...)
	return content
}

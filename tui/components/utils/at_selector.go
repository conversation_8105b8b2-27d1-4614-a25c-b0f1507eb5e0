package utils

import (
	"os"
	"path/filepath"
	"sort"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core/utils/fileutil"
	"github.com/qoder-ai/qodercli/tui/config"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
	"github.com/qoder-ai/qodercli/tui/util"
)

// AtQueryChangedMsg 由编辑器根据独立@匹配发出
// Active=true 表示进入/更新补全；Active=false 表示退出
// Query 为 @ 后的查询串（可为空）
type AtQueryChangedMsg struct {
	Active bool
	Query  string
}

// AtCancelMsg 显式退出补全模式
type AtCancelMsg struct{}

// AtCompletionMsg 选中某个候选，交由上层转发为 CompletionSelectedMsg
type AtCompletionMsg struct {
	SearchString    string
	CompletionValue string
}

// AtSelector 内联文件路径补全
// 行为：
// - 接收 AtQueryChangedMsg 驱动候选
// - 上/下移动，tab/enter 插入并退出，esc 退出

// - 不负责发送消息

type AtSelector struct {
	width   int
	active  bool
	query   string
	items   []string
	cursor  int
	maxRows int
}

func NewAtSelector() *AtSelector {
	return &AtSelector{maxRows: 7}
}

func (s *AtSelector) Init() tea.Cmd { return nil }

func (s *AtSelector) IsActive() bool { return s.active }

func (s *AtSelector) SetSize(width int) { s.width = width }

func (s *AtSelector) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch m := msg.(type) {
	case tea.WindowSizeMsg:
		s.width = m.Width
		return s, nil
	case AtCancelMsg:
		s.active = false
		s.items = nil
		s.query = ""
		s.cursor = 0
		return s, nil
	case AtQueryChangedMsg:
		if !m.Active {
			return s, util.CmdHandler(AtCancelMsg{})
		}
		s.active = true
		s.query = m.Query
		s.items = s.getSuggestions(m.Query)
		s.cursor = 0
		return s, nil
	case tea.KeyMsg:
		if !s.active {
			return s, nil
		}
		switch m.String() {
		case "up":
			if s.cursor > 0 {
				s.cursor--
			}
			return s, nil
		case "down":
			if s.cursor < len(s.items)-1 {
				s.cursor++
			}
			return s, nil
		case "tab", "enter":
			if len(s.items) == 0 {
				return s, util.CmdHandler(AtCancelMsg{})
			}
			val := s.items[s.cursor]
			search := "@" + s.query
			return s, tea.Batch(
				util.CmdHandler(AtCompletionMsg{SearchString: search, CompletionValue: "@" + val}),
				util.CmdHandler(AtCancelMsg{}),
			)
		case "esc":
			return s, util.CmdHandler(AtCancelMsg{})
		}
	}
	return s, nil
}

func (s *AtSelector) View() string {
	if !s.active || len(s.items) == 0 || s.width == 0 {
		return ""
	}
	t := theme.CurrentTheme()
	maxWidth := s.width - 2 // 与 command_selector 对齐，左右各预留 1 空间

	// 计算滚动窗口，保证当前选中项可见
	start := 0
	if len(s.items) > s.maxRows {
		half := s.maxRows / 2
		if s.cursor >= half && s.cursor < len(s.items)-half {
			start = s.cursor - half
		} else if s.cursor >= len(s.items)-half {
			start = len(s.items) - s.maxRows
		}
	}
	end := start + s.maxRows
	if end > len(s.items) {
		end = len(s.items)
	}

	rows := s.items[start:end]
	var rendered []string
	for i, it := range rows {
		name := it
		if len(name) > maxWidth-4 {
			name = name[:maxWidth-7] + "..."
		}
		globalIndex := start + i
		if globalIndex == s.cursor {
			selectedStyle := styles.BaseStyle().Foreground(t.Primary()).Bold(true)
			rendered = append(rendered, selectedStyle.Render("→ "+name))
		} else {
			normalStyle := styles.BaseStyle().Foreground(t.Text()).PaddingLeft(1)
			rendered = append(rendered, normalStyle.Render(" "+name))
		}
	}
	return strings.Join(rendered, "\n")
}

// getSuggestions 基于 query 生成相对路径候选（包含匹配 + 简单匹配度排序）
func (s *AtSelector) getSuggestions(query string) []string {
	wd := config.WorkingDirectory()
	base := wd
	partial := query
	// 支持前缀路径：@src/u → base=src partial=u
	if idx := strings.LastIndex(query, "/"); idx >= 0 {
		base = filepath.Join(wd, query[:idx])
		partial = query[idx+1:]
	}
	if base == "" {
		base = "."
	}

	paths, _, err := fileutil.GlobWithDoublestar("**/*", base, 200)
	if err != nil {
		util.ReportError(err)
		return nil
	}

	// 空查询：返回前 200 个相对路径（稳定）
	q := strings.ToLower(partial)
	if q == "" {
		res := make([]string, 0, 200)
		for _, p := range paths {
			rel, _ := filepath.Rel(wd, p)
			if strings.HasPrefix(rel, "../") {
				continue
			}
			res = append(res, rel)
			if len(res) >= 200 {
				break
			}
		}
		return res
	}

	type scored struct {
		path  string
		score int
	}
	var arr []scored
	for _, p := range paths {
		rel, _ := filepath.Rel(wd, p)
		if strings.HasPrefix(rel, "../") {
			continue
		}
		name := filepath.Base(rel)
		lowerName := strings.ToLower(name)
		lowerRel := strings.ToLower(rel)
		idxName := strings.Index(lowerName, q)
		idxRel := strings.Index(lowerRel, q)
		if idxName < 0 && idxRel < 0 {
			continue
		}

		// 打分：文件名优先，其次整条路径；靠前优先；开头额外加分；文件名更短、路径更浅加分
		s := 0
		if idxName >= 0 {
			s += 3000 - idxName
			if idxName == 0 {
				s += 300
			}
			s += 120 - len(lowerName)
		}
		if idxRel >= 0 {
			s += 1500 - idxRel
			if idxRel == 0 {
				s += 120
			}
		}
		depth := strings.Count(rel, string(os.PathSeparator))
		s += 80 - depth // 越浅越好
		arr = append(arr, scored{path: rel, score: s})
	}

	sort.Slice(arr, func(i, j int) bool {
		if arr[i].score != arr[j].score {
			return arr[i].score > arr[j].score
		}
		return arr[i].path < arr[j].path
	})

	// 统一返回前 200 项匹配结果
	limit := 200
	if len(arr) < limit {
		limit = len(arr)
	}
	res := make([]string, 0, limit)
	for i := 0; i < limit; i++ {
		res = append(res, arr[i].path)
	}
	return res
}

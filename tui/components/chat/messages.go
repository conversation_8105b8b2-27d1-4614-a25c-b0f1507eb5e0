package chat

import (
	"context"
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/muesli/reflow/wordwrap"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"github.com/qoder-ai/qodercli/core/version"
	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"
	messagestypes "github.com/qoder-ai/qodercli/tui/components/types/messages"
	"github.com/qoder-ai/qodercli/tui/components/utils/tool_renders"
	"github.com/qoder-ai/qodercli/tui/config"
	"github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
	"github.com/qoder-ai/qodercli/tui/util"
)

// RenderedMessage 渲染后的消息
type RenderedMessage = messagestypes.RenderedMessage

// MessagesComponent 消息组件
// 新的简化架构：直接在组件内渲染所有消息，高度随内容动态变化
// 职责：
//   - 监听消息/工具事件并维护完整的消息列表
//   - 实时更新工具调用状态和shell执行结果
//   - 支持历史消息的任意修改和重新渲染
//   - 组件高度随消息总内容动态调整
//   - 支持ctrl+r展开功能，显示完整内容
type MessagesComponent struct {
	app          runtime.AppRuntime
	session      core.Session
	width        int
	height       int
	windowHeight int // 终端窗口总高度

	// 消息列表管理（按时间顺序）
	messageOrder     []string                    // 消息ID的显示顺序
	messages         map[string]message.Message  // 消息ID -> 原始消息对象
	renderedMessages map[string]*RenderedMessage // 消息ID -> 渲染结果

	toolRendererManager *tool_renders.ToolRendererManager

	// 工具调用状态跟踪
	toolCallParent  map[string]string             // toolCallId -> parent assistant messageId
	parentToolCalls map[string][]message.ToolCall // parent messageId -> tool calls
	toolResults     map[string]message.ToolResult // toolCallId -> latest result

	// bash处理器
	bashHandler messagestypes.BashHandler
	// command处理器
	commandHandler messagestypes.CommandHandler
	// memory处理器
	memoryHandler messagestypes.MemoryHandler

	// 展开状态管理
	expandMode           messagestypes.ExpandMode // 展开模式状态
	partialModeBaseCount int                      // partial模式时的基准消息数量
}

// NewMessagesComponent 创建消息组件
func NewMessagesComponent(app runtime.AppRuntime, bashHandler messagestypes.BashHandler, commandHandler messagestypes.CommandHandler, memoryHandler messagestypes.MemoryHandler) *MessagesComponent {
	m := &MessagesComponent{
		app:                  app,
		messageOrder:         make([]string, 0),
		messages:             make(map[string]message.Message),
		renderedMessages:     make(map[string]*RenderedMessage),
		toolRendererManager:  tool_renders.NewToolRendererManager(app),
		toolCallParent:       make(map[string]string),
		parentToolCalls:      make(map[string][]message.ToolCall),
		toolResults:          make(map[string]message.ToolResult),
		bashHandler:          bashHandler,
		commandHandler:       commandHandler,
		memoryHandler:        memoryHandler,
		width:                80,
		height:               1,
		expandMode:           messagestypes.ExpandModeNormal,
		partialModeBaseCount: 0,
	}

	return m
}

// Init 初始化组件
func (m *MessagesComponent) Init() tea.Cmd {
	return nil
}

// Update 处理外部事件（窗口大小、会话切换、消息事件）
func (m *MessagesComponent) Update(msg tea.Msg) (*MessagesComponent, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.windowHeight = msg.Height
		// 更新bash处理器的宽度
		m.bashHandler.SetWidth(msg.Width)
		// 更新command处理器的宽度
		m.commandHandler.SetWidth(msg.Width)
		// 更新memory处理器的宽度
		m.memoryHandler.SetWidth(msg.Width)
		// 窗口大小变化时重新渲染所有消息
		m.refreshAllMessages()
		return m, nil

	case tea.KeyMsg:
		// 处理ESC键：如果有shell命令正在执行，则取消执行
		if msg.String() == "esc" {
			// 检查当前是否有shell命令正在执行
			if execState := m.bashHandler.GetExecutionState(); execState != nil && execState.ExecutionID != "" {
				activeExecutionId := execState.ExecutionID
				// 取消shell执行
				err := m.app.CancelShell(activeExecutionId)
				if err != nil {
					return m, util.CmdHandler(util.InfoMsg{
						Type: util.InfoTypeError,
						Msg:  fmt.Sprintf("Failed to cancel shell command: %v", err),
					})
				}
				return m, nil
			}
		}
		return m, nil

	case messagestypes.SessionChangedMsg:
		// 切换会话时重置内部状态，加载历史消息
		m.session = msg.Session
		m.messageOrder = make([]string, 0)
		m.messages = make(map[string]message.Message)
		m.renderedMessages = make(map[string]*RenderedMessage)
		// 重置工具调用状态
		m.toolCallParent = make(map[string]string)
		m.parentToolCalls = make(map[string][]message.ToolCall)
		m.toolResults = make(map[string]message.ToolResult)
		return m, m.loadMessages()

	case messagestypes.BashAppendOutputMsg, messagestypes.BashFinishedMsg, messagestypes.BashOperationStartMsg, messagestypes.BashOperationCompleteMsg:
		return m, m.bashHandler.HandleMessage(msg, m.session, m.renderedMessages, m.messages)

	case pubsub.Event[message.Message]:
		// 只处理当前会话的消息，或工具子会话的消息
		if msg.Payload.SessionId != m.session.Id {
			// 检查是否为工具子会话的消息
			if _, ok := m.toolCallParent[msg.Payload.SessionId]; ok {
				// 更新父消息的工具调用结果
				if cmd := m.handleMessageEvent(msg); cmd != nil {
					cmds = append(cmds, cmd)
				}
			}
			return m, tea.Batch(cmds...)
		}

		if cmd := m.handleMessageEvent(msg); cmd != nil {
			cmds = append(cmds, cmd)
		}

	case messagestypes.MessagesLoadedMsg:
		// 历史消息：直接加载到消息列表
		m.loadHistoryMessages(msg.Messages)
		// 通知editor，历史消息加载完成（触发一次send操作，以确保当有initPrompt时，在历史消息加载完成后发送消息）
		cmds = append(cmds, util.CmdHandler(editortypes.TriggerEditorSendMsg{}))

	case messagestypes.ShellEventListenerMsg:
		cmd, shouldContinue := m.bashHandler.ProcessShellEvent(msg.ExecutionID, msg.EventCh)
		if shouldContinue {
			if teaCmd, ok := cmd.(tea.Cmd); ok {
				return m, teaCmd
			}
			return m, util.CmdHandler(cmd)
		}
		return m, nil

	case messagestypes.MemoryOperationStartMsg, messagestypes.MemoryOperationCompleteMsg:
		// 处理记忆操作消息
		if cmd := m.memoryHandler.HandleMessage(msg, m.session, m.renderedMessages, m.messages); cmd != nil {
			cmds = append(cmds, cmd)
		}

	case util.InfoMsg:
		m.addInfoMessage(msg)
	}

	return m, tea.Batch(cmds...)
}

// refreshAllMessages 重新渲染所有消息
func (m *MessagesComponent) refreshAllMessages() {
	for _, msgID := range m.messageOrder {
		if msg, ok := m.messages[msgID]; ok {
			// 重新渲染消息，需要根据消息类型使用对应的处理器
			if msg.Role == "assistant" && len(m.parentToolCalls[msgID]) > 0 {
				// 有工具调用的assistant消息
				combined := m.buildAssistantWithTools(msg)
				m.renderedMessages[msgID] = &RenderedMessage{
					ID:           msgID,
					Role:         string(msg.Role),
					RenderedText: combined,
					IsFinished:   msg.IsFinished(),
				}
			} else if m.bashHandler.IsOperationMessage(msg) {
				// Bash消息：直接重新渲染，不修改messageOrder
				m.refreshBashMessage(msg)
			} else if m.commandHandler.IsCommandMessage(msg) {
				// Command消息：直接重新渲染，不修改messageOrder
				m.refreshCommandMessage(msg)
			} else if m.memoryHandler.IsOperationMessage(msg) {
				// Memory消息：直接重新渲染，不修改messageOrder
				m.refreshMemoryMessage(msg)
			} else {
				// 普通消息
				rendered := m.preprocessMessage(msg)
				m.renderedMessages[msgID] = rendered
			}
		}
	}
}

// refreshBashMessage 重新渲染bash消息（不修改messageOrder）
func (m *MessagesComponent) refreshBashMessage(msg message.Message) {
	content := msg.Content().Text
	if strings.Contains(content, "<bash-input>") {
		// Input消息：查找对应的result消息并合并渲染
		var stdout, stderr string
		for _, resultMsg := range m.messages {
			if resultMsg.ParentId == msg.Id && strings.Contains(resultMsg.Content().Text, "<bash-stdout>") {
				stdout, stderr = m.bashHandler.ExtractResult(resultMsg.Content().Text)
				break
			}
		}
		command := m.bashHandler.ExtractCommand(content)
		rendered := m.bashHandler.RenderOperation(command, stdout, stderr)
		m.renderedMessages[msg.Id] = &RenderedMessage{
			ID:           msg.Id,
			Role:         "bash",
			RenderedText: rendered,
			IsFinished:   true,
		}
	}
}

// refreshCommandMessage 重新渲染command消息（不修改messageOrder）
func (m *MessagesComponent) refreshCommandMessage(msg message.Message) {
	content := msg.Content().String()
	if strings.Contains(content, "<command-message>") {
		// Command信息消息：解析并查找对应的结果消息
		cmdInfo := m.commandHandler.ParseCommandContent(content)

		// 查找对应的结果消息
		for _, resultMsg := range m.messages {
			if resultMsg.ParentId == msg.Id && strings.Contains(resultMsg.Content().String(), "<local-command-stdout>") {
				resultInfo := m.commandHandler.ParseCommandContent(resultMsg.Content().String())
				cmdInfo.Output = resultInfo.Output
				cmdInfo.Error = resultInfo.Error
				break
			}
		}

		// 渲染命令
		rendered := m.commandHandler.RenderCommand(cmdInfo)
		rendered.ID = msg.Id
		m.renderedMessages[msg.Id] = rendered
	}
}

// refreshMemoryMessage 重新渲染memory消息（不修改messageOrder）
func (m *MessagesComponent) refreshMemoryMessage(msg message.Message) {
	content := msg.Content().Text
	if strings.Contains(content, "<user-memory-input>") {
		// Memory输入消息：查找对应的结果消息
		var location string
		var success bool
		var hasResult bool
		for _, resultMsg := range m.messages {
			if resultMsg.ParentId == msg.Id && strings.Contains(resultMsg.Content().Text, "<user-memory-result>") {
				location, success = m.memoryHandler.ExtractResult(resultMsg.Content().Text)
				hasResult = true
				break
			}
		}

		// 渲染记忆操作
		memoryContent := m.memoryHandler.ExtractContent(content)
		var rendered string
		if hasResult {
			// 有结果消息：显示完整的操作结果
			rendered = m.memoryHandler.RenderOperation(memoryContent, location, success)
		} else {
			// 没有结果消息：显示等待状态
			rendered = m.memoryHandler.RenderPendingOperation(memoryContent)
		}

		m.renderedMessages[msg.Id] = &RenderedMessage{
			ID:           msg.Id,
			Role:         "memory",
			RenderedText: rendered,
			IsFinished:   hasResult, // 只有有结果时才标记为完成
		}
	}
}

// addInfoMessage 添加信息消息到消息列表
func (m *MessagesComponent) addInfoMessage(info util.InfoMsg) {
	if strings.TrimSpace(info.Msg) == "" || info.Type == util.InfoTypeInfo {
		return
	}

	msgID := fmt.Sprintf("info-%d", time.Now().UnixNano())
	t := theme.CurrentTheme()
	label := "ERROR"
	labelColor := t.Error()
	switch info.Type {
	case util.InfoTypeWarn:
		label = "WARN"
		labelColor = t.Warning()
	case util.InfoTypeError:
		label = "ERROR"
		labelColor = t.Error()
	}
	labelStyle := styles.BaseStyle().Foreground(labelColor).PaddingLeft(2)
	msgStyle := styles.BaseStyle().Foreground(t.Text())
	line := labelStyle.Render("["+label+"] ") + msgStyle.Render(m.wrapText(info.Msg, m.width))

	m.messageOrder = append(m.messageOrder, msgID)
	m.renderedMessages[msgID] = &RenderedMessage{
		ID:           msgID,
		Role:         "info",
		RenderedText: line,
		IsFinished:   true,
	}
}

// loadHistoryMessages 加载历史消息到消息列表
func (m *MessagesComponent) loadHistoryMessages(messages []message.Message) {
	// 清空现有消息
	m.messageOrder = make([]string, 0)
	m.messages = make(map[string]message.Message)
	m.renderedMessages = make(map[string]*RenderedMessage)

	// 重置工具调用状态
	m.toolCallParent = make(map[string]string)
	m.parentToolCalls = make(map[string][]message.ToolCall)
	m.toolResults = make(map[string]message.ToolResult)

	// 1) 预扫描：收集所有父 assistant 的 tool calls 与对应结果
	for _, msg := range messages {
		// 预扫描阶段过滤Meta消息
		if !msg.IsMeta {
			m.messages[msg.Id] = msg
		}

		// 收集父 assistant 的工具调用
		if msg.Role == "assistant" && len(msg.ToolCalls()) > 0 {
			m.parentToolCalls[msg.Id] = append([]message.ToolCall(nil), msg.ToolCalls()...)
			for _, tc := range msg.ToolCalls() {
				m.toolCallParent[tc.Id] = msg.Id
			}
		}

		// 收集工具结果（历史里的 tool 消息）
		if msg.Role == "tool" {
			for _, res := range msg.ToolResults() {
				m.toolResults[res.ToolCallId] = res
			}
		}
	}

	// 2) 渲染输出：
	// 先处理所有input消息，再处理result消息，确保父子关系正确建立
	for _, msg := range messages {
		// 跳过Meta消息的渲染
		if msg.IsMeta {
			continue
		}

		switch msg.Role {
		case "tool":
			// 历史中工具消息不单独渲染，已合并到父 assistant
			continue
		case "assistant":
			m.messageOrder = append(m.messageOrder, msg.Id)
			if len(msg.ToolCalls()) > 0 {
				// 合成父消息 + 工具渲染
				combined := m.buildAssistantWithTools(msg)
				m.renderedMessages[msg.Id] = &RenderedMessage{
					ID:           msg.Id,
					Role:         string(msg.Role),
					RenderedText: combined,
					IsFinished:   msg.IsFinished(),
				}
			} else {
				// 无工具：正常预处理
				rendered := m.preprocessMessage(msg)
				m.renderedMessages[msg.Id] = rendered
			}
		default:
			// user/system/others：按优先级检查消息类型
			if m.bashHandler.IsOperationMessage(msg) && strings.Contains(msg.Content().Text, "<bash-input>") {
				// Bash input消息
				m.bashHandler.HandleOperationMessage(msg, &m.messageOrder, m.renderedMessages, m.messages)
			} else if m.memoryHandler.IsOperationMessage(msg) && strings.Contains(msg.Content().Text, "<user-memory-input>") {
				// Memory input消息
				m.memoryHandler.HandleOperationMessage(msg, &m.messageOrder, m.renderedMessages, m.messages)
			} else if m.commandHandler.IsCommandMessage(msg) {
				// Command消息
				m.commandHandler.HandleCommandMessage(msg, &m.messageOrder, m.renderedMessages, m.messages)
			} else if !m.bashHandler.IsOperationMessage(msg) && !m.memoryHandler.IsOperationMessage(msg) {
				// 普通消息：正常预处理
				m.messageOrder = append(m.messageOrder, msg.Id)
				rendered := m.preprocessMessage(msg)
				m.renderedMessages[msg.Id] = rendered
			}
		}
	}

	// 处理bash result消息
	for _, msg := range messages {
		if msg.IsMeta {
			continue
		}
		if m.bashHandler.IsOperationMessage(msg) && strings.Contains(msg.Content().Text, "<bash-stdout>") {
			m.bashHandler.HandleOperationMessage(msg, &m.messageOrder, m.renderedMessages, m.messages)
		}
	}

	// 处理memory result消息
	for _, msg := range messages {
		if msg.IsMeta {
			continue
		}
		if m.memoryHandler.IsOperationMessage(msg) && strings.Contains(msg.Content().Text, "<user-memory-result>") {
			m.memoryHandler.HandleOperationMessage(msg, &m.messageOrder, m.renderedMessages, m.messages)
		}
	}

	// 处理command result消息
	for _, msg := range messages {
		if msg.IsMeta {
			continue
		}
		if m.commandHandler.IsCommandMessage(msg) && strings.Contains(msg.Content().String(), "<local-command-stdout>") {
			m.commandHandler.HandleCommandMessage(msg, &m.messageOrder, m.renderedMessages, m.messages)
		}
	}
}

// handleMessageEvent 处理消息创建/更新事件（核心入口）
func (m *MessagesComponent) handleMessageEvent(event pubsub.Event[message.Message]) tea.Cmd {
	switch event.Type {
	case pubsub.CreatedEvent:
		return m.handleMessageCreated(event.Payload)
	case pubsub.UpdatedEvent:
		return m.handleMessageUpdated(event.Payload)
	}
	return nil
}

// handleMessageCreated 处理消息创建事件
func (m *MessagesComponent) handleMessageCreated(msg message.Message) tea.Cmd {
	// 跳过meta消息的渲染
	if msg.IsMeta {
		// 仍然保存消息到列表，但不添加到渲染顺序中
		m.messages[msg.Id] = msg
		return nil
	}

	// 检查是否已经渲染过这条消息（避免重复渲染）
	if _, exists := m.renderedMessages[msg.Id]; exists {
		return nil
	}

	// 将消息添加到消息列表
	m.messages[msg.Id] = msg

	// 检查是否为工具子会话的消息，如果是则更新父消息
	if parentID, ok := m.toolCallParent[msg.SessionId]; ok {
		// 工具子会话的消息，不直接添加到消息列表，而是触发父消息更新
		if parentMsg, ok := m.messages[parentID]; ok {
			combined := m.buildAssistantWithTools(parentMsg)
			m.renderedMessages[parentID] = &RenderedMessage{
				ID:           parentID,
				Role:         string(parentMsg.Role),
				RenderedText: combined,
				IsFinished:   parentMsg.IsFinished(),
			}
		}
		return nil
	}

	switch msg.Role {
	case "user", "system":
		// 检查是否为bash操作消息
		if m.bashHandler.IsOperationMessage(msg) {
			return m.bashHandler.HandleOperationMessage(msg, &m.messageOrder, m.renderedMessages, m.messages)
		}

		// 检查是否为command消息
		if m.commandHandler.IsCommandMessage(msg) {
			return m.commandHandler.HandleCommandMessage(msg, &m.messageOrder, m.renderedMessages, m.messages)
		}

		// 检查是否为memory操作消息
		if m.memoryHandler.IsOperationMessage(msg) {
			return m.memoryHandler.HandleOperationMessage(msg, &m.messageOrder, m.renderedMessages, m.messages)
		}

		// 用户/系统消息：直接添加到消息列表
		m.messageOrder = append(m.messageOrder, msg.Id)
		rendered := m.preprocessMessage(msg)
		m.renderedMessages[msg.Id] = rendered
		return nil

	case "tool":
		// 工具结果：更新父 assistant 的内容
		for _, res := range msg.ToolResults() {
			m.toolResults[res.ToolCallId] = res
			parentID, ok := m.toolCallParent[res.ToolCallId]
			if !ok {
				continue
			}
			parentMsg, ok := m.messages[parentID]
			if !ok {
				continue
			}
			// 重新渲染父消息和工具调用
			combined := m.buildAssistantWithTools(parentMsg)
			m.renderedMessages[parentID] = &RenderedMessage{
				ID:           parentID,
				Role:         string(parentMsg.Role),
				RenderedText: combined,
				IsFinished:   parentMsg.IsFinished(),
			}
		}
		return nil

	case "assistant":
		// Assistant消息
		m.messageOrder = append(m.messageOrder, msg.Id)
		if len(msg.ToolCalls()) > 0 {
			// 有工具调用：设置工具调用映射
			m.parentToolCalls[msg.Id] = append([]message.ToolCall(nil), msg.ToolCalls()...)
			for _, tc := range msg.ToolCalls() {
				m.toolCallParent[tc.Id] = msg.Id
			}
			// 渲染消息和工具调用
			combined := m.buildAssistantWithTools(msg)
			m.renderedMessages[msg.Id] = &RenderedMessage{
				ID:           msg.Id,
				Role:         string(msg.Role),
				RenderedText: combined,
				IsFinished:   msg.IsFinished(),
			}
		} else {
			// 无工具调用：直接渲染
			rendered := m.preprocessMessage(msg)
			m.renderedMessages[msg.Id] = rendered
		}
		return nil
	}
	return nil
}

// handleMessageUpdated 处理消息更新事件
func (m *MessagesComponent) handleMessageUpdated(msg message.Message) tea.Cmd {
	// 更新消息存储
	m.messages[msg.Id] = msg

	if msg.Role != "assistant" {
		// 非 assistant：若来自 Task 子会话，用于刷新父消息
		if parentID, ok := m.toolCallParent[msg.SessionId]; ok {
			if parentMsg, ok := m.messages[parentID]; ok {
				combined := m.buildAssistantWithTools(parentMsg)
				m.renderedMessages[parentID] = &RenderedMessage{
					ID:           parentID,
					Role:         string(parentMsg.Role),
					RenderedText: combined,
					IsFinished:   parentMsg.IsFinished(),
				}
			}
		}
		return nil
	}

	// assistant 更新：合并工具并更新渲染
	if len(msg.ToolCalls()) > 0 || len(m.parentToolCalls[msg.Id]) > 0 {
		if len(msg.ToolCalls()) > 0 {
			m.parentToolCalls[msg.Id] = append([]message.ToolCall(nil), msg.ToolCalls()...)
			for _, tc := range msg.ToolCalls() {
				m.toolCallParent[tc.Id] = msg.Id
			}
		}

		// 重新渲染消息和工具调用
		combined := m.buildAssistantWithTools(msg)
		m.renderedMessages[msg.Id] = &RenderedMessage{
			ID:           msg.Id,
			Role:         string(msg.Role),
			RenderedText: combined,
			IsFinished:   msg.IsFinished(),
		}
		return nil
	}

	// 无工具：直接更新渲染结果
	if msg.IsFinished() && msg.FinishReason() != message.FinishReasonCanceled {
		rendered := m.preprocessMessage(msg)
		m.renderedMessages[msg.Id] = rendered
	}
	return nil
}

// preprocessMessage 预渲染消息
func (m *MessagesComponent) preprocessMessage(msg message.Message) *RenderedMessage {
	hasThinking := msg.ReasoningContent().Thinking != ""
	hasContent := msg.Content().String() != ""

	var renderedText string
	if hasThinking && hasContent {
		// Mixed：分别渲染 thinking 与 content 部分
		renderedText = m.renderMixedMessage(msg)
	} else {
		// 其他：统一样式渲染
		style := m.getStyleForContentType(msg, hasThinking)
		raw := m.extractRawContent(msg)

		var linesRendered []string
		lines := strings.Split(strings.TrimSpace(raw), "\n")
		for _, ln := range lines {
			if strings.TrimSpace(ln) == "" {
				continue
			}
			linesRendered = append(linesRendered, style.Render(ln))
		}
		renderedText = strings.Join(linesRendered, "\n")
	}

	return &RenderedMessage{
		ID:           msg.Id,
		Role:         string(msg.Role),
		RenderedText: renderedText,
		IsFinished:   msg.IsFinished(),
	}
}

// renderMixedMessage 渲染 mixed 类型（thinking + content 分别处理）
func (m *MessagesComponent) renderMixedMessage(msg message.Message) string {
	t := theme.CurrentTheme()

	thinkingPart, contentPart := m.extractMixedContent(msg)
	var parts []string

	// thinking
	if thinkingPart != "" {
		thinkingStyle := styles.BaseStyle().
			Foreground(t.Accent()).
			Italic(true).
			PaddingLeft(0)
		for _, ln := range strings.Split(strings.TrimSpace(thinkingPart), "\n") {
			if strings.TrimSpace(ln) != "" {
				parts = append(parts, thinkingStyle.Render(ln))
			}
		}
	}

	// content
	if contentPart != "" {
		contentStyle := styles.BaseStyle().
			Foreground(t.Primary()).
			PaddingLeft(0)
		for _, ln := range strings.Split(strings.TrimSpace(contentPart), "\n") {
			if strings.TrimSpace(ln) != "" {
				parts = append(parts, contentStyle.Render(ln))
			}
		}
	}

	return strings.Join(parts, "\n")
}

// getStyleForContentType 根据角色/类型选择统一样式（仅处理 thinking/content，不含工具）
func (m *MessagesComponent) getStyleForContentType(msg message.Message, isThinking bool) lipgloss.Style {
	t := theme.CurrentTheme()
	base := styles.BaseStyle().Width(m.width - 1)
	if isThinking {
		return base.Foreground(t.Accent()).PaddingLeft(0).Italic(true)
	} else {
		if msg.Role == "user" {
			return base.Foreground(t.Secondary()).PaddingLeft(0)
		}
		if msg.Role == "assistant" {
			return base.Foreground(t.Primary()).PaddingLeft(0)
		}
		if msg.Role == "system" {
			return base.Foreground(t.Secondary()).PaddingLeft(0)
		}
		return base.PaddingLeft(0)
	}
}

// extractRawContent 提取用于渲染的基础文本（添加思考/圆点标识对齐，不包含工具）
func (m *MessagesComponent) extractRawContent(msg message.Message) string {
	var parts []string

	// thinking
	if thinking := msg.ReasoningContent().Thinking; thinking != "" {
		formatted := m.wrapText(thinking, m.width)
		lines := strings.Split(formatted, "\n")
		var processed []string
		for i, ln := range lines {
			if i == 0 {
				processed = append(processed, "◇ "+ln)
			} else {
				processed = append(processed, "  "+ln)
			}
		}
		parts = append(parts, strings.Join(processed, "\n"))
	}

	// content（根据角色添加前缀/对齐，保持统一的视觉语言）
	switch msg.Role {
	case "user":
		if c := msg.Content().String(); c != "" {
			if len(msg.BinaryContent()) > 0 {
				for _, b := range msg.BinaryContent() {
					c += fmt.Sprintf(" ![%s%s]()", styles.DocumentIcon, b.Path)
				}
			}
			t := theme.CurrentTheme()
			base := styles.BaseStyle().Width(m.width - 1)
			rendered := base.Foreground(t.Text()).PaddingLeft(0).Render(m.wrapText(c, m.width))

			lines := strings.Split(rendered, "\n")
			for i := range lines {
				if i == 0 {
					lines[i] = "> " + lines[i]
				} else {
					lines[i] = "  " + lines[i]
				}
			}
			parts = append(parts, strings.Join(lines, "\n"))
		}
	case "system":
		if c := msg.Content().Text; c != "" {
			rendered := m.toMarkdown(c, m.width-4)
			lines := strings.Split(rendered, "\n")
			for i := range lines {
				if i == 0 {
					lines[i] = "●" + lines[i]
				} else {
					lines[i] = " " + lines[i]
				}
			}
			parts = append(parts, strings.Join(lines, "\n"))
		}
	case "assistant":
		if c := msg.Content().String(); c != "" {
			rendered := m.toMarkdown(c, m.width-2)
			if rendered != "" {
				lines := strings.Split(rendered, "\n")
				for i := range lines {
					if i == 0 {
						lines[i] = "●" + lines[i]
					} else {
						lines[i] = " " + lines[i]
					}
				}
				parts = append(parts, strings.Join(lines, "\n"))
			}
		}
	default:
		if c := msg.Content().String(); c != "" {
			parts = append(parts, m.toMarkdown(c, m.width-1))
		}
	}

	// 工具调用单独处理，不在此处拼接
	return strings.Join(parts, "\n")
}

// extractMixedContent 将 mixed 拆分为 thinking/content 两段，分别添加标识与对齐
func (m *MessagesComponent) extractMixedContent(msg message.Message) (string, string) {
	var thinkingPart, contentPart string

	if thinking := msg.ReasoningContent().Thinking; thinking != "" {
		formatted := m.wrapText(thinking, m.width)
		lines := strings.Split(formatted, "\n")
		for i := range lines {
			if i == 0 {
				lines[i] = "◇ " + lines[i]
			} else {
				lines[i] = "  " + lines[i]
			}
		}
		thinkingPart = strings.Join(lines, "\n")
	}

	if content := msg.Content().String(); content != "" {
		rendered := m.toMarkdown(content, m.width-2)
		if rendered != "" {
			lines := strings.Split(rendered, "\n")
			for i := range lines {
				if i == 0 {
					lines[i] = "●" + lines[i]
				} else {
					lines[i] = " " + lines[i]
				}
			}
			contentPart = strings.Join(lines, "\n")
		}
	}
	return thinkingPart, contentPart
}

// renderLogo 顶部 Logo 与当前工作目录
func (m *MessagesComponent) renderLogo() string {
	//	logo := `   ____                __
	//  / __ \  ____    ____/ / ___    ____
	// / / / / / __ \  / __  / / _ \  / __/
	/// /_/ / / /_/ / /  __/ / /
	//\___\_\ \____/  \____/  \___/ /_/   `
	t := theme.CurrentTheme()

	// 分别渲染logo的不同部分，使"Qoder CLI"加粗
	welcomePart := styles.BaseStyle().PaddingBottom(1).Foreground(t.TextEmphasized()).Render("✦ Welcome to ")
	boldPart := styles.BaseStyle().PaddingBottom(1).Foreground(t.TextEmphasized()).Bold(true).Render("Qoder CLI")
	separatorPart := styles.BaseStyle().PaddingBottom(1).Foreground(t.TextEmphasized()).Render("!")
	// linePart := styles.BaseStyle().PaddingBottom(1).Foreground(t.TextEmphasized()).Render("--------------------------------------------------------")

	content := lipgloss.JoinVertical(
		lipgloss.Left,
		lipgloss.JoinHorizontal(lipgloss.Bottom,
			lipgloss.JoinHorizontal(lipgloss.Left, welcomePart, boldPart, separatorPart),
			styles.BaseStyle().PaddingBottom(1).Foreground(t.TextMuted()).Render(version.Version)),
		// linePart,
		styles.BaseStyle().Foreground(t.TextMuted()).Render("cwd: "+config.WorkingDirectory()))

	return content
}

// loadMessages 拉取并返回历史消息
func (m *MessagesComponent) loadMessages() tea.Cmd {
	return tea.Cmd(func() tea.Msg {
		msgs, err := m.app.ListMessages(context.Background(), m.session.Id)
		if err != nil {
			return nil
		}
		return messagestypes.MessagesLoadedMsg{Messages: msgs}
	})
}

// toMarkdown 渲染 markdown 文本
func (m *MessagesComponent) toMarkdown(content string, width int) string {
	r := styles.GetMarkdownRenderer(width)
	rendered, _ := r.Render(content)
	return rendered
}

// wrapText 对文本进行软换行，宽度为整体宽度-4（留出前缀对齐）
func (m *MessagesComponent) wrapText(content string, width int) string {
	maxLineWidth := width - 2
	if maxLineWidth < 20 {
		maxLineWidth = 20
	}
	return wordwrap.String(content, maxLineWidth)
}

// buildAssistantWithTools 组合父 assistant 的 thinking/content 与工具调用（含已到达结果）
func (m *MessagesComponent) buildAssistantWithTools(msg message.Message) string {
	var parts []string
	// 若父消息有静态内容，则按当前宽度一次性渲染加入预览
	base := m.preprocessMessage(msg)
	if base != nil && strings.TrimSpace(base.RenderedText) != "" {
		parts = append(parts, strings.TrimRight(base.RenderedText, "\n"))
	}

	toolCalls := m.parentToolCalls[msg.Id]
	if len(toolCalls) == 0 {
		toolCalls = msg.ToolCalls()
	}
	for _, tc := range toolCalls {
		var resPtr *message.ToolResult
		if res, ok := m.toolResults[tc.Id]; ok {
			resPtr = &res
		}
		rendered := m.toolRendererManager.Render(tc, resPtr, m.width-1)
		if strings.TrimSpace(rendered) != "" {
			parts = append(parts, rendered)
		}
	}
	return strings.Join(parts, "\n\n")
}

// renderWelcomeMessage 渲染欢迎信息内容
func (m *MessagesComponent) renderWelcomeMessage() string {
	content := lipgloss.JoinVertical(
		lipgloss.Left,
		styles.BaseStyle().
			Border(lipgloss.RoundedBorder(), true, true, true, true).
			BorderForeground(lipgloss.AdaptiveColor{
				Dark:  "#a0a0a0", // 暗色模式：更暗的白色
				Light: "#909090", // 亮色模式：更暗的白色
			}).
			Padding(0, 2, 0, 2).
			Render(m.renderLogo()),
		"",
		m.renderWelcomeTips())

	return content
}

// renderWelcomeTips 顶部提示信息
func (m *MessagesComponent) renderWelcomeTips() string {
	t := theme.CurrentTheme()
	base := styles.BaseStyle()

	// 固定提示
	tipsHeader := base.Foreground(t.Text()).PaddingLeft(1).Render("Tips for getting started:\n")
	tip1 := base.Foreground(t.Text()).PaddingLeft(1).Render("1. Ask questions, edit files, or run commands.")
	tip2 := base.Foreground(t.Text()).PaddingLeft(1).Render("2. Be specific for the best results.")
	helpTip := base.Foreground(t.Text()).PaddingLeft(1).Render("3. Type ") +
		base.Foreground(t.Primary()).Bold(true).Render("/help") +
		base.Foreground(t.Text()).Render(" for more information.")

	var tips []string
	tips = append(tips, tipsHeader, tip1, tip2, helpTip)
	return lipgloss.JoinVertical(lipgloss.Left, tips...)
}

// Height 返回组件当前高度
func (m *MessagesComponent) Height() int {
	return m.height
}

// View 渲染组件视图
func (m *MessagesComponent) View() string {
	var parts []string

	// 添加欢迎信息
	parts = append(parts, m.renderWelcomeMessage())

	// 按顺序渲染所有消息
	for _, msgID := range m.messageOrder {
		if rendered, ok := m.renderedMessages[msgID]; ok && rendered != nil {
			if strings.TrimSpace(rendered.RenderedText) != "" {
				parts = append(parts, rendered.RenderedText)
			}
		}
	}

	// 连接所有部分
	content := strings.Join(parts, "\n\n")

	// 计算总高度
	m.height = lipgloss.Height(content)
	if m.height == 0 {
		m.height = 1
	}

	return content
}

// ToggleExpansion 切换展开状态 (Normal ↔ Partial)
func (m *MessagesComponent) ToggleExpansion() tea.Cmd {
	switch m.expandMode {
	case messagestypes.ExpandModeNormal:
		m.expandMode = messagestypes.ExpandModePartial
		// 记录进入partial模式时的基准消息数量
		m.partialModeBaseCount = len(m.messageOrder)
		// 设置所有渲染器的展开模式
		m.setAllRenderersExpandedMode(true)
		// 重新渲染所有消息
		m.refreshAllMessages()
		return func() tea.Msg {
			return messagestypes.EnterExpandedModeMsg{Mode: messagestypes.ExpandModePartial}
		}
	default:
		m.expandMode = messagestypes.ExpandModeNormal
		// 重置基准计数
		m.partialModeBaseCount = 0
		// 设置所有渲染器的展开模式
		m.setAllRenderersExpandedMode(false)
		// 重新渲染所有消息
		m.refreshAllMessages()
		return func() tea.Msg {
			return messagestypes.ExitExpandedModeMsg{}
		}
	}
}

// IsExpanded 返回当前是否为展开状态
func (m *MessagesComponent) IsExpanded() bool {
	return m.expandMode != messagestypes.ExpandModeNormal
}

// ToggleFullExpansion 切换完全展开状态 (Partial ↔ Full)
func (m *MessagesComponent) ToggleFullExpansion() tea.Cmd {
	switch m.expandMode {
	case messagestypes.ExpandModePartial:
		m.expandMode = messagestypes.ExpandModeFull
		// 重新渲染所有消息
		m.refreshAllMessages()
		return func() tea.Msg {
			return messagestypes.ToggleFullExpansionMsg{}
		}
	case messagestypes.ExpandModeFull:
		m.expandMode = messagestypes.ExpandModePartial
		// 切换回partial模式时，重新设置基准计数
		m.partialModeBaseCount = len(m.messageOrder)
		// 重新渲染所有消息
		m.refreshAllMessages()
		return func() tea.Msg {
			return messagestypes.ToggleFullExpansionMsg{}
		}
	default:
		// 如果当前不在展开模式，不做任何操作
		return nil
	}
}

// setAllRenderersExpandedMode 设置所有渲染器的展开模式
func (m *MessagesComponent) setAllRenderersExpandedMode(expanded bool) {
	// 设置工具渲染器管理器的展开模式
	m.toolRendererManager.SetExpandedMode(expanded)
}

// getPartialModeMessages 获取部分模式下要显示的消息ID列表
func (m *MessagesComponent) getPartialModeMessages() []string {
	// 如果有基准计数，使用基准位置开始显示消息（包括新消息）
	if m.partialModeBaseCount > 0 {
		// 从基准计数的最近2条开始，显示到当前所有消息
		startIndex := m.partialModeBaseCount - 2
		if startIndex < 0 {
			startIndex = 0
		}
		// 确保不超出实际消息范围
		if startIndex >= len(m.messageOrder) {
			return m.messageOrder
		}
		return m.messageOrder[startIndex:]
	}

	// fallback：如果基准计数无效，使用传统逻辑
	if len(m.messageOrder) <= 2 {
		return m.messageOrder // 如果消息总数≤2，显示全部
	}
	// 返回最近2条消息的ID
	return m.messageOrder[len(m.messageOrder)-2:]
}

// generateCtrlEHint 生成Ctrl+E提示信息
func (m *MessagesComponent) generateCtrlEHint() string {
	currentCount := len(m.messageOrder)

	// 如果消息总数≤2，部分模式和完整模式显示内容相同，不需要提示
	if currentCount <= 2 {
		return ""
	}

	switch m.expandMode {
	case messagestypes.ExpandModePartial:
		// Partial模式：使用进入模式时的基准计数，保持提示信息稳定
		baseCount := m.partialModeBaseCount
		if baseCount <= 0 {
			baseCount = currentCount // fallback：如果基准计数无效，使用当前计数
		}
		hiddenCount := baseCount - 2
		if hiddenCount > 0 {
			return fmt.Sprintf("Ctrl+E to show %d previous messages", hiddenCount)
		}
		return ""
	case messagestypes.ExpandModeFull:
		// Full模式：使用当前实际消息数，动态显示将要隐藏的消息数
		hiddenCount := currentCount - 2
		if hiddenCount > 0 {
			return fmt.Sprintf("Ctrl+E to hide %d previous messages", hiddenCount)
		}
		return ""
	}
	return ""
}

// renderCtrlEHint 渲染Ctrl+E提示的样式，带分割线
func (m *MessagesComponent) renderCtrlEHint(hint string) string {
	t := theme.CurrentTheme()

	// 计算可用宽度
	availableWidth := m.width - 2 // 减去左右padding
	if availableWidth < 10 {
		availableWidth = 80 // 最小宽度
	}

	// 计算提示文本长度
	hintLength := len(hint)

	// 如果提示文本太长，使用简单样式
	if hintLength >= availableWidth-4 {
		style := lipgloss.NewStyle().
			Foreground(t.TextMuted()).
			Italic(true).
			PaddingLeft(1)
		return style.Render(hint)
	}

	// 计算左右分割线长度
	remainingWidth := availableWidth - hintLength - 2 // 减去提示文本和两个空格
	if remainingWidth < 2 {
		// 如果剩余宽度太小，回退到简单样式
		style := lipgloss.NewStyle().
			Foreground(t.TextMuted()).
			Italic(true).
			PaddingLeft(1)
		return style.Render(hint)
	}

	leftWidth := remainingWidth / 2
	rightWidth := remainingWidth - leftWidth

	// 生成分割线
	leftDivider := strings.Repeat("─", leftWidth)
	rightDivider := strings.Repeat("─", rightWidth)

	// 组合：左分割线 + 空格 + 提示文本 + 空格 + 右分割线
	dividerContent := leftDivider + " " + hint + " " + rightDivider

	// 应用样式
	style := lipgloss.NewStyle().
		Foreground(t.TextMuted()).
		Italic(true).
		PaddingLeft(1)

	return style.Render(dividerContent)
}

// ViewExpanded 展开模式下的视图渲染
func (m *MessagesComponent) ViewExpanded() string {
	var parts []string

	// 1. 添加Welcome消息
	parts = append(parts, m.renderWelcomeMessage())

	// 2. 根据展开模式添加Ctrl+E提示
	if hint := m.generateCtrlEHint(); hint != "" {
		parts = append(parts, m.renderCtrlEHint(hint))
	}

	// 3. 根据模式选择要显示的消息
	var messageIDs []string
	switch m.expandMode {
	case messagestypes.ExpandModePartial:
		messageIDs = m.getPartialModeMessages()
	case messagestypes.ExpandModeFull:
		messageIDs = m.messageOrder
	default:
		messageIDs = m.messageOrder // fallback to all messages
	}

	// 4. 渲染选定的消息
	for _, msgID := range messageIDs {
		if rendered, ok := m.renderedMessages[msgID]; ok && rendered != nil {
			if strings.TrimSpace(rendered.RenderedText) != "" {
				parts = append(parts, rendered.RenderedText)
			}
		}
	}

	// 连接所有部分
	content := strings.Join(parts, "\n\n")

	// 计算总高度
	m.height = lipgloss.Height(content)
	if m.height == 0 {
		m.height = 1
	}

	return content
}

package chat

import (
	"context"
	"fmt"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"
	"github.com/qoder-ai/qodercli/tui/components/types/hint"
	messagestypes "github.com/qoder-ai/qodercli/tui/components/types/messages"
	"github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
)

// HintComponent 提供快捷键提示功能
type HintComponent struct {
	width               int
	height              int
	state               hint.HintState
	content             string
	focused             bool
	autoApproveOn       bool
	isEditorEmpty       bool
	isAttachmentsDelete bool
	runningBashCount    int
	app                 runtime.AppRuntime
}

// hintContent 状态内容映射
var hintContent = map[hint.HintState]string{
	hint.HintStateDefault: "? for shortcuts, ctrl+v to paste screenshot or files",
	hint.HintStateExpanded: "" +
		"!      for bash mode      # to memorize                   \\⏎ for newline\n" +
		"/ or + for commands       double tap esc to clear         ctrl+f to add attachments\n" +
		"@      for file paths     shift + tab to auto-accept      ctrl+d to delete attachments\n",
	hint.HintStateMemory:           "# for memorize",
	hint.HintQuitTip:               "Press ctrl+c again to quit",
	hint.HintEscTip:                "Press esc again to clear",
	hint.HintStateBash:             "! for bash mode",
	hint.HintStateExpandedMessages: "Showing detailed transcript · Ctrl+R to toggle",
}

// getHeightForState 根据状态返回对应高度
func getHeightForState(state hint.HintState) int {
	switch state {
	case hint.HintStateDefault:
		return 1 // 默认状态：1行
	case hint.HintQuitTip:
		return 1 // 退出tip：1行
	case hint.HintStateExpanded:
		return 3 // 展开状态：3行
	case hint.HintStateExpandedMessages:
		return 2 // 展开消息模式：2行（分割线+提示信息）
	default:
		return 1
	}
}

// NewHintComponent 创建新的提示组件
func NewHintComponent(app runtime.AppRuntime) *HintComponent {
	return &HintComponent{
		state:            hint.HintStateDefault,
		content:          hintContent[hint.HintStateDefault],
		height:           getHeightForState(hint.HintStateDefault), // 初始高度为1行
		autoApproveOn:    app.IsAutoApproveAll(),
		isEditorEmpty:    true,
		runningBashCount: 0,
		app:              app,
	}
}

// Init 初始化组件
func (h *HintComponent) Init() tea.Cmd {
	// 获取初始运行中的bash数量并启动shell事件监听
	return tea.Batch(
		func() tea.Msg {
			count := h.countRunningBashes()
			return hint.RunningBashCountChangedMsg{Count: count}
		},
		h.waitForShellEvent(),
	)
}

// Update 更新组件状态
func (h *HintComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		h.width = msg.Width
		h.height = getHeightForState(h.state)
		return h, nil

	case hint.SwitchStateMsg:
		h.setState(msg.State)
		return h, nil

	case hint.SwitchEditorStatusMsg:
		h.isEditorEmpty = msg.Empty
		h.isAttachmentsDelete = msg.AttachmentDelete
		h.setState(h.state)
		return h, nil

	case editortypes.MemoryModeChangedMsg:
		// 记忆模式触发时，文案改为 "# to memorize"；退出时恢复默认
		if msg.Active {
			h.state = hint.HintStateMemory
			h.content = h.getContent(hint.HintStateMemory)
			h.height = getHeightForState(h.state)
			return h, nil
		}
		// 退出记忆模式，恢复默认提示
		h.state = hint.HintStateDefault
		h.content = h.getContent(hint.HintStateDefault)
		h.height = getHeightForState(h.state)
		return h, nil
	case editortypes.BashModeChangedMsg:
		// 记忆模式触发时，文案改为 "! to bash mode"；退出时恢复默认
		if msg.Active {
			h.state = hint.HintStateBash
			h.content = h.getContent(hint.HintStateBash)
			h.height = getHeightForState(h.state)
			return h, nil
		}
		// 退出记忆模式，恢复默认提示
		h.state = hint.HintStateDefault
		h.content = h.getContent(hint.HintStateDefault)
		h.height = getHeightForState(h.state)
		return h, nil
	case messagestypes.EnterExpandedModeMsg:
		// 进入展开消息模式
		h.state = hint.HintStateExpandedMessages
		h.content = h.getContent(hint.HintStateExpandedMessages)
		h.height = getHeightForState(h.state)
		return h, nil

	case messagestypes.ExitExpandedModeMsg:
		// 退出展开消息模式，恢复默认状态
		h.state = hint.HintStateDefault
		h.content = h.getContent(hint.HintStateDefault)
		h.height = getHeightForState(h.state)
		return h, nil

	case editortypes.AutoApproveChangedMsg:
		h.autoApproveOn = msg.Enabled
		// 如果处于默认状态，则更新提示内容
		if h.state == hint.HintStateDefault {
			h.setState(hint.HintStateDefault)
		}
		return h, nil

	case hint.RunningBashCountChangedMsg:
		h.runningBashCount = msg.Count
		// 如果处于默认状态，更新提示内容以反映bash计数变化
		if h.state == hint.HintStateDefault {
			h.setState(hint.HintStateDefault)
		}
		return h, nil

	case hint.ShellEventReceivedMsg:
		// 收到shell事件，重新计算bash数量
		newCount := h.countRunningBashes()
		var cmd tea.Cmd
		if newCount != h.runningBashCount {
			cmd = func() tea.Msg {
				return hint.RunningBashCountChangedMsg{Count: newCount}
			}
		}
		// 继续监听下一个shell事件
		return h, tea.Batch(cmd, h.waitForShellEvent())
	}

	return h, nil
}

// View 渲染组件视图
func (h *HintComponent) View() string {
	if h.width == 0 {
		return ""
	}
	base := styles.BaseStyle().
		Padding(0, 1).
		Width(h.width).
		Height(h.height)
	t := theme.CurrentTheme()

	// 默认提示样式（灰色）
	return base.Foreground(t.BaseGray()).Render(h.content)
}

// SetSize 设置组件尺寸
func (h *HintComponent) SetSize(width, height int) tea.Cmd {
	h.width = width
	h.height = height
	return nil
}

// GetSize 获取组件尺寸
func (h *HintComponent) GetSize() (int, int) {
	return h.width, h.height
}

// IsFocused 检查组件是否获得焦点
func (h *HintComponent) IsFocused() bool {
	return h.focused
}

// Focus 设置组件焦点
func (h *HintComponent) Focus() tea.Cmd {
	h.focused = true
	return nil
}

// Blur 取消组件焦点
func (h *HintComponent) Blur() tea.Cmd {
	h.focused = false
	return nil
}

// setState 设置新状态并更新内容和高度
func (h *HintComponent) setState(newState hint.HintState) tea.Cmd {
	h.state = newState
	h.height = getHeightForState(newState)
	h.content = h.getContent(newState)
	return nil
}

// GetState 获取当前状态
func (h *HintComponent) GetState() hint.HintState {
	return h.state
}

func (h *HintComponent) getContent(state hint.HintState) string {
	base := styles.BaseStyle().
		Padding(0, 1).
		Width(h.width).
		Height(h.height)
	t := theme.CurrentTheme()

	if state == hint.HintStateDefault {
		if h.isAttachmentsDelete {
			return "tips: press index number to delete attachment, 'r' for all"
		}
		if h.autoApproveOn {
			head := styles.BaseStyle().Foreground(t.BaseRed()).Render("bypass permissions on ")
			tail := styles.BaseStyle().Foreground(t.TextMuted()).Render("(shift+tab to cycle)")
			return base.Render(head + tail)
		}
		if !h.isEditorEmpty {
			return ""
		}

		// 构建默认提示文本，包含bash计数
		baseText := "? for shortcuts, ctrl+v to paste screenshot or files"
		if h.runningBashCount > 0 {

			bashCountText := fmt.Sprintf("%d bashes running, enter /bashes to view shells", h.runningBashCount)
			if h.runningBashCount == 1 {
				bashCountText = "1 bash running, /bashes to view shells"
			}
			return fmt.Sprintf("%s · %s", styles.BaseStyle().Foreground(t.BaseBlue()).Render(bashCountText), baseText)
		}
		return baseText
	}
	if state == hint.HintStateMemory {
		return base.Foreground(t.Primary()).Render(hintContent[state])
	}
	if h.state == hint.HintStateBash {
		return base.Foreground(t.SyntaxOperator()).Render(hintContent[state])
	}
	if h.state == hint.HintStateExpandedMessages {
		// 展开消息模式：分割线 + 提示信息
		divider := strings.Repeat("─", h.width-2) // 减去padding
		return base.Foreground(t.TextMuted()).Render(divider + "\n" + hintContent[state])
	}

	if content, ok := hintContent[state]; ok {
		return content
	}

	return hintContent[hint.HintStateDefault]
}

// countRunningBashes 统计当前正在运行的bash数量
func (h *HintComponent) countRunningBashes() int {
	shells := h.app.ListShells()
	count := 0
	for _, shell := range shells {
		if shell.IsExecuting() {
			count++
		}
	}
	return count
}

// waitForShellEvent 等待shell事件
func (h *HintComponent) waitForShellEvent() tea.Cmd {
	return func() tea.Msg {
		ctx := context.Background()
		eventChan := h.app.SubscribeShell(ctx)

		// 等待下一个shell事件
		select {
		case <-eventChan:
			return hint.ShellEventReceivedMsg{}
		case <-ctx.Done():
			return nil
		}
	}
}

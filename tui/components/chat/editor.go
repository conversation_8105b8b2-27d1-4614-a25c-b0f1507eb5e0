package chat

import (
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/charmbracelet/lipgloss"
	hinttypes "github.com/qoder-ai/qodercli/tui/components/types/hint"

	"github.com/qoder-ai/qodercli/tui/clipboard"

	"github.com/charmbracelet/bubbles/key"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/tui/components/chat/textarea"

	editorhandler "github.com/qoder-ai/qodercli/tui/components/handler/editor"
	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"
	messagestypes "github.com/qoder-ai/qodercli/tui/components/types/messages"
	"github.com/qoder-ai/qodercli/tui/components/utils"

	"github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
	"github.com/qoder-ai/qodercli/tui/util"
)

var (
	atPattern = regexp.MustCompile(`(^|\s)@([\w./~-]*)$`)
)

// EditorKeyMaps 编辑器快捷键映射
type EditorKeyMaps struct {
	Send       key.Binding
	PasteImage key.Binding
	Escape     key.Binding
	Up         key.Binding
	Down       key.Binding

	AttachmentDeleteMode key.Binding
	DeleteAllAttachments key.Binding

	SwitchApproveMode   key.Binding
	ClearPendingMessage key.Binding
}

// 编辑器按键绑定
var editorMaps = EditorKeyMaps{
	Send: key.NewBinding(
		key.WithKeys("enter"),
		key.WithHelp("enter", "send message"),
	),
	PasteImage: key.NewBinding(
		key.WithKeys("ctrl+v"),
		key.WithHelp("ctrl+v", "paste image"),
	),
	Escape: key.NewBinding(
		key.WithKeys("esc"),
		key.WithHelp("esc", "cancel"),
	),
	Up: key.NewBinding(
		key.WithKeys("up"),
		key.WithHelp("up", "up"),
	),
	Down: key.NewBinding(
		key.WithKeys("down"),
		key.WithHelp("down", "down"),
	),
	AttachmentDeleteMode: key.NewBinding(
		key.WithKeys("ctrl+d"),
		key.WithHelp("ctrl+d+{i}", "delete specified attachment"),
	),
	DeleteAllAttachments: key.NewBinding(
		key.WithKeys("r"),
		key.WithHelp("ctrl+d+r", "delete all attachments"),
	),
	SwitchApproveMode: key.NewBinding(
		key.WithKeys("shift+tab"),
		key.WithHelp("shift+tab", "switch permission approve mode"),
	),
	ClearPendingMessage: key.NewBinding(
		key.WithKeys("ctrl+q"),
		key.WithHelp("ctrl+q", "clear pending message"),
	),
}

// EditorComponent 编辑器组件 - 使用handler架构处理复杂逻辑
type EditorComponent struct {
	// 基本属性
	initPrompt string
	app        runtime.AppRuntime
	session    core.Session
	textarea   textarea.Model
	hint       *HintComponent

	// 状态
	state       *editortypes.EditorState
	attachments []message.Attachment

	// 历史记录
	history []editortypes.HistoryEntry
	histIdx int

	// 当前模式
	currentMode editortypes.EditorMode

	historyHandler        editortypes.HistoryHandler
	attachmentHandler     editortypes.AttachmentHandler
	externalHandler       editortypes.ExternalEditorHandler
	pendingMessageHandler editortypes.PendingMessageHandler
}

// NewEditorComponent 创建新的编辑器组件
func NewEditorComponent(app runtime.AppRuntime, hint *HintComponent, initPrompt string, session core.Session) *EditorComponent {
	ta := createTextArea(nil)

	state := &editortypes.EditorState{
		Width:          80,
		Height:         1,
		Focused:        true,
		DeleteMode:     false,
		MemoryMode:     false,
		BashMode:       false,
		EscCount:       0,
		EscLastTime:    time.Time{},
		PendingMessage: nil,
	}

	e := &EditorComponent{
		initPrompt:  initPrompt,
		app:         app,
		textarea:    ta,
		hint:        hint,
		session:     session,
		state:       state,
		attachments: make([]message.Attachment, 0),
		currentMode: editortypes.ModeNormal,

		historyHandler:        editorhandler.NewHistoryHandler(),
		attachmentHandler:     editorhandler.NewAttachmentHandler(),
		externalHandler:       editorhandler.NewExternalEditorHandler(),
		pendingMessageHandler: editorhandler.NewPendingMessageHandler(),
	}

	return e
}

// Init 初始化组件
func (e *EditorComponent) Init() tea.Cmd {
	e.state.Focused = true
	if e.initPrompt != "" {
		e.textarea.SetValue(e.initPrompt)
		// 携带initPrompt启动时，自动发送
		if e.session.Id == "" {
			return e.send()
		}
	}
	// 加载历史
	e.history = e.historyHandler.LoadHistory(e.app)
	e.histIdx = len(e.history)
	return textarea.Blink
}

// Update 处理外部事件
func (e *EditorComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		e.state.Width = msg.Width
		e.textarea.SetWidth(msg.Width - 2)
		return e, nil

	case messagestypes.SessionChangedMsg:
		e.session = msg.Session
		return e, nil

	case utils.AtCompletionMsg:
		existingValue := e.textarea.Value()
		modifiedValue := e.processCompletionSelection(existingValue, msg.SearchString, msg.CompletionValue)
		e.textarea.SetValue(modifiedValue)
		return e, nil

	case utils.InputCommandToEditorMsg:
		e.textarea.SetValue(msg.Command.ID + " ")
		return e, nil

	case editortypes.TriggerEditorSendMsg:
		return e, e.send()

	case editortypes.ExitMemoryModeAndClearMsg:
		e.textarea.Reset()
		e.attachments = nil
		return e, nil

	case utils.MemoryTargetSelectedMsg:
		return e, e.handleMemoryTargetSelected(msg)

	case editortypes.AttachmentAddedMsg:
		return e, e.handleAttachmentAdded(msg)

	case editortypes.ProcessPendingMsg:
		return e, e.processPending()

	case tea.KeyMsg:
		return e, e.handleKeyMsg(msg)

	case utils.AttachmentAddedMsg:
		if len(e.attachments) >= editortypes.MaxAttachments {
			return e, cmd
		}
		// 去重：若同路径已存在，则忽略
		duplicate := false
		for i := range e.attachments {
			if e.attachments[i].FilePath == msg.Attachment.FilePath && msg.Attachment.FilePath != "" {
				duplicate = true
				break
			}
		}
		if !duplicate {
			e.attachments = append(e.attachments, msg.Attachment)
		}
	}

	return e, cmd
}

// handleMemoryTargetSelected 处理记忆目标选择
func (e *EditorComponent) handleMemoryTargetSelected(msg utils.MemoryTargetSelectedMsg) tea.Cmd {
	content := strings.TrimSpace(msg.Content)
	success := content != ""
	var location string

	if success {
		err := e.app.AppendMemory(msg.Location, content)
		if err == nil {
			location = string(msg.Location)
		} else {
			success = false
		}
	}

	return tea.Batch(
		util.CmdHandler(messagestypes.MemoryOperationCompleteMsg{
			Content:    content,
			Location:   location,
			Success:    success,
			InputMsgID: msg.InputMsgID,
		}),
		util.CmdHandler(editortypes.ExitMemoryModeAndClearMsg{}),
	)
}

// handleAttachmentAdded 处理附件添加
func (e *EditorComponent) handleAttachmentAdded(msg editortypes.AttachmentAddedMsg) tea.Cmd {
	newAttachments, err := e.attachmentHandler.AddAttachment(msg.Attachment, e.attachments)
	if err != nil {
		return util.ReportError(err)
	}
	e.attachments = newAttachments
	return nil
}

// handleKeyMsg 处理按键消息
func (e *EditorComponent) handleKeyMsg(msg tea.KeyMsg) tea.Cmd {
	var cmds []tea.Cmd

	// 历史浏览：光标在首/末行时生效
	if key.Matches(msg, editorMaps.Up) || key.Matches(msg, editorMaps.Down) {
		if cmd := e.handleHistoryNavigation(msg.String()); cmd != nil {
			return cmd
		}
	}

	// Shift+Tab 切换全局自动授权
	if key.Matches(msg, editorMaps.SwitchApproveMode) {
		return e.handleAutoApproveToggle()
	}

	// 粘贴图片
	if key.Matches(msg, editorMaps.PasteImage) {
		return e.handlePasteImage()
	}

	// 处理ESC按键
	if key.Matches(msg, editorMaps.Escape) {
		escCmds := e.handleEscape()
		return tea.Batch(escCmds...)
	} else {
		// 按其他键时重置ESC计数
		e.resetEscState()
	}

	// 清空待发送消息
	if key.Matches(msg, editorMaps.ClearPendingMessage) && e.state.PendingMessage != nil {
		return e.clearPending()
	}

	// 记录操作前的内容状态
	oldContent := e.textarea.Value()

	// 特殊处理：在空编辑器中按 ? 直接触发hint展开
	if oldContent == "" && msg.String() == "?" {
		return e.handleQuestionMark()
	}

	// 检查内容为空时的回退操作
	if oldContent == "" {
		if cmd := e.handleEmptyContentKeys(msg.String()); cmd != nil {
			return cmd
		}
	}

	// 处理删除附件相关按键
	if handled, cmd := e.handleAttachmentDeleteKeys(msg); handled {
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		return tea.Batch(cmds...) // 按键被处理，不继续传递给textarea
	}

	// 处理发送按键
	if e.textarea.Focused() && key.Matches(msg, editorMaps.Send) {
		return e.handleSendKey()
	}

	// 更新textarea
	var textareaCmd tea.Cmd
	e.textarea, textareaCmd = e.textarea.Update(msg)
	cmds = append(cmds, textareaCmd)

	// 检查内容是否发生变化
	newContent := e.textarea.Value()
	if oldContent != newContent {
		contentCmds := e.handleContentChange(oldContent, newContent)
		cmds = append(cmds, contentCmds...)
	}

	return tea.Batch(cmds...)
}

// handleHistoryNavigation 处理历史浏览
func (e *EditorComponent) handleHistoryNavigation(direction string) tea.Cmd {
	var histDirection editortypes.HistoryDirection
	if direction == "up" && e.textarea.Line() == 0 {
		histDirection = editortypes.HistoryUp
	} else if direction == "down" && e.textarea.Line() == e.textarea.LineCount()-1 {
		histDirection = editortypes.HistoryDown
	} else {
		return nil // 不处理
	}

	newIndex, cmds := e.historyHandler.HandleHistoryNavigation(histDirection, e.histIdx, len(e.history), &e.textarea)

	if newIndex != e.histIdx {
		e.histIdx = newIndex
		if newIndex < len(e.history) {
			// 应用历史记录条目
			entry := e.history[newIndex]
			mode, applyCmds := e.historyHandler.ApplyHistoryEntry(entry, &e.textarea)
			e.currentMode = mode
			e.updateModeState(mode)

			// 恢复附件
			if histHandler, ok := e.historyHandler.(*editorhandler.HistoryHandlerImpl); ok {
				e.attachments = histHandler.RestoreAttachmentsFromHistory(entry)
			}

			cmds = append(cmds, applyCmds...)
		} else {
			// 清空状态
			e.attachments = nil
			e.currentMode = editortypes.ModeNormal
			e.updateModeState(editortypes.ModeNormal)
			cmds = append(cmds, util.CmdHandler(hinttypes.SwitchEditorStatusMsg{Empty: true}))
		}
	}

	return tea.Batch(cmds...)
}

// handleAutoApproveToggle 处理自动批准切换
func (e *EditorComponent) handleAutoApproveToggle() tea.Cmd {
	enabled := e.app.ToggleAutoApproveAll()
	return util.CmdHandler(editortypes.AutoApproveChangedMsg{Enabled: enabled})
}

// handlePasteImage 处理粘贴图片
func (e *EditorComponent) handlePasteImage() tea.Cmd {
	attachments, err := clipboard.GetAttachmentsFromClipboard()
	if err != nil {
		return util.ReportError(err)
	}

	cmds := make([]tea.Cmd, len(attachments))
	for i := range attachments {
		cmds[i] = util.CmdHandler(editortypes.AttachmentAddedMsg{Attachment: attachments[i]})
	}

	return tea.Batch(cmds...)
}

// handleEscape 处理ESC键
func (e *EditorComponent) handleEscape() []tea.Cmd {
	var cmds []tea.Cmd

	if !e.state.Focused {
		return nil
	}

	// Agent Busy时取消运行
	if e.session.Id != "" {
		agent := e.app.GetCoderAgent()
		if agentImpl, ok := agent.(interface {
			IsSessionBusy(string) bool
			Cancel(string)
		}); ok && agentImpl.IsSessionBusy(e.session.Id) {
			agentImpl.Cancel(e.session.Id)
			return nil
		}
	}

	// 双击ESC，清空textarea内容
	currentTime := time.Now()
	if !e.state.EscLastTime.IsZero() && currentTime.Sub(e.state.EscLastTime) > 500*time.Millisecond {
		e.state.EscCount = 0
	}
	e.state.EscCount++
	e.state.EscLastTime = currentTime

	if e.state.EscCount >= 2 {
		e.textarea.SetValue("")
		e.state.EscCount = 0
		e.state.EscLastTime = time.Time{} // 重置时间
		cmds = append(cmds, util.CmdHandler(hinttypes.SwitchEditorStatusMsg{
			Empty: true,
		}))
		// 发送内容变化消息
		switch e.currentMode {
		case editortypes.ModeMemory:
			cmds = append(cmds, util.CmdHandler(hinttypes.SwitchStateMsg{
				State: hinttypes.HintStateMemory,
			}))
		case editortypes.ModeBash:
			cmds = append(cmds, util.CmdHandler(hinttypes.SwitchStateMsg{
				State: hinttypes.HintStateBash,
			}))
		default:
			cmds = append(cmds, util.CmdHandler(hinttypes.SwitchStateMsg{
				State: hinttypes.HintStateDefault,
			}))
		}
		return cmds
	}

	if e.textarea.Value() == "" {
		// 内容为空时，esc退出特殊模式
		cmds = append(cmds, e.quitSpecialMode())
	} else {
		// 内容不为空时，显示esc hint提示
		switch e.currentMode {
		case editortypes.ModeMemory:
			cmds = append(cmds, tea.Sequence(
				func() tea.Msg { return hinttypes.SwitchStateMsg{State: hinttypes.HintEscTip} },
				tea.Tick(500*time.Millisecond, func(t time.Time) tea.Msg { return hinttypes.SwitchStateMsg{State: hinttypes.HintStateMemory} })))
		case editortypes.ModeBash:
			cmds = append(cmds, tea.Sequence(
				func() tea.Msg { return hinttypes.SwitchStateMsg{State: hinttypes.HintEscTip} },
				tea.Tick(500*time.Millisecond, func(t time.Time) tea.Msg { return hinttypes.SwitchStateMsg{State: hinttypes.HintStateBash} })))
		default:
			cmds = append(cmds, tea.Sequence(
				func() tea.Msg { return hinttypes.SwitchStateMsg{State: hinttypes.HintEscTip} },
				tea.Tick(500*time.Millisecond, func(t time.Time) tea.Msg { return hinttypes.SwitchStateMsg{State: hinttypes.HintStateDefault} })))
		}
	}

	// 单击ESC，退出删除模式
	e.state.DeleteMode = false
	return cmds
}

// resetEscState 重置ESC状态
func (e *EditorComponent) resetEscState() {
	e.state.EscCount = 0
	e.state.EscLastTime = time.Time{}
}

// handleQuestionMark 处理问号触发
func (e *EditorComponent) handleQuestionMark() tea.Cmd {
	return util.CmdHandler(hinttypes.SwitchStateMsg{
		State: hinttypes.HintStateExpanded,
	})
}

// handleEmptyContentKeys 处理内容为空时的按键
func (e *EditorComponent) handleEmptyContentKeys(keyStr string) tea.Cmd {
	switch keyStr {
	case "backspace", "delete":
		// 内容为空时回退，退出特殊模式
		return e.quitSpecialMode()
	}
	return nil
}

// 退出特殊模式
func (e *EditorComponent) quitSpecialMode() tea.Cmd {
	e.currentMode = editortypes.ModeNormal
	e.updateModeState(editortypes.ModeNormal)

	return tea.Sequence(
		util.CmdHandler(hinttypes.SwitchEditorStatusMsg{
			Empty: true,
		}),
		util.CmdHandler(hinttypes.SwitchStateMsg{
			State: hinttypes.HintStateDefault,
		}),
		util.CmdHandler(editortypes.MemoryModeChangedMsg{Active: false}),
		util.CmdHandler(editortypes.BashModeChangedMsg{Active: false}),
	)
}

// handleAttachmentDeleteKeys 处理附件删除按键
func (e *EditorComponent) handleAttachmentDeleteKeys(msg tea.KeyMsg) (bool, tea.Cmd) {
	// ctrl+d 进入/退出删除模式
	if key.Matches(msg, editorMaps.AttachmentDeleteMode) && len(e.attachments) > 0 {
		var cmd tea.Cmd
		if e.state.DeleteMode {
			cmd = util.CmdHandler(hinttypes.SwitchEditorStatusMsg{
				Empty:            e.textarea.Value() == "",
				AttachmentDelete: false,
			})
		} else {
			cmd = util.CmdHandler(hinttypes.SwitchEditorStatusMsg{
				Empty:            e.textarea.Value() == "",
				AttachmentDelete: true,
			})
		}
		e.state.DeleteMode = !e.state.DeleteMode
		return true, cmd // 返回true表示按键被处理，不应传递给textarea
	}

	// 在删除模式下处理按键
	if e.state.DeleteMode {
		// 处理r键：删除所有附件
		if key.Matches(msg, editorMaps.DeleteAllAttachments) {
			e.state.DeleteMode = false
			e.attachments = e.attachmentHandler.DeleteAllAttachments()
			return true, util.CmdHandler(hinttypes.SwitchEditorStatusMsg{
				Empty:            e.textarea.Value() == "",
				AttachmentDelete: false,
			})
		}

		// 处理数字键：删除指定索引的附件
		if len(msg.Runes) > 0 && unicode.IsDigit(msg.Runes[0]) {
			e.state.DeleteMode = false
			e.attachments = e.attachmentHandler.HandleAttachmentDelete(msg.Runes[0], e.attachments)
			return true, util.CmdHandler(hinttypes.SwitchEditorStatusMsg{
				Empty:            e.textarea.Value() == "",
				AttachmentDelete: false,
			})
		}

		// 在删除模式下，除了ESC键外，其他所有按键都应该被拦截，不传递给textarea
		if !key.Matches(msg, editorMaps.Escape) {
			return true, nil
		}
	}

	return false, nil // 返回false表示按键未被处理，可以传递给textarea
}

// handleSendKey 处理发送按键
func (e *EditorComponent) handleSendKey() tea.Cmd {
	value := e.textarea.Value()

	// 如果内容为空或只有空白字符，什么都不做
	if strings.TrimSpace(value) == "" {
		return nil
	}

	// 如果最后一个字符是反斜杠，移除它并添加换行
	if len(value) > 0 && value[len(value)-1] == '\\' {
		e.textarea.SetValue(value[:len(value)-1] + "\n")
		return nil
	}

	// 根据模式处理发送
	switch e.currentMode {
	case editortypes.ModeMemory:
		// 检查是否忙碌
		if e.session.Id != "" {
			agent := e.app.GetCoderAgent()
			if agentImpl, ok := agent.(interface {
				IsSessionBusy(string) bool
			}); ok && agentImpl.IsSessionBusy(e.session.Id) {
				return nil
			}
		}
		content := e.textarea.Value()
		e.textarea.Reset()
		e.historyHandler.SaveToHistory(content, e.attachments, e.currentMode, e.app)
		e.history = e.historyHandler.LoadHistory(e.app)
		e.histIdx = len(e.history)
		return util.CmdHandler(messagestypes.MemoryOperationStartMsg{Content: content})

	case editortypes.ModeBash:
		// 检查是否忙碌
		if e.session.Id != "" {
			agent := e.app.GetCoderAgent()
			if agentImpl, ok := agent.(interface {
				IsSessionBusy(string) bool
			}); ok && agentImpl.IsSessionBusy(e.session.Id) {
				return nil
			}
		}
		content := e.textarea.Value()
		e.textarea.Reset()
		e.historyHandler.SaveToHistory(content, e.attachments, e.currentMode, e.app)
		e.history = e.historyHandler.LoadHistory(e.app)
		e.histIdx = len(e.history)
		return util.CmdHandler(messagestypes.BashOperationStartMsg{Command: content})

	default:
		return e.send()
	}
}

// handleContentChange 处理内容变化
func (e *EditorComponent) handleContentChange(oldContent, newContent string) []tea.Cmd {
	var cmds []tea.Cmd

	// 发送内容变化消息
	if e.currentMode == editortypes.ModeNormal {
		if newContent == "" {
			cmds = append(cmds, util.CmdHandler(hinttypes.SwitchEditorStatusMsg{Empty: true}))
		} else {
			cmds = append(cmds, util.CmdHandler(hinttypes.SwitchEditorStatusMsg{Empty: false}))
		}
	}

	// 检查模式切换
	newMode, processedContent, modeCmds := e.handleModeSwitch(newContent, e.currentMode)
	if newMode != e.currentMode {
		e.currentMode = newMode
		e.updateModeState(newMode)
		e.textarea.SetValue(processedContent)
		cmds = append(cmds, modeCmds...)
	}

	// 处理自动完成
	atCmd := e.handleAtCompletion(processedContent, e.state.BashMode)
	slashCmd := e.handleCommandCompletion(processedContent, e.state.MemoryMode, e.state.BashMode)
	cmds = append(cmds, atCmd, slashCmd)

	return cmds
}

// handleModeSwitch 处理模式切换
func (e *EditorComponent) handleModeSwitch(content string, currentMode editortypes.EditorMode) (editortypes.EditorMode, string, []tea.Cmd) {
	// 只有在以下情况才进行模式检测：
	// 1. 内容以!或#开头（新的模式切换）
	// 2. 内容为空（退出模式）
	// 3. 当前不在任何特殊模式中（ModeNormal）
	shouldDetectMode := false

	if len(content) > 0 {
		if currentMode == editortypes.ModeNormal {
			// 当前在普通模式，检查是否要进入特殊模式
			shouldDetectMode = (content[0] == '!' || content[0] == '#')
		} else {
			// 当前在特殊模式中，只有明确输入新的模式切换字符才切换
			shouldDetectMode = (content[0] == '!' || content[0] == '#')
		}
	}

	if !shouldDetectMode {
		return currentMode, content, nil
	}

	newMode := e.detectMode(content)
	if newMode == currentMode {
		return currentMode, content, nil
	}

	newContent, cmds := e.handleModeActivation(newMode, content)
	return newMode, newContent, cmds
}

// detectMode 检测内容应该使用的模式
func (e *EditorComponent) detectMode(content string) editortypes.EditorMode {
	if len(content) == 0 {
		return editortypes.ModeNormal
	}

	switch content[0] {
	case '#':
		return editortypes.ModeMemory
	case '!':
		return editortypes.ModeBash
	default:
		return editortypes.ModeNormal
	}
}

// handleModeActivation 处理模式激活
func (e *EditorComponent) handleModeActivation(mode editortypes.EditorMode, content string) (string, []tea.Cmd) {
	var cmds []tea.Cmd
	newContent := content

	switch mode {
	case editortypes.ModeMemory:
		if len(content) > 0 && content[0] == '#' {
			// 去除开头的#符号
			newContent = strings.TrimPrefix(content, "#")
			cmds = append(cmds,
				util.CmdHandler(editortypes.MemoryModeChangedMsg{Active: true}),
			)
		}
	case editortypes.ModeBash:
		if len(content) > 0 && content[0] == '!' {
			// 去除开头的!符号
			newContent = strings.TrimPrefix(content, "!")
			cmds = append(cmds,
				util.CmdHandler(editortypes.BashModeChangedMsg{Active: true}),
			)
		}
	}

	return newContent, cmds
}

// updateModeState 更新模式状态
func (e *EditorComponent) updateModeState(mode editortypes.EditorMode) {
	e.state.MemoryMode = (mode == editortypes.ModeMemory)
	e.state.BashMode = (mode == editortypes.ModeBash)
}

// handleAtCompletion 处理@符号自动完成
func (e *EditorComponent) handleAtCompletion(content string, inBashMode bool) tea.Cmd {
	if inBashMode {
		return util.CmdHandler(utils.AtQueryChangedMsg{Active: false})
	}

	matches := atPattern.FindStringSubmatch(content)
	if len(matches) == 3 {
		return util.CmdHandler(utils.AtQueryChangedMsg{Active: true, Query: matches[2]})
	}

	return util.CmdHandler(utils.AtQueryChangedMsg{Active: false})
}

// handleCommandCompletion 处理命令自动补全，支持/和+两种命令
func (e *EditorComponent) handleCommandCompletion(content string, inMemoryMode, inBashMode bool) tea.Cmd {
	if inMemoryMode || inBashMode {
		return util.CmdHandler(utils.CommandQueryChangedMsg{Active: false})
	}

	// 如果已经在@自动完成模式中，不触发斜杠自动完成
	if atPattern.MatchString(content) {
		return util.CmdHandler(utils.CommandQueryChangedMsg{Active: false})
	}

	isQueryCommand := false
	if strings.HasPrefix(content, "/") {
		isQueryCommand = true
	}
	if strings.HasPrefix(content, "+") {
		isQueryCommand = true
	}

	if isQueryCommand {
		// 若出现空格了，则退出命令提示框
		if strings.Contains(strings.TrimSpace(content), " ") {
			isQueryCommand = false
		}
	}

	if isQueryCommand {
		return util.CmdHandler(utils.CommandQueryChangedMsg{Active: true, Query: content})
	}

	return util.CmdHandler(utils.CommandQueryChangedMsg{Active: false})
}

// processCompletionSelection 处理自动完成选择
func (e *EditorComponent) processCompletionSelection(existingValue, searchString, completionValue string) string {
	// 替换搜索字符串为完成值
	modifiedValue := regexp.MustCompile(regexp.QuoteMeta(searchString)).ReplaceAllString(existingValue, completionValue)
	return modifiedValue + " "
}

// send 发送消息
func (e *EditorComponent) send() tea.Cmd {
	value := e.textarea.Value()
	attachments := e.attachments

	// 如果内容为空且没有附件，则不进行任何发送
	if strings.TrimSpace(value) == "" && len(attachments) == 0 {
		return nil
	}

	// 检查是否忙碌 - 如果忙碌则设置待发送消息
	if e.session.Id != "" {
		agent := e.app.GetCoderAgent()
		if agentImpl, ok := agent.(interface {
			IsSessionBusy(string) bool
		}); ok && agentImpl.IsSessionBusy(e.session.Id) {
			return e.setPendingMessage(value, attachments)
		}
	}

	// 立即发送消息
	return e.sendMessage(value, attachments)
}

// setPendingMessage 设置待发送消息
func (e *EditorComponent) setPendingMessage(text string, attachments []message.Attachment) tea.Cmd {
	// 使用handler设置待发送消息
	pendingMsg, cmd := e.pendingMessageHandler.SetPendingMessage(
		text,
		attachments,
		e.state.PendingMessage,
		e.historyHandler,
		e.currentMode,
		e.app,
	)

	// 更新状态
	e.state.PendingMessage = pendingMsg

	// 清空输入与附件
	e.textarea.Reset()
	e.attachments = nil

	// 更新历史索引
	e.history = e.historyHandler.LoadHistory(e.app)
	e.histIdx = len(e.history)

	return cmd
}

// sendMessage 立即发送消息
func (e *EditorComponent) sendMessage(text string, attachments []message.Attachment) tea.Cmd {
	// 清空输入与附件
	e.textarea.Reset()
	e.attachments = nil

	// 保存到历史
	e.historyHandler.SaveToHistory(text, attachments, e.currentMode, e.app)
	e.history = e.historyHandler.LoadHistory(e.app)
	e.histIdx = len(e.history)

	return tea.Batch(
		util.CmdHandler(editortypes.SendMsg{Text: text, Attachments: attachments}),
		util.CmdHandler(hinttypes.SwitchEditorStatusMsg{
			Empty: true,
		}),
	)
}

// processPending 处理待发送消息
func (e *EditorComponent) processPending() tea.Cmd {
	// 使用handler处理待发送消息
	pendingMsg, cmd := e.pendingMessageHandler.ProcessPending(
		e.state.PendingMessage,
		e.session.Id,
		e.app,
	)

	// 更新状态
	e.state.PendingMessage = pendingMsg

	return cmd
}

// clearPending 清空待发送消息
func (e *EditorComponent) clearPending() tea.Cmd {
	e.state.PendingMessage = e.pendingMessageHandler.ClearPending()
	return nil
}

// View 渲染组件视图
func (e *EditorComponent) View() string {
	var result []string

	// 渲染待发送消息
	pendingDisplay := e.renderPendingMessage()
	if pendingDisplay != "" {
		result = append(result, pendingDisplay)
	}

	// 渲染编辑器内容
	var editorLines []string
	inputContent := e.renderFixedHeightInput()
	editorLines = append(editorLines, inputContent)

	// 添加附件显示
	if len(e.attachments) > 0 {
		attachmentLines := e.attachmentHandler.RenderAttachments(e.attachments, e.state.DeleteMode, e.state.Width)
		editorLines = append(editorLines, attachmentLines)
	}

	// 应用边框样式到编辑器内容
	editorContent := strings.Join(editorLines, "\n")
	borderedEditor := e.renderEditorBorder(editorContent)
	result = append(result, borderedEditor)

	return strings.Join(result, "\n")
}

// renderPendingMessage 渲染待发送消息
func (e *EditorComponent) renderPendingMessage() string {
	return e.pendingMessageHandler.RenderPending(
		e.state.PendingMessage,
		e.attachmentHandler,
		e.state.Width,
	)
}

// renderFixedHeightInput 渲染固定高度输入
func (e *EditorComponent) renderFixedHeightInput() string {
	e.setPromptFunction()
	e.setPlaceholder()
	return e.textarea.View()
}

// setPromptFunction 设置提示符函数
func (e *EditorComponent) setPromptFunction() {
	textareaStyle := styles.BaseStyle()

	e.textarea.SetPromptFunc(2, func(lineIdx int) string {
		promptStyle := textareaStyle

		if e.state.MemoryMode {
			t := theme.CurrentTheme()
			promptStyle = promptStyle.Foreground(t.Primary())
		}
		if e.state.BashMode {
			t := theme.CurrentTheme()
			promptStyle = promptStyle.Foreground(t.SyntaxOperator())
		}

		if lineIdx == 0 {
			if e.state.MemoryMode {
				return promptStyle.Render(" # ")
			}
			if e.state.BashMode {
				return promptStyle.Render(" ! ")
			}
			return promptStyle.Render(" > ")
		} else {
			return promptStyle.Render("   ")
		}
	})
}

// setPlaceholder 设置占位符
func (e *EditorComponent) setPlaceholder() {
	if e.textarea.Value() == "" {
		e.textarea.Placeholder = "Type your message..."
		if e.state.MemoryMode {
			e.textarea.Placeholder = "Add to memory. Try \"Always use descriptive variable names\""
		}
		if e.state.BashMode {
			e.textarea.Placeholder = "Run a bash command. Try \"ls -l\""
		}
	}
}

// renderEditorBorder 渲染编辑器边框
func (e *EditorComponent) renderEditorBorder(content string) string {
	editorStyle := lipgloss.NewStyle().
		PaddingLeft(0).
		Border(lipgloss.RoundedBorder(), true, true, true, true)

	t := theme.CurrentTheme()
	if e.state.MemoryMode {
		editorStyle = editorStyle.BorderForeground(t.Primary())
	}
	if e.state.BashMode {
		editorStyle = editorStyle.BorderForeground(t.SyntaxOperator())
	}

	return editorStyle.Render(content)
}

// BindingKeys 返回按键绑定
func (e *EditorComponent) BindingKeys() []key.Binding {
	bindings := []key.Binding{}
	bindings = append(bindings, util.KeyMapToSlice(editorMaps)...)
	return bindings
}

// OpenExternalEditor 打开外部编辑器
func (e *EditorComponent) OpenExternalEditor(payload command.Event) tea.Cmd {
	return e.externalHandler.OpenExternalEditor(payload)
}

// OpenExternalEditorWithFile 打开外部编辑器编辑指定文件
func (e *EditorComponent) OpenExternalEditorWithFile(filePath string, inputChan chan any) tea.Cmd {
	return e.externalHandler.OpenExternalEditorWithFile(filePath, inputChan)
}

// createTextArea 创建文本区域
func createTextArea(existing *textarea.Model) textarea.Model {
	ta := textarea.New()

	// 应用样式
	ta.BlurredStyle.Base = styles.BaseStyle()
	ta.BlurredStyle.CursorLine = styles.BaseStyle()
	ta.BlurredStyle.Placeholder = styles.BaseStyle().Foreground(lipgloss.Color("240"))
	ta.BlurredStyle.Text = styles.BaseStyle()
	ta.FocusedStyle.Base = styles.BaseStyle()
	ta.FocusedStyle.CursorLine = styles.BaseStyle()
	ta.FocusedStyle.Placeholder = styles.BaseStyle().Foreground(lipgloss.Color("240"))
	ta.FocusedStyle.Text = styles.BaseStyle()

	// 配置设置
	ta.ShowLineNumbers = false
	ta.CharLimit = -1

	// 启用光标移动功能
	ta.KeyMap.LineNext.SetEnabled(true)
	ta.KeyMap.LinePrevious.SetEnabled(true)
	ta.KeyMap.WordBackward.SetEnabled(true)
	ta.KeyMap.WordForward.SetEnabled(true)

	if existing != nil {
		ta.SetValue(existing.Value())
		ta.SetWidth(existing.Width())
	}

	ta.Focus()
	return ta
}

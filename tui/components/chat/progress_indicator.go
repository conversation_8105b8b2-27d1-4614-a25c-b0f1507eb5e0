package chat

import (
	"context"
	"fmt"
	"strings"
	"time"

	progresshandler "github.com/qoder-ai/qodercli/tui/components/handler/progress"
	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"
	messagestypes "github.com/qoder-ai/qodercli/tui/components/types/messages"
	progresstypes "github.com/qoder-ai/qodercli/tui/components/types/progress"

	"github.com/charmbracelet/bubbles/spinner"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core"
	coreagent "github.com/qoder-ai/qodercli/core/llm/agent"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/core/monitoring"
	"github.com/qoder-ai/qodercli/core/pubsub"
	"github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
	"github.com/qoder-ai/qodercli/tui/util"
)

// ProgressIndicatorComponent 进度指示器组件
type ProgressIndicatorComponent struct {
	app     runtime.AppRuntime
	width   int
	height  int
	session core.Session

	// 动画与可见性
	spinner spinner.Model
	state   progresstypes.ProgressState
	visible bool

	// 指标统计
	startTime   time.Time
	elapsedSecs int // 已用秒数（每秒递增）

	// 当前处理中的 assistant 消息
	currentMessageID string
	isProcessing     bool

	// Token统计
	actualOutputTokens  int64 // 实际output tokens
	displayOutputTokens int64 // 显示的output tokens（平滑动画）

	// 单次请求累计指标（以用户请求为单位，而非整个会话）
	requestStartTime  time.Time                      // 本次请求开始时间
	processedMessages map[string]*message.TokenUsage // 按消息去重统计真实用量（请求级）

	// 连续刷新用的计时器标志
	tickerRunning bool

	// tick计数器，用于控制监控上报频率（每100个tick=1秒上报一次）
	tickCount int

	// 文件变更统计处理器
	fileStatsHandler progresstypes.FileStatsHandler

	// 当前消息状态
	messageStatus message.Status

	// summarize 文案
	summarizeMessage string
}

// NewProgressIndicatorComponent 创建进度指示器组件
func NewProgressIndicatorComponent(app runtime.AppRuntime) *ProgressIndicatorComponent {
	s := spinner.New()
	s.Spinner = spinner.Dot
	s.Style = styles.BaseStyle().Foreground(theme.CurrentTheme().Primary())

	return &ProgressIndicatorComponent{
		app:               app,
		spinner:           s,
		state:             progresstypes.ProgressStateHidden,
		visible:           false,
		height:            1,
		processedMessages: make(map[string]*message.TokenUsage),
		fileStatsHandler:  progresshandler.NewFileStatsHandler(app),
	}
}

// Init 初始化组件
func (p *ProgressIndicatorComponent) Init() tea.Cmd {
	if p.app == nil {
		return nil
	}
	ctx := context.Background()
	filesCh := p.app.SubscribeHistory(ctx)
	return func() tea.Msg { return <-filesCh }
}

// Update 消费消息并更新内部状态
func (p *ProgressIndicatorComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case progresstypes.FileStatsRecomputedMsg:
		p.fileStatsHandler.SetStats(msg.Stats)
		return p, nil
	case tea.WindowSizeMsg:
		p.width = msg.Width
		return p, nil

	case messagestypes.SessionChangedMsg:
		p.session = msg.Session
		p.reset()
		return p, p.recomputeAllCmd()
	case pubsub.Event[core.File]:
		if p.session.Id == "" || msg.Payload.SessionId != p.session.Id {
			return p, func() tea.Msg { return <-p.app.SubscribeHistory(context.Background()) }
		}
		p.fileStatsHandler.UpdateFileStat(msg.Payload, p.session.Id)
		return p, func() tea.Msg { return <-p.app.SubscribeHistory(context.Background()) }

	case pubsub.Event[message.Message]:
		if cmd := p.handleMessageEvent(msg); cmd != nil {
			cmds = append(cmds, cmd)
		}

	case pubsub.Event[coreagent.Event]:
		payload := msg.Payload

		// 处理 Agent 开始执行事件 (只有真正的 Agent 执行才会发布此事件)
		if payload.Type == coreagent.AgentEventTypeSystem && payload.Subtype == "init" && payload.SessionId == p.session.Id {
			cmd := p.startProgress()
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
		}

		// 处理自动压缩阶段事件（作为请求的一个阶段）
		if payload.Type == "auto_compact_phase" && payload.SessionId == p.session.Id {
			if payload.Done {
				// 自动压缩完成，切换到生成状态，但不重置计时
				if p.state == progresstypes.ProgressStateSummarizing {
					p.state = progresstypes.ProgressStateGenerating
				}
				p.summarizeMessage = ""
			} else {
				// 自动压缩开始，如果没有在处理中则开始进度，否则只切换状态
				if !p.isProcessing {
					cmd := p.startProgress()
					if cmd != nil {
						cmds = append(cmds, cmd)
					}
				}
				p.state = progresstypes.ProgressStateSummarizing
				p.summarizeMessage = payload.Progress
				p.show()
				cmds = append(cmds, tea.Tick(10*time.Millisecond, func(t time.Time) tea.Msg { return t }))
			}
			return p, tea.Batch(cmds...)
		}

		if payload.Type == "auto_compact_error" && payload.SessionId == p.session.Id {
			// 自动压缩错误，切换回生成状态
			if p.state == progresstypes.ProgressStateSummarizing {
				p.state = progresstypes.ProgressStateGenerating
			}
			p.summarizeMessage = ""
			return p, tea.Batch(cmds...)
		}

		// 处理 summarize 进度事件 (Agent总结时不会触发init事件)
		if payload.Type == coreagent.AgentEventTypeSummarize {
			if payload.Done {
				if p.state == progresstypes.ProgressStateSummarizing {
					p.state = progresstypes.ProgressStateGenerating
				}
				p.summarizeMessage = ""
				return p, tea.Batch(cmds...)
			}

			// 检查是否为新的 summarize 任务开始
			if p.state != progresstypes.ProgressStateSummarizing || !p.isProcessing {
				// 新的 summarize 开始：调用 startProgress 重置所有状态
				cmd := p.startProgress()
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
			}

			// 设置 summarize 特定状态
			p.state = progresstypes.ProgressStateSummarizing
			if strings.TrimSpace(payload.Progress) != "" {
				p.summarizeMessage = payload.Progress
			} else {
				p.summarizeMessage = "Summarizing..."
			}

			// 确保进度条可见
			p.show()
			cmds = append(cmds, tea.Tick(10*time.Millisecond, func(t time.Time) tea.Msg { return t }))
			return p, tea.Batch(cmds...)
		}

	case time.Time:
		if cmd := p.onTick(); cmd != nil {
			cmds = append(cmds, cmd)
		}
	}

	if p.visible {
		spinnerModel, spinnerCmd := p.spinner.Update(msg)
		p.spinner = spinnerModel
		if spinnerCmd != nil {
			cmds = append(cmds, spinnerCmd)
		}
	}

	return p, tea.Batch(cmds...)
}

// View 渲染进度指示器
func (p *ProgressIndicatorComponent) View() string {
	t := theme.CurrentTheme()
	baseStyle := styles.BaseStyle()
	indicatorStyle := baseStyle.Width(p.width)

	if p.visible {
		parts := []string{p.spinner.View(), p.getStatusText(), p.formatMetrics()}
		return indicatorStyle.Render(strings.Join(parts, ""))
	}
	fileStats := p.fileStatsHandler.GetStats()
	if len(fileStats) == 0 {
		return ""
	}
	files, add, del := p.fileStatsHandler.AggregateTotals()
	label := styles.BaseStyle().Foreground(t.TextMuted()).PaddingLeft(2).Render(fmt.Sprintf("%d files edited", files))
	addStr := styles.BaseStyle().Foreground(t.Success()).PaddingLeft(1).Render(fmt.Sprintf("+%d", add))
	delStr := styles.BaseStyle().Foreground(t.Error()).PaddingLeft(1).Render(fmt.Sprintf("-%d", del))
	return indicatorStyle.Render(strings.Join([]string{label, addStr, delStr}, ""))
}

// handleMessageEvent 处理消息事件，追踪进度状态
func (p *ProgressIndicatorComponent) handleMessageEvent(event pubsub.Event[message.Message]) tea.Cmd {
	msg := event.Payload

	switch event.Type {
	case pubsub.UpdatedEvent:
		// 处理assistant消息更新，包含主Agent、子Agent和自动压缩的tokens统计
		if msg.Role == "assistant" && p.isProcessing {
			// 统计所有assistant消息的tokens，包括Meta消息（用于自动压缩流式显示）
			// 这样可以包含Task工具调用的子Agent和自动压缩产生的tokens
			return p.updateProgress(msg)
		}
	}
	return nil
}

// startProgress 用户消息创建后开始进度
func (p *ProgressIndicatorComponent) startProgress() tea.Cmd {
	p.currentMessageID = ""
	p.isProcessing = true

	// 新请求：重置请求级指标
	p.requestStartTime = time.Now()
	p.elapsedSecs = 0

	// 重置token统计
	p.actualOutputTokens = 0
	p.displayOutputTokens = 0

	// 清空去重表
	p.processedMessages = make(map[string]*message.TokenUsage)

	p.startTime = time.Now()
	p.state = progresstypes.ProgressStateGenerating
	p.show()
	// 进入执行状态后也继续监听文件事件
	listen := func() tea.Msg { return <-p.app.SubscribeHistory(context.Background()) }

	return tea.Batch(
		tea.Tick(10*time.Millisecond, func(t time.Time) tea.Msg { return t }),
		p.spinner.Tick,
		listen,
	)
}

// updateProgress 根据 assistant 消息的变更更新指标
func (p *ProgressIndicatorComponent) updateProgress(msg message.Message) tea.Cmd {
	if !p.visible {
		return nil
	}

	if msg.Status == message.StatusFinished {
		time.Sleep(1)
	}

	// 只处理当前session的消息
	if msg.SessionId != p.session.Id {
		return nil
	}

	if p.currentMessageID == "" {
		p.currentMessageID = msg.Id
	}

	// 只使用真实的Token用量数据，忽略没有usage信息的消息
	currentUsage := msg.GetUsage()
	if currentUsage == nil {
		// 消息还没有真实usage信息，跳过此次更新
		return nil
	}

	// 更新消息的usage记录
	p.processedMessages[msg.Id] = currentUsage

	// 重新计算所有消息的总usage
	p.recalculateTotalUsage()

	// 如果当前是summarize状态，保持不变，否则根据消息内容设置状态
	if p.state != progresstypes.ProgressStateSummarizing {
		if msg.ReasoningContent().Thinking != "" && msg.Content().String() == "" {
			p.state = progresstypes.ProgressStateThinking
		} else {
			p.state = progresstypes.ProgressStateGenerating
		}
	}

	p.messageStatus = msg.Status

	return nil
}

// recalculateTotalUsage 重新计算所有消息的总output tokens
func (p *ProgressIndicatorComponent) recalculateTotalUsage() {
	var totalOutput int64

	// 遍历所有已处理的消息，重新计算总output tokens
	for _, usage := range p.processedMessages {
		if usage != nil {
			totalOutput += usage.OutputTokens
		}
	}

	// 更新总计
	p.actualOutputTokens = totalOutput
}

// stopProgress 停止进度展示
func (p *ProgressIndicatorComponent) stopProgress() tea.Cmd {
	p.hide()
	p.isProcessing = false
	p.currentMessageID = ""

	// Agent空闲时触发待发送消息处理
	return util.CmdHandler(editortypes.ProcessPendingMsg{})
}

// show 显示并启动 spinner
func (p *ProgressIndicatorComponent) show() {
	if !p.visible {
		p.visible = true
		p.tickerRunning = true
		p.spinner = spinner.New()
		p.spinner.Spinner = spinner.Dot
		p.spinner.Style = styles.BaseStyle().Foreground(theme.CurrentTheme().Primary())
	}
}

// hide 隐藏并停止计时
func (p *ProgressIndicatorComponent) hide() {
	p.visible = false
	p.tickerRunning = false
}

// reset 重置组件所有状态
func (p *ProgressIndicatorComponent) reset() {
	p.hide()
	p.state = progresstypes.ProgressStateHidden
	p.isProcessing = false
	p.currentMessageID = ""
	p.tickCount = 0

	// Reset all token and timing fields
	p.actualOutputTokens = 0
	p.displayOutputTokens = 0
	p.elapsedSecs = 0

	// Reset session timing
	p.requestStartTime = time.Time{}

	// Reset message tracking
	p.processedMessages = make(map[string]*message.TokenUsage)
	p.fileStatsHandler.Reset()
}

// getStatusText 返回当前状态对应的提示文案
func (p *ProgressIndicatorComponent) getStatusText() string {
	base := styles.BaseStyle()
	switch p.state {
	case progresstypes.ProgressStateThinking:
		return base.Foreground(theme.CurrentTheme().Text()).Render("Thinking...")
	case progresstypes.ProgressStateGenerating:
		return base.Foreground(theme.CurrentTheme().Text()).Render("Generating...")
	case progresstypes.ProgressStateSummarizing:
		return base.Foreground(theme.CurrentTheme().Text()).Render(p.summarizeMessage)
	default:
		return base.Foreground(theme.CurrentTheme().Text()).Render("Working...")
	}
}

func (p *ProgressIndicatorComponent) formatMetrics() string {
	base := styles.BaseStyle()

	// 采用请求级计时：若有请求起始时间，用实时差值；否则使用累计秒表
	displaySeconds := p.elapsedSecs
	if !p.requestStartTime.IsZero() {
		displaySeconds = int(time.Since(p.requestStartTime).Seconds())
	}

	// 只显示output tokens数量
	outputTokens := p.displayOutputTokens

	// 根据消息状态决定显示符号
	var symbol string
	if p.messageStatus == message.StatusToolCalling {
		symbol = "⚒" // 工具调用阶段：显示工具符号
	} else {
		symbol = "↓" // 其他情况：显示下载符号
	}

	// 构建tokens显示字符串：符号+output数量
	tokensStr := fmt.Sprintf("%s %d tokens", symbol, outputTokens)

	content := fmt.Sprintf("(%d s · %s · esc to interrupt)", displaySeconds, tokensStr)
	return base.Foreground(theme.CurrentTheme().Text()).Render(content)
}

// recomputeAllCmd 全量重算当前会话的文件变更（用于会话切换后初始展示）
func (p *ProgressIndicatorComponent) recomputeAllCmd() tea.Cmd {
	if p.app == nil || p.session.Id == "" {
		return nil
	}
	return func() tea.Msg {
		stats, err := p.fileStatsHandler.RecomputeAll(p.session.Id)
		if err != nil {
			return nil
		}
		return progresstypes.FileStatsRecomputedMsg{Stats: stats}
	}
}

// onTick 每10ms触发：更新时间、上报活跃时间、平滑 token、空闲收尾
func (p *ProgressIndicatorComponent) onTick() tea.Cmd {
	if !(p.visible && p.tickerRunning) {
		return nil
	}

	// 请求级计时优先（实时计算），不依赖tick频率
	if !p.requestStartTime.IsZero() {
		p.elapsedSecs = int(time.Since(p.requestStartTime).Seconds())
	} else {
		// 如果没有请求开始时间，则基于startTime计算
		p.elapsedSecs = int(time.Since(p.startTime).Seconds())
	}

	// 增加tick计数器，每100个tick（1秒）上报一次活跃时间
	p.tickCount++
	if p.session.Id != "" && p.isProcessing && p.tickCount%100 == 0 {
		monitoring.AddActiveTimeSeconds(context.Background(), p.session.Id, 1)
	}

	// 更新token动画
	p.updateTokenAnimation()

	// 只有在Agent不忙碌才能隐藏进度指示器
	if !p.app.GetCoderAgent().IsSessionBusy(p.session.Id) && p.isProcessing {
		return p.stopProgress()
	}

	return tea.Tick(10*time.Millisecond, func(t time.Time) tea.Msg { return t })
}

// updateTokenAnimation 更新output token平滑动画
func (p *ProgressIndicatorComponent) updateTokenAnimation() {
	p.updateSingleTokenAnimation(&p.displayOutputTokens, p.actualOutputTokens)
}

// updateSingleTokenAnimation 更新单个token类型的平滑动画
func (p *ProgressIndicatorComponent) updateSingleTokenAnimation(current *int64, target int64) {
	if *current == target {
		return
	}

	diff := target - *current

	// 确保0.5秒内完成动画：10ms tick × 50次 = 0.5秒
	// 计算每次至少需要增加的数量，保证在50次tick内完成
	minStepToComplete := maxInt64(1, (diff+49)/50) // 向上取整，确保0.5秒内完成

	var step int64
	switch {
	case diff > 500:
		// 超大量token：极速增长（约0.1秒完成）
		step = maxInt64(minStepToComplete, diff/10)
	case diff > 200:
		// 大量token：快速增长（约0.2秒完成）
		step = maxInt64(minStepToComplete, diff/20)
	case diff > 50:
		// 中等token：中等速度（约0.3秒完成）
		step = maxInt64(minStepToComplete, diff/30)
	case diff > 10:
		// 少量token：缓慢增长，让用户看清每个变化（约0.4秒完成）
		step = maxInt64(minStepToComplete, diff/40)
	default:
		// 最少token：确保在0.5秒内完成
		step = maxInt64(1, minStepToComplete)
	}

	*current += step

	// 确保不超过目标值
	if *current >= target {
		*current = target
	}
}

// maxInt64 返回两个int64中的较大值
func maxInt64(a, b int64) int64 {
	if a > b {
		return a
	}
	return b
}

package chat

import (
	"fmt"
	"strings"
	"time"

	"github.com/qoder-ai/qodercli/tui/runtime"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/models"
	"github.com/qoder-ai/qodercli/qoder"
	statustypes "github.com/qoder-ai/qodercli/tui/components/types/status"
	tuinewconfig "github.com/qoder-ai/qodercli/tui/config"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
)

type currentSessionGetter interface {
	GetCurrentSession() core.Session
}

// StatusComponent 状态组件
// 仅展示模型信息与 token/cost
type StatusComponent struct {
	width              int
	sessionGetter      currentSessionGetter
	app                runtime.AppRuntime
	loginMonitorActive bool // 登录状态监听是否活跃
}

func (s *StatusComponent) Init() tea.Cmd { return nil }

func (s *StatusComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		s.width = msg.Width
		return s, nil

	case statustypes.StartLoginMonitorMsg:
		// 开始登录状态监听
		s.loginMonitorActive = true
		return s, s.startLoginMonitor()

	case statustypes.StopLoginMonitorMsg:
		// 停止登录状态监听
		s.loginMonitorActive = false
		return s, nil

	case statustypes.LoginCheckMsg:
		// 检查登录状态，如果已登录则停止监听
		if s.loginMonitorActive && qoder.IsUserLoggedIn() {
			s.loginMonitorActive = false
			return s, nil
		}
		// 如果还在监听且未登录，继续下一次检查
		if s.loginMonitorActive {
			return s, s.nextLoginCheck()
		}
		return s, nil
	}
	return s, nil
}

func (s *StatusComponent) View() string {
	// 检查登录状态，如果未登录则优先显示登录提示
	loginStatus := s.renderLoginStatus()
	if loginStatus != "" {
		// 应用一致的2字符padding，与消息组件和编辑器组件保持一致
		statusStyle := lipgloss.NewStyle().Border(lipgloss.ASCIIBorder(), true, false, false, false).PaddingLeft(0)
		return statusStyle.Render(loginStatus)
	}

	mi := s.renderModelInfo()
	ti := s.renderToken()

	status := mi // 模型信息
	status += ti // 令牌信息

	// 应用一致的2字符padding，与消息组件和编辑器组件保持一致
	statusStyle := lipgloss.NewStyle().Border(lipgloss.ASCIIBorder(), true, false, false, false).PaddingLeft(0)
	return statusStyle.Render(status)
}

// renderToken 渲染令牌信息
func (s *StatusComponent) renderToken() string {
	session := s.sessionGetter.GetCurrentSession()
	if session.Id == "" {
		return ""
	}

	t := theme.CurrentTheme()

	modelID := tuinewconfig.GetCurrent().Agents[config.AgentCoder].Model
	model := models.SupportedModels[modelID]

	totalTokens := session.PromptTokens + session.CompletionTokens
	tokens := formatTokens(totalTokens, model.ContextWindow)

	tokensStyle := styles.Padded().
		Foreground(t.Text())

	percentage := (float64(totalTokens) / float64(model.ContextWindow)) * 100
	if percentage > statustypes.DefaultFormatConfig.TokenWarningPercentage {
		tokensStyle = tokensStyle.Background(t.Warning())
	}
	return tokensStyle.Render(tokens)
}

// renderModelInfo 渲染模型信息
func (s *StatusComponent) renderModelInfo() string {
	t := theme.CurrentTheme()
	cfg := tuinewconfig.GetCurrent()

	coder, ok := cfg.Agents[config.AgentCoder]
	if !ok {
		return "Unknown"
	}
	model := models.SupportedModels[coder.Model]

	return styles.Padded().
		Foreground(t.Secondary()).
		Render(model.Name)
}

// formatTokensAndCost 格式化令牌信息
func formatTokens(tokens, contextWindow int64) string {
	// 以人类可读的格式格式化令牌数（例如：110K, 1.2M）
	var formattedTokens string
	cfg := statustypes.DefaultFormatConfig

	switch {
	case tokens >= cfg.TokenMegaThreshold:
		formattedTokens = fmt.Sprintf("%.1fM", float64(tokens)/float64(cfg.TokenMegaThreshold))
	case tokens >= cfg.TokenKiloThreshold:
		formattedTokens = fmt.Sprintf("%.1fK", float64(tokens)/float64(cfg.TokenKiloThreshold))
	default:
		formattedTokens = fmt.Sprintf("%d", tokens)
	}

	// 如果存在.0后缀则移除
	if strings.HasSuffix(formattedTokens, ".0K") {
		formattedTokens = strings.Replace(formattedTokens, ".0K", "K", 1)
	}
	if strings.HasSuffix(formattedTokens, ".0M") {
		formattedTokens = strings.Replace(formattedTokens, ".0M", "M", 1)
	}

	percentage := (float64(tokens) / float64(contextWindow)) * 100
	if percentage > cfg.TokenWarningPercentage {
		// 添加警告图标和百分比
		formattedTokens = fmt.Sprintf("%s(%d%%)", styles.WarningIcon, int(percentage))
	}

	return fmt.Sprintf("Context: %s", formattedTokens)
}

// renderLoginStatus 渲染登录状态
func (s *StatusComponent) renderLoginStatus() string {
	if !qoder.IsUserLoggedIn() {
		t := theme.CurrentTheme()
		warningStyle := styles.Padded().
			Foreground(t.Warning()).
			Bold(true)
		return warningStyle.Render(statustypes.LoginRequiredMessage)
	}
	return "" // 已登录时不显示额外信息
}

// startLoginMonitor 开始登录状态监听
func (s *StatusComponent) startLoginMonitor() tea.Cmd {
	return s.nextLoginCheck()
}

// nextLoginCheck 下一次登录状态检查
func (s *StatusComponent) nextLoginCheck() tea.Cmd {
	return tea.Tick(time.Second, func(t time.Time) tea.Msg {
		return statustypes.LoginCheckMsg{}
	})
}

// NewStatusCmp 创建新的状态组件
func NewStatusCmp(app runtime.AppRuntime, sessionGetter currentSessionGetter) *StatusComponent {
	return &StatusComponent{
		app:           app,
		sessionGetter: sessionGetter,
	}
}

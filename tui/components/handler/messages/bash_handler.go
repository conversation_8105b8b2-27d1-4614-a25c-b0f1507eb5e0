package messages

import (
	"context"
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/google/uuid"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/message"
	messagestypes "github.com/qoder-ai/qodercli/tui/components/types/messages"
	tuievent "github.com/qoder-ai/qodercli/tui/event"
	tuiruntime "github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
	"github.com/qoder-ai/qodercli/tui/util"
)

// BashHandler 处理bash相关消息和渲染的专门组件
type BashHandler struct {
	app            tuiruntime.AppRuntime
	width          int
	ExecutionState *messagestypes.BashExecutionState // 公开字段以便外部访问
}

// NewBashHandler 创建新的bash处理器
func NewBashHandler(app tuiruntime.AppRuntime, width int) *BashHandler {
	return &BashHandler{
		app:            app,
		width:          width,
		ExecutionState: &messagestypes.BashExecutionState{},
	}
}

// SetWidth 设置渲染宽度
func (h *BashHandler) SetWidth(width int) {
	h.width = width
}

// GetExecutionState 获取执行状态
func (h *BashHandler) GetExecutionState() *messagestypes.BashExecutionState {
	return h.ExecutionState
}

// sendBashOperationStart 发送bash操作的开始消息：meta + input
func (h *BashHandler) sendBashOperationStart(ctx context.Context, sessionId, command string, parentId *string) (*string, error) {
	// 1. 创建Meta消息
	metaContent := "Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."

	var parentIdStr string
	if parentId != nil {
		parentIdStr = *parentId
	}
	metaMsg, err := h.app.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.User,
		Parts:    []message.ContentPart{message.TextContent{Text: metaContent, Type: "text"}},
		ParentId: parentIdStr,
		IsMeta:   true, // meta消息不参与渲染
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create bash meta message: %w", err)
	}

	// 2. 创建输入消息
	inputContent := fmt.Sprintf("<bash-input>%s</bash-input>", command)

	inputMsg, err := h.app.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.User,
		Parts:    []message.ContentPart{message.TextContent{Text: inputContent, Type: "text"}},
		ParentId: metaMsg.Id,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create bash input message: %w", err)
	}

	// 返回input消息ID，用于后续创建result消息的parent
	return &inputMsg.Id, nil
}

// sendBashOperationComplete 发送bash操作的结果消息：result
func (h *BashHandler) sendBashOperationComplete(ctx context.Context, sessionId, stdout, stderr string, parentId *string) error {
	// 创建结果消息
	resultContent := fmt.Sprintf("<bash-stdout>%s</bash-stdout><bash-stderr>%s</bash-stderr>", stdout, stderr)

	var parentIdStr string
	if parentId != nil {
		parentIdStr = *parentId
	}
	_, err := h.app.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.User,
		Parts:    []message.ContentPart{message.TextContent{Text: resultContent, Type: "text"}},
		ParentId: parentIdStr,
	})
	if err != nil {
		return fmt.Errorf("failed to create bash result message: %w", err)
	}

	return nil
}

// HandleMessage 处理bash相关消息
func (h *BashHandler) HandleMessage(msg tea.Msg, session core.Session, renderedMessages map[string]*messagestypes.RenderedMessage, messages map[string]message.Message) tea.Cmd {
	switch msg := msg.(type) {
	case messagestypes.BashAppendOutputMsg:
		return h.handleAppendOutput(msg, renderedMessages, messages)
	case messagestypes.BashFinishedMsg:
		return h.handleFinished(msg, session, renderedMessages, messages)
	case messagestypes.BashOperationStartMsg:
		return h.handleOperationStart(msg, session)
	case messagestypes.BashOperationCompleteMsg:
		return h.handleOperationComplete(msg, session)
	case messagestypes.ShellEventListenerMsg:
		return h.handleShellEvents(msg)
	}
	return nil
}

// IsOperationMessage 检查是否为bash操作消息
func (h *BashHandler) IsOperationMessage(msg message.Message) bool {
	content := msg.Content().Text
	return strings.Contains(content, "<bash-input>") || strings.Contains(content, "<bash-stdout>")
}

// HandleOperationMessage 处理数据库中的bash操作消息
func (h *BashHandler) HandleOperationMessage(msg message.Message, messageOrder *[]string, renderedMessages map[string]*messagestypes.RenderedMessage, messages map[string]message.Message) tea.Cmd {
	content := msg.Content().Text

	if strings.Contains(content, "<bash-input>") {
		return h.handleInputMessage(msg, messageOrder, renderedMessages)
	}

	if strings.Contains(content, "<bash-stdout>") {
		return h.handleResultMessage(msg, renderedMessages, messages)
	}

	return nil
}

// RenderOperation 渲染bash操作
func (h *BashHandler) RenderOperation(command, stdout, stderr string) string {
	t := theme.CurrentTheme()

	// 渲染命令行
	indicator := styles.BaseStyle().Foreground(t.SyntaxOperator()).Render("! ")
	cmd := styles.BaseStyle().Foreground(t.Text()).Render(command)
	header := styles.BaseStyle().PaddingLeft(0).Render(indicator + cmd)

	// 构建内容部分
	var parts []string
	parts = append(parts, header)

	// 处理标准输出
	if stdout != "" {
		parts = append(parts, h.renderOutput(stdout, false)...)
	}

	// 处理标准错误
	if stderr != "" {
		parts = append(parts, h.renderOutput(stderr, true)...)
	}

	return lipgloss.JoinVertical(lipgloss.Left, parts...)
}

// ExtractCommand 从bash-input内容中提取命令
func (h *BashHandler) ExtractCommand(content string) string {
	const (
		startTag = "<bash-input>"
		endTag   = "</bash-input>"
	)

	start := strings.Index(content, startTag)
	end := strings.Index(content, endTag)
	if start >= 0 && end > start {
		return content[start+len(startTag) : end]
	}
	return ""
}

// ExtractResult 从bash-stdout/stderr内容中提取结果
func (h *BashHandler) ExtractResult(content string) (stdout, stderr string) {
	stdout = h.extractTagContent(content, "<bash-stdout>", "</bash-stdout>")
	stderr = h.extractTagContent(content, "<bash-stderr>", "</bash-stderr>")
	return stdout, stderr
}

// GenerateExecutionID 生成执行ID
func (h *BashHandler) GenerateExecutionID() string {
	return fmt.Sprintf("shell-%d-%s", time.Now().UnixNano(), uuid.New().String()[:8])
}

// 私有方法

// handleAppendOutput 处理输出追加（节流：每1秒更新一次UI）
func (h *BashHandler) handleAppendOutput(msg messagestypes.BashAppendOutputMsg, renderedMessages map[string]*messagestypes.RenderedMessage, messages map[string]message.Message) tea.Cmd {
	if h.ExecutionState == nil || h.ExecutionState.ExecutionID != msg.ParentID {
		return nil
	}

	if h.ExecutionState.InputMsgID == nil {
		return nil
	}

	// 检查距离上次更新是否已过1秒
	now := time.Now()
	if now.Sub(h.ExecutionState.LastUpdateTime) < time.Second {
		// 未满1秒，跳过UI更新
		return nil
	}

	// 更新UI显示当前累积的输出
	inputMsgID := *h.ExecutionState.InputMsgID
	command := h.ExecutionState.Command
	rendered := h.RenderOperation(command, h.ExecutionState.Stdout, h.ExecutionState.Stderr)

	renderedMessages[inputMsgID] = &messagestypes.RenderedMessage{
		ID:           inputMsgID,
		Role:         "bash",
		RenderedText: rendered,
		IsFinished:   false,
	}

	// 更新最后更新时间
	h.ExecutionState.LastUpdateTime = now

	return nil
}

// handleFinished 处理bash执行完成
func (h *BashHandler) handleFinished(msg messagestypes.BashFinishedMsg, session core.Session, renderedMessages map[string]*messagestypes.RenderedMessage, messages map[string]message.Message) tea.Cmd {
	var command string
	var inputMsgID *string

	if h.ExecutionState != nil && h.ExecutionState.ExecutionID == msg.ParentID {
		command = h.ExecutionState.Command
		inputMsgID = h.ExecutionState.InputMsgID // 保存InputMsgID

		if inputMsgID != nil {
			inputMsgIDValue := *inputMsgID
			if inputMsg, ok := messages[inputMsgIDValue]; ok {
				cmdFromMsg := h.ExtractCommand(inputMsg.Content().Text)
				rendered := h.RenderOperation(cmdFromMsg, msg.Stdout, msg.Stderr)

				renderedMessages[inputMsgIDValue] = &messagestypes.RenderedMessage{
					ID:           inputMsgIDValue,
					Role:         "bash",
					RenderedText: rendered,
					IsFinished:   true,
				}
			}
		}

		// 重置状态
		h.ExecutionState = &messagestypes.BashExecutionState{}
	}

	// 发送操作完成消息
	return util.CmdHandler(messagestypes.BashOperationCompleteMsg{
		Command:    command,
		Stdout:     msg.Stdout,
		Stderr:     msg.Stderr,
		InputMsgID: inputMsgID, // 传递InputMsgID
	})
}

// handleOperationStart 处理操作开始
func (h *BashHandler) handleOperationStart(msg messagestypes.BashOperationStartMsg, session core.Session) tea.Cmd {
	return func() tea.Msg {
		ctx := context.Background()

		// 使用传入的会话，bash handler不再负责会话创建
		currentSession := session

		// 发送meta和input消息到数据库
		inputMsgID, err := h.sendBashOperationStart(ctx, currentSession.Id, msg.Command, nil)
		if err != nil {
			return util.InfoMsg{Type: util.InfoTypeError, Msg: fmt.Sprintf("Failed to send bash operation start: %v", err)}
		}

		// 启动shell执行
		executionID := h.GenerateExecutionID()
		eventCh, err := h.app.ExecuteShell(ctx, msg.Command, h.app.GetWorkingDir(), executionID)
		if err != nil {
			return util.InfoMsg{Type: util.InfoTypeError, Msg: fmt.Sprintf("Failed to execute shell command: %v", err)}
		}

		// 初始化执行状态
		h.ExecutionState = &messagestypes.BashExecutionState{
			ExecutionID:    executionID,
			Command:        msg.Command,
			InputMsgID:     inputMsgID,
			Stdout:         "",
			Stderr:         "",
			LastUpdateTime: time.Now(), // 初始化时间
		}

		// 开始监听shell事件
		return messagestypes.ShellEventListenerMsg{
			ExecutionID: executionID,
			EventCh:     eventCh,
		}
	}
}

// handleOperationComplete 处理操作完成
func (h *BashHandler) handleOperationComplete(msg messagestypes.BashOperationCompleteMsg, session core.Session) tea.Cmd {
	return func() tea.Msg {
		ctx := context.Background()

		// 使用消息中传递的InputMsgID
		parentID := msg.InputMsgID

		err := h.sendBashOperationComplete(ctx, session.Id, msg.Stdout, msg.Stderr, parentID)
		if err != nil {
			return util.InfoMsg{Type: util.InfoTypeError, Msg: fmt.Sprintf("Failed to send bash operation complete: %v", err)}
		}

		return nil
	}
}

// handleShellEvents 处理shell事件监听
func (h *BashHandler) handleShellEvents(msg messagestypes.ShellEventListenerMsg) tea.Cmd {
	return tea.Tick(time.Millisecond*10, func(t time.Time) tea.Msg {
		return messagestypes.ShellEventListenerMsg{
			ExecutionID: msg.ExecutionID,
			EventCh:     msg.EventCh,
		}
	})
}

// ProcessShellEvent 处理具体的shell事件
func (h *BashHandler) ProcessShellEvent(executionID string, eventCh <-chan tuievent.ShellEvent) (tea.Msg, bool) {
	select {
	case event, ok := <-eventCh:
		if !ok {
			// channel关闭，停止监听
			return nil, false
		}

		switch event.Type {
		case tuievent.ShellEventStarted:
			// 命令开始，继续监听
			return h.handleShellEvents(messagestypes.ShellEventListenerMsg{
				ExecutionID: executionID,
				EventCh:     eventCh,
			}), true
		case tuievent.ShellEventOutput:
			// 收集标准输出并发送显示事件
			h.CollectShellOutput(executionID, event.Content, false)
			return tea.Batch(
				util.CmdHandler(messagestypes.BashAppendOutputMsg{
					ParentID: executionID,
					Chunk:    event.Content,
				}),
				h.handleShellEvents(messagestypes.ShellEventListenerMsg{
					ExecutionID: executionID,
					EventCh:     eventCh,
				}),
			), true
		case tuievent.ShellEventError:
			// 收集标准错误输出并发送显示事件
			h.CollectShellOutput(executionID, event.Content, true)
			return tea.Batch(
				util.CmdHandler(messagestypes.BashAppendOutputMsg{
					ParentID: executionID,
					Chunk:    event.Content,
				}),
				h.handleShellEvents(messagestypes.ShellEventListenerMsg{
					ExecutionID: executionID,
					EventCh:     eventCh,
				}),
			), true
		case tuievent.ShellEventFinished:
			// 从状态获取收集的stdout和stderr
			var stdout, stderr string
			if h.ExecutionState != nil && h.ExecutionState.ExecutionID == executionID {
				stdout = h.ExecutionState.Stdout
				stderr = h.ExecutionState.Stderr
			}
			return util.CmdHandler(messagestypes.BashFinishedMsg{
				ParentID: executionID,
				ExitCode: event.ExitCode,
				Stdout:   stdout,
				Stderr:   stderr,
			}), true
		case tuievent.ShellEventCanceled:
			// 命令被取消
			return util.CmdHandler(messagestypes.BashFinishedMsg{
				ParentID: executionID,
				ExitCode: -1,
				Stdout:   "",
				Stderr:   "Command canceled by user",
			}), true
		}
	default:
		// 无事件时继续监听
		return h.handleShellEvents(messagestypes.ShellEventListenerMsg{
			ExecutionID: executionID,
			EventCh:     eventCh,
		}), true
	}
	return nil, false
}

// handleInputMessage 处理input消息
func (h *BashHandler) handleInputMessage(msg message.Message, messageOrder *[]string, renderedMessages map[string]*messagestypes.RenderedMessage) tea.Cmd {
	command := h.ExtractCommand(msg.Content().Text)
	if command == "" {
		return nil
	}

	rendered := h.RenderOperation(command, "", "")
	*messageOrder = append(*messageOrder, msg.Id)
	renderedMessages[msg.Id] = &messagestypes.RenderedMessage{
		ID:           msg.Id,
		Role:         "bash",
		RenderedText: rendered,
		IsFinished:   false,
	}

	return nil
}

// handleResultMessage 处理result消息
func (h *BashHandler) handleResultMessage(msg message.Message, renderedMessages map[string]*messagestypes.RenderedMessage, messages map[string]message.Message) tea.Cmd {
	stdout, stderr := h.ExtractResult(msg.Content().Text)
	inputMsgID := msg.ParentId

	if inputMsgID == "" {
		return nil
	}

	inputMsg, ok := messages[inputMsgID]
	if !ok {
		return nil
	}

	command := h.ExtractCommand(inputMsg.Content().Text)
	rendered := h.RenderOperation(command, stdout, stderr)

	renderedMessages[inputMsgID] = &messagestypes.RenderedMessage{
		ID:           inputMsgID,
		Role:         "bash",
		RenderedText: rendered,
		IsFinished:   true,
	}

	return nil
}

// renderOutput 渲染输出内容
func (h *BashHandler) renderOutput(content string, isError bool) []string {
	if content == "" {
		return nil
	}

	t := theme.CurrentTheme()
	lines := strings.Split(content, "\n")
	var parts []string

	for i, line := range lines {
		if strings.TrimSpace(line) == "" && i == len(lines)-1 {
			continue // 跳过最后的空行
		}

		var styledLine string
		if isError {
			styledLine = styles.BaseStyle().Foreground(t.Error()).Render(line)
		} else {
			styledLine = styles.BaseStyle().Foreground(t.Text()).Render(line)
		}

		// 添加缩进
		if i == 0 {
			parts = append(parts, "  └ "+styledLine)
		} else {
			parts = append(parts, "    "+styledLine)
		}
	}

	return parts
}

// extractTagContent 提取标签内容的通用方法
func (h *BashHandler) extractTagContent(content, startTag, endTag string) string {
	start := strings.Index(content, startTag)
	end := strings.Index(content, endTag)
	if start >= 0 && end > start {
		return content[start+len(startTag) : end]
	}
	return ""
}

// CollectShellOutput 收集shell输出
func (h *BashHandler) CollectShellOutput(executionID, content string, isError bool) {
	if h.ExecutionState == nil || h.ExecutionState.ExecutionID != executionID {
		return
	}

	if isError {
		h.ExecutionState.Stderr += content + "\n"
	} else {
		h.ExecutionState.Stdout += content + "\n"
	}
}

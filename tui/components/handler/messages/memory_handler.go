package messages

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/message"
	messagestypes "github.com/qoder-ai/qodercli/tui/components/types/messages"
	tuiruntime "github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
)

// MemoryHandler 记忆操作处理器
type MemoryHandler struct {
	app   tuiruntime.AppRuntime
	width int
}

// NewMemoryHandler 创建新的记忆处理器
func NewMemoryHandler(app tuiruntime.AppRuntime, width int) *MemoryHandler {
	return &MemoryHandler{
		app:   app,
		width: width,
	}
}

// SetWidth 设置渲染宽度
func (h *MemoryHandler) SetWidth(width int) {
	h.width = width
}

// IsOperationMessage 判断是否为记忆操作消息
func (h *MemoryHandler) IsOperationMessage(msg message.Message) bool {
	content := msg.Content().Text
	return strings.Contains(content, "<user-memory-input>") ||
		strings.Contains(content, "<user-memory-result>") ||
		(msg.IsMeta && strings.Contains(content, "running local commands"))
}

// HandleOperationMessage 处理记忆操作消息
func (h *MemoryHandler) HandleOperationMessage(msg message.Message, messageOrder *[]string, renderedMessages map[string]*messagestypes.RenderedMessage, messages map[string]message.Message) tea.Cmd {
	content := msg.Content().Text

	// 处理记忆输入消息
	if strings.Contains(content, "<user-memory-input>") {
		// 查找对应的结果消息
		var location string
		var success bool
		var hasResult bool
		for _, resultMsg := range messages {
			if resultMsg.ParentId == msg.Id && strings.Contains(resultMsg.Content().Text, "<user-memory-result>") {
				location, success = h.ExtractResult(resultMsg.Content().Text)
				hasResult = true
				break
			}
		}

		// 渲染记忆操作
		memoryContent := h.ExtractContent(content)
		var rendered string
		if hasResult {
			// 有结果消息：显示完整的操作结果
			rendered = h.RenderOperation(memoryContent, location, success)
		} else {
			// 没有结果消息：显示等待状态
			rendered = h.RenderPendingOperation(memoryContent)
		}

		*messageOrder = append(*messageOrder, msg.Id)
		renderedMessages[msg.Id] = &messagestypes.RenderedMessage{
			ID:           msg.Id,
			Role:         "memory",
			RenderedText: rendered,
			IsFinished:   hasResult, // 只有有结果时才标记为完成
		}
	}

	// 处理记忆结果消息
	if strings.Contains(content, "<user-memory-result>") {
		// 获取对应的输入消息ID
		inputMsgID := msg.ParentId
		if inputMsgID == "" {
			return nil
		}

		// 查找输入消息
		inputMsg, ok := messages[inputMsgID]
		if !ok {
			return nil
		}

		// 提取记忆内容和结果
		memoryContent := h.ExtractContent(inputMsg.Content().Text)
		location, success := h.ExtractResult(content)

		// 重新渲染输入消息（更新为完成状态）
		rendered := h.RenderOperation(memoryContent, location, success)
		renderedMessages[inputMsgID] = &messagestypes.RenderedMessage{
			ID:           inputMsgID,
			Role:         "memory",
			RenderedText: rendered,
			IsFinished:   true,
		}
	}

	return nil
}

// HandleMessage 处理记忆相关的tea消息
func (h *MemoryHandler) HandleMessage(msg tea.Msg, session core.Session, renderedMessages map[string]*messagestypes.RenderedMessage, messages map[string]message.Message) tea.Cmd {
	switch msg := msg.(type) {
	case messagestypes.MemoryOperationStartMsg:
		return h.handleOperationStart(session, msg.Content)
	case messagestypes.MemoryOperationCompleteMsg:
		return h.handleOperationComplete(session, msg.Content, msg.Location, msg.Success, msg.InputMsgID)
	}
	return nil
}

// handleOperationStart 处理记忆操作开始
func (h *MemoryHandler) handleOperationStart(session core.Session, content string) tea.Cmd {
	ctx := context.Background()

	// 发送记忆操作开始的三条消息
	inputMsgID, err := h.sendMemoryOperationStart(ctx, session.Id, content, nil)
	if err != nil {
		return func() tea.Msg {
			return messagestypes.MemoryOperationCompleteMsg{
				Content:    content,
				Location:   "",
				Success:    false,
				InputMsgID: nil,
			}
		}
	}

	// 触发记忆选择器
	return func() tea.Msg {
		return MemorySelectorTriggerMsg{
			Content:    content,
			InputMsgID: inputMsgID,
		}
	}
}

// handleOperationComplete 处理记忆操作完成
func (h *MemoryHandler) handleOperationComplete(session core.Session, content, location string, success bool, inputMsgID *string) tea.Cmd {
	ctx := context.Background()

	// 使用传递的InputMsgID作为Result消息的parent
	err := h.sendMemoryOperationComplete(ctx, session.Id, location, success, inputMsgID)
	if err != nil {
		// 如果发送失败，至少记录错误
		fmt.Printf("Failed to send memory operation complete message: %v\n", err)
	}

	return nil
}

// sendMemoryOperationStart 发送记忆操作开始的三条消息
func (h *MemoryHandler) sendMemoryOperationStart(ctx context.Context, sessionId, content string, parentId *string) (*string, error) {
	// 1. Meta消息
	metaContent := "Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."
	var parentIdStr string
	if parentId != nil {
		parentIdStr = *parentId
	}

	metaMsg, err := h.app.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.User,
		Parts:    []message.ContentPart{message.TextContent{Text: metaContent, Type: "text"}},
		ParentId: parentIdStr,
		IsMeta:   true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create memory meta message: %w", err)
	}

	// 2. 输入消息
	inputContent := fmt.Sprintf("<user-memory-input>%s</user-memory-input>", content)
	inputMsg, err := h.app.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.User,
		Parts:    []message.ContentPart{message.TextContent{Text: inputContent, Type: "text"}},
		ParentId: metaMsg.Id,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create memory input message: %w", err)
	}

	return &inputMsg.Id, nil
}

// sendMemoryOperationComplete 发送记忆操作完成消息
func (h *MemoryHandler) sendMemoryOperationComplete(ctx context.Context, sessionId, location string, success bool, parentId *string) error {
	var resultContent string
	if success {
		resultContent = fmt.Sprintf("<user-memory-result>Saved to %s</user-memory-result>", location)
	} else {
		resultContent = "<user-memory-result>Failed to save</user-memory-result>"
	}

	var parentIdStr string
	if parentId != nil {
		parentIdStr = *parentId
	}

	_, err := h.app.CreateMessage(ctx, sessionId, message.CreateMessageParams{
		Role:     message.User,
		Parts:    []message.ContentPart{message.TextContent{Text: resultContent, Type: "text"}},
		ParentId: parentIdStr,
	})
	if err != nil {
		return fmt.Errorf("failed to create memory result message: %w", err)
	}

	return nil
}

// ExtractContent 从消息内容中提取记忆内容
func (h *MemoryHandler) ExtractContent(content string) string {
	re := regexp.MustCompile(`<user-memory-input>(.*?)</user-memory-input>`)
	matches := re.FindStringSubmatch(content)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// ExtractResult 从消息内容中提取结果信息
func (h *MemoryHandler) ExtractResult(content string) (location string, success bool) {
	re := regexp.MustCompile(`<user-memory-result>(.*?)</user-memory-result>`)
	matches := re.FindStringSubmatch(content)
	if len(matches) > 1 {
		result := matches[1]
		if strings.Contains(result, "Saved to") {
			// 提取位置信息
			locationRe := regexp.MustCompile(`Saved to (.+)`)
			locationMatches := locationRe.FindStringSubmatch(result)
			if len(locationMatches) > 1 {
				return locationMatches[1], true
			}
			return "unknown location", true
		}
		return "", false
	}
	return "", false
}

// RenderOperation 渲染记忆操作
func (h *MemoryHandler) RenderOperation(content, location string, success bool) string {
	t := theme.CurrentTheme()

	// 记忆内容行
	prefixStyle := styles.BaseStyle().Foreground(t.Primary())
	contentStyle := styles.BaseStyle().Foreground(t.Text())
	line1 := prefixStyle.Render("# ") + contentStyle.Render(content)

	// 结果行
	resultStyle := styles.BaseStyle().Foreground(t.TextMuted())
	var line2 string

	// 检查是否为取消状态
	if !success && location == "CANCELLED" {
		cancelStyle := styles.BaseStyle().Foreground(t.TextMuted())
		line2 = cancelStyle.Render("  └ Cancelled")
	} else if success && location != "" {
		line2 = resultStyle.Render("  └ Saved to " + location)
	} else if success {
		line2 = resultStyle.Render("  └ Got it.")
	} else {
		errorStyle := styles.BaseStyle().Foreground(t.Error())
		line2 = errorStyle.Render("  └ Failed to save")
	}

	return line1 + "\n" + line2
}

// RenderPendingOperation 渲染等待中的记忆操作
func (h *MemoryHandler) RenderPendingOperation(content string) string {
	t := theme.CurrentTheme()

	// 记忆内容行
	prefixStyle := styles.BaseStyle().Foreground(t.Primary())
	contentStyle := styles.BaseStyle().Foreground(t.Text())
	line1 := prefixStyle.Render("# ") + contentStyle.Render(content)

	// 等待状态行
	pendingStyle := styles.BaseStyle().Foreground(t.TextMuted())
	line2 := pendingStyle.Render("  └ Selecting target...")

	return line1 + "\n" + line2
}

// MemorySelectorTriggerMsg 触发记忆选择器消息
type MemorySelectorTriggerMsg struct {
	Content    string
	InputMsgID *string
}

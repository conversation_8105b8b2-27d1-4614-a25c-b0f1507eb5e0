package messages

import (
	"fmt"
	"regexp"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core/message"
	messagestypes "github.com/qoder-ai/qodercli/tui/components/types/messages"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
)

// CommandHandler 处理本地命令消息的渲染逻辑
type CommandHandler struct {
	width int
}

// NewCommandHandler 创建新的命令处理器
func NewCommandHandler() messagestypes.CommandHandler {
	return &CommandHandler{
		width: 80, // 默认宽度
	}
}

// SetWidth 设置渲染宽度
func (h *CommandHandler) SetWidth(width int) {
	h.width = width
}

// IsCommandMessage 判断是否为命令消息
func (h *CommandHandler) IsCommandMessage(msg message.Message) bool {
	if msg.IsMeta {
		return true
	}

	content := msg.Content().String()
	// 检查是否包含命令相关的XML标签
	return strings.Contains(content, "<command-message>") ||
		strings.Contains(content, "<command-name>") ||
		strings.Contains(content, "<local-command-stdout>") ||
		strings.Contains(content, "<system-reminder>")
}

// HandleCommandMessage 处理命令消息
func (h *CommandHandler) HandleCommandMessage(msg message.Message, messageOrder *[]string, renderedMessages map[string]*messagestypes.RenderedMessage, messages map[string]message.Message) tea.Cmd {
	// 如果是Meta消息，跳过渲染
	if msg.IsMeta {
		return nil
	}

	// 存储消息
	messages[msg.Id] = msg

	content := msg.Content().String()

	if strings.Contains(content, "<command-message>") {
		// 命令信息消息：解析并准备渲染
		cmdInfo := h.ParseCommandContent(content)

		// 查找对应的结果消息
		resultMsg := h.findResultMessage(msg, messages)
		if resultMsg != nil {
			// 合并命令信息和结果
			resultInfo := h.ParseCommandContent(resultMsg.Content().String())
			cmdInfo.Output = resultInfo.Output
			cmdInfo.Error = resultInfo.Error
		}

		// 渲染命令
		*messageOrder = append(*messageOrder, msg.Id)
		rendered := h.RenderCommand(cmdInfo)
		rendered.ID = msg.Id
		renderedMessages[msg.Id] = rendered

	} else if strings.Contains(content, "<local-command-stdout>") {
		// 结果消息：查找对应的命令消息并更新渲染
		cmdMsg := h.findCommandMessage(msg, messages)
		if cmdMsg != nil {
			cmdInfo := h.ParseCommandContent(cmdMsg.Content().String())
			resultInfo := h.ParseCommandContent(content)
			cmdInfo.Output = resultInfo.Output
			cmdInfo.Error = resultInfo.Error

			// 更新命令消息的渲染
			rendered := h.RenderCommand(cmdInfo)
			rendered.ID = cmdMsg.Id
			renderedMessages[cmdMsg.Id] = rendered
		}
	}

	return nil
}

// ParseCommandContent 解析命令内容中的XML标签
func (h *CommandHandler) ParseCommandContent(content string) messagestypes.CommandInfo {
	info := messagestypes.CommandInfo{}

	// 解析命令消息 - 使用 (?s) 标志让 . 匹配换行符
	if match := regexp.MustCompile(`(?s)<command-message>(.*?)</command-message>`).FindStringSubmatch(content); len(match) > 1 {
		info.Message = strings.TrimSpace(match[1])
	}

	// 解析命令名称
	if match := regexp.MustCompile(`(?s)<command-name>(.*?)</command-name>`).FindStringSubmatch(content); len(match) > 1 {
		info.CommandName = strings.TrimSpace(match[1])
	}

	// 解析命令参数
	if match := regexp.MustCompile(`(?s)<command-args>(.*?)</command-args>`).FindStringSubmatch(content); len(match) > 1 {
		info.CommandArgs = strings.TrimSpace(match[1])
	}

	// 解析命令输出 - 使用 (?s) 标志让 . 匹配换行符
	if match := regexp.MustCompile(`(?s)<local-command-stdout>(.*?)</local-command-stdout>`).FindStringSubmatch(content); len(match) > 1 {
		info.Output = strings.TrimSpace(match[1])
	}

	// 解析错误信息
	if strings.Contains(content, "Error: ") {
		lines := strings.Split(content, "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "Error: ") {
				info.Error = strings.TrimPrefix(line, "Error: ")
				break
			}
		}
	}

	return info
}

// RenderCommand 渲染命令信息
func (h *CommandHandler) RenderCommand(cmdInfo messagestypes.CommandInfo) *messagestypes.RenderedMessage {
	t := theme.CurrentTheme()
	var parts []string

	// 1. 渲染命令头部
	commandHeader := h.renderCommandHeader(cmdInfo)
	if commandHeader != "" {
		parts = append(parts, commandHeader)
	}

	// 2. 渲染错误信息（如果有）
	if cmdInfo.Error != "" {
		errorStyle := styles.BaseStyle().
			Foreground(t.Error()).
			Bold(true).
			PaddingLeft(0)

		errorText := fmt.Sprintf("Error: %s", cmdInfo.Error)
		parts = append(parts, errorStyle.Render(errorText))
	}

	// 3. 渲染命令输出
	if cmdInfo.Output != "" {
		outputRendered := h.renderCommandOutput(cmdInfo.Output)
		if outputRendered != "" {
			parts = append(parts, outputRendered)
		}
	}

	renderedText := strings.Join(parts, "\n")

	return &messagestypes.RenderedMessage{
		Role:         "user",
		RenderedText: renderedText,
		IsFinished:   true,
	}
}

// renderCommandHeader 渲染命令头部
func (h *CommandHandler) renderCommandHeader(cmdInfo messagestypes.CommandInfo) string {
	t := theme.CurrentTheme()

	// 构建命令行显示
	var cmdLine strings.Builder
	if cmdInfo.CommandName != "" {
		cmdLine.WriteString(cmdInfo.CommandName)
	} else if cmdInfo.Message != "" {
		// 如果没有命令名称，使用消息内容
		cmdLine.WriteString(cmdInfo.Message)
	}

	if cmdInfo.CommandArgs != "" {
		cmdLine.WriteString(" ")
		cmdLine.WriteString(cmdInfo.CommandArgs)
	}

	// 应用样式
	indicatorStyle := styles.BaseStyle().
		Foreground(t.Secondary()).
		PaddingLeft(0)

	cmdStyle := styles.BaseStyle().
		Foreground(t.Text()).
		PaddingLeft(0)

	return indicatorStyle.Render("> ") + cmdStyle.Render(cmdLine.String())
}

// renderCommandOutput 渲染命令输出
func (h *CommandHandler) renderCommandOutput(output string) string {
	if output == "" {
		return ""
	}

	t := theme.CurrentTheme()

	// 使用markdown渲染器处理输出
	r := styles.GetMarkdownRenderer(h.width - 4)
	rendered, _ := r.Render(output)

	// 添加树状前缀对齐，与bash命令保持一致
	lines := strings.Split(strings.TrimSpace(rendered), "\n")
	var processedLines []string

	outputStyle := styles.BaseStyle().
		Foreground(t.Text()).
		PaddingLeft(0)

	for i, line := range lines {
		if strings.TrimSpace(line) == "" && i == len(lines)-1 {
			continue // 跳过最后的空行
		}

		// 去除markdown渲染后的多余换行符
		cleanLine := strings.TrimRight(line, "\n")

		// 添加树状缩进
		if i == 0 {
			processedLines = append(processedLines, outputStyle.Render("  └ "+cleanLine))
		} else {
			processedLines = append(processedLines, outputStyle.Render("    "+cleanLine))
		}
	}

	return strings.Join(processedLines, "\n")
}

// findResultMessage 查找对应的结果消息
func (h *CommandHandler) findResultMessage(cmdMsg message.Message, messages map[string]message.Message) *message.Message {
	// 遍历所有消息，查找parent_id为cmdMsg.Id的结果消息
	for _, msg := range messages {
		if msg.ParentId == cmdMsg.Id && strings.Contains(msg.Content().String(), "<local-command-stdout>") {
			return &msg
		}
	}
	return nil
}

// findCommandMessage 查找对应的命令消息
func (h *CommandHandler) findCommandMessage(resultMsg message.Message, messages map[string]message.Message) *message.Message {
	// 查找parent_id匹配的命令消息
	if resultMsg.ParentId != "" {
		if msg, ok := messages[resultMsg.ParentId]; ok && strings.Contains(msg.Content().String(), "<command-message>") {
			return &msg
		}
	}
	return nil
}

package editor

import (
	"os"
	"os/exec"
	"path/filepath"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core/llm/command"
	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"
	"github.com/qoder-ai/qodercli/tui/util"
)

// ExternalEditorHandlerImpl 外部编辑器处理器实现
type ExternalEditorHandlerImpl struct{}

// NewExternalEditorHandler 创建新的外部编辑器处理器
func NewExternalEditorHandler() editortypes.ExternalEditorHandler {
	return &ExternalEditorHandlerImpl{}
}

// GetDefaultEditor 获取默认编辑器
func (h *ExternalEditorHandlerImpl) GetDefaultEditor() string {
	editor := os.Getenv("EDITOR")
	if editor == "" {
		editor = "vim"
	}
	return editor
}

// OpenExternalEditor 打开外部编辑器
func (h *ExternalEditorHandlerImpl) OpenExternalEditor(payload command.Event) tea.Cmd {
	editor := h.GetDefaultEditor()

	tmpfile, err := os.CreateTemp("", "msg_*.md")
	if err != nil {
		// 发送错误结果给命令
		if payload.InputChan != nil {
			payload.InputChan <- map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			}
		}
		return util.ReportError(err)
	}
	tmpfile.Close()

	c := exec.Command(editor, tmpfile.Name()) //nolint:gosec
	c.Stdin = os.Stdin
	c.Stdout = os.Stdout
	c.Stderr = os.Stderr

	return tea.ExecProcess(c, func(err error) tea.Msg {
		defer os.Remove(tmpfile.Name())

		if err != nil {
			// 发送错误结果给命令
			if payload.InputChan != nil {
				payload.InputChan <- map[string]interface{}{
					"success": false,
					"error":   err.Error(),
				}
			}
			return util.ReportError(err)
		}

		content, err := os.ReadFile(tmpfile.Name())
		if err != nil {
			// 发送错误结果给命令
			if payload.InputChan != nil {
				payload.InputChan <- map[string]interface{}{
					"success": false,
					"error":   err.Error(),
				}
			}
			return util.ReportError(err)
		}

		// 发送结果给命令
		if payload.InputChan != nil {
			if len(content) == 0 {
				// 内容为空时也标记为成功，但不包含content字段，让命令自己决定如何处理
				payload.InputChan <- map[string]interface{}{
					"success": true,
				}
			} else {
				payload.InputChan <- map[string]interface{}{
					"success": true,
					"content": string(content),
				}
			}
		}

		// 使用ForceRenderFromStartMsg彻底清理包括回滚缓冲区的所有内容
		return editortypes.ForceRenderFromStartMsg{}
	})
}

// OpenExternalEditorWithFile 打开外部编辑器编辑文件
func (h *ExternalEditorHandlerImpl) OpenExternalEditorWithFile(filePath string, inputChan chan any) tea.Cmd {
	editor := h.GetDefaultEditor()

	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		// 发送错误结果给命令
		if inputChan != nil {
			inputChan <- map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			}
		}
		return util.ReportError(err)
	}

	c := exec.Command(editor, filePath) //nolint:gosec
	c.Stdin = os.Stdin
	c.Stdout = os.Stdout
	c.Stderr = os.Stderr

	// 退出vim编辑器时先重绘UI防止重影
	return tea.ExecProcess(c, func(err error) tea.Msg {
		if err != nil {
			// 发送错误结果给命令
			if inputChan != nil {
				inputChan <- map[string]interface{}{
					"success": false,
					"error":   err.Error(),
				}
			}
			return util.ReportError(err)
		}

		// 文件编辑成功，发送成功结果
		if inputChan != nil {
			inputChan <- map[string]interface{}{
				"success": true,
				"message": "Memory file edited successfully",
			}
		}

		// 使用ForceRenderFromStartMsg彻底清理包括回滚缓冲区的所有内容
		return editortypes.ForceRenderFromStartMsg{}
	})
}

// CreateTempFile 创建临时文件
func (h *ExternalEditorHandlerImpl) CreateTempFile(prefix, suffix string) (*os.File, error) {
	return os.CreateTemp("", prefix+"*"+suffix)
}

// ExecuteEditor 执行编辑器命令
func (h *ExternalEditorHandlerImpl) ExecuteEditor(editor, filePath string) *exec.Cmd {
	c := exec.Command(editor, filePath) //nolint:gosec
	c.Stdin = os.Stdin
	c.Stdout = os.Stdout
	c.Stderr = os.Stderr
	return c
}

// ReadFileContent 读取文件内容
func (h *ExternalEditorHandlerImpl) ReadFileContent(filePath string) ([]byte, error) {
	return os.ReadFile(filePath)
}

// SendResult 发送结果到输入通道
func (h *ExternalEditorHandlerImpl) SendResult(inputChan chan any, success bool, data map[string]interface{}) {
	if inputChan == nil {
		return
	}

	result := map[string]interface{}{
		"success": success,
	}

	for k, v := range data {
		result[k] = v
	}

	inputChan <- result
}

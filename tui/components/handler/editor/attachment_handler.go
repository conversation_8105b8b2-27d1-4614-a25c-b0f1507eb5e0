package editor

import (
	"bytes"
	"fmt"
	"image/jpeg"
	"image/png"
	"net/http"
	"slices"
	"strings"
	"unicode/utf8"

	"github.com/charmbracelet/lipgloss"
	"github.com/google/uuid"
	"github.com/qoder-ai/qodercli/core/message"
	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"
	"github.com/qoder-ai/qodercli/tui/styles"
	"github.com/qoder-ai/qodercli/tui/theme"
)

const (
	maxAttachmentSizeBytes = int64(32 * 1024 * 1024) // 5MB
	imageSizeToCompress    = int64(256 * 1024)       // 256KB
)

// AttachmentHandlerImpl 附件处理器实现
type AttachmentHandlerImpl struct{}

// NewAttachmentHandler 创建新的附件处理器
func NewAttachmentHandler() editortypes.AttachmentHandler {
	return &AttachmentHandlerImpl{}
}

// AddAttachment 添加附件
func (h *AttachmentHandlerImpl) AddAttachment(attachment message.Attachment, currentAttachments []message.Attachment) ([]message.Attachment, error) {
	// 检查数量限制
	if len(currentAttachments) >= editortypes.MaxAttachments {
		return currentAttachments, fmt.Errorf("can't add more than %d attachments", editortypes.MaxAttachments)
	}

	// 去重：若同路径已存在，则忽略
	for _, existing := range currentAttachments {
		if existing.FilePath == attachment.FilePath && attachment.FilePath != "" {
			return currentAttachments, nil // 重复附件，忽略
		}
	}

	// 验证附件
	if err := h.ValidateAttachment(attachment); err != nil {
		return currentAttachments, err
	}

	newAttachments := append(currentAttachments, attachment)
	return newAttachments, nil
}

// DeleteAttachment 删除附件
func (h *AttachmentHandlerImpl) DeleteAttachment(index int, attachments []message.Attachment) []message.Attachment {
	if index < 0 || index >= len(attachments) {
		return attachments
	}

	if index == 0 {
		return attachments[1:]
	}
	return slices.Delete(attachments, index, index+1)
}

// DeleteAllAttachments 删除所有附件
func (h *AttachmentHandlerImpl) DeleteAllAttachments() []message.Attachment {
	return nil
}

// ProcessImageAttachment 处理图片附件
func (h *AttachmentHandlerImpl) ProcessImageAttachment(imageData []byte) (message.Attachment, error) {
	if int64(len(imageData)) > maxAttachmentSizeBytes {
		return message.Attachment{}, fmt.Errorf("file too large, max 5MB")
	}

	mimeBufferSize := min(512, len(imageData))
	mimeType := http.DetectContentType(imageData[:mimeBufferSize])

	// 如果图片太大了，要压缩下防止上下文超长
	if mimeType == "image/png" && int64(len(imageData)) > imageSizeToCompress {
		img, err := png.Decode(bytes.NewReader(imageData))
		if err == nil {
			quality := 100 * int(imageSizeToCompress) / len(imageData)
			output := bytes.NewBuffer([]byte{})
			// 用jpeg压缩
			err = jpeg.Encode(output, img, &jpeg.Options{
				Quality: quality,
			})
			if err == nil {
				imageData = output.Bytes()
				mimeType = "image/jpeg"
			}
		}
	}

	fileName := "image"
	attachment := message.Attachment{
		FilePath: uuid.NewString(),
		FileName: fileName,
		MimeType: mimeType,
		Content:  imageData,
	}

	return attachment, nil
}

// RenderAttachments 渲染附件列表
func (h *AttachmentHandlerImpl) RenderAttachments(attachments []message.Attachment, deleteMode bool, width int) string {
	if len(attachments) == 0 {
		return ""
	}

	var styledAttachments []string
	t := theme.CurrentTheme()
	attachmentStyles := styles.BaseStyle().Foreground(t.Text())

	// 基于 FilePath 去重显示
	seen := make(map[string]bool)
	for i, attachment := range attachments {
		key := attachment.FilePath
		if key == "" {
			// 无路径（如剪贴板图片），用文件名做键
			key = attachment.FileName
		}
		if seen[key] {
			continue
		}
		seen[key] = true

		var filename string
		if utf8.RuneCountInString(attachment.FileName) > 15 {
			runes := []rune(attachment.FileName)
			filename = fmt.Sprintf(" %s %s...", styles.DocumentIcon, string(runes[:12]))
		} else {
			filename = fmt.Sprintf(" %s %s", styles.DocumentIcon, attachment.FileName)
		}

		if deleteMode {
			filename = fmt.Sprintf("%d:%s ", i, filename)
		}

		styledAttachments = append(styledAttachments, attachmentStyles.Render(filename))
	}

	content := lipgloss.JoinHorizontal(lipgloss.Left, styledAttachments...)
	return content
}

// ValidateAttachment 验证附件
func (h *AttachmentHandlerImpl) ValidateAttachment(attachment message.Attachment) error {
	// 检查大小限制
	if int64(len(attachment.Content)) > maxAttachmentSizeBytes {
		return fmt.Errorf("file too large, max 32MB")
	}

	// 检查文件名
	if strings.TrimSpace(attachment.FileName) == "" {
		return fmt.Errorf("attachment filename cannot be empty")
	}

	return nil
}

// HandleAttachmentDelete 处理附件删除按键
func (h *AttachmentHandlerImpl) HandleAttachmentDelete(key rune, attachments []message.Attachment) []message.Attachment {
	if key < '0' || key > '9' {
		return attachments
	}

	num := int(key - '0')
	if num >= len(attachments) {
		return attachments
	}

	return h.DeleteAttachment(num, attachments)
}

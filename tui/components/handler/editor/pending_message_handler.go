package editor

import (
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qodercli/core/message"
	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"
	hinttypes "github.com/qoder-ai/qodercli/tui/components/types/hint"
	"github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/theme"
	"github.com/qoder-ai/qodercli/tui/util"
)

// PendingMessageHandlerImpl 待发送消息处理器实现
type PendingMessageHandlerImpl struct{}

// NewPendingMessageHandler 创建新的待发送消息处理器
func NewPendingMessageHandler() editortypes.PendingMessageHandler {
	return &PendingMessageHandlerImpl{}
}

// SetPendingMessage 设置待发送消息
func (h *PendingMessageHandlerImpl) SetPendingMessage(
	text string,
	attachments []message.Attachment,
	currentPending *editortypes.QueuedMessage,
	historyHandler editortypes.HistoryHandler,
	mode editortypes.EditorMode,
	app runtime.AppRuntime,
) (*editortypes.QueuedMessage, tea.Cmd) {
	var pendingMsg *editortypes.QueuedMessage

	if currentPending == nil {
		// 创建新的待发送消息
		pendingMsg = &editortypes.QueuedMessage{
			Text:        text,
			Attachments: make([]message.Attachment, 0, editortypes.MaxAttachments),
			Timestamp:   time.Now(),
		}
	} else {
		// 追加到现有待发送消息
		pendingMsg = currentPending
		if strings.TrimSpace(pendingMsg.Text) != "" && strings.TrimSpace(text) != "" {
			// 使用真正的换行符追加新内容，确保多行消息格式正确
			pendingMsg.Text += "\n" + text
		} else if strings.TrimSpace(text) != "" {
			pendingMsg.Text = text
		}
		// 更新时间戳
		pendingMsg.Timestamp = time.Now()
	}

	// 合并附件，遵循数量限制
	h.mergeAttachmentsToPending(pendingMsg, attachments)

	// 保存到历史
	historyHandler.SaveToHistory(text, attachments, mode, app)

	return pendingMsg, util.CmdHandler(hinttypes.SwitchEditorStatusMsg{
		Empty: true,
	})
}

// ProcessPending 处理待发送消息
func (h *PendingMessageHandlerImpl) ProcessPending(
	pendingMessage *editortypes.QueuedMessage,
	sessionId string,
	app runtime.AppRuntime,
) (*editortypes.QueuedMessage, tea.Cmd) {
	// 如果没有待发送消息，不做任何处理
	if pendingMessage == nil {
		return nil, nil
	}

	// 检查Agent是否仍然忙碌
	if sessionId != "" {
		agent := app.GetCoderAgent()
		if agentImpl, ok := agent.(interface {
			IsSessionBusy(string) bool
		}); ok && agentImpl.IsSessionBusy(sessionId) {
			// 仍然忙碌，稍后重试
			return pendingMessage, tea.Tick(500*time.Millisecond, func(time.Time) tea.Msg {
				return editortypes.ProcessPendingMsg{}
			})
		}
	}

	// Agent空闲，发送待发送消息
	return nil, util.CmdHandler(editortypes.SendMsg{
		Text:        pendingMessage.Text,
		Attachments: pendingMessage.Attachments,
	})
}

// ClearPending 清空待发送消息
func (h *PendingMessageHandlerImpl) ClearPending() *editortypes.QueuedMessage {
	return nil
}

// RenderPending 渲染待发送消息
func (h *PendingMessageHandlerImpl) RenderPending(
	pendingMessage *editortypes.QueuedMessage,
	attachmentHandler editortypes.AttachmentHandler,
	width int,
) string {
	if pendingMessage == nil {
		return ""
	}

	t := theme.CurrentTheme()
	var lines []string
	lines = append(lines, "\n")

	// 内容行
	text := strings.TrimSpace(pendingMessage.Text)
	if text != "" {
		contentStyle := lipgloss.NewStyle().
			Foreground(t.Text()).
			PaddingLeft(2)

		contentLines := strings.Split(text, "\n")
		for _, line := range contentLines {
			if strings.TrimSpace(line) != "" {
				lines = append(lines, contentStyle.Render(line))
			}
		}
	}

	// 附件行
	if len(pendingMessage.Attachments) > 0 {
		attachmentLines := attachmentHandler.RenderAttachments(pendingMessage.Attachments, false, width)
		if attachmentLines != "" {
			// 添加左侧缩进以保持与内容对齐
			indentedAttachments := lipgloss.NewStyle().PaddingLeft(2).Render(attachmentLines)
			lines = append(lines, indentedAttachments)
		}
	}

	// 清空提示行
	clearHint := "Press Ctrl+Q to clear"
	hintStyle := lipgloss.NewStyle().
		Foreground(t.TextMuted()).
		Italic(true).
		PaddingLeft(2)
	lines = append(lines, hintStyle.Render(clearHint))

	return strings.Join(lines, "\n")
}

// CalculateAttachmentLimit 计算附件数量限制
func (h *PendingMessageHandlerImpl) CalculateAttachmentLimit(
	currentAttachments []message.Attachment,
	pendingMessage *editortypes.QueuedMessage,
) (bool, int) {
	totalAttachments := len(currentAttachments)
	if pendingMessage != nil {
		totalAttachments += len(pendingMessage.Attachments)
	}

	exceeded := totalAttachments >= editortypes.MaxAttachments
	return exceeded, totalAttachments
}

// mergeAttachmentsToPending 合并附件到待发送消息，遵循数量限制
func (h *PendingMessageHandlerImpl) mergeAttachmentsToPending(pendingMsg *editortypes.QueuedMessage, newAttachments []message.Attachment) {
	for _, newAttach := range newAttachments {
		// 检查是否已存在
		exists := false
		for _, existing := range pendingMsg.Attachments {
			if existing.FilePath == newAttach.FilePath && newAttach.FilePath != "" {
				exists = true
				break
			}
		}

		// 如果不存在且未达到限制，则添加
		if !exists && len(pendingMsg.Attachments) < editortypes.MaxAttachments {
			pendingMsg.Attachments = append(pendingMsg.Attachments, newAttach)
		}
	}
}

package editor

import (
	hinttypes "github.com/qoder-ai/qodercli/tui/components/types/hint"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	coreconfig "github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/logging"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/tui/components/chat/textarea"
	editortypes "github.com/qoder-ai/qodercli/tui/components/types/editor"
	"github.com/qoder-ai/qodercli/tui/runtime"
	"github.com/qoder-ai/qodercli/tui/util"
)

const maxAttachmentSize = int64(5 * 1024 * 1024)

// HistoryHandlerImpl 历史处理器实现
type HistoryHandlerImpl struct{}

// NewHistoryHandler 创建新的历史处理器
func NewHistoryHandler() editortypes.HistoryHandler {
	return &HistoryHandlerImpl{}
}

// LoadHistory 加载历史记录
func (h *HistoryHandlerImpl) LoadHistory(app runtime.AppRuntime) []editortypes.HistoryEntry {
	return app.GetHistoryList()
}

// ApplyHistoryEntry 应用历史记录条目
func (h *HistoryHandlerImpl) ApplyHistoryEntry(entry editortypes.HistoryEntry, textarea *textarea.Model) (editortypes.EditorMode, []tea.Cmd) {
	var cmds []tea.Cmd
	content := entry.Display
	mode := editortypes.ModeNormal

	// 根据前缀确定模式
	if strings.HasPrefix(content, "!") {
		content = content[1:]
		mode = editortypes.ModeBash
		cmds = append(cmds, util.CmdHandler(editortypes.BashModeChangedMsg{Active: true}))
	} else if strings.HasPrefix(content, "#") {
		content = content[1:]
		mode = editortypes.ModeMemory
		cmds = append(cmds, util.CmdHandler(editortypes.MemoryModeChangedMsg{Active: true}))
	} else {
		cmds = append(cmds,
			util.CmdHandler(hinttypes.SwitchEditorStatusMsg{Empty: false}),
			util.CmdHandler(editortypes.BashModeChangedMsg{Active: false}),
			util.CmdHandler(editortypes.MemoryModeChangedMsg{Active: false}),
		)
	}

	textarea.SetValue(content)
	return mode, cmds
}

// SaveToHistory 保存到历史记录
func (h *HistoryHandlerImpl) SaveToHistory(content string, attachments []message.Attachment, mode editortypes.EditorMode, app runtime.AppRuntime) {
	var display string
	switch mode {
	case editortypes.ModeBash:
		display = "!" + content
	case editortypes.ModeMemory:
		display = "#" + content
	default:
		display = content
	}

	// 只保存文件型附件到历史
	pasted := make([]coreconfig.PastedFileInfo, 0, len(attachments))
	for _, att := range attachments {
		if att.FilePath != "" {
			pasted = append(pasted, coreconfig.PastedFileInfo{
				Path:     att.FilePath,
				MimeType: att.MimeType,
			})
		}
	}

	_ = app.AppendHistory(coreconfig.HistoryEntry{
		Display:        display,
		PastedContents: pasted,
	})
}

// HandleHistoryNavigation 处理历史浏览
func (h *HistoryHandlerImpl) HandleHistoryNavigation(direction editortypes.HistoryDirection, currentIndex int, maxIndex int, textarea *textarea.Model) (int, []tea.Cmd) {
	var cmds []tea.Cmd
	newIndex := currentIndex

	switch direction {
	case editortypes.HistoryUp:
		if currentIndex > 0 {
			newIndex = currentIndex - 1
		}
	case editortypes.HistoryDown:
		if currentIndex < maxIndex-1 {
			newIndex = currentIndex + 1
		} else if currentIndex == maxIndex-1 {
			// 到达末尾，清空内容
			newIndex = maxIndex
			textarea.SetValue("")
			cmds = append(cmds,
				util.CmdHandler(editortypes.BashModeChangedMsg{Active: false}),
				util.CmdHandler(editortypes.MemoryModeChangedMsg{Active: false}),
			)
		}
	}

	return newIndex, cmds
}

// RestoreAttachmentsFromHistory 从历史记录恢复附件
func (h *HistoryHandlerImpl) RestoreAttachmentsFromHistory(entry editortypes.HistoryEntry) []message.Attachment {
	var attachments []message.Attachment

	for _, f := range entry.PastedContents {
		if f.Path == "" {
			continue
		}

		// 检查文件是否存在
		info, err := os.Stat(f.Path)
		if err != nil || info.IsDir() {
			continue
		}

		// 读取文件内容
		data, rerr := os.ReadFile(f.Path)
		if rerr != nil {
			continue
		}

		// 检查文件大小
		if int64(len(data)) > maxAttachmentSize {
			logging.ErrorPersist("file too large, max 5MB")
			continue
		}

		// 确定MIME类型
		mime := f.MimeType
		if mime == "" {
			bufSize := min(512, len(data))
			if bufSize > 0 {
				mime = http.DetectContentType(data[:bufSize])
			} else {
				mime = "application/octet-stream"
			}
		}

		// 创建附件
		base := filepath.Base(f.Path)
		attachments = append(attachments, message.Attachment{
			FilePath: f.Path,
			FileName: base,
			MimeType: mime,
			Content:  data,
		})
	}

	return attachments
}

package progress

import (
	"context"
	"strings"

	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/utils/diff"
	progresstypes "github.com/qoder-ai/qodercli/tui/components/types/progress"
	"github.com/qoder-ai/qodercli/tui/config"
	"github.com/qoder-ai/qodercli/tui/runtime"
)

// FileStatsHandlerImpl 文件变更统计处理器实现
type FileStatsHandlerImpl struct {
	app       runtime.AppRuntime
	fileStats map[string]struct{ Add, Del int }
}

// 确保实现了接口
var _ progresstypes.FileStatsHandler = (*FileStatsHandlerImpl)(nil)

// NewFileStatsHandler 创建文件变更统计处理器
func NewFileStatsHandler(app runtime.AppRuntime) *FileStatsHandlerImpl {
	return &FileStatsHandlerImpl{
		app:       app,
		fileStats: make(map[string]struct{ Add, Del int }),
	}
}

// UpdateFileStat 更新单个文件的变更统计
func (h *FileStatsHandlerImpl) UpdateFileStat(file core.File, sessionId string) error {
	if file.Version == core.InitialVersion {
		return nil
	}

	ctx := context.Background()
	files, err := h.app.ListBySession(ctx, sessionId)
	if err != nil {
		return err
	}

	var initial core.File
	for _, f := range files {
		if f.Path == file.Path && f.Version == core.InitialVersion {
			initial = f
			break
		}
	}

	if initial.Id == "" {
		return nil
	}

	if initial.Content == file.Content {
		delete(h.fileStats, h.trimWorkingDir(file.Path))
		return nil
	}

	_, adds, dels := diff.GenerateDiff(initial.Content, file.Content, file.Path, config.WorkingDirectory())
	display := h.trimWorkingDir(file.Path)
	if adds > 0 || dels > 0 {
		h.fileStats[display] = struct{ Add, Del int }{Add: adds, Del: dels}
	} else {
		delete(h.fileStats, display)
	}

	return nil
}

// RecomputeAll 全量重新计算会话的文件变更统计
func (h *FileStatsHandlerImpl) RecomputeAll(sessionId string) (map[string]struct{ Add, Del int }, error) {
	if h.app == nil || sessionId == "" {
		return nil, nil
	}

	ctx := context.Background()
	latest, err := h.app.ListLatestSessionFiles(ctx, sessionId)
	if err != nil {
		return nil, err
	}

	all, err := h.app.ListBySession(ctx, sessionId)
	if err != nil {
		return nil, err
	}

	// 索引初始版本
	initialByPath := map[string]core.File{}
	for _, f := range all {
		if f.Version == core.InitialVersion {
			initialByPath[f.Path] = f
		}
	}

	m := make(map[string]struct{ Add, Del int })
	for _, lf := range latest {
		if lf.Version == core.InitialVersion {
			continue
		}

		iv, ok := initialByPath[lf.Path]
		if !ok {
			continue
		}

		if iv.Content == lf.Content {
			continue
		}

		_, adds, dels := diff.GenerateDiff(iv.Content, lf.Content, lf.Path, config.WorkingDirectory())
		if adds > 0 || dels > 0 {
			display := h.trimWorkingDir(lf.Path)
			m[display] = struct{ Add, Del int }{Add: adds, Del: dels}
		}
	}

	return m, nil
}

// GetStats 获取当前的文件统计
func (h *FileStatsHandlerImpl) GetStats() map[string]struct{ Add, Del int } {
	return h.fileStats
}

// SetStats 设置文件统计
func (h *FileStatsHandlerImpl) SetStats(stats map[string]struct{ Add, Del int }) {
	h.fileStats = stats
}

// Reset 重置所有统计
func (h *FileStatsHandlerImpl) Reset() {
	h.fileStats = make(map[string]struct{ Add, Del int })
}

// AggregateTotals 聚合总计数据
func (h *FileStatsHandlerImpl) AggregateTotals() (files, add, del int) {
	files = len(h.fileStats)
	for _, st := range h.fileStats {
		add += st.Add
		del += st.Del
	}
	return
}

// trimWorkingDir 去除工作目录前缀
func (h *FileStatsHandlerImpl) trimWorkingDir(path string) string {
	wd := config.WorkingDirectory()
	res := strings.TrimPrefix(path, wd)
	res = strings.TrimPrefix(res, "/")
	return res
}

package interaction

// InteractionType 交互类型枚举
type InteractionType string

const (
	PermissionInteraction InteractionType = "permission"
	ConfirmInteraction    InteractionType = "confirm"
)

// UITexts UI文本配置
type UITexts struct {
	// 权限对话框文本
	PermissionTitle        string
	PermissionAllowText    string
	PermissionAllowSession string
	PermissionDenyText     string

	// 交互提示文本
	NavigationHint string
}

// DefaultUITexts 默认UI文本配置
var DefaultUITexts = UITexts{
	PermissionTitle:        "Permission Required",
	PermissionAllowText:    "Allow (a)",
	PermissionAllowSession: "Allow, and don't ask again this Session (s)",
	PermissionDenyText:     "Deny, and tell me what to do differently (d)",
	NavigationHint:         "Use ↑↓ to navigate, Enter to select",
}

package progress

import (
	"github.com/qoder-ai/qodercli/core"
)

// ProgressState 进度指示器的可视状态
type ProgressState int

const (
	ProgressStateHidden ProgressState = iota
	ProgressStateThinking
	ProgressStateGenerating
	ProgressStateSummarizing
)

// FileStatsHandler 文件变更统计处理器接口
type FileStatsHandler interface {
	// UpdateFileStat 更新单个文件的变更统计
	UpdateFileStat(file core.File, sessionId string) error

	// RecomputeAll 全量重新计算会话的文件变更统计
	RecomputeAll(sessionId string) (map[string]struct{ Add, Del int }, error)

	// GetStats 获取当前的文件统计
	GetStats() map[string]struct{ Add, Del int }

	// SetStats 设置文件统计
	SetStats(stats map[string]struct{ Add, Del int })

	// Reset 重置所有统计
	Reset()

	// AggregateTotals 聚合总计数据
	AggregateTotals() (files, add, del int)
}

// FileStatsRecomputedMsg 批量重算完成消息
type FileStatsRecomputedMsg struct {
	Stats map[string]struct{ Add, Del int }
}

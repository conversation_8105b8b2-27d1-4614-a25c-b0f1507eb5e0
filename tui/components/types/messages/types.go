package messages

import (
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qodercli/core"
	"github.com/qoder-ai/qodercli/core/message"
	tuievent "github.com/qoder-ai/qodercli/tui/event"
)

// ExpandMode 展开模式枚举
type ExpandMode int

const (
	ExpandModeNormal  ExpandMode = iota // 正常模式
	ExpandModePartial                   // 部分展开模式 (Ctrl+R) - 显示最近2条消息
	ExpandModeFull                      // 完全展开模式 (Ctrl+E) - 显示所有消息
)

// RenderedMessage 渲染后的消息
type RenderedMessage struct {
	ID           string
	Role         string
	RenderedText string // 预着色渲染后的完整文本
	IsFinished   bool
}

// BashExecutionState bash执行状态
type BashExecutionState struct {
	ExecutionID    string
	Command        string
	InputMsgID     *string
	Stdout         string
	Stderr         string
	LastUpdateTime time.Time // 上次更新UI的时间
}

// BashHandler 接口定义
type BashHandler interface {
	SetWidth(width int)
	HandleMessage(msg tea.Msg, session core.Session, renderedMessages map[string]*RenderedMessage, messages map[string]message.Message) tea.Cmd
	IsOperationMessage(msg message.Message) bool
	HandleOperationMessage(msg message.Message, messageOrder *[]string, renderedMessages map[string]*RenderedMessage, messages map[string]message.Message) tea.Cmd
	ProcessShellEvent(executionID string, eventCh <-chan tuievent.ShellEvent) (tea.Msg, bool)
	CollectShellOutput(executionID, content string, isError bool)
	GetExecutionState() *BashExecutionState
	// 重新渲染时需要的方法
	ExtractCommand(content string) string
	ExtractResult(content string) (stdout, stderr string)
	RenderOperation(command, stdout, stderr string) string
}

// CommandInfo 解析后的命令信息
type CommandInfo struct {
	CommandName string
	CommandArgs string
	Message     string
	Output      string
	Error       string
}

// CommandHandler 接口定义
type CommandHandler interface {
	SetWidth(width int)
	IsCommandMessage(msg message.Message) bool
	HandleCommandMessage(msg message.Message, messageOrder *[]string, renderedMessages map[string]*RenderedMessage, messages map[string]message.Message) tea.Cmd
	ParseCommandContent(content string) CommandInfo
	RenderCommand(cmdInfo CommandInfo) *RenderedMessage
}

// MemoryInfo 解析后的记忆信息
type MemoryInfo struct {
	Content  string
	Location string
	Success  bool
}

// MemoryHandler 接口定义
type MemoryHandler interface {
	SetWidth(width int)
	IsOperationMessage(msg message.Message) bool
	HandleOperationMessage(msg message.Message, messageOrder *[]string, renderedMessages map[string]*RenderedMessage, messages map[string]message.Message) tea.Cmd
	HandleMessage(msg tea.Msg, session core.Session, renderedMessages map[string]*RenderedMessage, messages map[string]message.Message) tea.Cmd
	// 重新渲染时需要的方法
	ExtractContent(content string) string
	ExtractResult(content string) (location string, success bool)
	RenderOperation(content, location string, success bool) string
	RenderPendingOperation(content string) string
}

// 消息类型定义
type (
	// BashAppendOutputMsg Shell输出追加消息
	BashAppendOutputMsg struct {
		ParentID string
		Chunk    string
	}

	// BashFinishedMsg Shell运行完成消息
	BashFinishedMsg struct {
		ParentID string
		ExitCode int
		Stdout   string
		Stderr   string
	}

	// BashOperationStartMsg 开始Bash操作消息
	BashOperationStartMsg struct {
		Command string
	}

	// BashOperationCompleteMsg 完成Bash操作消息
	BashOperationCompleteMsg struct {
		Command    string
		Stdout     string
		Stderr     string
		InputMsgID *string
	}

	// ShellEventListenerMsg Shell事件监听器消息
	ShellEventListenerMsg struct {
		ExecutionID string
		EventCh     <-chan tuievent.ShellEvent
	}

	// MemoryOperationStartMsg 开始记忆操作消息
	MemoryOperationStartMsg struct {
		Content string
	}

	// MemoryOperationCompleteMsg 完成记忆操作消息
	MemoryOperationCompleteMsg struct {
		Content    string
		Location   string // 保存的记忆文件位置
		Success    bool
		InputMsgID *string // Input消息ID，用于建立Result消息的父子关系
	}

	// MessagesLoadedMsg —— 历史消息加载完成
	MessagesLoadedMsg struct{ Messages []message.Message }

	// SessionChangedMsg —— 当前会话已切换
	SessionChangedMsg struct{ Session core.Session }

	// EnterExpandedModeMsg —— 进入展开模式
	EnterExpandedModeMsg struct {
		Mode ExpandMode // 展开模式类型
	}

	// ExitExpandedModeMsg —— 退出展开模式
	ExitExpandedModeMsg struct{}

	// ToggleFullExpansionMsg —— 切换完全展开模式
	ToggleFullExpansionMsg struct{}
)

package status

// FormatConfig 格式化配置
type FormatConfig struct {
	// 令牌格式化阈值
	TokenMegaThreshold int64
	TokenKiloThreshold int64

	// 成本格式化精度
	CostDecimalPlaces int

	// 警告阈值
	TokenWarningPercentage float64
}

// DefaultFormatConfig 默认格式化配置
var DefaultFormatConfig = FormatConfig{
	TokenMegaThreshold:     1_000_000,
	TokenKiloThreshold:     1_000,
	CostDecimalPlaces:      2,
	TokenWarningPercentage: 80.0,
}

// 登录状态相关配置
const (
	LoginRequiredMessage = "Not logged in yet, please log in using the \"/login\" command"
)

// 登录状态监听消息类型
type (
	// LoginCheckMsg 登录状态检查消息
	LoginCheckMsg struct{}

	// StartLoginMonitorMsg 开始登录状态监听
	StartLoginMonitorMsg struct{}

	// StopLoginMonitorMsg 停止登录状态监听
	StopLoginMonitorMsg struct{}
)

package hint

// HintState 定义提示状态
type HintState int

const (
	HintStateDefault HintState = iota
	HintStateExpanded
	HintStateMemory // 记忆状态
	HintQuitTip
	HintEscTip
	HintStateBash             // bash状态
	HintStateExpandedMessages // 展开消息模式
)

// SwitchStateMsg 切换退出提示
type SwitchStateMsg struct {
	State HintState
}

type SwitchEditorStatusMsg struct {
	Empty            bool
	AttachmentDelete bool
}

// RunningBashCountChangedMsg bash计数变化消息
type RunningBashCountChangedMsg struct {
	Count int
}

// ShellEventReceivedMsg shell事件接收消息
type ShellEventReceivedMsg struct{}

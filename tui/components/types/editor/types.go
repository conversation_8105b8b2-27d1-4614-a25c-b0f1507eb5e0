package editor

import (
	"time"

	tea "github.com/charmbracelet/bubbletea"
	coreconfig "github.com/qoder-ai/qodercli/core/config"
	"github.com/qoder-ai/qodercli/core/llm/command"
	"github.com/qoder-ai/qodercli/core/message"
	"github.com/qoder-ai/qodercli/tui/components/chat/textarea"
	"github.com/qoder-ai/qodercli/tui/runtime"
)

// EditorMode 编辑器模式
type EditorMode int

const (
	ModeNormal EditorMode = iota
	ModeMemory
	ModeBash
)

// QueuedMessage 排队消息结构
type QueuedMessage struct {
	Text        string
	Attachments []message.Attachment
	Timestamp   time.Time
}

// EditorState 编辑器状态
type EditorState struct {
	Width       int
	Height      int
	Focused     bool
	DeleteMode  bool
	MemoryMode  bool
	BashMode    bool
	EscCount    int
	EscLastTime time.Time

	// 待发送消息
	PendingMessage *QueuedMessage
}

// HistoryEntry 历史记录条目
type HistoryEntry = coreconfig.HistoryEntry

// HistoryHandler 历史处理器接口
type HistoryHandler interface {
	// LoadHistory 加载历史记录
	LoadHistory(app runtime.AppRuntime) []HistoryEntry
	// ApplyHistoryEntry 应用历史记录条目
	ApplyHistoryEntry(entry HistoryEntry, textarea *textarea.Model) (EditorMode, []tea.Cmd)
	// SaveToHistory 保存到历史记录
	SaveToHistory(content string, attachments []message.Attachment, mode EditorMode, app runtime.AppRuntime)
	// HandleHistoryNavigation 处理历史浏览
	HandleHistoryNavigation(direction HistoryDirection, currentIndex int, maxIndex int, textarea *textarea.Model) (newIndex int, cmds []tea.Cmd)
}

// AttachmentHandler 附件处理器接口
type AttachmentHandler interface {
	// AddAttachment 添加附件
	AddAttachment(attachment message.Attachment, currentAttachments []message.Attachment) ([]message.Attachment, error)
	// DeleteAttachment 删除附件
	DeleteAttachment(index int, attachments []message.Attachment) []message.Attachment
	// DeleteAllAttachments 删除所有附件
	DeleteAllAttachments() []message.Attachment
	// ProcessImageAttachment 处理图片附件
	ProcessImageAttachment(imageData []byte) (message.Attachment, error)
	// RenderAttachments 渲染附件列表
	RenderAttachments(attachments []message.Attachment, deleteMode bool, width int) string
	// ValidateAttachment 验证附件
	ValidateAttachment(attachment message.Attachment) error
	// HandleAttachmentDelete 处理附件删除按键
	HandleAttachmentDelete(key rune, attachments []message.Attachment) []message.Attachment
}

// ExternalEditorHandler 外部编辑器处理器接口
type ExternalEditorHandler interface {
	// OpenExternalEditor 打开外部编辑器
	OpenExternalEditor(payload command.Event) tea.Cmd
	// OpenExternalEditorWithFile 打开外部编辑器编辑文件
	OpenExternalEditorWithFile(filePath string, inputChan chan any) tea.Cmd
	// GetDefaultEditor 获取默认编辑器
	GetDefaultEditor() string
}

// PendingMessageHandler 待发送消息处理器接口
type PendingMessageHandler interface {
	// SetPendingMessage 设置待发送消息
	SetPendingMessage(text string, attachments []message.Attachment, currentPending *QueuedMessage, historyHandler HistoryHandler, mode EditorMode, app runtime.AppRuntime) (*QueuedMessage, tea.Cmd)
	// ProcessPending 处理待发送消息
	ProcessPending(pendingMessage *QueuedMessage, sessionId string, app runtime.AppRuntime) (*QueuedMessage, tea.Cmd)
	// ClearPending 清空待发送消息
	ClearPending() *QueuedMessage
	// RenderPending 渲染待发送消息
	RenderPending(pendingMessage *QueuedMessage, attachmentHandler AttachmentHandler, width int) string
	// CalculateAttachmentLimit 计算附件数量限制
	CalculateAttachmentLimit(currentAttachments []message.Attachment, pendingMessage *QueuedMessage) (bool, int)
}

// HistoryDirection 历史浏览方向
type HistoryDirection int

const (
	HistoryUp HistoryDirection = iota
	HistoryDown
)

// 消息类型定义
type (
	// SendMsg 发送消息事件
	SendMsg struct {
		Text        string
		Attachments []message.Attachment
	}

	// TriggerEditorSendMsg 触发编辑器发送当前内容
	TriggerEditorSendMsg struct{}

	// ExitMemoryModeAndClearMsg 退出记忆模式并清空输入
	ExitMemoryModeAndClearMsg struct{}

	// AttachmentAddedMsg 附件添加消息
	AttachmentAddedMsg struct {
		Attachment message.Attachment
	}

	// BashModeChangedMsg bash模式变化消息
	BashModeChangedMsg struct {
		Active bool
	}

	// MemoryModeChangedMsg 记忆模式变化消息
	MemoryModeChangedMsg struct {
		Active bool
	}

	// AutoApproveChangedMsg 自动批准变化消息
	AutoApproveChangedMsg struct {
		Enabled bool
	}

	// ForceRenderFromStartMsg 强制从头渲染消息 - 彻底清理包括回滚缓冲区
	ForceRenderFromStartMsg struct{}

	// ProcessPendingMsg 处理待发送消息事件
	ProcessPendingMsg struct{}
)

// 编辑器配置常量
const (
	MaxAttachments = 5 // 最大附件数量
)
